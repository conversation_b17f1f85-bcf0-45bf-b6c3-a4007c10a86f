# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo

# vercel
.vercel

# medusa
dist/
.cache/
uploads/
.env
.env.*
!.env.template
.DS_Store
/medusa-db.sql
/medusa-db.sql.gz

# prisma
migrations/

/src/generated/prisma
