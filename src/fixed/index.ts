import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { config } from "dotenv";
import Medusa from "@medusajs/js-sdk";
import { z } from "zod";

// Load environment variables
config();

// Constants
const MEDUSA_BACKEND_URL = process.env.MEDUSA_BACKEND_URL || "http://localhost:9000";
const DEFAULT_TENANT_ID = process.env.DEFAULT_TENANT_ID || "default";

// Create Medusa client
const medusaClient = new Medusa({
  baseUrl: MEDUSA_BACKEND_URL,
  publishableKey: process.env.PUBLISHABLE_KEY,
});

// Define tool schemas
const listProductsSchema = z.object({
  limit: z.number().optional().describe("Maximum number of products to return"),
  offset: z.number().optional().describe("Number of products to skip"),
  q: z.string().optional().describe("Search query"),
  category_id: z.string().optional().describe("Filter by category ID"),
});

const getProductSchema = z.object({
  id: z.string().describe("Product ID"),
});

const listCategoriesSchema = z.object({
  limit: z.number().optional().describe("Maximum number of categories to return"),
  offset: z.number().optional().describe("Number of categories to skip"),
  parent_id: z.string().optional().describe("Filter by parent category ID"),
});

async function main(): Promise<void> {
  console.error("Starting Medusa MCP Server...");

  // Create MCP server
  const server = new McpServer(
    {
      name: "Medusa Store MCP Server",
      version: "1.0.0",
    },
    {
      capabilities: {
        tools: {},
      },
    }
  );

  // Define tools
  server.tool(
    "listProducts",
    "List products from the Medusa store",
    listProductsSchema,
    async (input, context) => {
      try {
        console.log("Listing products with input:", input);
        console.log("Context:", context);

        // Get tenant ID from context if available
        const tenantId = context?.headers?.["x-tenant-id"] || DEFAULT_TENANT_ID;
        console.log("Using tenant ID:", tenantId);

        // Prepare query parameters
        const queryParams = new URLSearchParams();
        if (input.limit) queryParams.append("limit", input.limit.toString());
        if (input.offset) queryParams.append("offset", input.offset.toString());
        if (input.q) queryParams.append("q", input.q);
        if (input.category_id) queryParams.append("category_id", input.category_id);

        // Make request to Medusa
        const response = await medusaClient.client.fetch("/store/products", {
          method: "get",
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenant-id": tenantId,
          },
          query: queryParams,
        });

        return response;
      } catch (error) {
        console.error("Error listing products:", error);
        return {
          error: {
            message: error.message || "Failed to list products",
            code: error.status || 500,
          },
        };
      }
    }
  );

  server.tool(
    "getProduct",
    "Get a product by ID from the Medusa store",
    getProductSchema,
    async (input, context) => {
      try {
        console.log("Getting product with input:", input);
        console.log("Context:", context);

        // Get tenant ID from context if available
        const tenantId = context?.headers?.["x-tenant-id"] || DEFAULT_TENANT_ID;
        console.log("Using tenant ID:", tenantId);

        // Make request to Medusa
        const response = await medusaClient.client.fetch(`/store/products/${input.id}`, {
          method: "get",
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenant-id": tenantId,
          },
        });

        return response;
      } catch (error) {
        console.error("Error getting product:", error);
        return {
          error: {
            message: error.message || "Failed to get product",
            code: error.status || 500,
          },
        };
      }
    }
  );

  server.tool(
    "listCategories",
    "List product categories from the Medusa store",
    listCategoriesSchema,
    async (input, context) => {
      try {
        console.log("Listing categories with input:", input);
        console.log("Context:", context);

        // Get tenant ID from context if available
        const tenantId = context?.headers?.["x-tenant-id"] || DEFAULT_TENANT_ID;
        console.log("Using tenant ID:", tenantId);

        // Prepare query parameters
        const queryParams = new URLSearchParams();
        if (input.limit) queryParams.append("limit", input.limit.toString());
        if (input.offset) queryParams.append("offset", input.offset.toString());
        if (input.parent_id) queryParams.append("parent_category_id", input.parent_id);

        // Make request to Medusa
        const response = await medusaClient.client.fetch("/store/product-categories", {
          method: "get",
          headers: {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "x-tenant-id": tenantId,
          },
          query: queryParams,
        });

        return response;
      } catch (error) {
        console.error("Error listing categories:", error);
        return {
          error: {
            message: error.message || "Failed to list categories",
            code: error.status || 500,
          },
        };
      }
    }
  );

  // Connect to transport
  const transport = new StdioServerTransport();
  console.error("Connecting server to transport...");
  await server.connect(transport);

  console.error("Medusa MCP Server running on stdio");
}

main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
});
