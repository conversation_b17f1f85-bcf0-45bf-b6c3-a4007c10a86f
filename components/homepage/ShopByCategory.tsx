'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowRightIcon } from '@heroicons/react/24/outline';

interface Category {
  id: number;
  name: string;
  slug: string;
  image: string;
  productCount: number;
  description: string;
}

const defaultCategories: Category[] = [
  {
    id: 1,
    name: "Electronics",
    slug: "electronics",
    image: "/images/categories/electronics.jpg",
    productCount: 1250,
    description: "Latest gadgets and tech"
  },
  {
    id: 2,
    name: "Fashion",
    slug: "fashion",
    image: "/images/categories/fashion.jpg",
    productCount: 2100,
    description: "Trendy clothing & accessories"
  },
  {
    id: 3,
    name: "Home & Garden",
    slug: "home-garden",
    image: "/images/categories/home-garden.jpg",
    productCount: 890,
    description: "Furniture & home decor"
  },
  {
    id: 4,
    name: "Sports & Fitness",
    slug: "sports-fitness",
    image: "/images/categories/sports.jpg",
    productCount: 650,
    description: "Athletic gear & equipment"
  },
  {
    id: 5,
    name: "Books & Media",
    slug: "books-media",
    image: "/images/categories/books.jpg",
    productCount: 1800,
    description: "Books, movies & music"
  },
  {
    id: 6,
    name: "Beauty & Health",
    slug: "beauty-health",
    image: "/images/categories/beauty.jpg",
    productCount: 750,
    description: "Skincare & wellness"
  },
  {
    id: 7,
    name: "Automotive",
    slug: "automotive",
    image: "/images/categories/automotive.jpg",
    productCount: 420,
    description: "Car parts & accessories"
  },
  {
    id: 8,
    name: "Toys & Games",
    slug: "toys-games",
    image: "/images/categories/toys.jpg",
    productCount: 980,
    description: "Fun for all ages"
  }
];

export default function ShopByCategory() {
  const [categories, setCategories] = useState<Category[]>(defaultCategories);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading from API
    const loadCategories = async () => {
      try {
        // TODO: Replace with actual API call
        // const response = await fetch('/api/categories');
        // const data = await response.json();
        // setCategories(data);
        
        setTimeout(() => {
          setCategories(defaultCategories);
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error('Error loading categories:', error);
        setCategories(defaultCategories);
        setIsLoading(false);
      }
    };

    loadCategories();
  }, []);

  if (isLoading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-48 animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Shop by Category
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our wide range of products across different categories
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={`/categories/${category.slug}`}
              className="group relative overflow-hidden rounded-lg bg-gray-100 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
            >
              {/* Category Image */}
              <div className="aspect-square relative overflow-hidden">
                <div 
                  className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-300 group-hover:scale-110"
                  style={{ 
                    backgroundImage: `url(${category.image})`,
                    backgroundColor: '#f3f4f6' // Fallback color
                  }}
                />
                {/* Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-20 group-hover:bg-opacity-30 transition-all duration-300" />
                
                {/* Content */}
                <div className="absolute inset-0 flex flex-col justify-end p-4">
                  <div className="text-white">
                    <h3 className="font-semibold text-lg mb-1 group-hover:text-yellow-300 transition-colors duration-200">
                      {category.name}
                    </h3>
                    <p className="text-sm text-white/90 mb-2">
                      {category.description}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-white/80">
                        {category.productCount.toLocaleString()} items
                      </span>
                      <ArrowRightIcon className="h-4 w-4 text-white/80 group-hover:text-yellow-300 group-hover:translate-x-1 transition-all duration-200" />
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* View All Categories Button */}
        <div className="text-center mt-12">
          <Link
            href="/categories"
            className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
          >
            View All Categories
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
