'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Autoplay } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon, ClockIcon, FireIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import 'swiper/css';
import 'swiper/css/navigation';

interface Deal {
  id: number;
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  image: string;
  category: string;
  endTime: string;
  soldCount: number;
  totalStock: number;
  isLimitedTime: boolean;
}

const defaultDeals: Deal[] = [
  {
    id: 1,
    name: "4K Ultra HD Smart TV 55\"",
    slug: "4k-ultra-hd-smart-tv-55",
    originalPrice: 899.99,
    salePrice: 599.99,
    discount: 33,
    image: "/images/products/smart-tv.jpg",
    category: "Electronics",
    endTime: "2024-12-31T23:59:59",
    soldCount: 45,
    totalStock: 100,
    isLimitedTime: true
  },
  {
    id: 2,
    name: "Gaming Mechanical Keyboard",
    slug: "gaming-mechanical-keyboard",
    originalPrice: 149.99,
    salePrice: 89.99,
    discount: 40,
    image: "/images/products/gaming-keyboard.jpg",
    category: "Electronics",
    endTime: "2024-12-25T23:59:59",
    soldCount: 78,
    totalStock: 150,
    isLimitedTime: true
  },
  {
    id: 3,
    name: "Wireless Charging Station",
    slug: "wireless-charging-station",
    originalPrice: 79.99,
    salePrice: 49.99,
    discount: 38,
    image: "/images/products/charging-station.jpg",
    category: "Electronics",
    endTime: "2024-12-30T23:59:59",
    soldCount: 123,
    totalStock: 200,
    isLimitedTime: false
  },
  {
    id: 4,
    name: "Premium Yoga Mat Set",
    slug: "premium-yoga-mat-set",
    originalPrice: 59.99,
    salePrice: 34.99,
    discount: 42,
    image: "/images/products/yoga-mat.jpg",
    category: "Sports & Fitness",
    endTime: "2024-12-28T23:59:59",
    soldCount: 67,
    totalStock: 120,
    isLimitedTime: true
  },
  {
    id: 5,
    name: "Stainless Steel Cookware Set",
    slug: "stainless-steel-cookware-set",
    originalPrice: 299.99,
    salePrice: 179.99,
    discount: 40,
    image: "/images/products/cookware-set.jpg",
    category: "Home & Kitchen",
    endTime: "2024-12-26T23:59:59",
    soldCount: 34,
    totalStock: 80,
    isLimitedTime: true
  },
  {
    id: 6,
    name: "Bluetooth Portable Speaker",
    slug: "bluetooth-portable-speaker",
    originalPrice: 129.99,
    salePrice: 79.99,
    discount: 38,
    image: "/images/products/bluetooth-speaker.jpg",
    category: "Electronics",
    endTime: "2024-12-29T23:59:59",
    soldCount: 156,
    totalStock: 250,
    isLimitedTime: false
  }
];

export default function PopularDeals() {
  const [deals, setDeals] = useState<Deal[]>(defaultDeals);
  const [isLoading, setIsLoading] = useState(true);
  const [timeLeft, setTimeLeft] = useState<{ [key: number]: string }>({});

  useEffect(() => {
    const loadDeals = async () => {
      try {
        // TODO: Replace with actual API call
        setTimeout(() => {
          setDeals(defaultDeals);
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error('Error loading deals:', error);
        setDeals(defaultDeals);
        setIsLoading(false);
      }
    };

    loadDeals();
  }, []);

  useEffect(() => {
    const updateCountdowns = () => {
      const now = new Date().getTime();
      const newTimeLeft: { [key: number]: string } = {};

      deals.forEach(deal => {
        const endTime = new Date(deal.endTime).getTime();
        const difference = endTime - now;

        if (difference > 0) {
          const days = Math.floor(difference / (1000 * 60 * 60 * 24));
          const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
          
          if (days > 0) {
            newTimeLeft[deal.id] = `${days}d ${hours}h ${minutes}m`;
          } else {
            newTimeLeft[deal.id] = `${hours}h ${minutes}m`;
          }
        } else {
          newTimeLeft[deal.id] = 'Expired';
        }
      });

      setTimeLeft(newTimeLeft);
    };

    updateCountdowns();
    const interval = setInterval(updateCountdowns, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [deals]);

  const getProgressPercentage = (soldCount: number, totalStock: number) => {
    return Math.min((soldCount / totalStock) * 100, 100);
  };

  if (isLoading) {
    return (
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg border p-4">
                <div className="bg-gray-200 rounded-lg h-48 mb-4 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <div className="flex items-center mb-4">
              <FireIcon className="h-8 w-8 text-red-500 mr-3" />
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                Popular Deals
              </h2>
            </div>
            <p className="text-lg text-gray-600">
              Limited time offers you don't want to miss
            </p>
          </div>
          
          {/* Navigation Arrows */}
          <div className="hidden md:flex space-x-2">
            <button className="deals-prev p-2 rounded-full bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-all duration-200">
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button className="deals-next p-2 rounded-full bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 transition-all duration-200">
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Deals Carousel */}
        <Swiper
          modules={[Navigation, Autoplay]}
          spaceBetween={24}
          navigation={{
            prevEl: '.deals-prev',
            nextEl: '.deals-next',
          }}
          autoplay={{
            delay: 6000,
            disableOnInteraction: false,
          }}
          breakpoints={{
            640: {
              slidesPerView: 2,
            },
            1024: {
              slidesPerView: 3,
            },
          }}
          slidesPerView={1}
          className="pb-4"
        >
          {deals.map((deal) => (
            <SwiperSlide key={deal.id}>
              <div className="bg-white rounded-lg border-2 border-red-100 hover:border-red-200 hover:shadow-xl transition-all duration-300 overflow-hidden group">
                {/* Deal Image */}
                <div className="relative aspect-square overflow-hidden">
                  <div 
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-300 group-hover:scale-105"
                    style={{ 
                      backgroundImage: `url(${deal.image})`,
                      backgroundColor: '#f3f4f6'
                    }}
                  />
                  
                  {/* Discount Badge */}
                  <div className="absolute top-3 left-3 bg-red-500 text-white text-sm font-bold px-3 py-1 rounded-full">
                    -{deal.discount}%
                  </div>
                  
                  {/* Limited Time Badge */}
                  {deal.isLimitedTime && (
                    <div className="absolute top-3 right-3 bg-orange-500 text-white text-xs font-semibold px-2 py-1 rounded">
                      Limited Time
                    </div>
                  )}
                </div>

                {/* Deal Info */}
                <div className="p-4">
                  <div className="text-sm text-gray-500 mb-1">{deal.category}</div>
                  <h3 className="font-semibold text-gray-900 mb-3 line-clamp-2">
                    {deal.name}
                  </h3>
                  
                  {/* Prices */}
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="text-xl font-bold text-red-600">
                      ${deal.salePrice}
                    </span>
                    <span className="text-sm text-gray-500 line-through">
                      ${deal.originalPrice}
                    </span>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mb-3">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Sold: {deal.soldCount}</span>
                      <span>Available: {deal.totalStock - deal.soldCount}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-red-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${getProgressPercentage(deal.soldCount, deal.totalStock)}%` }}
                      ></div>
                    </div>
                  </div>
                  
                  {/* Countdown Timer */}
                  {deal.isLimitedTime && timeLeft[deal.id] && timeLeft[deal.id] !== 'Expired' && (
                    <div className="flex items-center text-sm text-orange-600 mb-4">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      <span>Ends in: {timeLeft[deal.id]}</span>
                    </div>
                  )}
                  
                  {/* Shop Now Button */}
                  <Link
                    href={`/products/${deal.slug}`}
                    className="w-full bg-red-600 text-white text-center py-2 rounded-md hover:bg-red-700 transition-colors duration-200 block font-semibold"
                  >
                    Shop Now
                  </Link>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* View All Deals Button */}
        <div className="text-center mt-12">
          <Link
            href="/deals"
            className="inline-flex items-center px-8 py-3 bg-red-600 text-white text-base font-medium rounded-md hover:bg-red-700 transition-colors duration-200"
          >
            View All Deals
            <ChevronRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
