'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Pagination, Autoplay } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/24/outline';
import 'swiper/css';
import 'swiper/css/navigation';
import 'swiper/css/pagination';

interface BannerSlide {
  id: number;
  title: string;
  subtitle: string;
  description: string;
  image: string;
  buttonText: string;
  buttonLink: string;
  backgroundColor: string;
}

const defaultSlides: BannerSlide[] = [
  {
    id: 1,
    title: "Summer Sale 2024",
    subtitle: "Up to 70% Off",
    description: "Discover amazing deals on electronics, fashion, and home essentials",
    image: "/images/banners/summer-sale.jpg",
    buttonText: "Shop Now",
    buttonLink: "/products",
    backgroundColor: "bg-gradient-to-r from-blue-600 to-purple-600"
  },
  {
    id: 2,
    title: "New Arrivals",
    subtitle: "Fresh Collection",
    description: "Explore the latest trends in fashion and lifestyle products",
    image: "/images/banners/new-arrivals.jpg",
    buttonText: "Explore",
    buttonLink: "/categories",
    backgroundColor: "bg-gradient-to-r from-green-500 to-teal-600"
  },
  {
    id: 3,
    title: "Electronics Mega Sale",
    subtitle: "Best Prices Guaranteed",
    description: "Latest smartphones, laptops, and gadgets at unbeatable prices",
    image: "/images/banners/electronics-sale.jpg",
    buttonText: "Shop Electronics",
    buttonLink: "/categories/electronics",
    backgroundColor: "bg-gradient-to-r from-orange-500 to-red-600"
  },
  {
    id: 4,
    title: "Home & Garden",
    subtitle: "Transform Your Space",
    description: "Beautiful furniture and decor to make your house a home",
    image: "/images/banners/home-garden.jpg",
    buttonText: "Shop Home",
    buttonLink: "/categories/home-garden",
    backgroundColor: "bg-gradient-to-r from-emerald-500 to-cyan-600"
  },
  {
    id: 5,
    title: "Fashion Forward",
    subtitle: "Style That Speaks",
    description: "Trendy clothing and accessories for every occasion",
    image: "/images/banners/fashion.jpg",
    buttonText: "Shop Fashion",
    buttonLink: "/categories/fashion",
    backgroundColor: "bg-gradient-to-r from-pink-500 to-rose-600"
  }
];

export default function HeroBanner() {
  const [slides, setSlides] = useState<BannerSlide[]>(defaultSlides);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading from Strapi CMS
    const loadSlidesFromStrapi = async () => {
      try {
        // TODO: Replace with actual Strapi API call
        // const response = await fetch('http://localhost:1339/api/banner-slides?populate=*');
        // const data = await response.json();
        // setSlides(data.data || defaultSlides);
        
        // For now, use default slides
        setTimeout(() => {
          setSlides(defaultSlides);
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error loading banner slides:', error);
        setSlides(defaultSlides);
        setIsLoading(false);
      }
    };

    loadSlidesFromStrapi();
  }, []);

  if (isLoading) {
    return (
      <div className="relative h-96 md:h-[500px] lg:h-[600px] bg-gray-200 animate-pulse">
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-gray-500">Loading banner...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-96 md:h-[500px] lg:h-[600px] overflow-hidden">
      <Swiper
        modules={[Navigation, Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        navigation={{
          prevEl: '.hero-prev',
          nextEl: '.hero-next',
        }}
        pagination={{
          clickable: true,
          bulletClass: 'swiper-pagination-bullet hero-bullet',
          bulletActiveClass: 'swiper-pagination-bullet-active hero-bullet-active',
        }}
        autoplay={{
          delay: 4000,
          disableOnInteraction: false,
        }}
        loop={true}
        className="h-full"
      >
        {slides.map((slide) => (
          <SwiperSlide key={slide.id}>
            <div className={`relative h-full ${slide.backgroundColor} flex items-center`}>
              {/* Background Image */}
              <div 
                className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-20"
                style={{ backgroundImage: `url(${slide.image})` }}
              />
              
              {/* Content */}
              <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
                <div className="max-w-2xl">
                  <h2 className="text-sm font-semibold text-white/90 uppercase tracking-wide mb-2">
                    {slide.subtitle}
                  </h2>
                  <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-4">
                    {slide.title}
                  </h1>
                  <p className="text-lg md:text-xl text-white/90 mb-8 max-w-lg">
                    {slide.description}
                  </p>
                  <a
                    href={slide.buttonLink}
                    className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-gray-900 bg-white hover:bg-gray-50 transition-colors duration-200 shadow-lg hover:shadow-xl"
                  >
                    {slide.buttonText}
                  </a>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom Navigation Arrows */}
      <button className="hero-prev absolute left-4 top-1/2 -translate-y-1/2 z-20 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all duration-200 backdrop-blur-sm">
        <ChevronLeftIcon className="h-6 w-6" />
      </button>
      <button className="hero-next absolute right-4 top-1/2 -translate-y-1/2 z-20 p-2 rounded-full bg-white/20 hover:bg-white/30 text-white transition-all duration-200 backdrop-blur-sm">
        <ChevronRightIcon className="h-6 w-6" />
      </button>

      {/* Custom Pagination Styles */}
      <style jsx global>{`
        .hero-bullet {
          width: 12px !important;
          height: 12px !important;
          background: rgba(255, 255, 255, 0.5) !important;
          opacity: 1 !important;
          margin: 0 6px !important;
          transition: all 0.3s ease !important;
        }
        .hero-bullet-active {
          background: white !important;
          transform: scale(1.2) !important;
        }
        .swiper-pagination {
          bottom: 20px !important;
        }
      `}</style>
    </div>
  );
}
