'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Autoplay } from 'swiper/modules';
import { ChevronLeftIcon, ChevronRightIcon, StarIcon, HeartIcon } from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon, HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';
import 'swiper/css';
import 'swiper/css/navigation';

interface Product {
  id: number;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isWishlisted: boolean;
  badge?: string;
}

const defaultProducts: Product[] = [
  {
    id: 1,
    name: "Wireless Bluetooth Headphones",
    slug: "wireless-bluetooth-headphones",
    price: 79.99,
    originalPrice: 99.99,
    image: "/images/products/headphones.jpg",
    rating: 4.5,
    reviewCount: 128,
    category: "Electronics",
    isWishlisted: false,
    badge: "Best Seller"
  },
  {
    id: 2,
    name: "Smart Fitness Watch",
    slug: "smart-fitness-watch",
    price: 199.99,
    image: "/images/products/smartwatch.jpg",
    rating: 4.8,
    reviewCount: 89,
    category: "Electronics",
    isWishlisted: true,
    badge: "New"
  },
  {
    id: 3,
    name: "Premium Coffee Maker",
    slug: "premium-coffee-maker",
    price: 149.99,
    originalPrice: 179.99,
    image: "/images/products/coffee-maker.jpg",
    rating: 4.6,
    reviewCount: 156,
    category: "Home & Kitchen",
    isWishlisted: false
  },
  {
    id: 4,
    name: "Organic Cotton T-Shirt",
    slug: "organic-cotton-tshirt",
    price: 29.99,
    image: "/images/products/tshirt.jpg",
    rating: 4.3,
    reviewCount: 67,
    category: "Fashion",
    isWishlisted: false,
    badge: "Eco-Friendly"
  },
  {
    id: 5,
    name: "Professional Camera Lens",
    slug: "professional-camera-lens",
    price: 599.99,
    originalPrice: 699.99,
    image: "/images/products/camera-lens.jpg",
    rating: 4.9,
    reviewCount: 234,
    category: "Electronics",
    isWishlisted: false,
    badge: "Pro Choice"
  },
  {
    id: 6,
    name: "Ergonomic Office Chair",
    slug: "ergonomic-office-chair",
    price: 299.99,
    image: "/images/products/office-chair.jpg",
    rating: 4.4,
    reviewCount: 92,
    category: "Furniture",
    isWishlisted: true
  }
];

export default function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>(defaultProducts);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        // TODO: Replace with actual API call
        // const response = await fetch('/api/products/featured');
        // const data = await response.json();
        // setProducts(data);
        
        setTimeout(() => {
          setProducts(defaultProducts);
          setIsLoading(false);
        }, 500);
      } catch (error) {
        console.error('Error loading featured products:', error);
        setProducts(defaultProducts);
        setIsLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);

  const toggleWishlist = (productId: number) => {
    setProducts(prev => 
      prev.map(product => 
        product.id === productId 
          ? { ...product, isWishlisted: !product.isWishlisted }
          : product
      )
    );
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<StarSolidIcon key={i} className="h-4 w-4 text-yellow-400" />);
    }

    if (hasHalfStar) {
      stars.push(<StarIcon key="half" className="h-4 w-4 text-yellow-400" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<StarIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />);
    }

    return stars;
  };

  if (isLoading) {
    return (
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="h-8 bg-gray-200 rounded w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-200 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg p-4">
                <div className="bg-gray-200 rounded-lg h-48 mb-4 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 animate-pulse"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Featured Products
            </h2>
            <p className="text-lg text-gray-600">
              Handpicked products just for you
            </p>
          </div>
          
          {/* Navigation Arrows */}
          <div className="hidden md:flex space-x-2">
            <button className="featured-prev p-2 rounded-full bg-white shadow-md hover:shadow-lg text-gray-600 hover:text-gray-900 transition-all duration-200">
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button className="featured-next p-2 rounded-full bg-white shadow-md hover:shadow-lg text-gray-600 hover:text-gray-900 transition-all duration-200">
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Products Carousel */}
        <Swiper
          modules={[Navigation, Autoplay]}
          spaceBetween={24}
          navigation={{
            prevEl: '.featured-prev',
            nextEl: '.featured-next',
          }}
          autoplay={{
            delay: 5000,
            disableOnInteraction: false,
          }}
          breakpoints={{
            640: {
              slidesPerView: 2,
            },
            768: {
              slidesPerView: 3,
            },
            1024: {
              slidesPerView: 4,
            },
          }}
          slidesPerView={1}
          className="pb-4"
        >
          {products.map((product) => (
            <SwiperSlide key={product.id}>
              <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group">
                {/* Product Image */}
                <div className="relative aspect-square overflow-hidden">
                  <div 
                    className="absolute inset-0 bg-cover bg-center bg-no-repeat transition-transform duration-300 group-hover:scale-105"
                    style={{ 
                      backgroundImage: `url(${product.image})`,
                      backgroundColor: '#f3f4f6'
                    }}
                  />
                  
                  {/* Badge */}
                  {product.badge && (
                    <div className="absolute top-3 left-3 bg-blue-600 text-white text-xs font-semibold px-2 py-1 rounded">
                      {product.badge}
                    </div>
                  )}
                  
                  {/* Wishlist Button */}
                  <button
                    onClick={() => toggleWishlist(product.id)}
                    className="absolute top-3 right-3 p-2 rounded-full bg-white/80 hover:bg-white transition-all duration-200"
                  >
                    {product.isWishlisted ? (
                      <HeartSolidIcon className="h-5 w-5 text-red-500" />
                    ) : (
                      <HeartIcon className="h-5 w-5 text-gray-600" />
                    )}
                  </button>
                </div>

                {/* Product Info */}
                <div className="p-4">
                  <div className="text-sm text-gray-500 mb-1">{product.category}</div>
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    {product.name}
                  </h3>
                  
                  {/* Rating */}
                  <div className="flex items-center mb-3">
                    <div className="flex items-center">
                      {renderStars(product.rating)}
                    </div>
                    <span className="ml-2 text-sm text-gray-500">
                      ({product.reviewCount})
                    </span>
                  </div>
                  
                  {/* Price */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-gray-900">
                        ${product.price}
                      </span>
                      {product.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          ${product.originalPrice}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {/* View Product Button */}
                  <Link
                    href={`/products/${product.slug}`}
                    className="mt-4 w-full bg-blue-600 text-white text-center py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 block"
                  >
                    View Product
                  </Link>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* View All Products Button */}
        <div className="text-center mt-12">
          <Link
            href="/products"
            className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
          >
            View All Products
            <ChevronRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
