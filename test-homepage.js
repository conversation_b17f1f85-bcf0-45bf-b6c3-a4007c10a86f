#!/usr/bin/env node

const puppeteer = require('puppeteer');

async function testHomepage() {
  console.log('🚀 Starting homepage test...');
  
  const browser = await puppeteer.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Listen for console messages
    page.on('console', msg => {
      console.log(`🖥️  Browser Console [${msg.type()}]:`, msg.text());
    });
    
    // Listen for errors
    page.on('error', err => {
      console.error('❌ Page Error:', err.message);
    });
    
    console.log('📱 Navigating to homepage...');
    await page.goto('http://localhost:3002', { waitUntil: 'networkidle0' });
    
    // Wait for the page to load
    await page.waitForTimeout(3000);
    
    // Check if Shop by Category section is present
    const shopByCategorySection = await page.$('text=Shop by Category');
    if (shopByCategorySection) {
      console.log('✅ Shop by Category section found');
    } else {
      console.log('❌ Shop by Category section not found');
    }
    
    // Check if category images are loaded
    const categoryImages = await page.$$('img[alt*="Electronics"], img[alt*="Fashion"], img[alt*="Home"]');
    console.log(`📸 Found ${categoryImages.length} category images`);
    
    // Check for any broken images
    const brokenImages = await page.evaluate(() => {
      const images = Array.from(document.querySelectorAll('img'));
      return images.filter(img => !img.complete || img.naturalWidth === 0).length;
    });
    
    if (brokenImages === 0) {
      console.log('✅ All images loaded successfully');
    } else {
      console.log(`⚠️  Found ${brokenImages} broken images`);
    }
    
    // Check if banner background color is removed
    const bannerWithBg = await page.$('[class*="bg-gradient"], [class*="bg-blue"], [class*="bg-purple"]');
    if (!bannerWithBg) {
      console.log('✅ Banner background color removed successfully');
    } else {
      console.log('⚠️  Banner still has background color classes');
    }
    
    console.log('✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

// Check if puppeteer is available
try {
  testHomepage();
} catch (error) {
  console.log('⚠️  Puppeteer not available, running basic curl test instead...');
  
  const { exec } = require('child_process');
  
  exec('curl -s http://localhost:3002', (error, stdout, stderr) => {
    if (error) {
      console.error('❌ Curl test failed:', error.message);
      return;
    }
    
    if (stdout.includes('Shop by Category')) {
      console.log('✅ Shop by Category section found in HTML');
    } else {
      console.log('❌ Shop by Category section not found in HTML');
    }
    
    if (stdout.includes('bg-gradient') || stdout.includes('bg-blue') || stdout.includes('bg-purple')) {
      console.log('⚠️  Background color classes still present in HTML');
    } else {
      console.log('✅ Background color classes removed from HTML');
    }
    
    console.log('✅ Basic test completed!');
  });
}
