#!/bin/bash

# Test MCP Services Script
# This script tests all MCP services and reports their status

echo "🔍 Testing MCP Services..."
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to test HTTP endpoint
test_endpoint() {
    local url=$1
    local name=$2
    local expected_status=${3:-200}
    
    echo -n "Testing $name... "
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo -e "${GREEN}✅ PASS${NC}"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        return 1
    fi
}

# Function to test MCP JSON-RPC endpoint
test_mcp_endpoint() {
    local url=$1
    local name=$2
    local payload=$3
    
    echo -n "Testing $name MCP endpoint... "
    
    response=$(curl -s -X POST "$url" \
        -H "Content-Type: application/json" \
        -d "$payload")
    
    if echo "$response" | grep -q '"jsonrpc":"2.0"'; then
        echo -e "${GREEN}✅ PASS${NC}"
        echo "  Response: $(echo "$response" | jq -r '.result.content[0].text' 2>/dev/null || echo "$response")"
        return 0
    else
        echo -e "${RED}❌ FAIL${NC}"
        echo "  Response: $response"
        return 1
    fi
}

# Test 1: Simple MCP Server
echo -e "\n${YELLOW}1. Simple MCP Server (Port 6278)${NC}"
test_endpoint "http://localhost:6278" "Simple MCP Server"

# Test 2: MCP Proxy
echo -e "\n${YELLOW}2. MCP Proxy (Port 6279)${NC}"
test_mcp_endpoint "http://localhost:6279" "MCP Proxy" \
    '{"jsonrpc":"2.0","method":"mcp.callTool","params":{"name":"test","args":{}},"id":1}'

# Test 3: Check if processes are running
echo -e "\n${YELLOW}3. Process Status${NC}"
echo "Checking running MCP processes..."

if pgrep -f "simple-server.js" > /dev/null; then
    echo -e "Simple Server: ${GREEN}✅ Running${NC}"
else
    echo -e "Simple Server: ${RED}❌ Not Running${NC}"
fi

if pgrep -f "mcp-proxy.js" > /dev/null; then
    echo -e "MCP Proxy: ${GREEN}✅ Running${NC}"
else
    echo -e "MCP Proxy: ${RED}❌ Not Running${NC}"
fi

# Test 4: Check ports
echo -e "\n${YELLOW}4. Port Status${NC}"
if netstat -tuln | grep -q ":6278 "; then
    echo -e "Port 6278: ${GREEN}✅ Open${NC}"
else
    echo -e "Port 6278: ${RED}❌ Closed${NC}"
fi

if netstat -tuln | grep -q ":6279 "; then
    echo -e "Port 6279: ${GREEN}✅ Open${NC}"
else
    echo -e "Port 6279: ${RED}❌ Closed${NC}"
fi

# Test 5: Environment Check
echo -e "\n${YELLOW}5. Environment Check${NC}"
if [ -f ".env" ]; then
    echo -e ".env file: ${GREEN}✅ Found${NC}"
    if grep -q "STRAPI_URL" .env; then
        echo -e "STRAPI_URL: ${GREEN}✅ Configured${NC}"
    else
        echo -e "STRAPI_URL: ${YELLOW}⚠️ Not configured${NC}"
    fi
    if grep -q "MEDUSA_BACKEND_URL" .env; then
        echo -e "MEDUSA_BACKEND_URL: ${GREEN}✅ Configured${NC}"
    else
        echo -e "MEDUSA_BACKEND_URL: ${YELLOW}⚠️ Not configured${NC}"
    fi
else
    echo -e ".env file: ${RED}❌ Not found${NC}"
fi

# Test 6: TypeScript Compilation
echo -e "\n${YELLOW}6. TypeScript Status${NC}"
if npx tsc --noEmit --skipLibCheck 2>/dev/null; then
    echo -e "TypeScript compilation: ${GREEN}✅ No errors${NC}"
else
    echo -e "TypeScript compilation: ${RED}❌ Has errors${NC}"
fi

# Summary
echo -e "\n${YELLOW}================================${NC}"
echo "🏁 MCP Services Test Complete"
echo -e "${YELLOW}================================${NC}"

# Recommendations
echo -e "\n${YELLOW}📋 Recommendations:${NC}"
echo "1. Ensure all MCP services are running before testing"
echo "2. Check .env file for proper configuration"
echo "3. Fix any TypeScript compilation errors"
echo "4. Test individual endpoints manually if needed"

echo -e "\n${YELLOW}🔧 Quick Commands:${NC}"
echo "Start Simple Server: node src/fixed/simple-server.js"
echo "Start MCP Proxy: node src/fixed/mcp-proxy.js"
echo "Test MCP Proxy: curl -X POST http://localhost:6279 -H 'Content-Type: application/json' -d '{\"jsonrpc\":\"2.0\",\"method\":\"mcp.callTool\",\"params\":{\"name\":\"test\"},\"id\":1}'"
