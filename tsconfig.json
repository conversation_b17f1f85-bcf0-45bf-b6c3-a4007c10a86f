{"compilerOptions": {"target": "ES2020", "module": "CommonJS", "skipDefaultLibCheck": true, "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "outDir": "./dist", "strict": false, "declaration": true, "declarationMap": true, "sourceMap": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "noEmit": true, "incremental": true, "isolatedModules": true, "jsx": "preserve", "plugins": [{"name": "next"}]}, "include": ["patch.js", "src/**/*", ".next/types/**/*.ts"], "exclude": ["node_modules", "dist"]}