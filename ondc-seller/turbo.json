{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "tasks": {"build": {"dependsOn": ["^build", "^db:generate"], "outputs": ["dist/**", ".next/**", "!.next/cache/**", "build/**"]}, "lint": {"outputs": []}, "dev": {"dependsOn": ["^db:generate"], "cache": false, "persistent": true}, "test": {"dependsOn": ["^db:generate"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx", "lib/**/*.dart", "test/**/*.dart"]}, "test:unit": {"dependsOn": ["^db:generate"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"]}, "test:integration": {"dependsOn": ["^db:generate", "^build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"]}, "test:e2e": {"dependsOn": ["^build"], "outputs": ["test-results/**"], "inputs": ["e2e/**/*.ts", "e2e/**/*.js"]}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "migration:create": {"cache": false}, "migration:run": {"cache": false}, "migration:revert": {"cache": false}, "docker:build": {"dependsOn": ["build"], "outputs": []}, "docker:push": {"dependsOn": ["docker:build"], "outputs": []}, "api:docs": {"outputs": ["docs/**", "openapi/**"]}, "flutter:build": {"outputs": ["build/**"]}, "flutter:test": {"outputs": ["coverage/**"]}}}