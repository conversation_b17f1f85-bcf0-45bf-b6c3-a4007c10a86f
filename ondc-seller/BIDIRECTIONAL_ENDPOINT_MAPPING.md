# ONDC Seller Platform - Bidirectional Endpoint Mapping

This document provides a comprehensive mapping of all API endpoints across the ONDC Seller Platform, showing the relationships between frontend, backend services, and external APIs.

## Table of Contents

1. [Frontend Routes](#1-frontend-routes)
2. [Frontend API Routes](#2-frontend-api-routes)
3. [Backend Services](#3-backend-services)
4. [External APIs](#4-external-apis)
5. [Endpoint Flow Mapping](#5-endpoint-flow-mapping)
6. [Service Dependencies](#6-service-dependencies)

## 1. Frontend Routes

### Next.js App Router Pages

| Route                                             | File                                                              | Description                          | Status         |
| ------------------------------------------------- | ----------------------------------------------------------------- | ------------------------------------ | -------------- |
| `/`                                               | `src/app/page.tsx`                                                | Homepage with categories and banners | ✅ Active      |
| `/[slug]`                                         | `src/app/[slug]/page.tsx`                                         | Dynamic pages from Strapi CMS        | ✅ Active      |
| `/admin`                                          | `src/app/admin/page.tsx`                                          | Admin dashboard                      | ✅ Active      |
| `/products`                                       | `src/app/products/page.tsx`                                       | Product listing page                 | ✅ Active      |
| `/products/category`                              | `src/app/products/category/page.tsx`                              | Category listing                     | ✅ Active      |
| `/products/category/[categoryId]`                 | `src/app/products/category/[categoryId]/page.tsx`                 | Category products                    | ✅ Active      |
| `/products/category/[categoryId]/[subcategoryId]` | `src/app/products/category/[categoryId]/[subcategoryId]/page.tsx` | Subcategory products                 | ✅ Active      |
| `/mcp-products`                                   | `src/app/mcp-products/page.tsx`                                   | MCP products testing page            | ✅ Active      |
| `/cart`                                           | `src/app/cart/page.tsx`                                           | Shopping cart                        | ✅ Active      |
| `/test-msw`                                       | `src/app/test-msw/page.tsx`                                       | MSW testing dashboard                | 🧪 Development |
| `/test-error`                                     | `src/app/test-error/page.tsx`                                     | Error testing page                   | 🧪 Development |

### Layout Components

| Route              | File                                                                | Description             |
| ------------------ | ------------------------------------------------------------------- | ----------------------- |
| Root Layout        | `src/app/layout.tsx`                                                | Main application layout |
| Category Layout    | `src/app/products/category/layout.tsx`                              | Category pages layout   |
| Category ID Layout | `src/app/products/category/[categoryId]/layout.tsx`                 | Dynamic category layout |
| Subcategory Layout | `src/app/products/category/[categoryId]/[subcategoryId]/layout.tsx` | Subcategory layout      |

## 2. Frontend API Routes

### Next.js API Routes

| Method | Route      | File                       | Description   | Status    |
| ------ | ---------- | -------------------------- | ------------- | --------- |
| POST   | `/api/mcp` | `src/app/api/mcp/route.ts` | MCP proxy API | ✅ Active |

### Frontend API Clients

| Service      | File                         | Description                | Endpoints Used         |
| ------------ | ---------------------------- | -------------------------- | ---------------------- |
| Medusa API   | `src/lib/api.ts`             | Medusa commerce API client | `/store/*`, `/admin/*` |
| Strapi API   | `src/lib/strapi.ts`          | Strapi CMS API client      | `/api/*`               |
| MCP API      | `src/lib/mcp-api.ts`         | MCP service client         | `/api/mcp`             |
| Category API | `src/lib/category-api.ts`    | Category management        | Local data + API       |
| Cart API     | `src/lib/medusa/cart-api.ts` | Shopping cart operations   | `/store/carts/*`       |

## 3. Backend Services

### Mock Backend (Port 9000)

| Method | Endpoint               | File                         | Description     | Status    |
| ------ | ---------------------- | ---------------------------- | --------------- | --------- |
| GET    | `/health`              | `packages/backend/server.js` | Health check    | ✅ Active |
| POST   | `/store/auth`          | `packages/backend/server.js` | Authentication  | ✅ Active |
| POST   | `/auth/user/emailpass` | `packages/backend/server.js` | Legacy auth     | ✅ Active |
| GET    | `/store/products`      | `packages/backend/server.js` | Product listing | ✅ Active |
| GET    | `/store/products/:id`  | `packages/backend/server.js` | Product details | ✅ Active |
| GET    | `/store/collections`   | `packages/backend/server.js` | Collections     | ✅ Active |

### Medusa Backend (Port 9001)

#### Store API Endpoints

| Method | Endpoint                      | Description             | Usage Status |
| ------ | ----------------------------- | ----------------------- | ------------ |
| POST   | `/store/auth`                 | Customer authentication | ✅ Used      |
| GET    | `/store/auth/me`              | Current customer info   | ✅ Used      |
| DELETE | `/store/auth`                 | Customer logout         | ✅ Used      |
| GET    | `/store/products`             | Product listings        | ✅ Used      |
| GET    | `/store/products/:id`         | Product details         | ✅ Used      |
| GET    | `/store/carts`                | Cart operations         | ✅ Used      |
| POST   | `/store/carts`                | Create cart             | ✅ Used      |
| GET    | `/store/carts/:id`            | Get cart                | ✅ Used      |
| POST   | `/store/carts/:id/line-items` | Add to cart             | ✅ Used      |
| POST   | `/store/customers`            | Customer registration   | 🔄 Planned   |
| GET    | `/store/orders`               | Order history           | 🔄 Planned   |

#### Admin API Endpoints

| Method | Endpoint              | Description          | Usage Status |
| ------ | --------------------- | -------------------- | ------------ |
| POST   | `/admin/products`     | Create product       | ✅ Used      |
| POST   | `/admin/products/:id` | Update product       | ✅ Used      |
| DELETE | `/admin/products/:id` | Delete product       | ✅ Used      |
| GET    | `/admin/orders`       | Order management     | 🔄 Planned   |
| POST   | `/admin/auth`         | Admin authentication | 🔄 Planned   |

#### Custom API Routes

| Method | Endpoint        | File                            | Description           |
| ------ | --------------- | ------------------------------- | --------------------- |
| GET    | `/store/custom` | `src/api/store/custom/route.ts` | Custom store endpoint |
| GET    | `/admin/custom` | `src/api/admin/custom/route.ts` | Custom admin endpoint |

### Strapi CMS (Port 1339)

#### Content Type APIs

| Method | Endpoint                  | Description        | Usage Status |
| ------ | ------------------------- | ------------------ | ------------ |
| GET    | `/api/banners`            | Homepage banners   | ✅ Used      |
| GET    | `/api/banners/:id`        | Single banner      | ✅ Used      |
| GET    | `/api/pages`              | CMS pages          | ✅ Used      |
| GET    | `/api/pages/:id`          | Single page        | ✅ Used      |
| GET    | `/api/sellers`            | Seller profiles    | 🔄 Planned   |
| GET    | `/api/products`           | CMS products       | 🔄 Planned   |
| GET    | `/api/product-categories` | Product categories | 🔄 Planned   |
| GET    | `/api/customers`          | Customer data      | 🔄 Planned   |
| GET    | `/api/orders`             | Order data         | 🔄 Planned   |

#### Authentication APIs

| Method | Endpoint          | Description         | Usage Status |
| ------ | ----------------- | ------------------- | ------------ |
| POST   | `/api/auth/local` | User authentication | ✅ Used      |
| GET    | `/api/users/me`   | Current user info   | ✅ Used      |

#### Upload APIs

| Method | Endpoint            | Description  | Usage Status |
| ------ | ------------------- | ------------ | ------------ |
| GET    | `/api/upload/files` | Media files  | ✅ Used      |
| POST   | `/api/upload`       | Upload media | 🔄 Planned   |

### ONDC Adapter (Port 8080)

#### Health Endpoints

| Method | Endpoint           | Description          | Status    |
| ------ | ------------------ | -------------------- | --------- |
| GET    | `/health`          | Service health check | ✅ Active |
| GET    | `/health/detailed` | Detailed health info | ✅ Active |

#### ONDC Protocol Endpoints

| Method | Endpoint               | Description  | Status     |
| ------ | ---------------------- | ------------ | ---------- |
| POST   | `/api/v1/ondc/search`  | ONDC search  | 🔄 Phase 2 |
| POST   | `/api/v1/ondc/select`  | ONDC select  | 🔄 Phase 2 |
| POST   | `/api/v1/ondc/init`    | ONDC init    | 🔄 Phase 2 |
| POST   | `/api/v1/ondc/confirm` | ONDC confirm | 🔄 Phase 2 |
| POST   | `/api/v1/ondc/status`  | ONDC status  | 🔄 Phase 2 |
| POST   | `/api/v1/ondc/track`   | ONDC track   | 🔄 Phase 3 |
| POST   | `/api/v1/ondc/cancel`  | ONDC cancel  | 🔄 Phase 3 |
| POST   | `/api/v1/ondc/update`  | ONDC update  | 🔄 Phase 3 |
| POST   | `/api/v1/ondc/rating`  | ONDC rating  | 🔄 Phase 3 |

### ONDC Seller App

#### Authentication Routes

| Method | Endpoint                  | Description     | Status    |
| ------ | ------------------------- | --------------- | --------- |
| POST   | `/v1/auth/login`          | User login      | ✅ Active |
| POST   | `/v1/auth/logout`         | User logout     | ✅ Active |
| POST   | `/v1/auth/forgotPassword` | Password reset  | ✅ Active |
| POST   | `/v1/auth/updatePassword` | Update password | ✅ Active |
| POST   | `/v1/auth/resetPassword`  | Reset password  | ✅ Active |

#### Product Routes

| Method | Endpoint                   | Description     | Status    |
| ------ | -------------------------- | --------------- | --------- |
| POST   | `/v1/products`             | Create product  | ✅ Active |
| PUT    | `/v1/products/:id`         | Update product  | ✅ Active |
| GET    | `/v1/products`             | List products   | ✅ Active |
| PUT    | `/v1/products/:id/publish` | Publish product | ✅ Active |

#### Order Routes

| Method | Endpoint         | Description  | Status    |
| ------ | ---------------- | ------------ | --------- |
| POST   | `/v1/orders`     | Create order | ✅ Active |
| GET    | `/v1/orders`     | List orders  | ✅ Active |
| GET    | `/v1/orders/:id` | Get order    | ✅ Active |

## 4. External APIs

### Mock Service Worker (MSW)

| Method | Endpoint              | Description          | Environment |
| ------ | --------------------- | -------------------- | ----------- |
| GET    | `/health`             | Health check         | Development |
| GET    | `/store/products`     | Mock products        | Development |
| GET    | `/store/products/:id` | Mock product details | Development |
| GET    | `/store/collections`  | Mock collections     | Development |
| POST   | `/store/auth`         | Mock authentication  | Development |

### Third-Party Services

| Service         | Endpoints      | Usage                   | Status     |
| --------------- | -------------- | ----------------------- | ---------- |
| OneSSO/Keycloak | `/auth/*`      | Authentication service  | 🔄 Planned |
| Supabase        | `/rest/v1/*`   | Database operations     | 🔄 Planned |
| RabbitMQ        | Message queues | Real-time notifications | 🔄 Planned |

## 5. Endpoint Flow Mapping

### Homepage Data Flow

```
Frontend (/) → Strapi (/api/banners) → Display banners
Frontend (/) → Mock Backend (/store/products) → Display featured products
Frontend (/) → Local Data (categories.ts) → Display categories
```

### Product Listing Flow

```
Frontend (/products) → Mock Backend (/store/products) → Display products
Frontend (/products) → MSW (if enabled) → Mock product data
```

### MCP Products Flow

```
Frontend (/mcp-products) → Frontend API (/api/mcp) → Mock Backend (/store/products)
```

### Authentication Flow

```
Frontend → Medusa (/store/auth) → JWT Token
Frontend → Strapi (/api/auth/local) → CMS Access
```

### Cart Operations Flow

```
Frontend → Medusa (/store/carts) → Cart Management
Frontend → Local Storage → Cart Persistence
```

## 6. Service Dependencies

### Port Allocation

| Service        | Port | Status     | Dependencies          |
| -------------- | ---- | ---------- | --------------------- |
| Frontend       | 3001 | ✅ Running | Mock Backend, Strapi  |
| Mock Backend   | 9000 | ✅ Running | None                  |
| Medusa Backend | 9001 | 🔄 Planned | PostgreSQL            |
| Strapi CMS     | 1339 | ✅ Running | PostgreSQL            |
| ONDC Adapter   | 8080 | 🔄 Planned | Medusa, External ONDC |

### Service Communication

```
Frontend (3001)
├── → Mock Backend (9000) [Products, Auth]
├── → Strapi CMS (1339) [Content, Banners]
├── → Medusa Backend (9001) [Commerce] [Planned]
└── → ONDC Adapter (8080) [ONDC Protocol] [Planned]

Mock Backend (9000)
└── → Standalone service

Strapi CMS (1339)
└── → PostgreSQL Database

ONDC Adapter (8080) [Planned]
├── → Medusa Backend (9001)
├── → External ONDC Network
└── → Authentication Service
```

### Multi-Tenant Support

All services support multi-tenancy through:

- `x-tenant-id` header
- Tenant-specific data filtering
- Tenant-based routing middleware

## Status Legend

- ✅ Active: Currently implemented and working
- 🔄 Planned: Scheduled for implementation
- 🧪 Development: Development/testing only
- ❌ Deprecated: No longer used

## 7. API Testing Endpoints

### Development Testing Routes

| Route         | Description           | Purpose                                |
| ------------- | --------------------- | -------------------------------------- |
| `/test-msw`   | MSW testing dashboard | Test mock service worker functionality |
| `/test-error` | Error testing page    | Test error handling and fallbacks      |
| `/test-mcp`   | MCP testing page      | Test MCP service integration           |

### Health Check Endpoints

| Service      | Endpoint       | Response Format                             |
| ------------ | -------------- | ------------------------------------------- |
| Mock Backend | `GET /health`  | `{"status": "ok", "timestamp": "ISO-8601"}` |
| Strapi CMS   | `GET /_health` | Strapi health format                        |
| ONDC Adapter | `GET /health`  | `{"status": "healthy", "services": {...}}`  |

## 8. Authentication & Authorization

### Token Flow

```
1. Frontend Login → POST /store/auth (Medusa)
2. Receive JWT Token → Store in localStorage
3. API Requests → Include Authorization: Bearer <token>
4. Token Refresh → POST /store/auth/token (if expired)
```

### Strapi Authentication

```
1. Frontend → POST /api/auth/local (Strapi)
2. Receive JWT → Use for CMS operations
3. API Requests → Include Authorization: Bearer <token>
```

### Multi-Tenant Headers

All API requests include:

- `x-tenant-id`: Tenant identifier
- `Authorization`: Bearer token (if authenticated)
- `Content-Type`: application/json

## 9. Error Handling

### Error Response Format

```json
{
  "error": "Error message",
  "code": "ERROR_CODE",
  "timestamp": "2025-05-26T10:30:00Z",
  "path": "/api/endpoint",
  "details": "Additional error details"
}
```

### HTTP Status Codes

| Code | Usage        | Services            |
| ---- | ------------ | ------------------- |
| 200  | Success      | All                 |
| 201  | Created      | POST endpoints      |
| 400  | Bad Request  | All                 |
| 401  | Unauthorized | Auth endpoints      |
| 403  | Forbidden    | Protected endpoints |
| 404  | Not Found    | GET endpoints       |
| 429  | Rate Limited | ONDC Adapter        |
| 500  | Server Error | All                 |

## 10. Rate Limiting

### ONDC Adapter Rate Limits

- **General API**: 60 requests/minute per IP
- **ONDC Protocol**: 100 requests/5 minutes per transaction
- **Authentication**: 10 requests/minute per IP

### Frontend Rate Limiting

- **API Routes**: 100 requests/minute per IP
- **Static Assets**: No limit
- **Development**: Rate limiting disabled

---

_Last Updated: 2025-05-26_
_Version: 1.0.0_
