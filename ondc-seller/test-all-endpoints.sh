#!/bin/bash

# ONDC Seller Platform - Comprehensive Endpoint Testing Script
# This script tests all available endpoints across all services

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Service URLs
FRONTEND_URL="http://localhost:3001"
BACKEND_URL="http://localhost:9000"
STRAPI_URL="http://localhost:1339"
MEDUSA_URL="http://localhost:9001"
ONDC_ADAPTER_URL="http://localhost:8080"

# Strapi API Token
STRAPI_TOKEN="baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2"

# Test results
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Function to print test results
print_result() {
    local test_name="$1"
    local status="$2"
    local response_code="$3"
    local details="$4"
    
    TOTAL_TESTS=$((TOTAL_TESTS + 1))
    
    if [ "$status" = "PASS" ]; then
        echo -e "${GREEN}✓ PASS${NC} $test_name (HTTP $response_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAIL${NC} $test_name (HTTP $response_code) - $details"
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# Function to test HTTP endpoint
test_endpoint() {
    local method="$1"
    local url="$2"
    local test_name="$3"
    local headers="$4"
    local data="$5"
    local expected_codes="$6"
    
    if [ -z "$expected_codes" ]; then
        expected_codes="200"
    fi
    
    echo -e "${BLUE}Testing:${NC} $test_name"
    
    # Build curl command
    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response.json"
    
    if [ -n "$headers" ]; then
        curl_cmd="$curl_cmd $headers"
    fi
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        curl_cmd="$curl_cmd -X POST -d '$data'"
    elif [ "$method" = "POST" ]; then
        curl_cmd="$curl_cmd -X POST"
    fi
    
    curl_cmd="$curl_cmd '$url'"
    
    # Execute curl command
    local response_code
    response_code=$(eval $curl_cmd 2>/dev/null || echo "000")
    
    # Check if response code is expected
    if echo "$expected_codes" | grep -q "$response_code"; then
        print_result "$test_name" "PASS" "$response_code"
        
        # Show response preview for successful requests
        if [ "$response_code" = "200" ] && [ -f "/tmp/response.json" ]; then
            local preview=$(head -c 100 /tmp/response.json 2>/dev/null || echo "")
            if [ -n "$preview" ]; then
                echo -e "${YELLOW}  Response preview:${NC} ${preview}..."
            fi
        fi
    else
        local error_details="Expected: $expected_codes, Got: $response_code"
        print_result "$test_name" "FAIL" "$response_code" "$error_details"
    fi
    
    echo ""
}

# Function to check service availability
check_service() {
    local service_name="$1"
    local url="$2"
    
    echo -e "${BLUE}Checking $service_name availability...${NC}"
    
    if curl -s --connect-timeout 5 "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ $service_name is running${NC}"
        return 0
    else
        echo -e "${RED}✗ $service_name is not available${NC}"
        return 1
    fi
}

echo "=================================================="
echo "ONDC Seller Platform - Endpoint Testing"
echo "=================================================="
echo ""

# Check service availability
echo -e "${YELLOW}1. SERVICE AVAILABILITY CHECK${NC}"
echo "=================================================="

check_service "Frontend" "$FRONTEND_URL"
FRONTEND_AVAILABLE=$?

check_service "Mock Backend" "$BACKEND_URL/health"
BACKEND_AVAILABLE=$?

check_service "Strapi CMS" "$STRAPI_URL"
STRAPI_AVAILABLE=$?

echo ""

# Test Frontend Routes
echo -e "${YELLOW}2. FRONTEND ROUTES${NC}"
echo "=================================================="

if [ $FRONTEND_AVAILABLE -eq 0 ]; then
    test_endpoint "GET" "$FRONTEND_URL/" "Homepage"
    test_endpoint "GET" "$FRONTEND_URL/products" "Products Page"
    test_endpoint "GET" "$FRONTEND_URL/products/category" "Category Page"
    test_endpoint "GET" "$FRONTEND_URL/mcp-products" "MCP Products Page"
    test_endpoint "GET" "$FRONTEND_URL/cart" "Cart Page"
    test_endpoint "GET" "$FRONTEND_URL/admin" "Admin Page"
    test_endpoint "GET" "$FRONTEND_URL/test-msw" "MSW Test Page"
    test_endpoint "GET" "$FRONTEND_URL/test-error" "Error Test Page"
else
    echo -e "${RED}Skipping frontend tests - service not available${NC}"
fi

echo ""

# Test Frontend API Routes
echo -e "${YELLOW}3. FRONTEND API ROUTES${NC}"
echo "=================================================="

if [ $FRONTEND_AVAILABLE -eq 0 ]; then
    test_endpoint "POST" "$FRONTEND_URL/api/mcp" "MCP API Endpoint" \
        "-H 'Content-Type: application/json' -H 'x-tenant-id: default'" \
        '{"tool":"getProducts","params":{"limit":5}}' \
        "200 500"
else
    echo -e "${RED}Skipping frontend API tests - service not available${NC}"
fi

echo ""

# Test Mock Backend
echo -e "${YELLOW}4. MOCK BACKEND ENDPOINTS${NC}"
echo "=================================================="

if [ $BACKEND_AVAILABLE -eq 0 ]; then
    test_endpoint "GET" "$BACKEND_URL/health" "Health Check"
    test_endpoint "GET" "$BACKEND_URL/store/products" "Products Listing" \
        "-H 'x-tenant-id: default'"
    test_endpoint "GET" "$BACKEND_URL/store/products/prod_01" "Product Details" \
        "-H 'x-tenant-id: default'"
    test_endpoint "GET" "$BACKEND_URL/store/collections" "Collections" \
        "-H 'x-tenant-id: default'"
    test_endpoint "POST" "$BACKEND_URL/store/auth" "Authentication" \
        "-H 'Content-Type: application/json'" \
        '{"email":"<EMAIL>","password":"password"}'
else
    echo -e "${RED}Skipping backend tests - service not available${NC}"
fi

echo ""

# Test Strapi CMS
echo -e "${YELLOW}5. STRAPI CMS ENDPOINTS${NC}"
echo "=================================================="

if [ $STRAPI_AVAILABLE -eq 0 ]; then
    test_endpoint "GET" "$STRAPI_URL/api/banners" "Banners API" \
        "-H 'Authorization: Bearer $STRAPI_TOKEN'"
    test_endpoint "GET" "$STRAPI_URL/api/pages" "Pages API" \
        "-H 'Authorization: Bearer $STRAPI_TOKEN'"
    test_endpoint "GET" "$STRAPI_URL/api/upload/files" "Upload Files API" \
        "-H 'Authorization: Bearer $STRAPI_TOKEN'"
    test_endpoint "GET" "$STRAPI_URL/api/sellers" "Sellers API" \
        "-H 'Authorization: Bearer $STRAPI_TOKEN'" \
        "" "200 404"
    test_endpoint "GET" "$STRAPI_URL/api/products" "CMS Products API" \
        "-H 'Authorization: Bearer $STRAPI_TOKEN'" \
        "" "200 404"
else
    echo -e "${RED}Skipping Strapi tests - service not available${NC}"
fi

echo ""

# Test Static Assets
echo -e "${YELLOW}6. STATIC ASSETS${NC}"
echo "=================================================="

if [ $FRONTEND_AVAILABLE -eq 0 ]; then
    test_endpoint "GET" "$FRONTEND_URL/images/categories/electronics.svg" "Electronics Category Image"
    test_endpoint "GET" "$FRONTEND_URL/images/categories/fashion.svg" "Fashion Category Image"
    test_endpoint "GET" "$FRONTEND_URL/images/categories/home-kitchen.svg" "Home & Kitchen Category Image"
    test_endpoint "GET" "$FRONTEND_URL/images/categories/beauty.svg" "Beauty Category Image"
    test_endpoint "GET" "$FRONTEND_URL/images/categories/grocery.svg" "Grocery Category Image"
    test_endpoint "GET" "$FRONTEND_URL/images/placeholder.png" "Placeholder Image"
else
    echo -e "${RED}Skipping static asset tests - service not available${NC}"
fi

echo ""

# Summary
echo "=================================================="
echo -e "${YELLOW}TEST SUMMARY${NC}"
echo "=================================================="
echo -e "Total Tests: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "Passed: ${GREEN}$PASSED_TESTS${NC}"
echo -e "Failed: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed!${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some tests failed. Check the output above for details.${NC}"
    exit 1
fi
