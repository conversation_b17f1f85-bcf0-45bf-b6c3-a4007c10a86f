{"name": "@ondc-seller/auth-service", "version": "0.0.1", "private": true, "description": "Authentication Service with OneSSO/Keycloak integration for ONDC Seller Platform", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rimraf dist coverage", "api:docs": "swagger-jsdoc -d swaggerDef.js src/**/*.ts -o docs/openapi.yaml", "docker:build": "docker build -t auth-service .", "docker:run": "docker run -p 3002:3002 auth-service"}, "dependencies": {"@ondc-seller/prisma": "*", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "joi": "^17.11.0", "axios": "^1.6.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "keycloak-connect": "^23.0.1", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "redis": "^4.6.10", "dotenv": "^16.3.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "rate-limiter-flexible": "^4.0.1", "express-rate-limit": "^7.1.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/express-session": "^1.17.10", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/node": "^20.10.5", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "rimraf": "^5.0.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["authentication", "keycloak", "onesso", "jwt", "oauth2", "typescript"], "author": "ONDC Seller Team", "license": "MIT"}