<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="homeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#10B981;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#047857;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#homeGrad)"/>
  
  <!-- House -->
  <path d="M80 120 L120 80 L160 120 L160 180 L80 180 Z" fill="#ffffff" opacity="0.9"/>
  <path d="M70 120 L120 70 L170 120" stroke="#ffffff" stroke-width="4" fill="none" opacity="0.9"/>
  <rect x="100" y="140" width="15" height="25" fill="#10B981"/>
  <rect x="125" y="140" width="15" height="15" fill="#10B981"/>
  
  <!-- Kitchen utensils -->
  <g fill="#ffffff" opacity="0.9">
    <!-- Pot -->
    <ellipse cx="220" cy="140" rx="20" ry="8"/>
    <rect x="200" y="132" width="40" height="20" rx="8"/>
    <rect x="195" y="135" width="8" height="4" rx="2"/>
    <rect x="237" y="135" width="8" height="4" rx="2"/>
    
    <!-- Spoon -->
    <ellipse cx="280" cy="100" rx="6" ry="4"/>
    <rect x="277" y="104" width="6" height="30" rx="3"/>
    
    <!-- Fork -->
    <rect x="310" y="100" width="4" height="35" rx="2"/>
    <rect x="308" y="98" width="2" height="8"/>
    <rect x="312" y="98" width="2" height="8"/>
    <rect x="316" y="98" width="2" height="8"/>
  </g>
  
  <!-- Sofa -->
  <rect x="200" y="180" width="80" height="40" rx="8" fill="#ffffff" opacity="0.9"/>
  <rect x="195" y="175" width="15" height="50" rx="7" fill="#ffffff" opacity="0.9"/>
  <rect x="275" y="175" width="15" height="50" rx="7" fill="#ffffff" opacity="0.9"/>
  
  <!-- Decorative elements -->
  <g fill="#ffffff" opacity="0.3">
    <circle cx="60" cy="220" r="3"/>
    <circle cx="340" cy="200" r="4"/>
    <circle cx="360" cy="160" r="3"/>
    <circle cx="40" cy="160" r="4"/>
  </g>
  
  <!-- Title -->
  <text x="200" y="250" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Home &amp; Kitchen</text>
</svg>
