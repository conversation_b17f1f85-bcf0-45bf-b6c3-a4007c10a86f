<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="fashionGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EC4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#BE185D;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#fashionGrad)"/>
  
  <!-- Dress -->
  <path d="M120 80 L140 80 L145 120 L155 180 L125 180 L115 120 Z" fill="#ffffff" opacity="0.9"/>
  <circle cx="130" cy="70" r="8" fill="#ffffff" opacity="0.9"/>
  
  <!-- Shirt -->
  <rect x="180" y="90" width="40" height="60" rx="4" fill="#ffffff" opacity="0.9"/>
  <rect x="175" y="95" width="10" height="30" rx="5" fill="#ffffff" opacity="0.9"/>
  <rect x="215" y="95" width="10" height="30" rx="5" fill="#ffffff" opacity="0.9"/>
  <circle cx="190" cy="105" r="2" fill="#EC4899"/>
  <circle cx="190" cy="115" r="2" fill="#EC4899"/>
  <circle cx="190" cy="125" r="2" fill="#EC4899"/>
  
  <!-- High heel shoe -->
  <path d="M280 160 L320 160 L325 170 L320 180 L285 180 L280 170 Z" fill="#ffffff" opacity="0.9"/>
  <rect x="275" y="170" width="8" height="20" fill="#ffffff" opacity="0.9"/>
  
  <!-- Handbag -->
  <ellipse cx="320" cy="120" rx="25" ry="20" fill="#ffffff" opacity="0.9"/>
  <path d="M300 110 Q310 100 320 110 Q330 100 340 110" stroke="#ffffff" stroke-width="3" fill="none" opacity="0.9"/>
  
  <!-- Fashion elements -->
  <g fill="#ffffff" opacity="0.4">
    <circle cx="80" cy="200" r="4"/>
    <circle cx="120" cy="220" r="3"/>
    <circle cx="160" cy="210" r="5"/>
    <circle cx="240" cy="200" r="4"/>
    <circle cx="280" cy="220" r="3"/>
    <circle cx="320" cy="210" r="4"/>
  </g>
  
  <!-- Title -->
  <text x="200" y="250" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Fashion</text>
</svg>
