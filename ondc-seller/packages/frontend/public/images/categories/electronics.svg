<svg width="400" height="300" viewBox="0 0 400 300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="electronicsGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="300" fill="url(#electronicsGrad)"/>
  
  <!-- Smartphone -->
  <rect x="80" y="80" width="60" height="100" rx="8" fill="#ffffff" opacity="0.9"/>
  <rect x="85" y="90" width="50" height="70" rx="4" fill="#1F2937"/>
  <circle cx="110" cy="175" r="3" fill="#ffffff"/>
  
  <!-- Laptop -->
  <rect x="180" y="120" width="80" height="50" rx="4" fill="#ffffff" opacity="0.9"/>
  <rect x="185" y="125" width="70" height="35" rx="2" fill="#1F2937"/>
  <rect x="180" y="170" width="80" height="8" rx="4" fill="#ffffff" opacity="0.9"/>
  
  <!-- Headphones -->
  <circle cx="320" cy="100" r="25" fill="#ffffff" opacity="0.9"/>
  <circle cx="320" cy="100" r="15" fill="#1F2937"/>
  <rect x="315" y="75" width="10" height="50" rx="5" fill="#ffffff" opacity="0.9"/>
  
  <!-- Circuit pattern -->
  <g stroke="#ffffff" stroke-width="2" fill="none" opacity="0.3">
    <line x1="50" y1="200" x2="100" y2="200"/>
    <line x1="100" y1="200" x2="100" y2="250"/>
    <line x1="100" y1="250" x2="150" y2="250"/>
    <circle cx="100" cy="200" r="3" fill="#ffffff"/>
    <circle cx="100" cy="250" r="3" fill="#ffffff"/>
    <circle cx="150" cy="250" r="3" fill="#ffffff"/>
  </g>
  
  <!-- Title -->
  <text x="200" y="250" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="24" font-weight="bold">Electronics</text>
</svg>
