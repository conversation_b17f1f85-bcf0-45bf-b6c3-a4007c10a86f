/**
 * Strapi CMS Client Library
 * 
 * This module provides a simplified interface for interacting with Strapi CMS
 * and includes caching and error handling.
 */

import { getBanners, getProducts, getBannerById, getProductById } from './strapi-api';

// Cache for storing API responses
const cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

const DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Get cached data or fetch from API
 */
async function getCachedData<T>(
  key: string,
  fetcher: () => Promise<T>,
  ttl: number = DEFAULT_TTL
): Promise<T> {
  const cached = cache.get(key);
  const now = Date.now();

  if (cached && now - cached.timestamp < cached.ttl) {
    return cached.data;
  }

  try {
    const data = await fetcher();
    cache.set(key, { data, timestamp: now, ttl });
    return data;
  } catch (error) {
    // If we have stale cached data, return it as fallback
    if (cached) {
      console.warn('API request failed, returning stale cached data:', error);
      return cached.data;
    }
    throw error;
  }
}

/**
 * Clear cache for a specific key or all cache
 */
export function clearCache(key?: string): void {
  if (key) {
    cache.delete(key);
  } else {
    cache.clear();
  }
}

/**
 * Get banners with caching
 */
export async function getCachedBanners() {
  return getCachedData('banners', getBanners);
}

/**
 * Get products with caching
 */
export async function getCachedProducts(params?: {
  page?: number;
  pageSize?: number;
  category?: string;
}) {
  const key = `products-${JSON.stringify(params || {})}`;
  return getCachedData(key, () => getProducts(params));
}

/**
 * Get banner by ID with caching
 */
export async function getCachedBannerById(id: number) {
  return getCachedData(`banner-${id}`, () => getBannerById(id));
}

/**
 * Get product by ID with caching
 */
export async function getCachedProductById(id: number) {
  return getCachedData(`product-${id}`, () => getProductById(id));
}

/**
 * Preload common data
 */
export async function preloadCommonData() {
  try {
    await Promise.all([
      getCachedBanners(),
      getCachedProducts({ page: 1, pageSize: 10 }),
    ]);
    console.log('Common data preloaded successfully');
  } catch (error) {
    console.error('Failed to preload common data:', error);
  }
}

/**
 * Health check for Strapi connection
 */
export async function checkStrapiHealth(): Promise<boolean> {
  try {
    await getBanners();
    return true;
  } catch (error) {
    console.error('Strapi health check failed:', error);
    return false;
  }
}

// Export all API functions for direct use
export * from './strapi-api';
