/**
 * Global Error Handler
 *
 * This module provides global error handling utilities for the application.
 */

export interface ErrorInfo {
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: string;
  url?: string;
  userAgent?: string;
  userId?: string;
  tenantId?: string;
}

export class ErrorHandler {
  private static instance: ErrorHandler;
  private errorQueue: ErrorInfo[] = [];
  private isOnline = true;

  private constructor() {
    this.setupGlobalHandlers();
    this.setupOnlineStatusListener();
  }

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    return ErrorHandler.instance;
  }

  private setupGlobalHandlers() {
    // Handle unhandled promise rejections
    if (typeof window !== 'undefined') {
      window.addEventListener('unhandledrejection', event => {
        this.logError(new Error(event.reason), {
          type: 'unhandledrejection',
          promise: event.promise,
        });
      });

      // Handle global JavaScript errors
      window.addEventListener('error', event => {
        this.logError(event.error || new Error(event.message), {
          type: 'javascript',
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        });
      });
    }
  }

  private setupOnlineStatusListener() {
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.isOnline = true;
        this.flushErrorQueue();
      });

      window.addEventListener('offline', () => {
        this.isOnline = false;
      });
    }
  }

  logError(error: Error, context?: Record<string, any>): void {
    const errorInfo: ErrorInfo = {
      message: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      ...context,
    };

    // Add to queue for offline handling
    this.errorQueue.push(errorInfo);

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error logged:', errorInfo);
    }

    // Send to logging service if online
    if (this.isOnline) {
      this.flushErrorQueue();
    }
  }

  private async flushErrorQueue(): Promise<void> {
    if (this.errorQueue.length === 0) return;

    const errors = [...this.errorQueue];
    this.errorQueue = [];

    try {
      // In a real application, send to your logging service
      await this.sendToLoggingService(errors);
    } catch (error) {
      // If sending fails, put errors back in queue
      this.errorQueue.unshift(...errors);
      console.error('Failed to send errors to logging service:', error);
    }
  }

  private async sendToLoggingService(errors: ErrorInfo[]): Promise<void> {
    // Mock implementation - replace with your actual logging service
    if (process.env.NODE_ENV === 'development') {
      console.log('Would send errors to logging service:', errors);
      return;
    }

    // Example implementation for a real logging service
    try {
      const response = await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ errors }),
      });

      if (!response.ok) {
        throw new Error(`Logging service responded with ${response.status}`);
      }
    } catch (error) {
      // Store in localStorage as fallback
      this.storeErrorsLocally(errors);
      throw error;
    }
  }

  private storeErrorsLocally(errors: ErrorInfo[]): void {
    try {
      const stored = localStorage.getItem('stored_errors');
      const existingErrors = stored ? JSON.parse(stored) : [];
      const allErrors = [...existingErrors, ...errors];

      // Keep only the last 100 errors to prevent localStorage bloat
      const recentErrors = allErrors.slice(-100);

      localStorage.setItem('stored_errors', JSON.stringify(recentErrors));
    } catch (error) {
      console.error('Failed to store errors locally:', error);
    }
  }

  getStoredErrors(): ErrorInfo[] {
    try {
      const stored = localStorage.getItem('stored_errors');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to retrieve stored errors:', error);
      return [];
    }
  }

  clearStoredErrors(): void {
    try {
      localStorage.removeItem('stored_errors');
    } catch (error) {
      console.error('Failed to clear stored errors:', error);
    }
  }
}

// Convenience functions
export function logError(error: Error, context?: Record<string, any>): void {
  ErrorHandler.getInstance().logError(error, context);
}

export function getStoredErrors(): ErrorInfo[] {
  return ErrorHandler.getInstance().getStoredErrors();
}

export function clearStoredErrors(): void {
  ErrorHandler.getInstance().clearStoredErrors();
}

// React Error Boundary helper
export function handleReactError(error: Error, errorInfo: { componentStack: string }): void {
  logError(error, {
    type: 'react',
    componentStack: errorInfo.componentStack,
  });
}

// Async operation wrapper with error handling
export async function withErrorHandling<T>(
  operation: () => Promise<T>,
  context?: Record<string, any>
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    logError(error instanceof Error ? error : new Error(String(error)), context);
    throw error;
  }
}

// Initialize error handler
if (typeof window !== 'undefined') {
  ErrorHandler.getInstance();
}

// Additional utility functions for compatibility
export function shouldShowErrorPage(error: unknown): boolean {
  return true; // Always show error page for now
}

export function getErrorPageStatusCode(error: unknown): number {
  if (error instanceof Error && 'statusCode' in error) {
    return (error as any).statusCode;
  }
  return 500;
}
