/**
 * Category API functions for ONDC Seller Platform
 * 
 * This module provides API functions for fetching category and product data
 * from various sources including Medusa, Strapi, and mock data.
 */

export interface ProductsQueryParams {
  page: number;
  pageSize: number;
  sortBy: string;
  filters: Record<string, string[]>;
  priceRange: { min: number; max: number } | null;
  inStockOnly: boolean;
}

export interface Product {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  handle: string;
  status: string;
  variants?: Array<{
    id: string;
    title: string;
    prices: Array<{
      amount: number;
      currency_code: string;
    }>;
    inventory_quantity: number;
  }>;
  images?: string[];
  metadata?: Record<string, any>;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  handle: string;
  subcategories?: Subcategory[];
}

export interface Subcategory {
  id: string;
  name: string;
  description?: string;
  handle: string;
  category_id: string;
}

export interface ProductsResponse {
  products: Product[];
  category?: Category;
  subcategory?: Subcategory;
  pagination?: {
    page: number;
    pageSize: number;
    pageCount: number;
    total: number;
  };
  error?: string;
}

// Mock data for development
const MOCK_CATEGORIES: Category[] = [
  {
    id: 'fashion',
    name: 'Fashion',
    description: 'Clothing and accessories for all',
    handle: 'fashion',
    subcategories: [
      { id: 'mens', name: "Men's Clothing", handle: 'mens', category_id: 'fashion' },
      { id: 'womens', name: "Women's Clothing", handle: 'womens', category_id: 'fashion' },
      { id: 'kids', name: "Kids' Clothing", handle: 'kids', category_id: 'fashion' },
      { id: 'footwear', name: 'Footwear', handle: 'footwear', category_id: 'fashion' },
    ],
  },
  {
    id: 'electronics',
    name: 'Electronics',
    description: 'Latest gadgets and electronics',
    handle: 'electronics',
    subcategories: [
      { id: 'smartphones', name: 'Smartphones', handle: 'smartphones', category_id: 'electronics' },
      { id: 'laptops', name: 'Laptops', handle: 'laptops', category_id: 'electronics' },
      { id: 'tablets', name: 'Tablets', handle: 'tablets', category_id: 'electronics' },
      { id: 'accessories', name: 'Accessories', handle: 'accessories', category_id: 'electronics' },
    ],
  },
];

const MOCK_PRODUCTS: Product[] = [
  {
    id: 'prod_1',
    title: 'Classic Cotton T-Shirt',
    description: 'Comfortable cotton t-shirt for everyday wear',
    thumbnail: '/images/products/tshirt-1.jpg',
    handle: 'classic-cotton-tshirt',
    status: 'published',
    variants: [
      {
        id: 'var_1',
        title: 'Medium / Blue',
        prices: [{ amount: 1999, currency_code: 'INR' }],
        inventory_quantity: 50,
      },
    ],
  },
  {
    id: 'prod_2',
    title: 'Smartphone Pro Max',
    description: 'Latest smartphone with advanced features',
    thumbnail: '/images/products/phone-1.jpg',
    handle: 'smartphone-pro-max',
    status: 'published',
    variants: [
      {
        id: 'var_2',
        title: '128GB / Black',
        prices: [{ amount: 79999, currency_code: 'INR' }],
        inventory_quantity: 25,
      },
    ],
  },
  {
    id: 'prod_3',
    title: 'Summer Dress',
    description: 'Light and breezy summer dress',
    thumbnail: '/images/products/dress-1.jpg',
    handle: 'summer-dress',
    status: 'published',
    variants: [
      {
        id: 'var_3',
        title: 'Small / Floral',
        prices: [{ amount: 2999, currency_code: 'INR' }],
        inventory_quantity: 30,
      },
    ],
  },
];

/**
 * Get subcategory information by category and subcategory ID
 */
export function getSubcategoryInfo(categoryId: string, subcategoryId: string): Subcategory | null {
  const category = MOCK_CATEGORIES.find(cat => cat.id === categoryId);
  if (!category || !category.subcategories) {
    return null;
  }
  
  return category.subcategories.find(sub => sub.id === subcategoryId) || null;
}

/**
 * Get category information by ID
 */
export function getCategoryInfo(categoryId: string): Category | null {
  return MOCK_CATEGORIES.find(cat => cat.id === categoryId) || null;
}

/**
 * Fetch products for a specific subcategory
 */
export async function getSubcategoryProducts(
  categoryId: string,
  subcategoryId: string,
  queryParams: ProductsQueryParams,
  tenantId: string
): Promise<ProductsResponse> {
  try {
    console.log(`[getSubcategoryProducts] Called with:`, {
      categoryId,
      subcategoryId,
      queryParams,
      tenantId,
    });

    // Get category and subcategory info
    const category = getCategoryInfo(categoryId);
    const subcategory = getSubcategoryInfo(categoryId, subcategoryId);

    if (!category || !subcategory) {
      return {
        products: [],
        error: `Category ${categoryId} or subcategory ${subcategoryId} not found`,
      };
    }

    // Filter products based on category/subcategory
    let filteredProducts = MOCK_PRODUCTS.filter(product => {
      // In a real implementation, products would have category/subcategory associations
      // For now, we'll use simple name matching
      const productTitle = product.title.toLowerCase();
      const subcategoryName = subcategory.name.toLowerCase();
      
      if (subcategoryId === 'mens' && productTitle.includes('t-shirt')) return true;
      if (subcategoryId === 'womens' && productTitle.includes('dress')) return true;
      if (subcategoryId === 'smartphones' && productTitle.includes('smartphone')) return true;
      
      return false;
    });

    // Apply filters
    if (queryParams.filters) {
      Object.entries(queryParams.filters).forEach(([filterType, values]) => {
        if (values.length > 0) {
          filteredProducts = filteredProducts.filter(product => {
            // Simple filter matching - in real implementation, this would be more sophisticated
            const productData = `${product.title} ${product.description}`.toLowerCase();
            return values.some(value => productData.includes(value.toLowerCase()));
          });
        }
      });
    }

    // Apply price range filter
    if (queryParams.priceRange) {
      filteredProducts = filteredProducts.filter(product => {
        const price = product.variants?.[0]?.prices?.[0]?.amount || 0;
        return price >= queryParams.priceRange!.min * 100 && 
               (queryParams.priceRange!.max === Infinity || price <= queryParams.priceRange!.max * 100);
      });
    }

    // Apply stock filter
    if (queryParams.inStockOnly) {
      filteredProducts = filteredProducts.filter(product => {
        const stock = product.variants?.[0]?.inventory_quantity || 0;
        return stock > 0;
      });
    }

    // Apply sorting
    filteredProducts.sort((a, b) => {
      switch (queryParams.sortBy) {
        case 'price-low':
          const priceA = a.variants?.[0]?.prices?.[0]?.amount || 0;
          const priceB = b.variants?.[0]?.prices?.[0]?.amount || 0;
          return priceA - priceB;
        case 'price-high':
          const priceA2 = a.variants?.[0]?.prices?.[0]?.amount || 0;
          const priceB2 = b.variants?.[0]?.prices?.[0]?.amount || 0;
          return priceB2 - priceA2;
        case 'name-asc':
          return a.title.localeCompare(b.title);
        case 'name-desc':
          return b.title.localeCompare(a.title);
        case 'oldest':
          return a.id.localeCompare(b.id);
        case 'newest':
        default:
          return b.id.localeCompare(a.id);
      }
    });

    // Apply pagination
    const total = filteredProducts.length;
    const pageCount = Math.ceil(total / queryParams.pageSize);
    const startIndex = (queryParams.page - 1) * queryParams.pageSize;
    const paginatedProducts = filteredProducts.slice(startIndex, startIndex + queryParams.pageSize);

    console.log(`[getSubcategoryProducts] Returning:`, {
      productsCount: paginatedProducts.length,
      total,
      pageCount,
    });

    return {
      products: paginatedProducts,
      category,
      subcategory,
      pagination: {
        page: queryParams.page,
        pageSize: queryParams.pageSize,
        pageCount,
        total,
      },
    };
  } catch (error) {
    console.error('[getSubcategoryProducts] Error:', error);
    return {
      products: [],
      error: error instanceof Error ? error.message : 'Failed to fetch products',
    };
  }
}

/**
 * Fetch products for a specific category
 */
export async function getCategoryProducts(
  categoryId: string,
  queryParams: ProductsQueryParams,
  tenantId: string
): Promise<ProductsResponse> {
  try {
    const category = getCategoryInfo(categoryId);
    
    if (!category) {
      return {
        products: [],
        error: `Category ${categoryId} not found`,
      };
    }

    // For category-level products, include all subcategory products
    let allProducts = MOCK_PRODUCTS.filter(product => {
      const productTitle = product.title.toLowerCase();
      
      if (categoryId === 'fashion') {
        return productTitle.includes('t-shirt') || productTitle.includes('dress');
      }
      if (categoryId === 'electronics') {
        return productTitle.includes('smartphone');
      }
      
      return false;
    });

    // Apply the same filtering, sorting, and pagination logic as subcategory
    // ... (similar implementation as getSubcategoryProducts)

    return {
      products: allProducts,
      category,
      pagination: {
        page: 1,
        pageSize: allProducts.length,
        pageCount: 1,
        total: allProducts.length,
      },
    };
  } catch (error) {
    console.error('[getCategoryProducts] Error:', error);
    return {
      products: [],
      error: error instanceof Error ? error.message : 'Failed to fetch products',
    };
  }
}
