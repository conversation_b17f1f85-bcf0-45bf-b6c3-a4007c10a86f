/**
 * Server-side tenant management utilities for ONDC Seller Platform
 * 
 * This module provides server-side utilities for managing multi-tenant functionality
 * in Next.js API routes and middleware.
 */

import { NextRequest, NextResponse } from 'next/server';

export interface ServerTenant {
  id: string;
  name: string;
  domain: string;
  settings?: Record<string, any>;
  isActive: boolean;
}

/**
 * Extract tenant ID from Next.js request
 * Priority: x-tenant-id header > subdomain > query parameter > default
 */
export function getTenantIdFromNextRequest(req: NextRequest): string {
  // Try to get from x-tenant-id header (set by middleware)
  const headerTenantId = req.headers.get('x-tenant-id');
  if (headerTenantId) {
    return headerTenantId;
  }

  // Try to get from URL search params
  const url = new URL(req.url);
  const queryTenantId = url.searchParams.get('tenant');
  if (queryTenantId) {
    return queryTenantId;
  }

  // Try to get from subdomain
  const hostname = url.hostname;
  const subdomain = hostname.split('.')[0];
  if (subdomain && subdomain !== 'localhost' && subdomain !== 'www') {
    return subdomain;
  }

  // Default tenant for development
  return 'demo-seller';
}

/**
 * Add tenant ID to Next.js response headers
 */
export function addTenantToResponse(response: NextResponse, tenantId: string): NextResponse {
  response.headers.set('x-tenant-id', tenantId);
  response.headers.set('x-tenant-context', 'server');
  return response;
}

/**
 * Create tenant-aware API response
 */
export function createTenantResponse(
  data: any,
  tenantId: string,
  status: number = 200
): NextResponse {
  const response = NextResponse.json(
    {
      ...data,
      tenant_id: tenantId,
      timestamp: new Date().toISOString(),
    },
    { status }
  );

  return addTenantToResponse(response, tenantId);
}

/**
 * Create tenant-aware error response
 */
export function createTenantErrorResponse(
  error: string | Error,
  tenantId: string,
  status: number = 500
): NextResponse {
  const errorMessage = error instanceof Error ? error.message : error;
  const errorStack = error instanceof Error ? error.stack : undefined;

  const response = NextResponse.json(
    {
      error: errorMessage,
      tenant_id: tenantId,
      timestamp: new Date().toISOString(),
      ...(process.env.NODE_ENV === 'development' && { stack: errorStack }),
    },
    { status }
  );

  return addTenantToResponse(response, tenantId);
}

/**
 * Validate tenant ID format for server-side usage
 */
export function isValidServerTenantId(tenantId: string): boolean {
  // Tenant ID should be alphanumeric with hyphens, 3-50 characters
  const tenantIdRegex = /^[a-z0-9-]{3,50}$/;
  return tenantIdRegex.test(tenantId);
}

/**
 * Get tenant configuration from environment or database
 */
export async function getServerTenantConfig(tenantId: string): Promise<ServerTenant | null> {
  // In a real implementation, this would fetch from a database
  // For now, return mock configuration
  const mockTenants: Record<string, ServerTenant> = {
    'demo-seller': {
      id: 'demo-seller',
      name: 'Demo Seller',
      domain: 'demo-seller.ondc.com',
      isActive: true,
      settings: {
        theme: 'blue',
        currency: 'INR',
        language: 'en',
        features: ['dashboard', 'products', 'orders'],
      },
    },
    'test-seller': {
      id: 'test-seller',
      name: 'Test Seller',
      domain: 'test-seller.ondc.com',
      isActive: true,
      settings: {
        theme: 'green',
        currency: 'INR',
        language: 'en',
        features: ['dashboard', 'products'],
      },
    },
  };

  return mockTenants[tenantId] || null;
}

/**
 * Check if tenant is active and valid
 */
export async function validateTenant(tenantId: string): Promise<boolean> {
  if (!isValidServerTenantId(tenantId)) {
    return false;
  }

  const tenant = await getServerTenantConfig(tenantId);
  return tenant !== null && tenant.isActive;
}

/**
 * Get tenant-specific database connection or configuration
 */
export function getTenantDatabaseConfig(tenantId: string): Record<string, any> {
  // In a real implementation, this would return tenant-specific database config
  // For now, return a mock configuration
  return {
    host: process.env.DATABASE_HOST || 'localhost',
    port: process.env.DATABASE_PORT || 5432,
    database: `ondc_seller_${tenantId}`,
    schema: tenantId,
    ssl: process.env.NODE_ENV === 'production',
  };
}

/**
 * Create tenant-aware API headers for external requests
 */
export function createTenantApiHeaders(tenantId: string): Record<string, string> {
  return {
    'x-tenant-id': tenantId,
    'Content-Type': 'application/json',
    'User-Agent': `ONDC-Seller-Platform/${tenantId}`,
    'x-api-version': '1.0',
  };
}

/**
 * Log tenant-specific activity
 */
export function logTenantActivity(
  tenantId: string,
  action: string,
  details?: Record<string, any>
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    tenant_id: tenantId,
    action,
    details,
    environment: process.env.NODE_ENV,
  };

  // In a real implementation, this would write to a logging service
  console.log('[TENANT_ACTIVITY]', JSON.stringify(logEntry));
}

/**
 * Middleware helper to extract and validate tenant
 */
export async function processTenantMiddleware(req: NextRequest): Promise<{
  tenantId: string;
  isValid: boolean;
  tenant: ServerTenant | null;
}> {
  const tenantId = getTenantIdFromNextRequest(req);
  const isValid = await validateTenant(tenantId);
  const tenant = isValid ? await getServerTenantConfig(tenantId) : null;

  // Log the tenant access
  logTenantActivity(tenantId, 'api_access', {
    path: req.nextUrl.pathname,
    method: req.method,
    isValid,
  });

  return {
    tenantId,
    isValid,
    tenant,
  };
}

/**
 * Create tenant-aware cache key
 */
export function createTenantCacheKey(tenantId: string, key: string): string {
  return `tenant:${tenantId}:${key}`;
}

/**
 * Get tenant-specific environment variables
 */
export function getTenantEnvironment(tenantId: string): Record<string, string> {
  // In a real implementation, this would fetch tenant-specific environment variables
  const baseEnv = {
    API_URL: process.env.API_URL || 'http://localhost:9000',
    FRONTEND_URL: process.env.FRONTEND_URL || 'http://localhost:3001',
  };

  // Add tenant-specific overrides
  const tenantEnv = {
    ...baseEnv,
    TENANT_ID: tenantId,
    TENANT_API_URL: `${baseEnv.API_URL}/tenant/${tenantId}`,
    TENANT_FRONTEND_URL: `${baseEnv.FRONTEND_URL}?tenant=${tenantId}`,
  };

  return tenantEnv;
}
