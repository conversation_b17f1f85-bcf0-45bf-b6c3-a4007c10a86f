/**
 * Tenant management utilities for ONDC Seller Platform
 * 
 * This module provides utilities for managing multi-tenant functionality
 * in the ONDC Seller Platform, including tenant identification and context.
 */

export interface Tenant {
  id: string;
  name: string;
  domain: string;
  settings?: Record<string, any>;
}

/**
 * Get the current tenant ID from various sources
 * Priority: URL parameter > localStorage > default
 */
export function getCurrentTenantId(): string {
  // In browser environment
  if (typeof window !== 'undefined') {
    // Try to get from URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const tenantFromUrl = urlParams.get('tenant');
    if (tenantFromUrl) {
      return tenantFromUrl;
    }

    // Try to get from localStorage
    const tenantFromStorage = localStorage.getItem('currentTenantId');
    if (tenantFromStorage) {
      return tenantFromStorage;
    }

    // Try to get from subdomain
    const hostname = window.location.hostname;
    const subdomain = hostname.split('.')[0];
    if (subdomain && subdomain !== 'localhost' && subdomain !== 'www') {
      return subdomain;
    }
  }

  // Default tenant for development
  return 'demo-seller';
}

/**
 * Set the current tenant ID in localStorage
 */
export function setCurrentTenantId(tenantId: string): void {
  if (typeof window !== 'undefined') {
    localStorage.setItem('currentTenantId', tenantId);
  }
}

/**
 * Get tenant information by ID
 */
export function getTenantById(tenantId: string): Tenant | null {
  // In a real implementation, this would fetch from an API
  // For now, return mock data
  const mockTenants: Record<string, Tenant> = {
    'demo-seller': {
      id: 'demo-seller',
      name: 'Demo Seller',
      domain: 'demo-seller.ondc.com',
      settings: {
        theme: 'blue',
        currency: 'INR',
        language: 'en',
      },
    },
    'test-seller': {
      id: 'test-seller',
      name: 'Test Seller',
      domain: 'test-seller.ondc.com',
      settings: {
        theme: 'green',
        currency: 'INR',
        language: 'en',
      },
    },
  };

  return mockTenants[tenantId] || null;
}

/**
 * Get all available tenants
 */
export function getAllTenants(): Tenant[] {
  // In a real implementation, this would fetch from an API
  return [
    {
      id: 'demo-seller',
      name: 'Demo Seller',
      domain: 'demo-seller.ondc.com',
    },
    {
      id: 'test-seller',
      name: 'Test Seller',
      domain: 'test-seller.ondc.com',
    },
  ];
}

/**
 * Check if a tenant exists
 */
export function tenantExists(tenantId: string): boolean {
  return getTenantById(tenantId) !== null;
}

/**
 * Get tenant-specific API headers
 */
export function getTenantHeaders(tenantId?: string): Record<string, string> {
  const currentTenantId = tenantId || getCurrentTenantId();
  return {
    'x-tenant-id': currentTenantId,
    'Content-Type': 'application/json',
  };
}

/**
 * Build tenant-specific API URL
 */
export function buildTenantApiUrl(endpoint: string, tenantId?: string): string {
  const currentTenantId = tenantId || getCurrentTenantId();
  const baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
  
  // Remove leading slash from endpoint if present
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
  
  return `${baseUrl}/${cleanEndpoint}`;
}

/**
 * Validate tenant ID format
 */
export function isValidTenantId(tenantId: string): boolean {
  // Tenant ID should be alphanumeric with hyphens, 3-50 characters
  const tenantIdRegex = /^[a-z0-9-]{3,50}$/;
  return tenantIdRegex.test(tenantId);
}

/**
 * Get tenant context for React components
 */
export function useTenantContext() {
  const tenantId = getCurrentTenantId();
  const tenant = getTenantById(tenantId);
  
  return {
    tenantId,
    tenant,
    isValidTenant: tenant !== null,
    setTenantId: setCurrentTenantId,
  };
}
