/**
 * Strapi CMS Connection Testing Utilities
 * 
 * This module provides functions to test connectivity and authentication
 * with the Strapi CMS backend.
 */

interface TestResult {
  success: boolean;
  status: number;
  statusText: string;
  data?: any;
  error?: string;
}

/**
 * Test basic connection to Strapi CMS
 */
export async function testStrapiConnection(): Promise<TestResult> {
  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
  
  try {
    console.log('Testing Strapi connection to:', strapiUrl);
    
    const response = await fetch(`${strapiUrl}/api`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    const data = await response.json();

    return {
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      data: data,
    };
  } catch (error) {
    console.error('Strapi connection test failed:', error);
    
    return {
      success: false,
      status: 0,
      statusText: 'Network Error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Test Strapi API token authentication
 */
export async function testStrapiApiToken(): Promise<TestResult> {
  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
  const apiToken = process.env.STRAPI_API_TOKEN;

  if (!apiToken) {
    return {
      success: false,
      status: 0,
      statusText: 'No API Token',
      error: 'STRAPI_API_TOKEN environment variable is not set',
    };
  }

  try {
    console.log('Testing Strapi API token authentication');
    
    const response = await fetch(`${strapiUrl}/api/users/me`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`,
      },
    });

    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      data = { message: 'Could not parse response as JSON' };
    }

    return {
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      data: data,
    };
  } catch (error) {
    console.error('Strapi API token test failed:', error);
    
    return {
      success: false,
      status: 0,
      statusText: 'Network Error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Test Strapi content types access
 */
export async function testStrapiContentTypes(): Promise<TestResult> {
  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
  const apiToken = process.env.STRAPI_API_TOKEN;

  if (!apiToken) {
    return {
      success: false,
      status: 0,
      statusText: 'No API Token',
      error: 'STRAPI_API_TOKEN environment variable is not set',
    };
  }

  try {
    console.log('Testing Strapi content types access');
    
    // Test access to a common content type (banners)
    const response = await fetch(`${strapiUrl}/api/banners`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`,
      },
    });

    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      data = { message: 'Could not parse response as JSON' };
    }

    return {
      success: response.ok,
      status: response.status,
      statusText: response.statusText,
      data: data,
    };
  } catch (error) {
    console.error('Strapi content types test failed:', error);
    
    return {
      success: false,
      status: 0,
      statusText: 'Network Error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Test Strapi GraphQL endpoint
 */
export async function testStrapiGraphQL(): Promise<TestResult> {
  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
  const apiToken = process.env.STRAPI_API_TOKEN;

  if (!apiToken) {
    return {
      success: false,
      status: 0,
      statusText: 'No API Token',
      error: 'STRAPI_API_TOKEN environment variable is not set',
    };
  }

  try {
    console.log('Testing Strapi GraphQL endpoint');
    
    const query = `
      query {
        __schema {
          types {
            name
          }
        }
      }
    `;

    const response = await fetch(`${strapiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`,
      },
      body: JSON.stringify({ query }),
    });

    let data;
    try {
      data = await response.json();
    } catch (parseError) {
      data = { message: 'Could not parse response as JSON' };
    }

    return {
      success: response.ok && !data.errors,
      status: response.status,
      statusText: response.statusText,
      data: data,
    };
  } catch (error) {
    console.error('Strapi GraphQL test failed:', error);
    
    return {
      success: false,
      status: 0,
      statusText: 'Network Error',
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Run all Strapi tests
 */
export async function runAllStrapiTests(): Promise<{
  connection: TestResult;
  apiToken: TestResult;
  contentTypes: TestResult;
  graphql: TestResult;
}> {
  console.log('Running comprehensive Strapi tests...');
  
  const [connection, apiToken, contentTypes, graphql] = await Promise.all([
    testStrapiConnection(),
    testStrapiApiToken(),
    testStrapiContentTypes(),
    testStrapiGraphQL(),
  ]);

  return {
    connection,
    apiToken,
    contentTypes,
    graphql,
  };
}
