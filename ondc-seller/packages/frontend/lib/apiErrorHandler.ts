/**
 * API Error Handler Utilities
 *
 * This module provides utilities for handling API errors consistently
 * across the application.
 */

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export class ApiErrorHandler {
  static handle(error: any): ApiError {
    // Handle fetch errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      return {
        message: 'Network error. Please check your internet connection.',
        status: 0,
        code: 'NETWORK_ERROR',
      };
    }

    // Handle HTTP errors
    if (error.status) {
      switch (error.status) {
        case 400:
          return {
            message: 'Bad request. Please check your input.',
            status: 400,
            code: 'BAD_REQUEST',
            details: error.details,
          };
        case 401:
          return {
            message: 'Unauthorized. Please log in again.',
            status: 401,
            code: 'UNAUTHORIZED',
          };
        case 403:
          return {
            message: "Access forbidden. You don't have permission to perform this action.",
            status: 403,
            code: 'FORBIDDEN',
          };
        case 404:
          return {
            message: 'Resource not found.',
            status: 404,
            code: 'NOT_FOUND',
          };
        case 429:
          return {
            message: 'Too many requests. Please try again later.',
            status: 429,
            code: 'RATE_LIMITED',
          };
        case 500:
          return {
            message: 'Internal server error. Please try again later.',
            status: 500,
            code: 'INTERNAL_ERROR',
          };
        case 502:
          return {
            message: 'Service temporarily unavailable. Please try again later.',
            status: 502,
            code: 'BAD_GATEWAY',
          };
        case 503:
          return {
            message: 'Service unavailable. Please try again later.',
            status: 503,
            code: 'SERVICE_UNAVAILABLE',
          };
        default:
          return {
            message: `HTTP error ${error.status}: ${error.statusText || 'Unknown error'}`,
            status: error.status,
            code: 'HTTP_ERROR',
          };
      }
    }

    // Handle JavaScript errors
    if (error instanceof Error) {
      return {
        message: error.message,
        code: 'JAVASCRIPT_ERROR',
      };
    }

    // Handle string errors
    if (typeof error === 'string') {
      return {
        message: error,
        code: 'STRING_ERROR',
      };
    }

    // Handle unknown errors
    return {
      message: 'An unknown error occurred. Please try again.',
      code: 'UNKNOWN_ERROR',
      details: error,
    };
  }

  static isRetryable(error: ApiError): boolean {
    const retryableCodes = [
      'NETWORK_ERROR',
      'RATE_LIMITED',
      'INTERNAL_ERROR',
      'BAD_GATEWAY',
      'SERVICE_UNAVAILABLE',
    ];

    return retryableCodes.includes(error.code || '');
  }

  static getRetryDelay(attempt: number): number {
    // Exponential backoff: 1s, 2s, 4s, 8s, 16s
    return Math.min(1000 * Math.pow(2, attempt), 16000);
  }

  static async withRetry<T>(operation: () => Promise<T>, maxAttempts: number = 3): Promise<T> {
    let lastError: ApiError;

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = this.handle(error);

        // Don't retry if error is not retryable or this is the last attempt
        if (!this.isRetryable(lastError) || attempt === maxAttempts - 1) {
          throw lastError;
        }

        // Wait before retrying
        const delay = this.getRetryDelay(attempt);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!;
  }
}

/**
 * Convenience function for handling API errors
 */
export function handleApiError(error: any): ApiError {
  return ApiErrorHandler.handle(error);
}

/**
 * Convenience function for retrying operations
 */
export function withRetry<T>(operation: () => Promise<T>, maxAttempts: number = 3): Promise<T> {
  return ApiErrorHandler.withRetry(operation, maxAttempts);
}

/**
 * Create a standardized error response
 */
export function createErrorResponse(
  message: string,
  status: number = 500,
  code?: string,
  details?: any
): ApiError {
  return {
    message,
    status,
    code,
    details,
  };
}

/**
 * Check if an error is a specific type
 */
export function isErrorType(error: ApiError, code: string): boolean {
  return error.code === code;
}

/**
 * Format error message for display to users
 */
export function formatErrorMessage(error: ApiError): string {
  // Don't expose technical details to users
  const userFriendlyMessages: Record<string, string> = {
    NETWORK_ERROR: 'Please check your internet connection and try again.',
    UNAUTHORIZED: 'Please log in to continue.',
    FORBIDDEN: "You don't have permission to perform this action.",
    NOT_FOUND: 'The requested item could not be found.',
    RATE_LIMITED: 'Too many requests. Please wait a moment and try again.',
    INTERNAL_ERROR: 'Something went wrong. Please try again later.',
    BAD_GATEWAY: 'Service is temporarily unavailable. Please try again later.',
    SERVICE_UNAVAILABLE: 'Service is currently unavailable. Please try again later.',
  };

  return userFriendlyMessages[error.code || ''] || error.message;
}

// Additional utility functions for compatibility
export function getErrorPageStatusCode(error: unknown): number {
  if (error && typeof error === 'object' && 'status' in error) {
    return (error as any).status;
  }
  return 500;
}
