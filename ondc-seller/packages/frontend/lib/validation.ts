/**
 * Comprehensive validation utilities for ONDC Seller application
 * Provides field-specific validation rules and error handling
 */

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^[+]?[\d\s\-\(\)]{10,15}$/,
  url: /^https?:\/\/.+\..+/,
  sku: /^[A-Z0-9\-_]{3,50}$/,
  couponCode: /^[A-Z0-9\-_]{3,20}$/,
  slug: /^[a-z0-9\-]+$/,
  price: /^\d+(\.\d{1,2})?$/,
  percentage: /^(100|[1-9]?\d)(\.\d{1,2})?$/,
  postalCode: /^[A-Z0-9\s\-]{3,10}$/i,
  hexColor: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
};

// Validation rules for different field types
export const FIELD_VALIDATION_RULES: Record<string, ValidationRule> = {
  // Product fields
  productName: {
    required: true,
    minLength: 2,
    maxLength: 200,
  },
  sku: {
    required: true,
    pattern: VALIDATION_PATTERNS.sku,
    custom: (value: string) => {
      if (value && value.length < 3) return 'SKU must be at least 3 characters';
      if (value && value.length > 50) return 'SKU must be less than 50 characters';
      return null;
    },
  },
  price: {
    required: true,
    min: 0.01,
    max: 999999.99,
    pattern: VALIDATION_PATTERNS.price,
  },
  description: {
    required: true,
    minLength: 10,
    maxLength: 5000,
  },
  shortDescription: {
    maxLength: 500,
  },
  quantity: {
    min: 0,
    max: 999999,
  },
  category: {
    required: true,
  },

  // User fields
  email: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
  },
  password: {
    required: true,
    minLength: 8,
    maxLength: 128,
    custom: (value: string) => {
      if (!value) return null;
      if (!/(?=.*[a-z])/.test(value)) return 'Password must contain at least one lowercase letter';
      if (!/(?=.*[A-Z])/.test(value)) return 'Password must contain at least one uppercase letter';
      if (!/(?=.*\d)/.test(value)) return 'Password must contain at least one number';
      if (!/(?=.*[@$!%*?&])/.test(value))
        return 'Password must contain at least one special character';
      return null;
    },
  },
  phone: {
    pattern: VALIDATION_PATTERNS.phone,
  },
  firstName: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },
  lastName: {
    required: true,
    minLength: 2,
    maxLength: 50,
  },

  // Coupon fields
  couponCode: {
    required: true,
    pattern: VALIDATION_PATTERNS.couponCode,
    custom: (value: string) => {
      if (value && value.length < 3) return 'Coupon code must be at least 3 characters';
      if (value && value.length > 20) return 'Coupon code must be less than 20 characters';
      return null;
    },
  },
  couponName: {
    required: true,
    minLength: 3,
    maxLength: 100,
  },
  discountValue: {
    required: true,
    min: 0.01,
    max: 100,
  },
  minimumAmount: {
    min: 0,
    max: 999999.99,
  },
  maximumDiscount: {
    min: 0,
    max: 999999.99,
  },
  startDate: {
    required: true,
  },
  endDate: {
    required: true,
  },

  // Category fields
  categoryName: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  categorySlug: {
    required: true,
    pattern: VALIDATION_PATTERNS.slug,
    minLength: 2,
    maxLength: 100,
  },
  categoryDescription: {
    required: true,
    minLength: 10,
    maxLength: 1000,
  },

  // Collection fields
  collectionName: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  collectionSlug: {
    required: true,
    pattern: VALIDATION_PATTERNS.slug,
    minLength: 2,
    maxLength: 100,
  },
  collectionDescription: {
    required: true,
    minLength: 10,
    maxLength: 1000,
  },

  // Attribute fields
  attributeName: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  attributeCode: {
    required: true,
    pattern: /^[a-z0-9_]+$/,
    minLength: 2,
    maxLength: 50,
    custom: (value: string) => {
      if (value && !/^[a-z0-9_]+$/.test(value)) {
        return 'Code must contain only lowercase letters, numbers, and underscores';
      }
      return null;
    },
  },

  // CMS fields
  pageTitle: {
    required: true,
    minLength: 3,
    maxLength: 200,
  },
  pageSlug: {
    required: true,
    pattern: VALIDATION_PATTERNS.slug,
    minLength: 3,
    maxLength: 100,
  },
  metaTitle: {
    maxLength: 60,
  },
  metaDescription: {
    maxLength: 160,
  },

  // Widget fields
  widgetName: {
    required: true,
    minLength: 3,
    maxLength: 100,
  },
  widgetTitle: {
    required: true,
    minLength: 3,
    maxLength: 200,
  },
  sortOrder: {
    min: 0,
    max: 999,
  },

  // Settings fields
  storeName: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  storeDescription: {
    maxLength: 1000,
  },
  contactEmail: {
    required: true,
    pattern: VALIDATION_PATTERNS.email,
  },
  contactPhone: {
    pattern: VALIDATION_PATTERNS.phone,
  },
  website: {
    pattern: VALIDATION_PATTERNS.url,
  },
  address: {
    required: true,
    minLength: 10,
    maxLength: 500,
  },
  city: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  state: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  postalCode: {
    required: true,
    pattern: VALIDATION_PATTERNS.postalCode,
  },
  country: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
};

/**
 * Validates a single field value against its validation rules
 */
export function validateField(
  fieldName: string,
  value: any,
  customRules?: ValidationRule
): string | null {
  const rules = customRules || FIELD_VALIDATION_RULES[fieldName];
  if (!rules) return null;

  // Required validation
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return `${formatFieldName(fieldName)} is required`;
  }

  // Skip other validations if value is empty and not required
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null;
  }

  const stringValue = String(value).trim();
  const numericValue = Number(value);

  // Length validations
  if (rules.minLength && stringValue.length < rules.minLength) {
    return `${formatFieldName(fieldName)} must be at least ${rules.minLength} characters`;
  }

  if (rules.maxLength && stringValue.length > rules.maxLength) {
    return `${formatFieldName(fieldName)} must be less than ${rules.maxLength} characters`;
  }

  // Numeric validations
  if (rules.min !== undefined && numericValue < rules.min) {
    return `${formatFieldName(fieldName)} must be at least ${rules.min}`;
  }

  if (rules.max !== undefined && numericValue > rules.max) {
    return `${formatFieldName(fieldName)} must be less than ${rules.max}`;
  }

  // Pattern validation
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    return getPatternErrorMessage(fieldName, rules.pattern);
  }

  // Custom validation
  if (rules.custom) {
    const customError = rules.custom(value);
    if (customError) return customError;
  }

  return null;
}

/**
 * Validates multiple fields at once
 */
export function validateForm(
  data: Record<string, any>,
  fieldRules?: Record<string, ValidationRule>
): ValidationResult {
  const errors: Record<string, string> = {};

  Object.keys(data).forEach(fieldName => {
    const error = validateField(fieldName, data[fieldName], fieldRules?.[fieldName]);
    if (error) {
      errors[fieldName] = error;
    }
  });

  return {
    isValid: Object.keys(errors).length === 0,
    errors,
  };
}

/**
 * Formats field names for user-friendly error messages
 */
function formatFieldName(fieldName: string): string {
  return fieldName
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .replace(/Id$/, ' ID')
    .replace(/Url$/, ' URL')
    .replace(/Sku$/, ' SKU');
}

/**
 * Gets appropriate error message for pattern validation failures
 */
function getPatternErrorMessage(fieldName: string, pattern: RegExp): string {
  const field = formatFieldName(fieldName);

  if (pattern === VALIDATION_PATTERNS.email) {
    return `${field} must be a valid email address`;
  }
  if (pattern === VALIDATION_PATTERNS.phone) {
    return `${field} must be a valid phone number`;
  }
  if (pattern === VALIDATION_PATTERNS.url) {
    return `${field} must be a valid URL`;
  }
  if (pattern === VALIDATION_PATTERNS.sku) {
    return `${field} must contain only letters, numbers, hyphens, and underscores`;
  }
  if (pattern === VALIDATION_PATTERNS.couponCode) {
    return `${field} must contain only letters, numbers, hyphens, and underscores`;
  }
  if (pattern === VALIDATION_PATTERNS.slug) {
    return `${field} must contain only lowercase letters, numbers, and hyphens`;
  }
  if (pattern === VALIDATION_PATTERNS.price) {
    return `${field} must be a valid price (e.g., 10.99)`;
  }
  if (pattern === VALIDATION_PATTERNS.percentage) {
    return `${field} must be a valid percentage (0-100)`;
  }
  if (pattern === VALIDATION_PATTERNS.postalCode) {
    return `${field} must be a valid postal code`;
  }
  if (pattern === VALIDATION_PATTERNS.hexColor) {
    return `${field} must be a valid hex color (e.g., #FF0000)`;
  }

  return `${field} format is invalid`;
}

/**
 * Real-time validation hook for form fields
 */
export function useFieldValidation(fieldName: string, value: any, customRules?: ValidationRule) {
  const error = validateField(fieldName, value, customRules);
  return { error, isValid: !error };
}

/**
 * Debounced validation for real-time feedback
 */
export function debounceValidation(fn: Function, delay: number = 300) {
  let timeoutId: NodeJS.Timeout;
  return (...args: any[]) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(null, args), delay);
  };
}
