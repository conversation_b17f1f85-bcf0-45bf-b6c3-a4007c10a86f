'use client';

import React from 'react';
import Link from 'next/link';

interface CartItem {
  id: string;
  variant_id: string;
  product_id: string;
  title: string;
  variant_title?: string;
  price: number;
  quantity: number;
  total: number;
  image?: string;
  handle?: string;
}

interface Cart {
  id: string;
  items: CartItem[];
  subtotal: number;
  total: number;
  item_count: number;
  created_at: string;
  updated_at: string;
}

interface CartSummaryProps {
  cart: Cart | null;
  loading?: boolean;
  onUpdateQuantity?: (lineItemId: string, quantity: number) => void;
  onRemoveItem?: (lineItemId: string) => void;
  showCheckoutButton?: boolean;
}

const CartSummary: React.FC<CartSummaryProps> = ({
  cart,
  loading = false,
  onUpdateQuantity,
  onRemoveItem,
  showCheckoutButton = true,
}) => {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 rounded mb-4"></div>
          <div className="space-y-3">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex justify-between">
                <div className="h-4 bg-gray-300 rounded w-1/2"></div>
                <div className="h-4 bg-gray-300 rounded w-1/4"></div>
              </div>
            ))}
          </div>
          <div className="border-t pt-4 mt-4">
            <div className="h-6 bg-gray-300 rounded w-1/3"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!cart || cart.items.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 text-center">
        <div className="text-gray-500 mb-4">
          <svg
            className="w-16 h-16 mx-auto mb-4 text-gray-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.1 5H19M7 13v6a2 2 0 002 2h6a2 2 0 002-2v-6"
            />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
        <p className="text-gray-500 mb-6">Add some products to get started</p>
        <Link
          href="/products"
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
        >
          Continue Shopping
        </Link>
      </div>
    );
  }

  const tax = cart.subtotal * 0.18; // 18% GST
  const shipping = cart.subtotal > 500 ? 0 : 50; // Free shipping over ₹500
  const finalTotal = cart.subtotal + tax + shipping;

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>

      {/* Cart Items */}
      <div className="space-y-4 mb-6">
        {cart.items.map((item) => (
          <div key={item.id} className="flex items-center space-x-4">
            {item.image && (
              <div className="w-16 h-16 bg-gray-100 rounded-md overflow-hidden flex-shrink-0">
                <img
                  src={item.image}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            <div className="flex-1 min-w-0">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {item.title}
              </h3>
              {item.variant_title && (
                <p className="text-sm text-gray-500">{item.variant_title}</p>
              )}
              <div className="flex items-center space-x-2 mt-1">
                <span className="text-sm text-gray-900">
                  {formatPrice(item.price)}
                </span>
                <span className="text-sm text-gray-500">×</span>
                <span className="text-sm text-gray-500">{item.quantity}</span>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {onUpdateQuantity && (
                <div className="flex items-center space-x-1">
                  <button
                    onClick={() => onUpdateQuantity(item.id, Math.max(0, item.quantity - 1))}
                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                    disabled={item.quantity <= 1}
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                    </svg>
                  </button>
                  <span className="w-8 text-center text-sm">{item.quantity}</span>
                  <button
                    onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                    className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>
              )}

              {onRemoveItem && (
                <button
                  onClick={() => onRemoveItem(item.id)}
                  className="text-red-500 hover:text-red-700 transition-colors"
                  title="Remove item"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              )}

              <div className="text-sm font-medium text-gray-900 min-w-0">
                {formatPrice(item.total)}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Order Totals */}
      <div className="border-t pt-4 space-y-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Subtotal ({cart.item_count} items)</span>
          <span className="text-gray-900">{formatPrice(cart.subtotal)}</span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Tax (GST 18%)</span>
          <span className="text-gray-900">{formatPrice(tax)}</span>
        </div>
        
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Shipping</span>
          <span className="text-gray-900">
            {shipping === 0 ? 'Free' : formatPrice(shipping)}
          </span>
        </div>
        
        {shipping === 0 && cart.subtotal <= 500 && (
          <div className="text-xs text-green-600">
            Free shipping on orders over ₹500
          </div>
        )}
        
        <div className="border-t pt-2 flex justify-between font-semibold">
          <span className="text-gray-900">Total</span>
          <span className="text-gray-900">{formatPrice(finalTotal)}</span>
        </div>
      </div>

      {/* Action Buttons */}
      {showCheckoutButton && (
        <div className="mt-6 space-y-3">
          <Link
            href="/checkout"
            className="w-full bg-blue-600 text-white text-center py-3 px-4 rounded-md font-medium hover:bg-blue-700 transition-colors block"
          >
            Proceed to Checkout
          </Link>
          
          <Link
            href="/products"
            className="w-full bg-gray-100 text-gray-900 text-center py-3 px-4 rounded-md font-medium hover:bg-gray-200 transition-colors block"
          >
            Continue Shopping
          </Link>
        </div>
      )}

      {/* Security Badge */}
      <div className="mt-4 flex items-center justify-center text-xs text-gray-500">
        <svg className="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
        </svg>
        Secure checkout
      </div>
    </div>
  );
};

export default CartSummary;
