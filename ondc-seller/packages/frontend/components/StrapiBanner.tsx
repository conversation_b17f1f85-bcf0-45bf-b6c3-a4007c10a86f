'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface Banner {
  id: number;
  attributes: {
    title: string;
    description?: string;
    image?: {
      data?: {
        attributes: {
          url: string;
          alternativeText?: string;
        };
      };
    };
    link?: string;
    isActive: boolean;
  };
}

interface StrapiBannerProps {
  banner: Banner;
  className?: string;
}

const StrapiBanner: React.FC<StrapiBannerProps> = ({ banner, className = '' }) => {
  const { title, description, image, link } = banner.attributes;
  
  const imageUrl = image?.data?.attributes?.url;
  const imageAlt = image?.data?.attributes?.alternativeText || title;

  const BannerContent = () => (
    <div className={`relative overflow-hidden rounded-lg bg-gradient-to-r from-blue-600 to-purple-600 ${className}`}>
      {/* Background Image */}
      {imageUrl && (
        <div className="absolute inset-0">
          <Image
            src={imageUrl.startsWith('http') ? imageUrl : `${process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339'}${imageUrl}`}
            alt={imageAlt}
            fill
            className="object-cover opacity-30"
          />
        </div>
      )}
      
      {/* Content */}
      <div className="relative z-10 p-8 text-white">
        <h2 className="text-2xl md:text-3xl font-bold mb-4">
          {title}
        </h2>
        
        {description && (
          <p className="text-lg mb-6 opacity-90 max-w-2xl">
            {description}
          </p>
        )}
        
        {link && (
          <div className="inline-flex items-center px-6 py-3 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-100 transition-colors">
            Learn More
            <svg className="w-5 h-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </div>
        )}
      </div>
    </div>
  );

  // If there's a link, wrap in Link component
  if (link) {
    return (
      <Link href={link} className="block">
        <BannerContent />
      </Link>
    );
  }

  return <BannerContent />;
};

export default StrapiBanner;
