'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { useCart } from '@/hooks/useCart';

interface ProductVariant {
  id: string;
  title: string;
  price: number;
  available: boolean;
  inventory_quantity?: number;
}

interface ProductImage {
  id: string;
  src: string;
  alt?: string;
}

interface Product {
  id: string;
  title: string;
  description: string;
  handle: string;
  vendor?: string;
  product_type?: string;
  tags?: string[];
  images: ProductImage[];
  variants: ProductVariant[];
  options?: Array<{
    name: string;
    values: string[];
  }>;
}

interface ProductDetailProps {
  product: Product;
}

const ProductDetail: React.FC<ProductDetailProps> = ({ product }) => {
  const [selectedVariant, setSelectedVariant] = useState(product.variants[0]);
  const [selectedImage, setSelectedImage] = useState(product.images[0]);
  const [quantity, setQuantity] = useState(1);
  const [isAddingToCart, setIsAddingToCart] = useState(false);

  const { addToCart } = useCart();

  const handleAddToCart = async () => {
    if (!selectedVariant || !selectedVariant.available) return;

    setIsAddingToCart(true);
    try {
      await addToCart(product.id, selectedVariant.id, quantity);
      // Show success message or redirect
      alert('Product added to cart successfully!');
    } catch (error) {
      console.error('Failed to add to cart:', error);
      alert('Failed to add product to cart. Please try again.');
    } finally {
      setIsAddingToCart(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="aspect-square relative bg-gray-100 rounded-lg overflow-hidden">
            <Image
              src={selectedImage?.src || '/images/placeholder-product.jpg'}
              alt={selectedImage?.alt || product.title}
              fill
              className="object-cover"
              priority
            />
          </div>

          {/* Thumbnail Images */}
          {product.images.length > 1 && (
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image) => (
                <button
                  key={image.id}
                  onClick={() => setSelectedImage(image)}
                  className={`aspect-square relative bg-gray-100 rounded-lg overflow-hidden border-2 transition-colors ${
                    selectedImage?.id === image.id
                      ? 'border-blue-500'
                      : 'border-transparent hover:border-gray-300'
                  }`}
                >
                  <Image
                    src={image.src}
                    alt={image.alt || product.title}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Product Information */}
        <div className="space-y-6">
          {/* Title and Price */}
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">
              {product.title}
            </h1>
            {product.vendor && (
              <p className="text-lg text-gray-600 mb-4">by {product.vendor}</p>
            )}
            <div className="text-3xl font-bold text-blue-600">
              {formatPrice(selectedVariant?.price || 0)}
            </div>
          </div>

          {/* Description */}
          <div className="prose prose-sm max-w-none">
            <p className="text-gray-700 leading-relaxed">
              {product.description}
            </p>
          </div>

          {/* Variants */}
          {product.variants.length > 1 && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Select Variant
              </h3>
              <div className="grid grid-cols-1 gap-2">
                {product.variants.map((variant) => (
                  <button
                    key={variant.id}
                    onClick={() => setSelectedVariant(variant)}
                    disabled={!variant.available}
                    className={`p-3 text-left border rounded-lg transition-colors ${
                      selectedVariant?.id === variant.id
                        ? 'border-blue-500 bg-blue-50'
                        : variant.available
                        ? 'border-gray-300 hover:border-gray-400'
                        : 'border-gray-200 bg-gray-50 cursor-not-allowed'
                    }`}
                  >
                    <div className="flex justify-between items-center">
                      <span className={variant.available ? 'text-gray-900' : 'text-gray-500'}>
                        {variant.title}
                      </span>
                      <span className={`font-medium ${variant.available ? 'text-blue-600' : 'text-gray-400'}`}>
                        {formatPrice(variant.price)}
                      </span>
                    </div>
                    {!variant.available && (
                      <span className="text-sm text-red-500">Out of Stock</span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Quantity Selector */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-3">
              Quantity
            </h3>
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setQuantity(Math.max(1, quantity - 1))}
                className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                </svg>
              </button>
              <span className="text-xl font-medium w-12 text-center">
                {quantity}
              </span>
              <button
                onClick={() => setQuantity(quantity + 1)}
                className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors"
              >
                <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
            </div>
          </div>

          {/* Add to Cart Button */}
          <div className="space-y-4">
            <button
              onClick={handleAddToCart}
              disabled={!selectedVariant?.available || isAddingToCart}
              className={`w-full py-4 px-6 rounded-lg font-medium text-lg transition-colors ${
                selectedVariant?.available && !isAddingToCart
                  ? 'bg-blue-600 text-white hover:bg-blue-700'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
              }`}
            >
              {isAddingToCart ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Adding to Cart...
                </span>
              ) : selectedVariant?.available ? (
                'Add to Cart'
              ) : (
                'Out of Stock'
              )}
            </button>

            {/* Additional Actions */}
            <div className="flex space-x-4">
              <button className="flex-1 py-3 px-4 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                Add to Wishlist
              </button>
              <button className="flex-1 py-3 px-4 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                Share
              </button>
            </div>
          </div>

          {/* Product Details */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Product Details
            </h3>
            <dl className="space-y-2">
              {product.product_type && (
                <div className="flex">
                  <dt className="text-sm font-medium text-gray-500 w-24">Type:</dt>
                  <dd className="text-sm text-gray-900">{product.product_type}</dd>
                </div>
              )}
              {product.vendor && (
                <div className="flex">
                  <dt className="text-sm font-medium text-gray-500 w-24">Brand:</dt>
                  <dd className="text-sm text-gray-900">{product.vendor}</dd>
                </div>
              )}
              {selectedVariant?.inventory_quantity !== undefined && (
                <div className="flex">
                  <dt className="text-sm font-medium text-gray-500 w-24">Stock:</dt>
                  <dd className="text-sm text-gray-900">
                    {selectedVariant.inventory_quantity} units available
                  </dd>
                </div>
              )}
            </dl>
          </div>

          {/* Tags */}
          {product.tags && product.tags.length > 0 && (
            <div className="border-t pt-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Tags
              </h3>
              <div className="flex flex-wrap gap-2">
                {product.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-full"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
