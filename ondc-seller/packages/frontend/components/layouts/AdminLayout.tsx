'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { createPortal } from 'react-dom';

import { useAuth } from '@/context/AuthContext';
import { LoadingProvider } from '@/contexts/LoadingContext';
import { SidebarNavLink, DropdownNavLink } from '@/components/admin/AdminNavLink';
import {
  HomeIcon,
  ShoppingBagIcon,
  TagIcon,
  RectangleStackIcon,
  ClipboardDocumentListIcon,
  UsersIcon,
  TicketIcon,
  DocumentTextIcon,
  PuzzlePieceIcon,
  Cog6ToothIcon,
  Bars3Icon,
  XMarkIcon,
  BellIcon,
  UserCircleIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  MagnifyingGlassIcon,
  ChartBarIcon,
  StarIcon,
  ShoppingCartIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';

interface AdminLayoutProps {
  children: React.ReactNode;
}

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<any>;
  children?: NavItem[];
}

const navigation: NavItem[] = [
  {
    name: 'Quick links',
    href: '#',
    icon: HomeIcon,
    children: [
      { name: 'Dashboard', href: '/admin', icon: HomeIcon },
      { name: 'New Product', href: '/admin/products/new', icon: ShoppingBagIcon },
      { name: 'New Coupon', href: '/admin/coupons/new', icon: TicketIcon },
    ],
  },
  {
    name: 'Catalog',
    href: '#',
    icon: ShoppingBagIcon,
    children: [
      { name: 'Products', href: '/admin/products', icon: ShoppingBagIcon },
      { name: 'Featured Management', href: '/admin/products/featured-management', icon: StarIcon },
      { name: 'Categories', href: '/admin/categories', icon: TagIcon },
      { name: 'Collections', href: '/admin/collections', icon: RectangleStackIcon },
      { name: 'Attributes', href: '/admin/attributes', icon: PuzzlePieceIcon },
    ],
  },
  {
    name: 'Sale',
    href: '#',
    icon: ClipboardDocumentListIcon,
    children: [{ name: 'Orders', href: '/admin/orders', icon: ClipboardDocumentListIcon }],
  },
  {
    name: 'Customer',
    href: '#',
    icon: UsersIcon,
    children: [
      { name: 'Customers', href: '/admin/customers', icon: UsersIcon },
      { name: 'Cart & Wishlist', href: '/admin/users/cart-wishlist', icon: ShoppingCartIcon },
    ],
  },
  {
    name: 'Analytics',
    href: '#',
    icon: ChartBarIcon,
    children: [
      { name: 'User Activity', href: '/admin/analytics/user-activity', icon: ChartBarIcon },
    ],
  },
  {
    name: 'Promotion',
    href: '#',
    icon: TicketIcon,
    children: [{ name: 'Coupons', href: '/admin/coupons', icon: TicketIcon }],
  },
  {
    name: 'CMS',
    href: '#',
    icon: DocumentTextIcon,
    children: [
      { name: 'Pages', href: '/admin/pages', icon: DocumentTextIcon },
      { name: 'Widgets', href: '/admin/widgets', icon: PuzzlePieceIcon },
    ],
  },
  { name: 'Setting', href: '/admin/settings', icon: Cog6ToothIcon },
];

export default function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [expandedSections, setExpandedSections] = useState<string[]>(['Quick links']);
  const pathname = usePathname();
  const router = useRouter();
  const { user, logout, isAdmin } = useAuth();

  // Redirect if not admin
  React.useEffect(() => {
    if (user && !isAdmin) {
      router.push('/');
    }
  }, [user, isAdmin, router]);

  const toggleSection = (sectionName: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionName)
        ? prev.filter(name => name !== sectionName)
        : [...prev, sectionName]
    );
  };

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed);
  };

  const handleLogout = () => {
    logout();
    router.push('/auth/login');
  };

  if (!user || !isAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
          <p className="text-gray-600 mb-4">You need admin privileges to access this area.</p>
          <Link
            href="/auth/login"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Login as Admin
          </Link>
        </div>
      </div>
    );
  }

  return (
    <LoadingProvider>
      <div className="h-screen flex overflow-hidden bg-gray-100">
        {/* Mobile sidebar */}
        <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'hidden'}`}>
          <div
            className="fixed inset-0 bg-gray-600 bg-opacity-75"
            onClick={() => setSidebarOpen(false)}
          />
          <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
            <div className="absolute top-0 right-0 -mr-12 pt-2">
              <button
                className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                onClick={() => setSidebarOpen(false)}
              >
                <XMarkIcon className="h-6 w-6 text-white" />
              </button>
            </div>
            <SidebarContent
              navigation={navigation}
              pathname={pathname}
              expandedSections={expandedSections}
              toggleSection={toggleSection}
            />
          </div>
        </div>

        {/* Desktop sidebar */}
        <div className="hidden md:flex md:flex-shrink-0">
          <div
            className={`flex flex-col h-screen transition-all duration-300 ${sidebarCollapsed ? 'w-16' : 'w-64'}`}
          >
            <SidebarContent
              navigation={navigation}
              pathname={pathname}
              expandedSections={expandedSections}
              toggleSection={toggleSection}
              collapsed={sidebarCollapsed}
              onToggleCollapse={toggleSidebar}
            />
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-col w-0 flex-1 overflow-hidden">
          {/* Top navigation */}
          <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
            <button
              className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden"
              onClick={() => setSidebarOpen(true)}
            >
              <Bars3Icon className="h-6 w-6" />
            </button>

            <div className="flex-1 px-4 flex justify-between">
              <div className="flex-1 flex">
                <div className="w-full flex md:ml-0">
                  <div className="relative w-full text-gray-400 focus-within:text-gray-600">
                    <div className="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                      <MagnifyingGlassIcon className="h-5 w-5" />
                    </div>
                    <input
                      className="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent"
                      placeholder="Search"
                      type="search"
                    />
                  </div>
                </div>
              </div>

              <div className="ml-4 flex items-center md:ml-6">
                <button className="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                  <BellIcon className="h-6 w-6" />
                </button>

                <div className="ml-3 relative">
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      <img
                        className="h-8 w-8 rounded-full"
                        src={user.avatar || '/images/avatars/default.jpg'}
                        alt={user.name}
                      />
                    </div>
                    <div className="hidden md:block">
                      <div className="text-sm font-medium text-gray-700">{user.name}</div>
                      <div className="text-xs text-gray-500">{user.role}</div>
                    </div>
                    <button
                      onClick={handleLogout}
                      className="text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <ArrowRightOnRectangleIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Page content */}
          <main className="flex-1 relative overflow-y-auto focus:outline-none">
            <div className="py-6">{children}</div>
          </main>
        </div>
      </div>
    </LoadingProvider>
  );
}

interface SidebarContentProps {
  navigation: NavItem[];
  pathname: string | null;
  expandedSections: string[];
  toggleSection: (sectionName: string) => void;
  collapsed?: boolean;
  onToggleCollapse?: () => void;
}

function SidebarContent({
  navigation,
  pathname,
  expandedSections,
  toggleSection,
  collapsed = false,
  onToggleCollapse,
}: SidebarContentProps) {
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = React.useState<NodeJS.Timeout | null>(null);
  const [isPopoverVisible, setIsPopoverVisible] = React.useState(false);
  const [popoverPosition, setPopoverPosition] = React.useState<{
    top: number;
    left: number;
  } | null>(null);
  const [isMounted, setIsMounted] = React.useState(false);

  // Handle mouse enter with delay and position calculation
  const handleMouseEnter = React.useCallback(
    (itemName: string, event: React.MouseEvent) => {
      if (!collapsed) return;

      if (hoverTimeout) clearTimeout(hoverTimeout);

      // Calculate position immediately while event target is available
      const target = event.currentTarget as HTMLElement;
      if (!target) {
        console.warn('No target element found for hover');
        return;
      }

      try {
        const rect = target.getBoundingClientRect();
        const position = {
          top: rect.top,
          left: rect.right + 8, // 8px gap from sidebar
        };

        console.log('Hover detected:', itemName, 'Position:', position);

        const timeout = setTimeout(() => {
          setPopoverPosition(position);
          setHoveredItem(itemName);
          setIsPopoverVisible(true);
        }, 150); // Reduced delay for better responsiveness

        setHoverTimeout(timeout);
      } catch (error) {
        console.error('Error calculating popover position:', error);
      }
    },
    [collapsed, hoverTimeout]
  );

  // Handle mouse leave
  const handleMouseLeave = React.useCallback(() => {
    if (!collapsed) return;

    if (hoverTimeout) clearTimeout(hoverTimeout);

    // Add small delay before hiding to allow mouse movement to popover
    const timeout = setTimeout(() => {
      setHoveredItem(null);
      setIsPopoverVisible(false);
    }, 150); // Increased delay for better UX

    setHoverTimeout(timeout);
  }, [collapsed, hoverTimeout]);

  // Handle immediate hide (for clicks)
  const handleImmediateHide = React.useCallback(() => {
    if (hoverTimeout) clearTimeout(hoverTimeout);
    setHoveredItem(null);
    setIsPopoverVisible(false);
  }, [hoverTimeout]);

  // Mount effect
  React.useEffect(() => {
    setIsMounted(true);
  }, []);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (hoverTimeout) clearTimeout(hoverTimeout);
    };
  }, [hoverTimeout]);
  return (
    <>
      <div className="flex flex-col h-full border-r border-gray-200 bg-white relative">
        <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
          <div className="flex items-center flex-shrink-0 px-4">
            {collapsed ? (
              <button
                onClick={onToggleCollapse}
                className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors"
                title="Expand sidebar"
              >
                <Bars3Icon className="h-5 w-5 text-white" />
              </button>
            ) : (
              <div className="flex items-center space-x-3">
                <button
                  onClick={onToggleCollapse}
                  className="p-1 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                  title="Collapse sidebar"
                >
                  <Bars3Icon className="h-5 w-5" />
                </button>
                <Link href="/admin" className="text-xl font-bold text-blue-600">
                  ONDC Seller
                </Link>
              </div>
            )}
          </div>

          <nav className="mt-5 flex-1 px-2 space-y-1 overflow-y-auto">
            {navigation.map(item => (
              <div key={item.name} className="relative">
                {item.children ? (
                  <div
                    onMouseEnter={e => handleMouseEnter(item.name, e)}
                    onMouseLeave={handleMouseLeave}
                    className="relative"
                  >
                    <button
                      onClick={() => !collapsed && toggleSection(item.name)}
                      className={`w-full group flex items-center px-2 py-2 text-sm font-medium text-gray-600 hover:bg-gray-50 hover:text-gray-900 ${
                        collapsed ? 'justify-center' : ''
                      } ${collapsed ? 'min-h-[44px]' : ''}`}
                      title={collapsed ? item.name : undefined}
                      data-menu-item={item.name}
                    >
                      <item.icon
                        className={`h-5 w-5 text-gray-400 group-hover:text-gray-500 ${
                          collapsed ? '' : 'mr-3'
                        }`}
                      />
                      {!collapsed && (
                        <>
                          {item.name}
                          <ChevronDownIcon
                            className={`ml-auto h-4 w-4 transform transition-transform ${
                              expandedSections.includes(item.name) ? 'rotate-180' : ''
                            }`}
                          />
                        </>
                      )}
                    </button>

                    {!collapsed && expandedSections.includes(item.name) && (
                      <div className="ml-6 space-y-1">
                        {item.children.map(child => (
                          <SidebarNavLink key={child.name} href={child.href} icon={child.icon}>
                            {child.name}
                          </SidebarNavLink>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <SidebarNavLink href={item.href} icon={item.icon} isCollapsed={collapsed}>
                    {item.name}
                  </SidebarNavLink>
                )}
              </div>
            ))}
          </nav>
        </div>

        <div className="flex-shrink-0 flex border-t border-gray-200 p-4">
          {collapsed ? (
            <div className="w-full flex justify-center">
              <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                <span className="text-white font-bold text-xs">E</span>
              </div>
            </div>
          ) : (
            <div className="text-xs text-gray-500">
              © {new Date().getFullYear()} ONDC Seller. All Rights Reserved.
              <br />
              Version 1.2.2
            </div>
          )}
        </div>
      </div>

      {/* Portal-based popover for collapsed sidebar */}
      {isMounted &&
        collapsed &&
        hoveredItem &&
        isPopoverVisible &&
        popoverPosition &&
        createPortal(
          <div
            className="fixed bg-white border border-gray-200 rounded-lg shadow-xl z-[9999] min-w-[200px] max-w-[280px] transition-all duration-200 ease-out"
            style={{
              top: popoverPosition.top,
              left: popoverPosition.left,
              opacity: isPopoverVisible ? 1 : 0,
              transform: isPopoverVisible
                ? 'translateX(0) scale(1)'
                : 'translateX(-8px) scale(0.95)',
            }}
            onMouseEnter={() => {
              if (hoverTimeout) clearTimeout(hoverTimeout);
              setIsPopoverVisible(true);
            }}
            onMouseLeave={handleMouseLeave}
            data-popover={hoveredItem}
          >
            <div className="py-2">
              <div className="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide border-b border-gray-100 bg-gray-50">
                {hoveredItem}
              </div>
              <div className="py-1">
                {navigation
                  .find(item => item.name === hoveredItem)
                  ?.children?.map(child => (
                    <DropdownNavLink
                      key={child.name}
                      href={child.href}
                      icon={child.icon}
                      onClick={handleImmediateHide}
                    >
                      {child.name}
                    </DropdownNavLink>
                  ))}
              </div>
            </div>
          </div>,
          document.body
        )}
    </>
  );
}
