'use client';

import { useEffect, ReactNode } from 'react';

interface MSWProviderProps {
  children: ReactNode;
}

export function MSWProvider({ children }: MSWProviderProps) {
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Check if MSW should be enabled
      const isMSWEnabled =
        process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_ENABLE_MSW === 'true';

      if (isMSWEnabled) {
        // Start MSW for API mocking
        import('../lib/msw')
          .then(async ({ startMSW }) => {
            try {
              await startMSW();
              console.log('[MSW] API mocking started');
            } catch (error) {
              console.warn('Failed to start MSW worker:', error);
            }
          })
          .catch(error => {
            console.warn('Failed to import MSW module:', error);
          });
      } else {
        console.log('[MSW] Mocking disabled');
      }
    }
  }, []);

  return <>{children}</>;
}
