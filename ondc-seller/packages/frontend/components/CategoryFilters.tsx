import React, { useState } from 'react';

interface FilterOption {
  id: string;
  name: string;
}

interface FilterGroup {
  id: string;
  name: string;
  options: FilterOption[];
}

interface PriceRange {
  min: number;
  max: number;
}

interface CategoryFiltersProps {
  filterGroups: FilterGroup[];
  priceRanges: PriceRange[];
  onFilterChange: (filters: Record<string, string[]>) => void;
  onPriceRangeChange: (priceRange: PriceRange | null) => void;
  onAvailabilityChange: (inStockOnly: boolean) => void;
  className?: string;
}

export default function CategoryFilters({
  filterGroups,
  priceRanges,
  onFilterChange,
  onPriceRangeChange,
  onAvailabilityChange,
  className = '',
}: CategoryFiltersProps) {
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});
  const [selectedPriceRange, setSelectedPriceRange] = useState<PriceRange | null>(null);
  const [inStockOnly, setInStockOnly] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});

  const handleFilterToggle = (groupId: string, optionId: string) => {
    const newFilters = { ...selectedFilters };
    
    if (!newFilters[groupId]) {
      newFilters[groupId] = [];
    }
    
    const optionIndex = newFilters[groupId].indexOf(optionId);
    if (optionIndex > -1) {
      newFilters[groupId].splice(optionIndex, 1);
      if (newFilters[groupId].length === 0) {
        delete newFilters[groupId];
      }
    } else {
      newFilters[groupId].push(optionId);
    }
    
    setSelectedFilters(newFilters);
    onFilterChange(newFilters);
  };

  const handlePriceRangeChange = (priceRange: PriceRange) => {
    const newPriceRange = selectedPriceRange?.min === priceRange.min && 
                          selectedPriceRange?.max === priceRange.max ? null : priceRange;
    setSelectedPriceRange(newPriceRange);
    onPriceRangeChange(newPriceRange);
  };

  const handleAvailabilityToggle = () => {
    const newInStockOnly = !inStockOnly;
    setInStockOnly(newInStockOnly);
    onAvailabilityChange(newInStockOnly);
  };

  const toggleGroupExpansion = (groupId: string) => {
    setExpandedGroups(prev => ({
      ...prev,
      [groupId]: !prev[groupId]
    }));
  };

  const clearAllFilters = () => {
    setSelectedFilters({});
    setSelectedPriceRange(null);
    setInStockOnly(false);
    onFilterChange({});
    onPriceRangeChange(null);
    onAvailabilityChange(false);
  };

  const hasActiveFilters = Object.keys(selectedFilters).length > 0 || 
                          selectedPriceRange !== null || 
                          inStockOnly;

  const formatPriceRange = (range: PriceRange) => {
    if (range.min === 0) {
      return `Under ₹${range.max.toLocaleString()}`;
    }
    if (range.max === Infinity) {
      return `₹${range.min.toLocaleString()}+`;
    }
    return `₹${range.min.toLocaleString()} - ₹${range.max.toLocaleString()}`;
  };

  return (
    <div className={`bg-card text-card-foreground rounded-lg border shadow-sm ${className}`}>
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">Filters</h2>
          {hasActiveFilters && (
            <button
              onClick={clearAllFilters}
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              Clear All
            </button>
          )}
        </div>

        <div className="space-y-6">
          {/* Filter Groups */}
          {filterGroups.map((group) => {
            const isExpanded = expandedGroups[group.id] !== false; // Default to expanded
            const hasSelectedOptions = selectedFilters[group.id]?.length > 0;

            return (
              <div key={group.id} className="border-b border-border pb-4 last:border-b-0">
                <button
                  onClick={() => toggleGroupExpansion(group.id)}
                  className="flex items-center justify-between w-full text-left"
                >
                  <h3 className={`font-medium ${hasSelectedOptions ? 'text-primary' : 'text-foreground'}`}>
                    {group.name}
                    {hasSelectedOptions && (
                      <span className="ml-2 text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
                        {selectedFilters[group.id].length}
                      </span>
                    )}
                  </h3>
                  <svg
                    className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isExpanded && (
                  <div className="mt-3 space-y-2">
                    {group.options.map((option) => {
                      const isSelected = selectedFilters[group.id]?.includes(option.id) || false;
                      
                      return (
                        <label
                          key={option.id}
                          className="flex items-center cursor-pointer hover:bg-secondary/50 p-1 rounded"
                        >
                          <input
                            type="checkbox"
                            checked={isSelected}
                            onChange={() => handleFilterToggle(group.id, option.id)}
                            className="h-4 w-4 rounded border-input text-primary focus:ring-ring focus:ring-2"
                          />
                          <span className="ml-3 text-sm text-foreground">{option.name}</span>
                        </label>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}

          {/* Price Range */}
          <div className="border-b border-border pb-4">
            <h3 className={`font-medium mb-3 ${selectedPriceRange ? 'text-primary' : 'text-foreground'}`}>
              Price Range
              {selectedPriceRange && (
                <span className="ml-2 text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
                  1
                </span>
              )}
            </h3>
            <div className="space-y-2">
              {priceRanges.map((range, index) => {
                const isSelected = selectedPriceRange?.min === range.min && 
                                 selectedPriceRange?.max === range.max;
                
                return (
                  <label
                    key={`price-${index}`}
                    className="flex items-center cursor-pointer hover:bg-secondary/50 p-1 rounded"
                  >
                    <input
                      type="radio"
                      name="price-range"
                      checked={isSelected}
                      onChange={() => handlePriceRangeChange(range)}
                      className="h-4 w-4 border-input text-primary focus:ring-ring focus:ring-2"
                    />
                    <span className="ml-3 text-sm text-foreground">
                      {formatPriceRange(range)}
                    </span>
                  </label>
                );
              })}
            </div>
          </div>

          {/* Availability */}
          <div>
            <h3 className={`font-medium mb-3 ${inStockOnly ? 'text-primary' : 'text-foreground'}`}>
              Availability
              {inStockOnly && (
                <span className="ml-2 text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
                  1
                </span>
              )}
            </h3>
            <label className="flex items-center cursor-pointer hover:bg-secondary/50 p-1 rounded">
              <input
                type="checkbox"
                checked={inStockOnly}
                onChange={handleAvailabilityToggle}
                className="h-4 w-4 rounded border-input text-primary focus:ring-ring focus:ring-2"
              />
              <span className="ml-3 text-sm text-foreground">In Stock Only</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );
}
