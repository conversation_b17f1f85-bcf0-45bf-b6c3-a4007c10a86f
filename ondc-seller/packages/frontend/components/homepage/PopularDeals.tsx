'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Autoplay } from 'swiper/modules';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  ClockIcon,
  FireIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import Image from 'next/image';
import 'swiper/css';
import 'swiper/css/navigation';

interface Deal {
  id: number;
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  image: string;
  category: string;
  endTime: string;
  soldCount: number;
  totalStock: number;
  isLimitedTime: boolean;
}

const defaultDeals: Deal[] = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    slug: 'premium-wireless-headphones',
    originalPrice: 899.99,
    salePrice: 599.99,
    discount: 33,
    image: '/images/products/headphones.jpg',
    category: 'Electronics',
    endTime: '2024-12-31T23:59:59',
    soldCount: 45,
    totalStock: 100,
    isLimitedTime: true,
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    slug: 'smart-fitness-watch',
    originalPrice: 149.99,
    salePrice: 89.99,
    discount: 40,
    image: '/images/products/smartwatch.jpg',
    category: 'Electronics',
    endTime: '2024-12-25T23:59:59',
    soldCount: 78,
    totalStock: 150,
    isLimitedTime: true,
  },
  {
    id: 3,
    name: 'Premium Coffee Maker',
    slug: 'premium-coffee-maker',
    originalPrice: 79.99,
    salePrice: 49.99,
    discount: 38,
    image: '/images/products/coffee-maker.jpg',
    category: 'Electronics',
    endTime: '2024-12-30T23:59:59',
    soldCount: 123,
    totalStock: 200,
    isLimitedTime: false,
  },
  {
    id: 4,
    name: 'Organic Cotton T-Shirt',
    slug: 'organic-cotton-tshirt',
    originalPrice: 59.99,
    salePrice: 34.99,
    discount: 42,
    image: '/images/products/tshirt.jpg',
    category: 'Sports & Fitness',
    endTime: '2024-12-28T23:59:59',
    soldCount: 67,
    totalStock: 120,
    isLimitedTime: true,
  },
  {
    id: 5,
    name: 'Professional Camera Lens',
    slug: 'professional-camera-lens',
    originalPrice: 299.99,
    salePrice: 179.99,
    discount: 40,
    image: '/images/products/camera-lens.jpg',
    category: 'Home & Kitchen',
    endTime: '2024-12-26T23:59:59',
    soldCount: 34,
    totalStock: 80,
    isLimitedTime: true,
  },
  {
    id: 6,
    name: 'Ergonomic Office Chair',
    slug: 'ergonomic-office-chair',
    originalPrice: 129.99,
    salePrice: 79.99,
    discount: 38,
    image: '/images/products/office-chair.jpg',
    category: 'Electronics',
    endTime: '2024-12-29T23:59:59',
    soldCount: 156,
    totalStock: 250,
    isLimitedTime: false,
  },
];

export default function PopularDeals() {
  const [deals, setDeals] = useState<Deal[]>(defaultDeals);
  const [timeLeft, setTimeLeft] = useState<{ [key: number]: string }>({});

  // Remove loading state for immediate display
  console.log('PopularDeals component rendered with deals:', deals.length);

  useEffect(() => {
    const updateCountdowns = () => {
      const now = new Date().getTime();
      const newTimeLeft: { [key: number]: string } = {};

      deals.forEach(deal => {
        const endTime = new Date(deal.endTime).getTime();
        const difference = endTime - now;

        if (difference > 0) {
          const days = Math.floor(difference / (1000 * 60 * 60 * 24));
          const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
          const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));

          if (days > 0) {
            newTimeLeft[deal.id] = `${days}d ${hours}h ${minutes}m`;
          } else {
            newTimeLeft[deal.id] = `${hours}h ${minutes}m`;
          }
        } else {
          newTimeLeft[deal.id] = 'Expired';
        }
      });

      setTimeLeft(newTimeLeft);
    };

    updateCountdowns();
    const interval = setInterval(updateCountdowns, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [deals]);

  const getProgressPercentage = (soldCount: number, totalStock: number) => {
    return Math.min((soldCount / totalStock) * 100, 100);
  };

  return (
    <section className="py-16 bg-white" data-testid="popular-deals">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <div className="flex items-center mb-4">
              <FireIcon className="h-8 w-8 text-blue-600 mr-3" />
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900">Popular Deals</h2>
            </div>
            <p className="text-lg text-gray-600">Limited time offers you don't want to miss</p>
          </div>

          {/* Navigation Arrows */}
          <div className="hidden md:flex space-x-2">
            <button className="deals-prev p-2 rounded-full bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 transition-all duration-200">
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button className="deals-next p-2 rounded-full bg-blue-50 hover:bg-blue-100 text-blue-600 hover:text-blue-700 transition-all duration-200">
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Deals Carousel */}
        <Swiper
          modules={[Navigation, Autoplay]}
          spaceBetween={24}
          navigation={{
            prevEl: '.deals-prev',
            nextEl: '.deals-next',
          }}
          autoplay={{
            delay: 6000,
            disableOnInteraction: false,
          }}
          breakpoints={{
            640: {
              slidesPerView: 2,
            },
            1024: {
              slidesPerView: 3,
            },
          }}
          slidesPerView={1}
          className="pb-4"
        >
          {deals.map(deal => (
            <SwiperSlide key={deal.id}>
              <div className="bg-white rounded-lg border-2 border-blue-100 hover:border-blue-200 hover:shadow-xl transition-all duration-300 overflow-hidden group">
                {/* Deal Image */}
                <div className="relative aspect-square overflow-hidden">
                  <Image
                    src={deal.image}
                    alt={deal.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    onError={e => {
                      console.error(`Failed to load deal image: ${deal.image}`);
                      e.currentTarget.src = '/images/products/placeholder.svg';
                    }}
                  />

                  {/* Discount Badge */}
                  <div className="absolute top-3 left-3 bg-green-500 text-white text-sm font-bold px-3 py-1 rounded-full">
                    -{deal.discount}%
                  </div>

                  {/* Limited Time Badge */}
                  {deal.isLimitedTime && (
                    <div className="absolute top-3 right-3 bg-blue-500 text-white text-xs font-semibold px-2 py-1 rounded">
                      Limited Time
                    </div>
                  )}
                </div>

                {/* Deal Info */}
                <div className="p-4">
                  <div className="text-sm text-gray-500 mb-1">{deal.category}</div>
                  <h3 className="font-semibold text-gray-900 mb-3 line-clamp-2">{deal.name}</h3>

                  {/* Prices */}
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="text-xl font-bold text-green-600">${deal.salePrice}</span>
                    <span className="text-sm text-gray-500 line-through">
                      ${deal.originalPrice}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-3">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Sold: {deal.soldCount}</span>
                      <span>Available: {deal.totalStock - deal.soldCount}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${getProgressPercentage(deal.soldCount, deal.totalStock)}%`,
                        }}
                      ></div>
                    </div>
                  </div>

                  {/* Countdown Timer */}
                  {deal.isLimitedTime && timeLeft[deal.id] && timeLeft[deal.id] !== 'Expired' && (
                    <div className="flex items-center text-sm text-blue-600 mb-4">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      <span>Ends in: {timeLeft[deal.id]}</span>
                    </div>
                  )}

                  {/* Shop Now Button */}
                  <Link
                    href={`/products/${deal.slug}`}
                    className="w-full bg-blue-600 text-white text-center py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 block font-semibold"
                    data-testid="deal-link"
                  >
                    Shop Now
                  </Link>
                </div>
              </div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* View All Deals Button */}
        <div className="text-center mt-12">
          <Link
            href="/deals"
            className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
            // className="inline-flex items-center px-8 py-3 bg-blue-600 text-white text-base font-medium rounded-md hover:bg-blue-700 transition-colors duration-200"
            data-testid="view-all-deals"
          >
            View All Deals
            <ChevronRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
