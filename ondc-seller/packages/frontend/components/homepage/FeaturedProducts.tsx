'use client';

import React, { useState, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Navigation, Autoplay } from 'swiper/modules';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  StarIcon,
  HeartIcon,
} from '@heroicons/react/24/outline';
import { StarIcon as StarSolidIcon, HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';
import Image from 'next/image';
import { getProducts } from '@/lib/strapi-api';
import 'swiper/css';
import 'swiper/css/navigation';

interface Product {
  id: number;
  name: string;
  slug: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isWishlisted: boolean;
  badge?: string;
}

const defaultProducts: Product[] = [
  {
    id: 1,
    name: 'Wireless Bluetooth Headphones',
    slug: 'wireless-bluetooth-headphones',
    price: 79.99,
    originalPrice: 99.99,
    image: '/images/products/headphones.jpg',
    rating: 4.5,
    reviewCount: 128,
    category: 'Electronics',
    isWishlisted: false,
    badge: 'Best Seller',
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    slug: 'smart-fitness-watch',
    price: 199.99,
    image: '/images/products/smartwatch.jpg',
    rating: 4.8,
    reviewCount: 89,
    category: 'Electronics',
    isWishlisted: true,
    badge: 'New',
  },
  {
    id: 3,
    name: 'Premium Coffee Maker',
    slug: 'premium-coffee-maker',
    price: 149.99,
    originalPrice: 179.99,
    image: '/images/products/coffee-maker.jpg',
    rating: 4.6,
    reviewCount: 156,
    category: 'Home & Kitchen',
    isWishlisted: false,
  },
  {
    id: 4,
    name: 'Organic Cotton T-Shirt',
    slug: 'organic-cotton-tshirt',
    price: 29.99,
    image: '/images/products/tshirt.jpg',
    rating: 4.3,
    reviewCount: 67,
    category: 'Fashion',
    isWishlisted: false,
    badge: 'Eco-Friendly',
  },
  {
    id: 5,
    name: 'Professional Camera Lens',
    slug: 'professional-camera-lens',
    price: 599.99,
    originalPrice: 699.99,
    image: '/images/products/camera-lens.jpg',
    rating: 4.9,
    reviewCount: 234,
    category: 'Electronics',
    isWishlisted: false,
    badge: 'Pro Choice',
  },
  {
    id: 6,
    name: 'Ergonomic Office Chair',
    slug: 'ergonomic-office-chair',
    price: 299.99,
    image: '/images/products/office-chair.jpg',
    rating: 4.4,
    reviewCount: 92,
    category: 'Furniture',
    isWishlisted: true,
  },
];

// Helper function to extract text from rich text content
function extractTextFromRichText(richText: any): string {
  if (!richText) return '';

  if (Array.isArray(richText)) {
    return richText
      .map((block: any) => {
        if (block.children && Array.isArray(block.children)) {
          return block.children.map((child: any) => child.text || '').join('');
        }
        return '';
      })
      .join(' ');
  }

  if (typeof richText === 'string') {
    return richText;
  }

  return '';
}

// Helper function to transform Strapi product data
function transformStrapiProduct(strapiProduct: any): Product {
  const attributes = strapiProduct.attributes || strapiProduct;
  const name = attributes.name || strapiProduct.name || 'Unknown Product';
  const price = attributes.price || strapiProduct.price || 0;
  const salePrice = attributes.sale_price || strapiProduct.sale_price;

  return {
    id: strapiProduct.id,
    name,
    slug: attributes.slug || name.toLowerCase().replace(/\s+/g, '-'),
    price: salePrice || price,
    originalPrice: salePrice ? price : undefined,
    image: '/images/products/placeholder.svg', // Default image for now
    rating: 4.0 + Math.random() * 1, // Random rating between 4-5
    reviewCount: Math.floor(Math.random() * 300) + 10,
    category: 'Featured',
    isWishlisted: false,
    badge: attributes.featured || strapiProduct.featured ? 'Featured' : undefined,
  };
}

export default function FeaturedProducts() {
  const [products, setProducts] = useState<Product[]>(defaultProducts);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Fetch featured products from Strapi CMS via Next.js API
  useEffect(() => {
    const fetchFeaturedProducts = async () => {
      try {
        console.log('🚀 FeaturedProducts: useEffect triggered, fetching products...');
        console.log('🚀 FeaturedProducts: Current loading state:', isLoading);
        setIsLoading(true);

        // Add a small delay to ensure the component is fully mounted
        await new Promise(resolve => setTimeout(resolve, 200));

        console.log('🚀 FeaturedProducts: Making API call to /api/products');
        const response = await fetch('/api/products?featured=true&pageSize=12');

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        console.log('✅ FeaturedProducts: API response received:', result);

        if (result.success && result.data && result.data.length > 0) {
          console.log('✅ FeaturedProducts: Products received from API:', result.data);
          setProducts(result.data);
          console.log('✅ FeaturedProducts: Products state updated');
        } else {
          console.log('⚠️ FeaturedProducts: No featured products found, using default products');
          setProducts(defaultProducts);
        }
      } catch (error) {
        console.error('❌ FeaturedProducts: Error fetching products:', error);
        console.log('🔄 FeaturedProducts: Using default products due to error');
        setProducts(defaultProducts);
      } finally {
        console.log('🏁 FeaturedProducts: Setting loading to false');
        setIsLoading(false);
      }
    };

    // Add a small delay before starting the fetch
    const timer = setTimeout(() => {
      fetchFeaturedProducts();
    }, 700);

    return () => clearTimeout(timer);
  }, []);

  console.log(
    'FeaturedProducts component rendered with products:',
    products.length,
    'loading:',
    isLoading
  );

  const toggleWishlist = (productId: number) => {
    setProducts(prev =>
      prev.map(product =>
        product.id === productId ? { ...product, isWishlisted: !product.isWishlisted } : product
      )
    );
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<StarSolidIcon key={i} className="h-4 w-4 text-yellow-400" />);
    }

    if (hasHalfStar) {
      stars.push(<StarIcon key="half" className="h-4 w-4 text-yellow-400" />);
    }

    const emptyStars = 5 - Math.ceil(rating);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<StarIcon key={`empty-${i}`} className="h-4 w-4 text-gray-300" />);
    }

    return stars;
  };

  return (
    <section className="py-16 bg-gray-50" data-testid="featured-products">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Products</h2>
            <p className="text-lg text-gray-600">Handpicked products just for you</p>
          </div>

          {/* Navigation Arrows */}
          <div className="hidden md:flex space-x-2">
            <button className="featured-prev p-2 rounded-full bg-white shadow-md hover:shadow-lg text-gray-600 hover:text-gray-900 transition-all duration-200">
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <button className="featured-next p-2 rounded-full bg-white shadow-md hover:shadow-lg text-gray-600 hover:text-gray-900 transition-all duration-200">
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
                <div className="aspect-square bg-gray-200 animate-pulse"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2 animate-pulse"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Products Carousel */}
        {!isLoading && (
          <Swiper
            modules={[Navigation, Autoplay]}
            spaceBetween={24}
            navigation={{
              prevEl: '.featured-prev',
              nextEl: '.featured-next',
            }}
            autoplay={{
              delay: 5000,
              disableOnInteraction: false,
            }}
            breakpoints={{
              640: {
                slidesPerView: 2,
              },
              768: {
                slidesPerView: 3,
              },
              1024: {
                slidesPerView: 4,
              },
            }}
            slidesPerView={1}
            className="pb-4"
          >
            {products.map(product => (
              <SwiperSlide key={product.id}>
                <div className="bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden group">
                  {/* Product Image */}
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                      onError={e => {
                        console.error(`Failed to load featured product image: ${product.image}`);
                        e.currentTarget.src = '/images/products/placeholder.svg';
                      }}
                    />

                    {/* Badge */}
                    {product.badge && (
                      <div className="absolute top-3 left-3 bg-blue-600 text-white text-xs font-semibold px-2 py-1 rounded">
                        {product.badge}
                      </div>
                    )}

                    {/* Wishlist Button */}
                    <button
                      onClick={() => toggleWishlist(product.id)}
                      className="absolute top-3 right-3 p-2 rounded-full bg-white/80 hover:bg-white transition-all duration-200"
                    >
                      {product.isWishlisted ? (
                        <HeartSolidIcon className="h-5 w-5 text-red-500" />
                      ) : (
                        <HeartIcon className="h-5 w-5 text-gray-600" />
                      )}
                    </button>
                  </div>

                  {/* Product Info */}
                  <div className="p-4">
                    <div className="text-sm text-gray-500 mb-1">{product.category}</div>
                    <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                      {product.name}
                    </h3>

                    {/* Rating */}
                    <div className="flex items-center mb-3">
                      <div className="flex items-center">{renderStars(product.rating)}</div>
                      <span className="ml-2 text-sm text-gray-500">({product.reviewCount})</span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-900">${product.price}</span>
                        {product.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            ${product.originalPrice}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* View Product Button */}
                    <Link
                      href={`/products/${product.slug}`}
                      className="mt-4 w-full bg-blue-600 text-white text-center py-2 rounded-md hover:bg-blue-700 transition-colors duration-200 block"
                      data-testid="product-link"
                    >
                      View Product
                    </Link>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        )}

        {/* View All Products Button */}
        <div className="text-center mt-12">
          <Link
            href="/products"
            className="inline-flex items-center px-8 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-400 transition-colors duration-200"
          >
            View All Products
            <ChevronRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </section>
  );
}
