'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';

interface McpProduct {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  image?: string;
  availability: 'in_stock' | 'out_of_stock' | 'limited';
  rating?: number;
  reviews?: number;
}

interface McpProductListProps {
  category?: string;
  limit?: number;
  showFilters?: boolean;
}

const McpProductList: React.FC<McpProductListProps> = ({
  category,
  limit = 12,
  showFilters = true,
}) => {
  const [products, setProducts] = useState<McpProduct[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState(category || 'all');
  const [sortBy, setSortBy] = useState('name');

  // Mock MCP products data
  const mockProducts: McpProduct[] = [
    {
      id: 'mcp-1',
      name: 'Smart Wireless Earbuds',
      description: 'High-quality wireless earbuds with noise cancellation',
      price: 2999,
      category: 'Electronics',
      image: 'https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=Earbuds',
      availability: 'in_stock',
      rating: 4.5,
      reviews: 128,
    },
    {
      id: 'mcp-2',
      name: 'Organic Cotton T-Shirt',
      description: 'Comfortable and sustainable organic cotton t-shirt',
      price: 899,
      category: 'Fashion',
      image: 'https://via.placeholder.com/300x300/10B981/FFFFFF?text=T-Shirt',
      availability: 'in_stock',
      rating: 4.3,
      reviews: 89,
    },
    {
      id: 'mcp-3',
      name: 'Fitness Tracker Watch',
      description: 'Advanced fitness tracking with heart rate monitor',
      price: 4999,
      category: 'Electronics',
      image: 'https://via.placeholder.com/300x300/F59E0B/FFFFFF?text=Watch',
      availability: 'limited',
      rating: 4.7,
      reviews: 210,
    },
    {
      id: 'mcp-4',
      name: 'Premium Coffee Beans',
      description: 'Single-origin coffee beans, freshly roasted',
      price: 599,
      category: 'Food',
      image: 'https://via.placeholder.com/300x300/8B5CF6/FFFFFF?text=Coffee',
      availability: 'in_stock',
      rating: 4.8,
      reviews: 75,
    },
    {
      id: 'mcp-5',
      name: 'Yoga Mat Pro',
      description: 'Non-slip premium yoga mat for all fitness levels',
      price: 1299,
      category: 'Sports',
      image: 'https://via.placeholder.com/300x300/EF4444/FFFFFF?text=Yoga+Mat',
      availability: 'in_stock',
      rating: 4.4,
      reviews: 65,
    },
    {
      id: 'mcp-6',
      name: 'Ceramic Plant Pot',
      description: 'Handcrafted ceramic pot perfect for indoor plants',
      price: 799,
      category: 'Home',
      image: 'https://via.placeholder.com/300x300/06B6D4/FFFFFF?text=Plant+Pot',
      availability: 'out_of_stock',
      rating: 4.6,
      reviews: 42,
    },
  ];

  useEffect(() => {
    // Simulate API call
    const fetchProducts = async () => {
      setLoading(true);
      try {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        let filteredProducts = mockProducts;
        
        // Filter by category
        if (selectedCategory !== 'all') {
          filteredProducts = filteredProducts.filter(
            product => product.category.toLowerCase() === selectedCategory.toLowerCase()
          );
        }
        
        // Sort products
        filteredProducts.sort((a, b) => {
          switch (sortBy) {
            case 'price_low':
              return a.price - b.price;
            case 'price_high':
              return b.price - a.price;
            case 'rating':
              return (b.rating || 0) - (a.rating || 0);
            case 'name':
            default:
              return a.name.localeCompare(b.name);
          }
        });
        
        // Apply limit
        if (limit) {
          filteredProducts = filteredProducts.slice(0, limit);
        }
        
        setProducts(filteredProducts);
        setError(null);
      } catch (err) {
        setError('Failed to fetch MCP products');
        console.error('MCP products fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [selectedCategory, sortBy, limit]);

  const getAvailabilityBadge = (availability: McpProduct['availability']) => {
    switch (availability) {
      case 'in_stock':
        return <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">In Stock</span>;
      case 'limited':
        return <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Limited</span>;
      case 'out_of_stock':
        return <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Out of Stock</span>;
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {showFilters && (
          <div className="flex flex-col sm:flex-row gap-4 animate-pulse">
            <div className="h-10 bg-gray-300 rounded w-48"></div>
            <div className="h-10 bg-gray-300 rounded w-48"></div>
          </div>
        )}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden animate-pulse">
              <div className="h-48 bg-gray-300"></div>
              <div className="p-4">
                <div className="h-4 bg-gray-300 rounded mb-2"></div>
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                <div className="h-6 bg-gray-300 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 text-lg font-medium mb-2">Error Loading Products</div>
        <p className="text-gray-600">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      {showFilters && (
        <div className="flex flex-col sm:flex-row gap-4">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Categories</option>
            <option value="electronics">Electronics</option>
            <option value="fashion">Fashion</option>
            <option value="food">Food & Beverages</option>
            <option value="sports">Sports & Fitness</option>
            <option value="home">Home & Garden</option>
          </select>
          
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="name">Sort by Name</option>
            <option value="price_low">Price: Low to High</option>
            <option value="price_high">Price: High to Low</option>
            <option value="rating">Highest Rated</option>
          </select>
        </div>
      )}

      {/* Products Grid */}
      {products.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-gray-500 text-lg">No products found</div>
          <p className="text-gray-400 mt-2">Try adjusting your filters</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {products.map((product) => (
            <div
              key={product.id}
              className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300"
            >
              <div className="relative h-48">
                <Image
                  src={product.image || 'https://via.placeholder.com/300x300/E5E7EB/9CA3AF?text=No+Image'}
                  alt={product.name}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-2 right-2">
                  {getAvailabilityBadge(product.availability)}
                </div>
              </div>

              <div className="p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-1">
                  {product.name}
                </h3>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                  {product.description}
                </p>

                <div className="flex items-center justify-between mb-3">
                  <span className="text-xl font-bold text-blue-600">
                    ₹{product.price.toLocaleString()}
                  </span>
                  {product.rating && (
                    <div className="flex items-center space-x-1">
                      <svg className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                        <path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z" />
                      </svg>
                      <span className="text-sm text-gray-600">{product.rating}</span>
                      <span className="text-xs text-gray-500">({product.reviews})</span>
                    </div>
                  )}
                </div>

                <Link
                  href={`/products/${product.id}`}
                  className="block w-full text-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default McpProductList;
