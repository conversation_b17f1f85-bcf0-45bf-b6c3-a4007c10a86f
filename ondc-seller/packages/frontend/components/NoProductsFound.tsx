import React from 'react';
import Link from 'next/link';

interface NoProductsFoundProps {
  message?: string;
  showHomeLink?: boolean;
  showClearFiltersButton?: boolean;
  onClearFilters?: () => void;
  customActions?: React.ReactNode;
  className?: string;
}

export default function NoProductsFound({
  message = 'No products found.',
  showHomeLink = true,
  showClearFiltersButton = false,
  onClearFilters,
  customActions,
  className = '',
}: NoProductsFoundProps) {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="bg-card text-card-foreground border rounded-lg p-8 max-w-md mx-auto">
        {/* Icon */}
        <div className="mx-auto w-16 h-16 bg-secondary rounded-full flex items-center justify-center mb-4">
          <svg
            className="w-8 h-8 text-muted-foreground"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"
            />
          </svg>
        </div>

        {/* Message */}
        <h3 className="text-lg font-semibold text-foreground mb-2">No Products Found</h3>
        <p className="text-muted-foreground mb-6">{message}</p>

        {/* Actions */}
        <div className="space-y-3">
          {customActions && customActions}
          
          {!customActions && (
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              {showClearFiltersButton && onClearFilters && (
                <button
                  onClick={onClearFilters}
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Clear Filters
                </button>
              )}
              
              {showHomeLink && (
                <Link
                  href="/"
                  className="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  Go to Home
                </Link>
              )}
            </div>
          )}
        </div>

        {/* Suggestions */}
        <div className="mt-6 pt-6 border-t border-border">
          <p className="text-sm text-muted-foreground mb-3">Try:</p>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Checking your spelling</li>
            <li>• Using fewer or different keywords</li>
            <li>• Removing some filters</li>
            <li>• Browsing our categories</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
