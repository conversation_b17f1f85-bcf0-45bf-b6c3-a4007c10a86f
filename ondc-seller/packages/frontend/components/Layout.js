import React from 'react';
import Head from 'next/head';
import Link from 'next/link';
import { useTenant } from '../context/TenantContext';

/**
 * Layout component for consistent UI across pages
 * @param {Object} props - Component props
 * @returns {JSX.Element} Layout component
 */
export default function Layout({ children, title = 'ONDC Seller Platform', description = 'ONDC Seller Platform - Your one-stop shop' }) {
  const { currentTenant, tenants, selectTenant, loading } = useTenant();
  
  return (
    <div className="container">
      <Head>
        <title>{title}</title>
        <meta name="description" content={description} />
        <link rel="icon" href="/favicon.ico" />
      </Head>
      
      <header className="header">
        <div className="logo">
          <Link href="/">ONDC Seller Platform</Link>
        </div>
        
        {/* Tenant Selector */}
        {!loading && tenants.length > 0 && (
          <div className="tenant-selector">
            <label htmlFor="tenant-select">Select Seller: </label>
            <select 
              id="tenant-select"
              value={currentTenant?.id || ''}
              onChange={(e) => selectTenant(e.target.value)}
            >
              {tenants.map(tenant => (
                <option key={tenant.id} value={tenant.id}>
                  {tenant.name}
                </option>
              ))}
            </select>
          </div>
        )}
        
        <nav className="nav">
          <Link href="/">Home</Link>
          <Link href="/products">Products</Link>
          <Link href="/about-us">About Us</Link>
          <Link href="/contact">Contact</Link>
        </nav>
      </header>
      
      <main className="main">
        {children}
      </main>
      
      <footer className="footer">
        <p>&copy; {new Date().getFullYear()} ONDC Seller Platform. All rights reserved.</p>
      </footer>
      
      <style jsx>{`
        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 0;
          border-bottom: 1px solid #eaeaea;
        }
        
        .logo {
          font-size: 1.5rem;
          font-weight: bold;
        }
        
        .logo a {
          color: #3B82F6;
          text-decoration: none;
        }
        
        .nav {
          display: flex;
          gap: 20px;
        }
        
        .nav a {
          color: #333;
          text-decoration: none;
        }
        
        .nav a:hover {
          color: #3B82F6;
        }
        
        .main {
          flex: 1;
          padding: 20px 0;
        }
        
        .footer {
          margin-top: auto;
          padding: 20px 0;
          border-top: 1px solid #eaeaea;
          text-align: center;
        }
        
        .tenant-selector {
          margin-right: 20px;
        }
        
        .tenant-selector select {
          padding: 8px;
          border-radius: 4px;
          border: 1px solid #ddd;
        }
        
        @media (max-width: 768px) {
          .header {
            flex-direction: column;
            align-items: flex-start;
          }
          
          .logo {
            margin-bottom: 10px;
          }
          
          .nav {
            margin-top: 10px;
            width: 100%;
            justify-content: space-between;
          }
          
          .tenant-selector {
            margin: 10px 0;
          }
        }
      `}</style>
    </div>
  );
}
