'use client';

import React, { useEffect, useState } from 'react';
import { useTenant } from '@/hooks/useTenant';

interface TenantInfo {
  id: string;
  name: string;
  domain?: string;
  settings?: Record<string, any>;
}

const ClientTenantDisplay: React.FC = () => {
  const { tenantId, tenantConfig } = useTenant();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="bg-gray-100 rounded-lg p-4 animate-pulse">
        <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
        <div className="h-3 bg-gray-300 rounded w-1/2"></div>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 className="text-lg font-semibold text-blue-900 mb-2">Current Tenant Information</h3>
      <div className="space-y-2 text-sm">
        <div>
          <span className="font-medium text-blue-800">Tenant ID:</span>
          <span className="ml-2 text-blue-700 font-mono">{tenantId}</span>
        </div>
        {tenantConfig && (
          <>
            <div>
              <span className="font-medium text-blue-800">Name:</span>
              <span className="ml-2 text-blue-700">{tenantConfig.name}</span>
            </div>
            {tenantConfig.domain && (
              <div>
                <span className="font-medium text-blue-800">Domain:</span>
                <span className="ml-2 text-blue-700">{tenantConfig.domain}</span>
              </div>
            )}
            {tenantConfig.settings && (
              <div>
                <span className="font-medium text-blue-800">Theme:</span>
                <span className="ml-2 text-blue-700">
                  {tenantConfig.settings.theme || 'default'}
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ClientTenantDisplay;
