'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { getCachedFooterContent, testFooterConnection } from '@/lib/footer-api';
import { StaticFooterData } from '@/types/footer';

const Footer: React.FC = () => {
  const [footerData, setFooterData] = useState<StaticFooterData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [strapiConnected, setStrapiConnected] = useState<boolean | null>(null);

  // Fallback footer data (current static content)
  const fallbackFooterData: StaticFooterData = {
    companyInfo: {
      name: 'ONDC Seller',
      description:
        'Empowering sellers on the Open Network for Digital Commerce. Join the digital revolution and grow your business with ONDC.',
      logoText: 'O',
    },
    sections: [
      {
        title: 'Seller Tools',
        links: [
          { name: 'Dashboard', href: '/dashboard' },
          { name: 'Product Management', href: '/products' },
          { name: 'Order Management', href: '/orders' },
          { name: 'Analytics', href: '/analytics' },
        ],
      },
      {
        title: 'Support',
        links: [
          { name: 'Help Center', href: '/help' },
          { name: 'Contact Us', href: '/contact' },
          { name: 'API Documentation', href: '/docs' },
          { name: 'Developer Guide', href: '/developers' },
        ],
      },
      {
        title: 'Legal',
        links: [
          { name: 'Terms of Service', href: '/terms' },
          { name: 'Privacy Policy', href: '/privacy' },
          { name: 'ONDC Guidelines', href: '/ondc-guidelines' },
          { name: 'Seller Agreement', href: '/seller-agreement' },
        ],
      },
    ],
    socialLinks: [
      { platform: 'Twitter', href: '#', icon: 'twitter' },
      { platform: 'Facebook', href: '#', icon: 'facebook' },
      { platform: 'LinkedIn', href: '#', icon: 'linkedin' },
    ],
    contactInfo: {
      address: 'Mumbai, Maharashtra, India',
      phone: '+91 1234567890',
      email: '<EMAIL>',
    },
    copyrightText: `© ${new Date().getFullYear()} ONDC Seller Platform. All rights reserved.`,
    poweredBy: 'Powered by ONDC',
    verificationBadge: {
      text: 'Verified Seller Platform',
      verified: true,
    },
  };

  // Fetch footer content from Strapi CMS
  useEffect(() => {
    const fetchFooterContent = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🚀 Footer: Fetching content from Strapi CMS...');

        // Test Strapi connection first
        const connectionTest = await testFooterConnection();
        setStrapiConnected(connectionTest.success);

        if (connectionTest.success) {
          console.log('✅ Footer: Strapi connection successful');
        } else {
          console.warn('⚠️ Footer: Strapi connection failed:', connectionTest.message);
        }

        // Fetch footer content (will fallback automatically if Strapi fails)
        const content = await getCachedFooterContent();
        setFooterData(content);

        console.log('✅ Footer: Content loaded successfully');
      } catch (err) {
        console.error('❌ Footer: Error fetching content:', err);
        setError(err instanceof Error ? err.message : 'Failed to load footer content');
        // Use fallback data on error
        setFooterData(fallbackFooterData);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFooterContent();
  }, []);

  // Use fallback data if no data is loaded
  const currentFooterData = footerData || fallbackFooterData;

  // Loading state
  if (isLoading) {
    return (
      <footer className="bg-gray-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Loading skeleton */}
            <div className="col-span-1">
              <div className="animate-pulse">
                <div className="h-8 bg-gray-700 rounded mb-4"></div>
                <div className="h-16 bg-gray-700 rounded mb-4"></div>
                <div className="flex space-x-4">
                  <div className="h-5 w-5 bg-gray-700 rounded"></div>
                  <div className="h-5 w-5 bg-gray-700 rounded"></div>
                  <div className="h-5 w-5 bg-gray-700 rounded"></div>
                </div>
              </div>
            </div>
            {[1, 2, 3].map(i => (
              <div key={i} className="col-span-1">
                <div className="animate-pulse">
                  <div className="h-6 bg-gray-700 rounded mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-700 rounded"></div>
                    <div className="h-4 bg-gray-700 rounded"></div>
                    <div className="h-4 bg-gray-700 rounded"></div>
                    <div className="h-4 bg-gray-700 rounded"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </footer>
    );
  }

  // Render social media icon
  const renderSocialIcon = (platform: string) => {
    const iconClass = 'h-5 w-5';

    switch (platform.toLowerCase()) {
      case 'twitter':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z" />
          </svg>
        );
      case 'facebook':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 24 24">
            <path d="M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z" />
          </svg>
        );
      case 'linkedin':
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 24 24">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z" />
          </svg>
        );
      default:
        return (
          <svg className={iconClass} fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
          </svg>
        );
    }
  };

  return (
    <footer className="bg-gray-900 text-white">
      {/* Development indicator */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-blue-600 text-white text-center py-1 text-xs">
          Footer CMS: {strapiConnected ? '✅ Connected' : '❌ Fallback'} |
          {error ? ` Error: ${error}` : ' Status: OK'}
        </div>
      )}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="col-span-1">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">
                  {currentFooterData.companyInfo.logoText}
                </span>
              </div>
              <span className="text-xl font-bold">{currentFooterData.companyInfo.name}</span>
            </div>
            <p className="text-gray-400 mb-4">{currentFooterData.companyInfo.description}</p>
            <div className="flex space-x-4">
              {currentFooterData.socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  className="text-gray-400 hover:text-white transition-colors"
                  aria-label={social.platform}
                >
                  {renderSocialIcon(social.platform)}
                </a>
              ))}
            </div>
          </div>

          {/* Footer Sections */}
          {currentFooterData.sections.map(section => (
            <div key={section.title} className="col-span-1">
              <h3 className="text-lg font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map(link => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">{currentFooterData.copyrightText}</p>
          <div className="flex items-center space-x-6 mt-4 md:mt-0">
            <span className="text-gray-400 text-sm">{currentFooterData.poweredBy}</span>
            {currentFooterData.verificationBadge.verified && (
              <div className="flex items-center space-x-2">
                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white text-xs font-bold">✓</span>
                </div>
                <span className="text-gray-400 text-sm">
                  {currentFooterData.verificationBadge.text}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
