import React from 'react';

/**
 * Error Boundary component to catch JavaScript errors in child components
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    this.setState({ errorInfo });
  }

  render() {
    if (this.state.hasError) {
      // Render fallback UI
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but there was an error loading this page.</p>
          <button onClick={() => window.location.reload()} className="retry-button">
            Try Again
          </button>
          
          {process.env.NODE_ENV === 'development' && this.state.error && (
            <div className="error-details">
              <h3>Error Details (Development Only)</h3>
              <p>{this.state.error.toString()}</p>
              {this.state.errorInfo && (
                <pre>
                  {this.state.errorInfo.componentStack}
                </pre>
              )}
            </div>
          )}
          
          <style jsx>{`
            .error-boundary {
              margin: 40px auto;
              padding: 20px;
              background-color: #FEE2E2;
              border: 1px solid #EF4444;
              border-radius: 8px;
              text-align: center;
              max-width: 800px;
            }
            
            .retry-button {
              background: #3B82F6;
              color: white;
              border: none;
              padding: 8px 16px;
              border-radius: 4px;
              cursor: pointer;
              margin-top: 20px;
            }
            
            .error-details {
              margin-top: 30px;
              text-align: left;
              background: #f8f8f8;
              padding: 15px;
              border-radius: 4px;
              overflow: auto;
            }
            
            .error-details pre {
              white-space: pre-wrap;
              font-size: 0.8rem;
              color: #666;
            }
          `}</style>
        </div>
      );
    }

    // If no error, render children normally
    return this.props.children;
  }
}

export default ErrorBoundary;
