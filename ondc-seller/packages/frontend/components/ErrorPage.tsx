'use client';

import React from 'react';
import Link from 'next/link';

interface ErrorPageProps {
  error?: Error;
  statusCode?: number;
  title?: string;
  message?: string;
  showRetry?: boolean;
  showHome?: boolean;
  onRetry?: () => void;
}

const ErrorPage: React.FC<ErrorPageProps> = ({
  error,
  statusCode = 500,
  title,
  message,
  showRetry = true,
  showHome = true,
  onRetry,
}) => {
  const getErrorDetails = () => {
    if (title && message) {
      return { title, message };
    }

    switch (statusCode) {
      case 404:
        return {
          title: 'Page Not Found',
          message: 'The page you are looking for does not exist or has been moved.',
        };
      case 403:
        return {
          title: 'Access Forbidden',
          message: 'You do not have permission to access this resource.',
        };
      case 401:
        return {
          title: 'Unauthorized',
          message: 'Please log in to access this page.',
        };
      case 500:
        return {
          title: 'Server Error',
          message: 'Something went wrong on our end. Please try again later.',
        };
      default:
        return {
          title: 'Something went wrong',
          message: error?.message || 'An unexpected error occurred.',
        };
    }
  };

  const { title: errorTitle, message: errorMessage } = getErrorDetails();

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Error Icon */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
          </div>

          {/* Error Content */}
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {errorTitle}
            </h1>
            <p className="text-gray-600 mb-8">
              {errorMessage}
            </p>

            {/* Status Code */}
            {statusCode && (
              <div className="mb-6">
                <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                  Error {statusCode}
                </span>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-4">
              {showRetry && (
                <button
                  onClick={handleRetry}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                    />
                  </svg>
                  Try Again
                </button>
              )}

              {showHome && (
                <Link
                  href="/"
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                    />
                  </svg>
                  Go Home
                </Link>
              )}

              {/* Additional Help Links */}
              <div className="pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-500 mb-3">
                  Need help? Try these options:
                </p>
                <div className="space-y-2">
                  <Link
                    href="/help"
                    className="block text-sm text-blue-600 hover:text-blue-500"
                  >
                    Visit Help Center
                  </Link>
                  <Link
                    href="/contact"
                    className="block text-sm text-blue-600 hover:text-blue-500"
                  >
                    Contact Support
                  </Link>
                  <Link
                    href="/status"
                    className="block text-sm text-blue-600 hover:text-blue-500"
                  >
                    Check System Status
                  </Link>
                </div>
              </div>
            </div>
          </div>

          {/* Error Details (Development Only) */}
          {process.env.NODE_ENV === 'development' && error && (
            <details className="mt-8 p-4 bg-gray-100 rounded-lg">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                Error Details (Development)
              </summary>
              <div className="text-xs text-gray-600 space-y-2">
                <div>
                  <strong>Message:</strong> {error.message}
                </div>
                {error.stack && (
                  <div>
                    <strong>Stack Trace:</strong>
                    <pre className="mt-1 whitespace-pre-wrap text-xs bg-white p-2 rounded border overflow-auto max-h-40">
                      {error.stack}
                    </pre>
                  </div>
                )}
              </div>
            </details>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-8">
        <p className="text-sm text-gray-500">
          If this problem persists, please contact our support team.
        </p>
      </div>
    </div>
  );
};

export default ErrorPage;
