'use client';

import React from 'react';
import Link from 'next/link';

interface Category {
  id: string;
  name: string;
  description: string;
  icon: string;
  productCount: number;
  href: string;
  color: string;
}

const PopularCategories: React.FC = () => {
  const categories: Category[] = [
    {
      id: 'electronics',
      name: 'Electronics',
      description: 'Smartphones, laptops, gadgets and more',
      icon: '📱',
      productCount: 2500,
      href: '/products/category/electronics',
      color: 'bg-blue-500',
    },
    {
      id: 'fashion',
      name: 'Fashion',
      description: 'Clothing, accessories, and footwear',
      icon: '👕',
      productCount: 3200,
      href: '/products/category/fashion',
      color: 'bg-pink-500',
    },
    {
      id: 'home-garden',
      name: 'Home & Garden',
      description: 'Furniture, decor, and gardening supplies',
      icon: '🏠',
      productCount: 1800,
      href: '/products/category/home-garden',
      color: 'bg-green-500',
    },
    {
      id: 'sports',
      name: 'Sports & Fitness',
      description: 'Exercise equipment and sportswear',
      icon: '⚽',
      productCount: 950,
      href: '/products/category/sports',
      color: 'bg-orange-500',
    },
    {
      id: 'books',
      name: 'Books & Media',
      description: 'Books, magazines, and digital media',
      icon: '📚',
      productCount: 1200,
      href: '/products/category/books',
      color: 'bg-purple-500',
    },
    {
      id: 'food',
      name: 'Food & Beverages',
      description: 'Groceries, snacks, and beverages',
      icon: '🍎',
      productCount: 2100,
      href: '/products/category/food',
      color: 'bg-red-500',
    },
    {
      id: 'beauty',
      name: 'Beauty & Personal Care',
      description: 'Cosmetics, skincare, and wellness',
      icon: '💄',
      productCount: 1600,
      href: '/products/category/beauty',
      color: 'bg-indigo-500',
    },
    {
      id: 'automotive',
      name: 'Automotive',
      description: 'Car accessories and maintenance',
      icon: '🚗',
      productCount: 750,
      href: '/products/category/automotive',
      color: 'bg-gray-600',
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900">Popular Categories</h2>
          <p className="mt-4 text-lg text-gray-600">Explore our most popular product categories</p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map(category => (
            <Link
              key={category.id}
              href={category.href}
              className="group relative bg-white rounded-xl shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-200 hover:border-gray-300"
            >
              <div className="p-6">
                {/* Icon and Color Bar */}
                <div className="flex items-center justify-between mb-4">
                  <div className="text-4xl">{category.icon}</div>
                  <div className={`w-3 h-3 rounded-full ${category.color}`}></div>
                </div>

                {/* Category Info */}
                <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors">
                  {category.name}
                </h3>
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">{category.description}</p>

                {/* Product Count */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {(category.productCount || 0).toLocaleString()} products
                  </span>
                  <svg
                    className="w-5 h-5 text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all duration-200"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </div>
              </div>

              {/* Hover Effect */}
              <div
                className={`absolute bottom-0 left-0 right-0 h-1 ${category.color} transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300`}
              ></div>
            </Link>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="mt-16 bg-gray-50 rounded-2xl p-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-blue-600">12,000+</div>
              <div className="text-gray-600 mt-1">Total Products</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-green-600">50+</div>
              <div className="text-gray-600 mt-1">Categories</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-purple-600">1,500+</div>
              <div className="text-gray-600 mt-1">Active Sellers</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-orange-600">98%</div>
              <div className="text-gray-600 mt-1">Customer Satisfaction</div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Ready to Start Selling?</h3>
          <p className="text-lg text-gray-600 mb-8">
            Join thousands of sellers already growing their business on ONDC
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/dashboard"
              className="inline-flex items-center px-8 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                />
              </svg>
              Start Selling Now
            </Link>
            <Link
              href="/products"
              className="inline-flex items-center px-8 py-3 border-2 border-blue-600 text-blue-600 font-medium rounded-lg hover:bg-blue-600 hover:text-white transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M20 7l-8-4-8 4m16 0l-8 4-8-4m16 0v10l-8 4-8-4V7"
                />
              </svg>
              Browse Products
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PopularCategories;
