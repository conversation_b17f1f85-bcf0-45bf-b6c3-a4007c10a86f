'use client';

import React, { useEffect, useState } from 'react';
import { getBanners } from '@/lib/strapi';
import StrapiBanner from '@/components/StrapiBanner';
import Breadcrumbs from '@/components/Breadcrumbs';

export default function CMSPage() {
  const [banners, setBanners] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setLoading(true);
        const response = await getBanners();
        setBanners((response as any).items || (response as any).data || []);
        setError(null);
      } catch (err) {
        console.error('Error fetching banners:', err);
        setError('Failed to load banners from CMS');
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, []);

  const breadcrumbs = [
    { label: 'Home', href: '/' },
    { label: 'CMS Content', href: '/cms' },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs items={breadcrumbs} />

      <h1 className="text-3xl font-bold mb-8">CMS Content</h1>

      {loading && (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      )}

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <p>{error}</p>
          <p className="text-sm mt-2">
            Make sure Strapi is running at{' '}
            {process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'}
          </p>
        </div>
      )}

      {!loading && !error && banners.length === 0 && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
          <p>No banners found in the CMS.</p>
          <p className="text-sm mt-2">
            Create some banners in the Strapi admin panel at{' '}
            <a
              href={`${process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'}/admin`}
              target="_blank"
              rel="noopener noreferrer"
              className="underline"
            >
              {process.env.NEXT_PUBLIC_STRAPI_URL || 'http://localhost:1337'}/admin
            </a>
          </p>
        </div>
      )}

      <div className="space-y-8">
        {banners.map(banner => (
          <StrapiBanner key={banner.id} banner={banner} />
        ))}
      </div>
    </div>
  );
}
