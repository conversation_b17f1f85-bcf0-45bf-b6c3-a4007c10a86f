import React from 'react';
import Breadcrumbs from '@/components/Breadcrumbs';
import PageHero from '@/components/ui/PageHero';
import ContentCard from '@/components/ui/ContentCard';
import StyledContent from '@/components/ui/StyledContent';

import { getPageBySlug, type Page } from '@/lib/strapi-api';

// Server-side function to fetch page content
async function getReturnsPageContent(): Promise<Page | null> {
  try {
    console.log('Server: Fetching returns page content from Strapi...');
    const content = await getPageBySlug('returns');
    console.log('Server: Fetched returns content:', content?.title);
    return content;
  } catch (error) {
    console.error('Server: Error fetching returns page:', error);
    return null;
  }
}

export default async function ReturnsPage() {
  // Fetch content on the server side
  const pageContent = await getReturnsPageContent();

  // Fallback content if Strapi content is not available
  const fallbackContent: Page = {
    id: 23,
    title: 'Returns & Refunds',
    slug: 'returns',
    content: `<h2>Returns & Refunds Policy</h2>
<p><strong>Last updated:</strong> June 10, 2025</p>

<h3>1. Return Policy Overview</h3>
<p>At ONDC Seller Platform, we want you to be completely satisfied with your purchase. If you are not satisfied, you may return eligible items within 7 days of delivery for a full refund or exchange.</p>

<h3>2. Eligible Items for Return</h3>
<ul>
<li>Items must be in original condition with all tags attached</li>
<li>Items must be in original packaging</li>
<li>Items must not show signs of wear or use</li>
<li>Items must be returned within 7 days of delivery</li>
</ul>

<h3>3. Non-Returnable Items</h3>
<ul>
<li>Perishable goods (food, flowers, etc.)</li>
<li>Personal care items</li>
<li>Customized or personalized items</li>
<li>Digital downloads</li>
<li>Gift cards</li>
</ul>

<h3>4. How to Return an Item</h3>
<ol>
<li>Log into your account and go to "My Orders"</li>
<li>Find the order containing the item you want to return</li>
<li>Click "Return Item" and select the reason for return</li>
<li>Print the return shipping label provided</li>
<li>Package the item securely and attach the return label</li>
<li>Drop off the package at any authorized shipping location</li>
</ol>

<h3>5. Refund Process</h3>
<p>Once we receive and inspect your returned item, we will process your refund within 5-7 business days. Refunds will be credited to your original payment method.</p>

<h3>6. Contact Us</h3>
<p>If you have any questions about returns or refunds, please contact <NAME_EMAIL> or +91 **********.</p>`,
    excerpt:
      'Learn about our returns and refunds policy for the ONDC Seller Platform. Easy returns within 7 days.',
    metaTitle: 'Returns & Refunds - ONDC Seller Platform',
    metaDescription:
      'Read our returns and refunds policy for ONDC Seller Platform. Easy returns within 7 days with full refund guarantee.',
    status: 'published',
    template: 'default',
    featured: false,
    author: 'Admin',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };

  // Use Strapi content if available, otherwise use fallback
  const displayContent = pageContent || fallbackContent;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <PageHero
        title={displayContent.title}
        description={
          displayContent.excerpt ||
          'Learn about our returns and refunds policy. Easy returns within 7 days with full refund guarantee.'
        }
        icon={
          <svg
            className="w-12 h-12 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
        }
        gradient="green"
      />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Breadcrumbs */}
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'Returns & Refunds', href: '/returns', active: true },
          ]}
        />

        <div className="max-w-4xl mx-auto">
          {/* Main Content */}
          <ContentCard variant="elevated" padding="xl">
            <StyledContent content={displayContent.content} />
          </ContentCard>
        </div>
      </div>
    </div>
  );
}
