'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  Box,
  Button,
  Alert,
  Typography,
  Paper,
  Container,
  Divider,
  Stack,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import { useAuth } from '@/context/AuthContext';
import Mu<PERSON><PERSON><PERSON><PERSON>ield from '@/components/admin/MuiFormField';
import { FormErrorBoundary } from '@/components/admin/ErrorBoundary';
import { useFormValidation } from '@/hooks/useFormValidation';
import { FIELD_VALIDATION_RULES } from '@/lib/validation';

export default function LoginPage() {
  const [rememberMe, setRememberMe] = useState(false);
  const { login } = useAuth();
  const router = useRouter();

  // Form validation with specific rules for login
  const {
    data,
    errors,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldError,
    clearErrors,
  } = useFormValidation({
    initialData: {
      email: '',
      password: '',
    },
    validationRules: {
      email: FIELD_VALIDATION_RULES.email,
      password: { required: true, minLength: 6 },
    },
    onSubmit: async formData => {
      try {
        await login(formData.email, formData.password);

        // Redirect based on user role
        if (formData.email.includes('admin') || formData.email === '<EMAIL>') {
          router.push('/admin');
        } else {
          router.push('/');
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Login failed';
        setFieldError('submit', errorMessage);
        throw err; // Re-throw to prevent form submission success
      }
    },
    onError: formErrors => {
      console.error('Login form validation errors:', formErrors);
    },
  });

  const handleDemoLogin = async (role: 'admin' | 'seller' | 'customer') => {
    const demoCredentials = {
      admin: { email: '<EMAIL>', password: '123456' },
      seller: { email: '<EMAIL>', password: '123456' },
      customer: { email: '<EMAIL>', password: '123456' },
    };

    const { email: demoEmail, password: demoPassword } = demoCredentials[role];

    clearErrors();

    try {
      await login(demoEmail, demoPassword);

      if (role === 'admin') {
        router.push('/admin');
      } else {
        router.push('/');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Login failed';
      setFieldError('submit', errorMessage);
    }
  };

  return (
    <FormErrorBoundary>
      <Container maxWidth="sm">
        <Paper elevation={3} sx={{ p: 4, mt: 8 }}>
          <Typography variant="h4" component="h1" align="center" gutterBottom>
            Sign In
          </Typography>

          <Box component="form" onSubmit={handleSubmit} sx={{ mt: 3 }}>
            {errors.submit && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {errors.submit}
              </Alert>
            )}

            <Stack spacing={3}>
              <MuiFormField
                label="Email Address"
                name="email"
                type="email"
                value={data.email}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter your email"
                required
                error={errors.email}
                disabled={isSubmitting}
                validationRules={FIELD_VALIDATION_RULES.email}
              />

              <MuiFormField
                label="Password"
                name="password"
                type="password"
                value={data.password}
                onChange={handleChange}
                onBlur={handleBlur}
                placeholder="Enter your password"
                required
                error={errors.password}
                disabled={isSubmitting}
                validationRules={{ required: true, minLength: 6 }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={rememberMe}
                      onChange={e => setRememberMe(e.target.checked)}
                      name="rememberMe"
                      color="primary"
                    />
                  }
                  label="Remember me"
                />

                <Link href="/auth/forgot-password" style={{ textDecoration: 'none' }}>
                  <Typography variant="body2" color="primary" sx={{ cursor: 'pointer' }}>
                    Forgot password?
                  </Typography>
                </Link>
              </Box>

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={isSubmitting}
                sx={{ mt: 3, mb: 2 }}
              >
                {isSubmitting ? 'Signing in...' : 'SIGN IN'}
              </Button>
            </Stack>

            <Divider sx={{ my: 3 }}>
              <Typography variant="body2" color="text.secondary">
                Or try demo accounts
              </Typography>
            </Divider>

            <Stack spacing={2}>
              <Button
                variant="outlined"
                fullWidth
                onClick={() => handleDemoLogin('admin')}
                disabled={isSubmitting}
              >
                Demo Admin (<EMAIL>)
              </Button>

              <Button
                variant="outlined"
                fullWidth
                onClick={() => handleDemoLogin('seller')}
                disabled={isSubmitting}
              >
                Demo Seller (<EMAIL>)
              </Button>

              <Button
                variant="outlined"
                fullWidth
                onClick={() => handleDemoLogin('customer')}
                disabled={isSubmitting}
              >
                Demo Customer (<EMAIL>)
              </Button>
            </Stack>

            <Box sx={{ textAlign: 'center', mt: 3 }}>
              <Typography variant="body2" color="text.secondary">
                Don't have an account?{' '}
                <Link href="/auth/register" style={{ textDecoration: 'none' }}>
                  <Typography
                    component="span"
                    variant="body2"
                    color="primary"
                    sx={{ cursor: 'pointer' }}
                  >
                    Sign up
                  </Typography>
                </Link>
              </Typography>
            </Box>
          </Box>

          {/* Demo credentials info */}
          <Paper
            variant="outlined"
            sx={{
              mt: 3,
              p: 2,
              bgcolor: 'primary.50',
              borderColor: 'primary.200',
            }}
          >
            <Typography variant="subtitle2" color="primary.main" gutterBottom>
              Demo Credentials:
            </Typography>
            <Stack spacing={0.5}>
              <Typography variant="caption" color="primary.dark">
                <strong>Admin:</strong> <EMAIL> / 123456
              </Typography>
              <Typography variant="caption" color="primary.dark">
                <strong>Seller:</strong> <EMAIL> / 123456
              </Typography>
              <Typography variant="caption" color="primary.dark">
                <strong>Customer:</strong> <EMAIL> / 123456
              </Typography>
              <Typography variant="caption" color="primary.dark">
                <strong>EverShop Demo:</strong> <EMAIL> / 123456
              </Typography>
            </Stack>
          </Paper>
        </Paper>
      </Container>
    </FormErrorBoundary>
  );
}
