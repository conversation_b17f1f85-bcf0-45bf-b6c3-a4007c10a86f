import { NextRequest, NextResponse } from 'next/server';

const categoryColors: Record<string, string> = {
  'electronics.jpg': '#3b82f6',
  'fashion.jpg': '#ec4899',
  'home.jpg': '#10b981',
  'beauty.jpg': '#f59e0b',
  'sports.jpg': '#ef4444',
  'books.jpg': '#8b5cf6',
  'default-category.jpg': '#6366f1'
};

const categoryIcons: Record<string, string> = {
  'electronics.jpg': '📱',
  'fashion.jpg': '👕',
  'home.jpg': '🏠',
  'beauty.jpg': '💄',
  'sports.jpg': '⚽',
  'books.jpg': '📚',
  'default-category.jpg': '🏷️'
};

const categoryNames: Record<string, string> = {
  'electronics.jpg': 'Electronics',
  'fashion.jpg': 'Fashion',
  'home.jpg': 'Home & Garden',
  'beauty.jpg': 'Beauty',
  'sports.jpg': 'Sports',
  'books.jpg': 'Books',
  'default-category.jpg': 'Category'
};

function generatePlaceholderSVG(filename: string, width: number = 400, height: number = 300): string {
  const color = categoryColors[filename] || '#6366f1';
  const icon = categoryIcons[filename] || '🏷️';
  const name = categoryNames[filename] || 'Category';
  
  return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#gradient)"/>
      <text x="50%" y="40%" font-family="Arial, sans-serif" font-size="32" fill="white" text-anchor="middle" dy=".3em">${icon}</text>
      <text x="50%" y="60%" font-family="Arial, sans-serif" font-size="18" fill="white" text-anchor="middle" dy=".3em" font-weight="bold">${name}</text>
      <text x="50%" y="75%" font-family="Arial, sans-serif" font-size="12" fill="rgba(255,255,255,0.8)" text-anchor="middle" dy=".3em">Placeholder Image</text>
    </svg>
  `;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  const filename = params.filename;
  
  // Generate SVG placeholder
  const svg = generatePlaceholderSVG(filename);
  
  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
    },
  });
}
