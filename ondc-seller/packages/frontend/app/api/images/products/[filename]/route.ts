import { NextRequest, NextResponse } from 'next/server';

const productColors: Record<string, string> = {
  'headphones.jpg': '#1f2937',
  'smartphone.jpg': '#374151',
  'laptop.jpg': '#4b5563',
  'watch.jpg': '#6b7280',
  'shoes.jpg': '#9ca3af',
  'shirt.jpg': '#d1d5db',
  'default-product.jpg': '#6366f1',
  'placeholder.jpg': '#8b5cf6',
  'seller-avatar.jpg': '#3b82f6',
  'seller-cover.jpg': '#1e40af',
  'default-seller.jpg': '#059669',
  'default-seller-cover.jpg': '#0d9488',
  'default-avatar.jpg': '#dc2626',
  'hero-default.jpg': '#7c3aed',
  'team-john.jpg': '#1e40af',
  'team-jane.jpg': '#7c2d12',
  'team-mike.jpg': '#166534',
  'team-sarah.jpg': '#be185d',
  'mission.jpg': '#0f766e'
};

const productIcons: Record<string, string> = {
  'headphones.jpg': '🎧',
  'smartphone.jpg': '📱',
  'laptop.jpg': '💻',
  'watch.jpg': '⌚',
  'shoes.jpg': '👟',
  'shirt.jpg': '👕',
  'default-product.jpg': '📦',
  'placeholder.jpg': '🖼️',
  'seller-avatar.jpg': '👤',
  'seller-cover.jpg': '🏪',
  'default-seller.jpg': '🏬',
  'default-seller-cover.jpg': '🌟',
  'default-avatar.jpg': '👨‍💼',
  'hero-default.jpg': '🎯',
  'team-john.jpg': '👨‍💼',
  'team-jane.jpg': '👩‍💻',
  'team-mike.jpg': '👨‍🔧',
  'team-sarah.jpg': '👩‍💼',
  'mission.jpg': '🎯'
};

const productNames: Record<string, string> = {
  'headphones.jpg': 'Headphones',
  'smartphone.jpg': 'Smartphone',
  'laptop.jpg': 'Laptop',
  'watch.jpg': 'Smart Watch',
  'shoes.jpg': 'Shoes',
  'shirt.jpg': 'T-Shirt',
  'default-product.jpg': 'Product',
  'placeholder.jpg': 'Placeholder',
  'seller-avatar.jpg': 'Seller',
  'seller-cover.jpg': 'Store',
  'default-seller.jpg': 'Seller',
  'default-seller-cover.jpg': 'Store Cover',
  'default-avatar.jpg': 'Avatar',
  'hero-default.jpg': 'Hero Image',
  'team-john.jpg': 'John Doe',
  'team-jane.jpg': 'Jane Smith',
  'team-mike.jpg': 'Mike Johnson',
  'team-sarah.jpg': 'Sarah Wilson',
  'mission.jpg': 'Our Mission'
};

function generatePlaceholderSVG(filename: string, width: number = 300, height: number = 300): string {
  const color = productColors[filename] || '#6366f1';
  const icon = productIcons[filename] || '📦';
  const name = productNames[filename] || 'Product';
  
  return `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
          <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#gradient)"/>
      <circle cx="50%" cy="50%" r="60" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
      <text x="50%" y="45%" font-family="Arial, sans-serif" font-size="40" fill="white" text-anchor="middle" dy=".3em">${icon}</text>
      <text x="50%" y="65%" font-family="Arial, sans-serif" font-size="14" fill="white" text-anchor="middle" dy=".3em" font-weight="bold">${name}</text>
      <text x="50%" y="80%" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.7)" text-anchor="middle" dy=".3em">Placeholder</text>
    </svg>
  `;
}

export async function GET(
  request: NextRequest,
  { params }: { params: { filename: string } }
) {
  const filename = params.filename;
  
  // Generate SVG placeholder
  const svg = generatePlaceholderSVG(filename);
  
  return new NextResponse(svg, {
    headers: {
      'Content-Type': 'image/svg+xml',
      'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
    },
  });
}
