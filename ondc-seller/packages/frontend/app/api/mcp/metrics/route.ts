import { NextRequest, NextResponse } from 'next/server';
import { getTenantIdFromNextRequest } from '../../../../lib/server-tenant';

/**
 * MCP System Metrics Endpoint
 * GET /api/mcp/metrics - System performance metrics
 */
export async function GET(request: NextRequest) {
  try {
    const tenantId = getTenantIdFromNextRequest(request);
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '24h';
    
    console.log('[MCP Metrics] Request for tenant:', tenantId, 'period:', period);

    // Generate mock metrics based on period
    const generateMetrics = (period: string) => {
      const multiplier = period === '1h' ? 1 : period === '24h' ? 24 : period === '7d' ? 168 : 24;
      
      return {
        period,
        tenant_id: tenantId,
        timestamp: new Date().toISOString(),
        api_calls: {
          total: Math.floor(Math.random() * 1000 * multiplier) + 500 * multiplier,
          successful: Math.floor(Math.random() * 950 * multiplier) + 450 * multiplier,
          failed: Math.floor(Math.random() * 50 * multiplier) + 10 * multiplier,
          by_endpoint: {
            '/sync/products': Math.floor(Math.random() * 200 * multiplier) + 100 * multiplier,
            '/orders/fulfill': Math.floor(Math.random() * 150 * multiplier) + 75 * multiplier,
            '/inventory/sync': Math.floor(Math.random() * 100 * multiplier) + 50 * multiplier,
            '/customers/sync': Math.floor(Math.random() * 80 * multiplier) + 40 * multiplier,
            '/tools/execute': Math.floor(Math.random() * 300 * multiplier) + 150 * multiplier,
            '/health': Math.floor(Math.random() * 50 * multiplier) + 25 * multiplier
          }
        },
        sync_operations: {
          product_syncs: Math.floor(Math.random() * 50 * multiplier) + 25 * multiplier,
          inventory_syncs: Math.floor(Math.random() * 100 * multiplier) + 50 * multiplier,
          customer_syncs: Math.floor(Math.random() * 30 * multiplier) + 15 * multiplier,
          order_syncs: Math.floor(Math.random() * 80 * multiplier) + 40 * multiplier
        },
        performance: {
          average_response_time: Math.random() * 200 + 50,
          p95_response_time: Math.random() * 500 + 200,
          p99_response_time: Math.random() * 1000 + 500,
          throughput: Math.random() * 20 + 5,
          error_rate: Math.random() * 0.05
        },
        resource_usage: {
          cpu_average: Math.random() * 80 + 10,
          memory_average: Math.random() * 70 + 20,
          disk_io_average: Math.random() * 60 + 10,
          network_io_average: Math.random() * 50 + 10
        },
        business_metrics: {
          products_synced: Math.floor(Math.random() * 500 * multiplier) + 250 * multiplier,
          orders_processed: Math.floor(Math.random() * 200 * multiplier) + 100 * multiplier,
          customers_synced: Math.floor(Math.random() * 100 * multiplier) + 50 * multiplier,
          revenue_processed: Math.floor(Math.random() * 100000 * multiplier) + 50000 * multiplier
        },
        webhook_metrics: {
          webhooks_sent: Math.floor(Math.random() * 300 * multiplier) + 150 * multiplier,
          webhooks_successful: Math.floor(Math.random() * 280 * multiplier) + 140 * multiplier,
          webhooks_failed: Math.floor(Math.random() * 20 * multiplier) + 10 * multiplier,
          average_delivery_time: Math.random() * 1000 + 200
        }
      };
    };

    const metrics = generateMetrics(period);

    console.log('[MCP Metrics] Generated metrics for period:', period);

    return NextResponse.json(metrics);
  } catch (error) {
    console.error('[MCP Metrics] Error:', error);
    
    return NextResponse.json(
      { 
        error: 'Failed to fetch system metrics',
        details: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
