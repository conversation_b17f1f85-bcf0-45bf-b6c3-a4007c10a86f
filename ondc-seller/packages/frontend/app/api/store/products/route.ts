import { NextRequest, NextResponse } from 'next/server';
import { getFilteredProducts } from '@/data/msw-data';

// Use shared MSW data structure for consistency

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = request.headers.get('x-tenant-id') || 'default';
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get category and subcategory filters
    const categoryId = searchParams.get('category_id');
    const subcategoryId = searchParams.get('subcategory_id');
    const parentCategoryId = searchParams.get('parent_category_id');

    console.log('[API Store Products] Request:', {
      categoryId,
      subcategoryId,
      parentCategoryId,
      tenantId,
      limit,
      offset,
    });

    // Use shared MSW data structure for consistency
    const filteredProducts = getFilteredProducts({
      category: categoryId || undefined,
    });

    // Apply pagination
    const startIndex = offset;
    const endIndex = startIndex + limit;
    const paginatedProducts = filteredProducts.slice(startIndex, endIndex);

    const result = {
      products: paginatedProducts,
      count: paginatedProducts.length,
      total: filteredProducts.length,
      page: Math.floor(offset / limit) + 1,
      limit,
    };

    console.log('[API Store Products] Returning products:', {
      count: result.count,
      total: result.total,
      products: result.products.map(p => ({ id: p.id, title: p.title })),
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error('[API Store Products] Error:', error);
    return NextResponse.json({ error: 'Failed to fetch products' }, { status: 500 });
  }
}
