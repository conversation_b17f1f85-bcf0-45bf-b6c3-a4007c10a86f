import { NextRequest, NextResponse } from 'next/server';
import { MOCK_PRODUCTS, generateCategoryProducts } from '@/data/msw-data';

// Individual product API route using shared MSW data structure
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const tenantId = request.headers.get('x-tenant-id') || 'default';

    console.log('[API Store Product Detail] Fetching product:', {
      id,
      tenantId,
      timestamp: new Date().toISOString(),
    });

    // Find the product by ID (tenant filtering can be added later)
    let product = MOCK_PRODUCTS.find(p => p.id === id);

    // If not found, try to generate mock category/subcategory product
    if (!product && id.startsWith('mock_')) {
      // Parse id: mock_{category}_{n}
      const match = id.match(/^mock_([a-zA-Z0-9]+)_(\d+)$/);
      if (match) {
        const category = match[1];
        const index = parseInt(match[2], 10) - 1; // 0-based index
        const generated = generateCategoryProducts(category, index + 1);
        product = generated[index];
      }
    }

    if (!product) {
      console.log('[API Store Product Detail] Product not found:', { id, tenantId });
      return NextResponse.json(
        {
          message: `Product not found for tenant: ${tenantId}`,
          code: 'not_found',
        },
        { status: 404 }
      );
    }

    console.log('[API Store Product Detail] Product found:', {
      id: product.id,
      title: product.title,
      tenantId: tenantId,
    });

    // Return the product in the expected format
    return NextResponse.json({
      product: {
        ...product,
        // Ensure images are in the correct format
        images: product.images.map(img => (typeof img === 'string' ? img : img.src || '')),
      },
    });
  } catch (error) {
    console.error('[API Store Product Detail] Error:', error);
    return NextResponse.json(
      {
        message: 'Internal server error',
        code: 'internal_error',
      },
      { status: 500 }
    );
  }
}
