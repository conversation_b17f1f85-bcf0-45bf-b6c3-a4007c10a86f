import { NextRequest, NextResponse } from 'next/server';

// Import the same mock data (in real app, this would come from database)
const MOCK_ADMIN_ORDERS = [
  {
    id: 'ORD-001',
    orderNumber: '#10001',
    customerId: 'CUST-001',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    status: 'pending',
    paymentStatus: 'paid',
    fulfillmentStatus: 'unfulfilled',
    total: 25999,
    subtotal: 23999,
    tax: 1440,
    shipping: 560,
    discount: 0,
    currency: 'INR',
    items: [
      {
        id: 'ITEM-001',
        productId: '1',
        productName: 'Nike Air Max 270',
        sku: 'NIKE-AM270-001',
        quantity: 2,
        price: 12999,
        total: 25998,
        image: '/images/products/placeholder.svg',
      },
    ],
    shippingAddress: {
      name: '<PERSON>',
      address1: '123 Main Street',
      address2: 'Apt 4B',
      city: 'Mumbai',
      state: 'Maharashtra',
      postalCode: '400001',
      country: 'India',
      phone: '+91 9876543210',
    },
    billingAddress: {
      name: '<PERSON>',
      address1: '123 Main Street',
      address2: 'Apt 4B',
      city: 'Mumbai',
      state: 'Maharashtra',
      postalCode: '400001',
      country: 'India',
      phone: '+91 9876543210',
    },
    notes: 'Please deliver between 10 AM - 6 PM',
    tags: ['priority', 'new-customer'],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T14:45:00Z',
  },
  // Add other orders here (truncated for brevity)
];

// GET /api/admin/orders/[id] - Get specific order
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    console.log('[Admin Order API] GET request for ID:', id);

    const order = MOCK_ADMIN_ORDERS.find(o => o.id === id);

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    console.log('[Admin Order API] Found order:', order.orderNumber);

    return NextResponse.json({ order });
  } catch (error) {
    console.error('[Admin Order API] Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/orders/[id] - Update specific order
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    console.log('[Admin Order API] PUT request for ID:', id, body);

    const orderIndex = MOCK_ADMIN_ORDERS.findIndex(o => o.id === id);

    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Update order
    const updatedOrder = {
      ...MOCK_ADMIN_ORDERS[orderIndex],
      ...body,
      id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString(),
    };

    MOCK_ADMIN_ORDERS[orderIndex] = updatedOrder;

    console.log('[Admin Order API] Updated order:', updatedOrder.orderNumber);

    return NextResponse.json({
      message: 'Order updated successfully',
      order: updatedOrder,
    });
  } catch (error) {
    console.error('[Admin Order API] Update error:', error);
    return NextResponse.json(
      { error: 'Failed to update order', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/orders/[id] - Delete specific order (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    console.log('[Admin Order API] DELETE request for ID:', id);

    const orderIndex = MOCK_ADMIN_ORDERS.findIndex(o => o.id === id);

    if (orderIndex === -1) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    // Check if order can be deleted (only allow deletion of cancelled orders)
    const order = MOCK_ADMIN_ORDERS[orderIndex];
    if (order.status !== 'cancelled') {
      return NextResponse.json(
        { error: 'Only cancelled orders can be deleted. Please cancel the order first.' },
        { status: 400 }
      );
    }

    const deletedOrder = MOCK_ADMIN_ORDERS[orderIndex];
    MOCK_ADMIN_ORDERS.splice(orderIndex, 1);

    console.log('[Admin Order API] Deleted order:', deletedOrder.orderNumber);

    return NextResponse.json({
      message: 'Order deleted successfully',
      order: deletedOrder,
    });
  } catch (error) {
    console.error('[Admin Order API] Delete error:', error);
    return NextResponse.json(
      { error: 'Failed to delete order', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
