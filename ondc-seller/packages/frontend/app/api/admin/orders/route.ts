import { NextRequest, NextResponse } from 'next/server';

// Mock admin orders data
const MOCK_ADMIN_ORDERS = [
  {
    id: 'ORD-001',
    orderNumber: '#10001',
    customerId: 'CUST-001',
    customerName: '<PERSON>',
    customerEmail: '<EMAIL>',
    status: 'pending',
    paymentStatus: 'paid',
    fulfillmentStatus: 'unfulfilled',
    total: 25999,
    subtotal: 23999,
    tax: 1440,
    shipping: 560,
    discount: 0,
    currency: 'INR',
    items: [
      {
        id: 'ITEM-001',
        productId: '1',
        productName: 'Nike Air Max 270',
        sku: 'NIKE-AM270-001',
        quantity: 2,
        price: 12999,
        total: 25998,
        image: '/images/products/placeholder.svg',
      },
    ],
    shippingAddress: {
      name: '<PERSON>',
      address1: '123 Main Street',
      address2: 'Apt 4B',
      city: 'Mumbai',
      state: 'Maharashtra',
      postalCode: '400001',
      country: 'India',
      phone: '+91 9876543210',
    },
    billingAddress: {
      name: '<PERSON>',
      address1: '123 Main Street',
      address2: 'Apt 4B',
      city: 'Mumbai',
      state: 'Maharashtra',
      postalCode: '400001',
      country: 'India',
      phone: '+91 9876543210',
    },
    notes: 'Please deliver between 10 AM - 6 PM',
    tags: ['priority', 'new-customer'],
    createdAt: '2024-01-15T10:30:00Z',
    updatedAt: '2024-01-15T14:45:00Z',
  },
  {
    id: 'ORD-002',
    orderNumber: '#10002',
    customerId: 'CUST-002',
    customerName: 'Jane Smith',
    customerEmail: '<EMAIL>',
    status: 'processing',
    paymentStatus: 'paid',
    fulfillmentStatus: 'partial',
    total: 15999,
    subtotal: 15999,
    tax: 960,
    shipping: 0,
    discount: 960,
    currency: 'INR',
    items: [
      {
        id: 'ITEM-002',
        productId: '2',
        productName: 'Adidas Ultraboost 22',
        sku: 'ADIDAS-UB22-002',
        quantity: 1,
        price: 15999,
        total: 15999,
        image: '/images/products/placeholder.svg',
      },
    ],
    shippingAddress: {
      name: 'Jane Smith',
      address1: '456 Oak Avenue',
      address2: '',
      city: 'Delhi',
      state: 'Delhi',
      postalCode: '110001',
      country: 'India',
      phone: '+91 9876543211',
    },
    billingAddress: {
      name: 'Jane Smith',
      address1: '456 Oak Avenue',
      address2: '',
      city: 'Delhi',
      state: 'Delhi',
      postalCode: '110001',
      country: 'India',
      phone: '+91 9876543211',
    },
    notes: '',
    tags: ['repeat-customer'],
    createdAt: '2024-01-14T15:20:00Z',
    updatedAt: '2024-01-15T09:30:00Z',
  },
  {
    id: 'ORD-003',
    orderNumber: '#10003',
    customerId: 'CUST-003',
    customerName: 'Mike Johnson',
    customerEmail: '<EMAIL>',
    status: 'shipped',
    paymentStatus: 'paid',
    fulfillmentStatus: 'fulfilled',
    total: 8999,
    subtotal: 8999,
    tax: 540,
    shipping: 200,
    discount: 740,
    currency: 'INR',
    items: [
      {
        id: 'ITEM-003',
        productId: '3',
        productName: 'Puma RS-X',
        sku: 'PUMA-RSX-003',
        quantity: 1,
        price: 8999,
        total: 8999,
        image: '/images/products/placeholder.svg',
      },
    ],
    shippingAddress: {
      name: 'Mike Johnson',
      address1: '789 Pine Road',
      address2: 'Building C',
      city: 'Bangalore',
      state: 'Karnataka',
      postalCode: '560001',
      country: 'India',
      phone: '+91 9876543212',
    },
    billingAddress: {
      name: 'Mike Johnson',
      address1: '789 Pine Road',
      address2: 'Building C',
      city: 'Bangalore',
      state: 'Karnataka',
      postalCode: '560001',
      country: 'India',
      phone: '+91 9876543212',
    },
    notes: 'Fragile items - handle with care',
    tags: ['express-shipping'],
    createdAt: '2024-01-13T11:15:00Z',
    updatedAt: '2024-01-14T16:20:00Z',
  },
  {
    id: 'ORD-004',
    orderNumber: '#10004',
    customerId: 'CUST-004',
    customerName: 'Sarah Wilson',
    customerEmail: '<EMAIL>',
    status: 'delivered',
    paymentStatus: 'paid',
    fulfillmentStatus: 'fulfilled',
    total: 17999,
    subtotal: 17999,
    tax: 1080,
    shipping: 0,
    discount: 1080,
    currency: 'INR',
    items: [
      {
        id: 'ITEM-004',
        productId: '4',
        productName: 'New Balance 990v5',
        sku: 'NB-990V5-004',
        quantity: 1,
        price: 17999,
        total: 17999,
        image: '/images/products/placeholder.svg',
      },
    ],
    shippingAddress: {
      name: 'Sarah Wilson',
      address1: '321 Elm Street',
      address2: '',
      city: 'Chennai',
      state: 'Tamil Nadu',
      postalCode: '600001',
      country: 'India',
      phone: '+91 9876543213',
    },
    billingAddress: {
      name: 'Sarah Wilson',
      address1: '321 Elm Street',
      address2: '',
      city: 'Chennai',
      state: 'Tamil Nadu',
      postalCode: '600001',
      country: 'India',
      phone: '+91 9876543213',
    },
    notes: 'Gift wrapping requested',
    tags: ['gift', 'premium'],
    createdAt: '2024-01-12T09:45:00Z',
    updatedAt: '2024-01-13T18:30:00Z',
  },
  {
    id: 'ORD-005',
    orderNumber: '#10005',
    customerId: 'CUST-005',
    customerName: 'David Brown',
    customerEmail: '<EMAIL>',
    status: 'cancelled',
    paymentStatus: 'refunded',
    fulfillmentStatus: 'cancelled',
    total: 4999,
    subtotal: 4999,
    tax: 300,
    shipping: 150,
    discount: 450,
    currency: 'INR',
    items: [
      {
        id: 'ITEM-005',
        productId: '5',
        productName: 'Converse Chuck Taylor',
        sku: 'CONV-CT-005',
        quantity: 1,
        price: 4999,
        total: 4999,
        image: '/images/products/placeholder.svg',
      },
    ],
    shippingAddress: {
      name: 'David Brown',
      address1: '654 Maple Lane',
      address2: 'Unit 12',
      city: 'Pune',
      state: 'Maharashtra',
      postalCode: '411001',
      country: 'India',
      phone: '+91 9876543214',
    },
    billingAddress: {
      name: 'David Brown',
      address1: '654 Maple Lane',
      address2: 'Unit 12',
      city: 'Pune',
      state: 'Maharashtra',
      postalCode: '411001',
      country: 'India',
      phone: '+91 9876543214',
    },
    notes: 'Customer requested cancellation',
    tags: ['cancelled', 'refund-processed'],
    createdAt: '2024-01-11T14:20:00Z',
    updatedAt: '2024-01-12T10:15:00Z',
  },
];

// GET /api/admin/orders - List all orders with admin features
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const paymentStatus = searchParams.get('paymentStatus') || '';
    const fulfillmentStatus = searchParams.get('fulfillmentStatus') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    console.log('[Admin Orders API] GET request:', {
      page,
      limit,
      search,
      status,
      paymentStatus,
      fulfillmentStatus,
      sortBy,
      sortOrder,
    });

    // Filter orders based on search criteria
    let filteredOrders = [...MOCK_ADMIN_ORDERS];

    // Apply search filter
    if (search) {
      filteredOrders = filteredOrders.filter(
        order =>
          order.orderNumber.toLowerCase().includes(search.toLowerCase()) ||
          order.customerName.toLowerCase().includes(search.toLowerCase()) ||
          order.customerEmail.toLowerCase().includes(search.toLowerCase()) ||
          order.id.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply status filters
    if (status) {
      filteredOrders = filteredOrders.filter(order => order.status === status);
    }
    if (paymentStatus) {
      filteredOrders = filteredOrders.filter(order => order.paymentStatus === paymentStatus);
    }
    if (fulfillmentStatus) {
      filteredOrders = filteredOrders.filter(
        order => order.fulfillmentStatus === fulfillmentStatus
      );
    }

    // Apply sorting
    filteredOrders.sort((a, b) => {
      let aValue = a[sortBy as keyof typeof a];
      let bValue = b[sortBy as keyof typeof b];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = (bValue as string).toLowerCase();
      }

      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedOrders = filteredOrders.slice(offset, offset + limit);

    const response = {
      orders: paginatedOrders,
      pagination: {
        page,
        limit,
        total: filteredOrders.length,
        totalPages: Math.ceil(filteredOrders.length / limit),
        hasNext: offset + limit < filteredOrders.length,
        hasPrev: page > 1,
      },
      filters: {
        search,
        status,
        paymentStatus,
        fulfillmentStatus,
        sortBy,
        sortOrder,
      },
    };

    console.log('[Admin Orders API] Returning:', {
      count: paginatedOrders.length,
      total: filteredOrders.length,
      page,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('[Admin Orders API] Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch orders',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/orders - Create new order (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('[Admin Orders API] POST request:', body);

    // Validate required fields
    const requiredFields = ['customerId', 'items'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json({ error: `Missing required field: ${field}` }, { status: 400 });
      }
    }

    // Calculate totals
    const subtotal = body.items.reduce(
      (sum: number, item: any) => sum + item.price * item.quantity,
      0
    );
    const tax = Math.round(subtotal * 0.06); // 6% tax
    const shipping = body.shipping || 0;
    const discount = body.discount || 0;
    const total = subtotal + tax + shipping - discount;

    // Create new order
    const newOrder = {
      id: `ORD-${String(MOCK_ADMIN_ORDERS.length + 1).padStart(3, '0')}`,
      orderNumber: `#${10000 + MOCK_ADMIN_ORDERS.length + 1}`,
      customerId: body.customerId,
      customerName: body.customerName || 'Unknown Customer',
      customerEmail: body.customerEmail || '',
      status: body.status || 'pending',
      paymentStatus: body.paymentStatus || 'pending',
      fulfillmentStatus: body.fulfillmentStatus || 'unfulfilled',
      total,
      subtotal,
      tax,
      shipping,
      discount,
      currency: body.currency || 'INR',
      items: body.items,
      shippingAddress: body.shippingAddress || {},
      billingAddress: body.billingAddress || body.shippingAddress || {},
      notes: body.notes || '',
      tags: body.tags || [],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Add to mock data (in real app, this would be saved to database)
    MOCK_ADMIN_ORDERS.push(newOrder);

    console.log('[Admin Orders API] Created order:', newOrder.id);

    return NextResponse.json(
      { message: 'Order created successfully', order: newOrder },
      { status: 201 }
    );
  } catch (error) {
    console.error('[Admin Orders API] Create error:', error);
    return NextResponse.json(
      {
        error: 'Failed to create order',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
