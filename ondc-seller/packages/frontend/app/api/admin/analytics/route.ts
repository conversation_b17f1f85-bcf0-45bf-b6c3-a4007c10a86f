import { NextRequest, NextResponse } from 'next/server';

// Mock analytics data for admin dashboard
const MOCK_ANALYTICS_DATA = {
  overview: {
    totalRevenue: 2456789,
    totalOrders: 1234,
    totalCustomers: 567,
    totalProducts: 89,
    averageOrderValue: 1991,
    conversionRate: 3.2,
    returnRate: 2.1,
    customerLifetimeValue: 4335,
  },
  revenueByMonth: [
    { month: 'Jan', revenue: 185000, orders: 95 },
    { month: 'Feb', revenue: 210000, orders: 108 },
    { month: 'Mar', revenue: 195000, orders: 102 },
    { month: 'Apr', revenue: 225000, orders: 115 },
    { month: 'May', revenue: 240000, orders: 125 },
    { month: 'Jun', revenue: 235000, orders: 120 },
    { month: 'Jul', revenue: 260000, orders: 135 },
    { month: 'Aug', revenue: 275000, orders: 142 },
    { month: 'Sep', revenue: 255000, orders: 130 },
    { month: 'Oct', revenue: 285000, orders: 148 },
    { month: 'Nov', revenue: 295000, orders: 155 },
    { month: 'Dec', revenue: 291000, orders: 159 },
  ],
  topProducts: [
    {
      id: '1',
      name: 'Nike Air Max 270',
      sku: 'NIKE-AM270-001',
      revenue: 389970,
      unitsSold: 30,
      conversionRate: 4.2,
    },
    {
      id: '2',
      name: 'Adidas Ultraboost 22',
      sku: 'ADIDAS-UB22-002',
      revenue: 319980,
      unitsSold: 20,
      conversionRate: 3.8,
    },
    {
      id: '3',
      name: 'Puma RS-X',
      sku: 'PUMA-RSX-003',
      revenue: 269970,
      unitsSold: 30,
      conversionRate: 3.5,
    },
    {
      id: '4',
      name: 'New Balance 990v5',
      sku: 'NB-990V5-004',
      revenue: 179990,
      unitsSold: 10,
      conversionRate: 2.9,
    },
    {
      id: '5',
      name: 'Converse Chuck Taylor',
      sku: 'CONV-CT-005',
      revenue: 149970,
      unitsSold: 30,
      conversionRate: 4.1,
    },
  ],
  topCategories: [
    {
      id: '1',
      name: 'Electronics',
      revenue: 856000,
      orders: 234,
      products: 45,
      conversionRate: 3.8,
    },
    {
      id: '2',
      name: 'Clothing',
      revenue: 645000,
      orders: 189,
      products: 67,
      conversionRate: 3.2,
    },
    {
      id: '4',
      name: 'Sports & Outdoors',
      revenue: 534000,
      orders: 156,
      products: 34,
      conversionRate: 4.1,
    },
    {
      id: '3',
      name: 'Home & Garden',
      revenue: 421000,
      orders: 123,
      products: 28,
      conversionRate: 2.9,
    },
  ],
  customerSegments: [
    {
      segment: 'VIP Customers',
      count: 45,
      revenue: 675000,
      averageOrderValue: 3500,
      percentage: 27.5,
    },
    {
      segment: 'Repeat Customers',
      count: 156,
      revenue: 890000,
      averageOrderValue: 2100,
      percentage: 36.2,
    },
    {
      segment: 'New Customers',
      count: 234,
      revenue: 567000,
      averageOrderValue: 1200,
      percentage: 23.1,
    },
    {
      segment: 'Inactive Customers',
      count: 132,
      revenue: 324000,
      averageOrderValue: 950,
      percentage: 13.2,
    },
  ],
  ordersByStatus: [
    { status: 'pending', count: 45, percentage: 15.2 },
    { status: 'processing', count: 67, percentage: 22.6 },
    { status: 'shipped', count: 89, percentage: 30.1 },
    { status: 'delivered', count: 78, percentage: 26.4 },
    { status: 'cancelled', count: 17, percentage: 5.7 },
  ],
  trafficSources: [
    { source: 'Organic Search', visitors: 12450, percentage: 42.3 },
    { source: 'Direct', visitors: 8760, percentage: 29.8 },
    { source: 'Social Media', visitors: 4320, percentage: 14.7 },
    { source: 'Email Marketing', visitors: 2890, percentage: 9.8 },
    { source: 'Paid Ads', visitors: 980, percentage: 3.4 },
  ],
  deviceBreakdown: [
    { device: 'Mobile', visitors: 18900, percentage: 64.2 },
    { device: 'Desktop', visitors: 7560, percentage: 25.7 },
    { device: 'Tablet', visitors: 2970, percentage: 10.1 },
  ],
  recentActivity: [
    {
      id: '1',
      type: 'order',
      message: 'New order #10001 received',
      timestamp: '2024-01-15T14:30:00Z',
      amount: 25999,
    },
    {
      id: '2',
      type: 'customer',
      message: 'New customer registration: <EMAIL>',
      timestamp: '2024-01-15T14:15:00Z',
      amount: null,
    },
    {
      id: '3',
      type: 'product',
      message: 'Product "Nike Air Max 270" stock low (5 remaining)',
      timestamp: '2024-01-15T13:45:00Z',
      amount: null,
    },
    {
      id: '4',
      type: 'order',
      message: 'Order #10000 shipped',
      timestamp: '2024-01-15T13:20:00Z',
      amount: 15999,
    },
    {
      id: '5',
      type: 'review',
      message: 'New 5-star review for "Adidas Ultraboost 22"',
      timestamp: '2024-01-15T12:55:00Z',
      amount: null,
    },
  ],
};

// GET /api/admin/analytics - Get dashboard analytics data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || '30d'; // 7d, 30d, 90d, 1y
    const metric = searchParams.get('metric') || 'all'; // revenue, orders, customers, products
    const segment = searchParams.get('segment') || 'all'; // all, new, repeat, vip

    console.log('[Admin Analytics API] GET request:', {
      period,
      metric,
      segment,
    });

    // In a real application, you would filter data based on the period and other parameters
    // For now, we'll return the mock data with some basic filtering

    let responseData = { ...MOCK_ANALYTICS_DATA };

    // Filter by metric if specified
    if (metric !== 'all') {
      let filteredData: any = {};

      switch (metric) {
        case 'revenue':
          filteredData.overview = {
            totalRevenue: responseData.overview.totalRevenue,
            averageOrderValue: responseData.overview.averageOrderValue,
          };
          filteredData.revenueByMonth = responseData.revenueByMonth;
          break;
        case 'orders':
          filteredData.overview = {
            totalOrders: responseData.overview.totalOrders,
            conversionRate: responseData.overview.conversionRate,
          };
          filteredData.ordersByStatus = responseData.ordersByStatus;
          break;
        case 'customers':
          filteredData.overview = {
            totalCustomers: responseData.overview.totalCustomers,
            customerLifetimeValue: responseData.overview.customerLifetimeValue,
          };
          filteredData.customerSegments = responseData.customerSegments;
          break;
        case 'products':
          filteredData.overview = {
            totalProducts: responseData.overview.totalProducts,
            returnRate: responseData.overview.returnRate,
          };
          filteredData.topProducts = responseData.topProducts;
          filteredData.topCategories = responseData.topCategories;
          break;
        default:
          filteredData = responseData;
      }

      responseData = filteredData;
    }

    // Add period information to response
    const response = {
      ...responseData,
      metadata: {
        period,
        metric,
        segment,
        generatedAt: new Date().toISOString(),
        dataPoints: {
          overview: Object.keys(responseData.overview || {}).length,
          revenueByMonth: responseData.revenueByMonth?.length || 0,
          topProducts: responseData.topProducts?.length || 0,
          topCategories: responseData.topCategories?.length || 0,
          customerSegments: responseData.customerSegments?.length || 0,
          ordersByStatus: responseData.ordersByStatus?.length || 0,
          trafficSources: responseData.trafficSources?.length || 0,
          deviceBreakdown: responseData.deviceBreakdown?.length || 0,
          recentActivity: responseData.recentActivity?.length || 0,
        },
      },
    };

    console.log('[Admin Analytics API] Returning analytics data for period:', period);

    return NextResponse.json(response);
  } catch (error) {
    console.error('[Admin Analytics API] Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch analytics data',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
