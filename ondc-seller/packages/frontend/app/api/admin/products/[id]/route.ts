import { NextRequest, NextResponse } from 'next/server';

// Import the same mock data (in real app, this would come from database)
const MOCK_ADMIN_PRODUCTS = [
  {
    id: '1',
    name: 'Nike Air Max 270',
    sku: 'NIKE-AM270-001',
    price: 12999,
    comparePrice: 14999,
    quantity: 45,
    category: 'Shoes',
    status: 'active',
    image: '/images/products/placeholder.svg',
    description: 'Premium running shoes with advanced cushioning technology',
    weight: 0.8,
    dimensions: { length: 30, width: 20, height: 12 },
    tags: ['nike', 'running', 'sports'],
    vendor: 'Nike Inc.',
    createdAt: '2024-01-10T10:00:00Z',
    updatedAt: '2024-01-15T14:30:00Z',
  },
  {
    id: '2',
    name: 'Adidas Ultraboost 22',
    sku: 'ADIDAS-UB22-002',
    price: 15999,
    quantity: 23,
    category: 'Shoes',
    status: 'active',
    image: '/images/products/placeholder.svg',
    description: 'Energy-returning running shoes with responsive cushioning',
    weight: 0.9,
    dimensions: { length: 31, width: 21, height: 13 },
    tags: ['adidas', 'running', 'boost'],
    vendor: 'Adidas AG',
    createdAt: '2024-01-08T09:15:00Z',
    updatedAt: '2024-01-14T16:45:00Z',
  },
  {
    id: '3',
    name: 'Puma RS-X',
    sku: 'PUMA-RSX-003',
    price: 8999,
    comparePrice: 10999,
    quantity: 67,
    category: 'Shoes',
    status: 'active',
    image: '/images/products/placeholder.svg',
    description: 'Retro-inspired sneakers with modern comfort features',
    weight: 0.7,
    dimensions: { length: 29, width: 19, height: 11 },
    tags: ['puma', 'retro', 'casual'],
    vendor: 'Puma SE',
    createdAt: '2024-01-05T11:30:00Z',
    updatedAt: '2024-01-12T13:20:00Z',
  },
  {
    id: '4',
    name: 'New Balance 990v5',
    sku: 'NB-990V5-004',
    price: 17999,
    quantity: 12,
    category: 'Shoes',
    status: 'inactive',
    image: '/images/products/placeholder.svg',
    description: 'Premium lifestyle sneakers with superior craftsmanship',
    weight: 1.0,
    dimensions: { length: 32, width: 22, height: 14 },
    tags: ['new-balance', 'premium', 'lifestyle'],
    vendor: 'New Balance Inc.',
    createdAt: '2024-01-03T08:45:00Z',
    updatedAt: '2024-01-10T12:15:00Z',
  },
  {
    id: '5',
    name: 'Converse Chuck Taylor',
    sku: 'CONV-CT-005',
    price: 4999,
    quantity: 89,
    category: 'Shoes',
    status: 'draft',
    image: '/images/products/placeholder.svg',
    description: 'Classic canvas sneakers with timeless design',
    weight: 0.6,
    dimensions: { length: 28, width: 18, height: 10 },
    tags: ['converse', 'classic', 'canvas'],
    vendor: 'Converse Inc.',
    createdAt: '2024-01-01T07:00:00Z',
    updatedAt: '2024-01-08T10:30:00Z',
  },
];

// GET /api/admin/products/[id] - Get specific product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    console.log('[Admin Product API] GET request for ID:', id);

    const product = MOCK_ADMIN_PRODUCTS.find(p => p.id === id);

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    console.log('[Admin Product API] Found product:', product.name);

    return NextResponse.json({ product });
  } catch (error) {
    console.error('[Admin Product API] Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/products/[id] - Update specific product
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    console.log('[Admin Product API] PUT request for ID:', id, body);

    const productIndex = MOCK_ADMIN_PRODUCTS.findIndex(p => p.id === id);

    if (productIndex === -1) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Check if SKU is being changed and if it conflicts with another product
    if (body.sku && body.sku !== MOCK_ADMIN_PRODUCTS[productIndex].sku) {
      const existingProduct = MOCK_ADMIN_PRODUCTS.find(p => p.sku === body.sku && p.id !== id);
      if (existingProduct) {
        return NextResponse.json(
          { error: 'Product with this SKU already exists' },
          { status: 409 }
        );
      }
    }

    // Update product
    const updatedProduct = {
      ...MOCK_ADMIN_PRODUCTS[productIndex],
      ...body,
      id, // Ensure ID doesn't change
      updatedAt: new Date().toISOString(),
    };

    MOCK_ADMIN_PRODUCTS[productIndex] = updatedProduct;

    console.log('[Admin Product API] Updated product:', updatedProduct.name);

    return NextResponse.json({
      message: 'Product updated successfully',
      product: updatedProduct,
    });
  } catch (error) {
    console.error('[Admin Product API] Update error:', error);
    return NextResponse.json(
      { error: 'Failed to update product', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

// DELETE /api/admin/products/[id] - Delete specific product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    console.log('[Admin Product API] DELETE request for ID:', id);

    const productIndex = MOCK_ADMIN_PRODUCTS.findIndex(p => p.id === id);

    if (productIndex === -1) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    const deletedProduct = MOCK_ADMIN_PRODUCTS[productIndex];
    MOCK_ADMIN_PRODUCTS.splice(productIndex, 1);

    console.log('[Admin Product API] Deleted product:', deletedProduct.name);

    return NextResponse.json({
      message: 'Product deleted successfully',
      product: deletedProduct,
    });
  } catch (error) {
    console.error('[Admin Product API] Delete error:', error);
    return NextResponse.json(
      { error: 'Failed to delete product', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
