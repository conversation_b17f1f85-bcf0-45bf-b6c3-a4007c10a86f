import { NextRequest, NextResponse } from 'next/server';

// Mock admin products data with correct Medusa-compatible structure
const MOCK_ADMIN_PRODUCTS = [
  {
    id: '1',
    title: 'Nike Air Max 270',
    handle: 'nike-air-max-270',
    description: 'Premium running shoes with advanced cushioning technology',
    status: 'published',
    thumbnail: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    collection: {
      id: 'col_1',
      title: 'Athletic Shoes',
      handle: 'athletic-shoes',
    },
    tags: [
      { id: 'tag_1', value: 'nike' },
      { id: 'tag_2', value: 'running' },
      { id: 'tag_3', value: 'sports' },
    ],
    variants: [
      {
        id: 'var_1',
        title: 'Default Variant',
        sku: 'NIKE-AM270-001',
        inventory_quantity: 45,
        prices: [
          {
            id: 'price_1',
            currency_code: 'INR',
            amount: 12999,
          },
        ],
      },
    ],
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-15T14:30:00Z',
  },
  {
    id: '2',
    title: 'Adidas Ultraboost 22',
    handle: 'adidas-ultraboost-22',
    description: 'Energy-returning running shoes with responsive cushioning',
    status: 'published',
    thumbnail: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    collection: {
      id: 'col_1',
      title: 'Athletic Shoes',
      handle: 'athletic-shoes',
    },
    tags: [
      { id: 'tag_4', value: 'adidas' },
      { id: 'tag_2', value: 'running' },
      { id: 'tag_5', value: 'boost' },
    ],
    variants: [
      {
        id: 'var_2',
        title: 'Default Variant',
        sku: 'ADIDAS-UB22-002',
        inventory_quantity: 23,
        prices: [
          {
            id: 'price_2',
            currency_code: 'INR',
            amount: 15999,
          },
        ],
      },
    ],
    created_at: '2024-01-08T09:15:00Z',
    updated_at: '2024-01-14T16:45:00Z',
  },
  {
    id: '3',
    title: 'Puma RS-X',
    handle: 'puma-rs-x',
    description: 'Retro-inspired sneakers with modern comfort features',
    status: 'published',
    thumbnail: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    collection: {
      id: 'col_2',
      title: 'Casual Shoes',
      handle: 'casual-shoes',
    },
    tags: [
      { id: 'tag_6', value: 'puma' },
      { id: 'tag_7', value: 'retro' },
      { id: 'tag_8', value: 'casual' },
    ],
    variants: [
      {
        id: 'var_3',
        title: 'Default Variant',
        sku: 'PUMA-RSX-003',
        inventory_quantity: 67,
        prices: [
          {
            id: 'price_3',
            currency_code: 'INR',
            amount: 8999,
          },
        ],
      },
    ],
    created_at: '2024-01-05T11:30:00Z',
    updated_at: '2024-01-12T13:20:00Z',
  },
  {
    id: '4',
    title: 'New Balance 990v5',
    handle: 'new-balance-990v5',
    description: 'Premium lifestyle sneakers with superior craftsmanship',
    status: 'draft',
    thumbnail: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    collection: {
      id: 'col_3',
      title: 'Premium Collection',
      handle: 'premium-collection',
    },
    tags: [
      { id: 'tag_9', value: 'new-balance' },
      { id: 'tag_10', value: 'premium' },
      { id: 'tag_11', value: 'lifestyle' },
    ],
    variants: [
      {
        id: 'var_4',
        title: 'Default Variant',
        sku: 'NB-990V5-004',
        inventory_quantity: 12,
        prices: [
          {
            id: 'price_4',
            currency_code: 'INR',
            amount: 17999,
          },
        ],
      },
    ],
    created_at: '2024-01-03T08:45:00Z',
    updated_at: '2024-01-10T12:15:00Z',
  },
  {
    id: '5',
    title: 'Converse Chuck Taylor',
    handle: 'converse-chuck-taylor',
    description: 'Classic canvas sneakers with timeless design',
    status: 'draft',
    thumbnail: '/images/products/placeholder.svg',
    images: ['/images/products/placeholder.svg'],
    collection: {
      id: 'col_2',
      title: 'Casual Shoes',
      handle: 'casual-shoes',
    },
    tags: [
      { id: 'tag_12', value: 'converse' },
      { id: 'tag_13', value: 'classic' },
      { id: 'tag_14', value: 'canvas' },
    ],
    variants: [
      {
        id: 'var_5',
        title: 'Default Variant',
        sku: 'CONV-CT-005',
        inventory_quantity: 89,
        prices: [
          {
            id: 'price_5',
            currency_code: 'INR',
            amount: 4999,
          },
        ],
      },
    ],
    created_at: '2024-01-01T07:00:00Z',
    updated_at: '2024-01-08T10:30:00Z',
  },
];

// GET /api/admin/products - List all products with admin features
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const category = searchParams.get('category') || '';
    const sortBy = searchParams.get('sortBy') || 'updatedAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    console.log('[Admin Products API] GET request:', {
      page,
      limit,
      search,
      status,
      category,
      sortBy,
      sortOrder,
    });

    // Filter products based on search criteria
    let filteredProducts = [...MOCK_ADMIN_PRODUCTS];

    // Apply search filter
    if (search) {
      filteredProducts = filteredProducts.filter(
        product =>
          product.title.toLowerCase().includes(search.toLowerCase()) ||
          product.handle.toLowerCase().includes(search.toLowerCase()) ||
          product.description.toLowerCase().includes(search.toLowerCase()) ||
          product.variants.some(variant => variant.sku.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply status filter
    if (status) {
      filteredProducts = filteredProducts.filter(product => product.status === status);
    }

    // Apply category filter (using collection title for now)
    if (category) {
      filteredProducts = filteredProducts.filter(product =>
        product.collection?.title.toLowerCase().includes(category.toLowerCase())
      );
    }

    // Apply sorting
    filteredProducts.sort((a, b) => {
      let aValue = a[sortBy as keyof typeof a];
      let bValue = b[sortBy as keyof typeof b];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = (bValue as string).toLowerCase();
      }

      if (sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });

    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedProducts = filteredProducts.slice(offset, offset + limit);

    const response = {
      products: paginatedProducts,
      pagination: {
        page,
        limit,
        total: filteredProducts.length,
        totalPages: Math.ceil(filteredProducts.length / limit),
        hasNext: offset + limit < filteredProducts.length,
        hasPrev: page > 1,
      },
      filters: {
        search,
        status,
        category,
        sortBy,
        sortOrder,
      },
    };

    console.log('[Admin Products API] Returning:', {
      count: paginatedProducts.length,
      total: filteredProducts.length,
      page,
    });

    return NextResponse.json(response);
  } catch (error) {
    console.error('[Admin Products API] Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch products',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/admin/products - Create new product
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('[Admin Products API] POST request:', body);

    // Validate required fields
    const requiredFields = ['title'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json({ error: `Missing required field: ${field}` }, { status: 400 });
      }
    }

    // Generate handle from title if not provided
    const handle =
      body.handle ||
      body.title
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-|-$/g, '');

    // Check if handle already exists
    const existingProduct = MOCK_ADMIN_PRODUCTS.find(p => p.handle === handle);
    if (existingProduct) {
      return NextResponse.json(
        { error: 'Product with this handle already exists' },
        { status: 409 }
      );
    }

    // Create new product with Medusa-compatible structure
    const newProduct = {
      id: (MOCK_ADMIN_PRODUCTS.length + 1).toString(),
      title: body.title,
      handle: handle,
      description: body.description || '',
      status: body.status || 'draft',
      thumbnail: body.thumbnail || '/images/products/placeholder.svg',
      images: body.images || ['/images/products/placeholder.svg'],
      collection: body.collection || null,
      tags: body.tags || [],
      variants: body.variants || [
        {
          id: `var_${MOCK_ADMIN_PRODUCTS.length + 1}`,
          title: 'Default Variant',
          sku: body.sku || `SKU-${Date.now()}`,
          inventory_quantity: body.inventory_quantity || 0,
          prices: [
            {
              id: `price_${MOCK_ADMIN_PRODUCTS.length + 1}`,
              currency_code: 'INR',
              amount: body.price || 0,
            },
          ],
        },
      ],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Add to mock data (in real app, this would be saved to database)
    MOCK_ADMIN_PRODUCTS.push(newProduct);

    console.log('[Admin Products API] Created product:', newProduct.id);

    return NextResponse.json(
      { message: 'Product created successfully', product: newProduct },
      { status: 201 }
    );
  } catch (error) {
    console.error('[Admin Products API] Create error:', error);
    return NextResponse.json(
      {
        error: 'Failed to create product',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
