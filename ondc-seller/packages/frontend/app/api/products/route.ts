import { NextRequest, NextResponse } from 'next/server';
import { getProducts } from '@/lib/strapi-api';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse query parameters for Strapi API
    const page = searchParams.get('page') ? parseInt(searchParams.get('page')!) : undefined;
    const pageSize = searchParams.get('pageSize')
      ? parseInt(searchParams.get('pageSize')!)
      : parseInt(searchParams.get('limit') || '12');
    const category = searchParams.get('category') || searchParams.get('category_id') || undefined;
    const featured = searchParams.get('featured') === 'true' ? true : undefined;

    console.log('🚀 Products API: Fetching products from Strapi CMS...');
    console.log('📊 Parameters:', { page, pageSize, category, featured });

    // Fetch products from Strapi CMS
    const response = await getProducts({
      page,
      pageSize,
      category,
      featured,
    });

    console.log('✅ Products API: Successfully fetched products');
    console.log('📊 Products count:', response.data?.length || 0);

    // Transform response to match expected format for backward compatibility
    const products = response.data || [];
    const transformedProducts = products.map(product => ({
      id: product.id,
      title: product.name,
      description: Array.isArray(product.description)
        ? product.description.map(p => p.children?.map(c => c.text).join(' ')).join(' ')
        : product.description || '',
      thumbnail:
        product.images?.[0]?.url ||
        `https://via.placeholder.com/300x300/3B82F6/FFFFFF?text=${encodeURIComponent(product.name)}`,
      handle: product.slug || product.name.toLowerCase().replace(/\s+/g, '-'),
      status: product.product_status?.toLowerCase() || 'published',
      variants: [
        {
          id: `var_${product.id}`,
          title: 'Default',
          prices: [{ amount: product.price || 0, currency_code: 'inr' }],
          inventory_quantity: product.inventory_quantity || 0,
        },
      ],
      images: product.images || [
        {
          id: `img_${product.id}`,
          url: `https://via.placeholder.com/600x600/3B82F6/FFFFFF?text=${encodeURIComponent(product.name)}`,
        },
      ],
      tenant_id: 'default',
    }));

    return NextResponse.json({
      success: true,
      products: transformedProducts,
      count: transformedProducts.length,
      offset: 0,
      limit: pageSize,
      total: response.meta?.pagination?.total || transformedProducts.length,
      data: response.data || [],
      meta: response.meta || {},
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Products API: Error fetching products:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        products: [],
        count: 0,
        data: [],
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
