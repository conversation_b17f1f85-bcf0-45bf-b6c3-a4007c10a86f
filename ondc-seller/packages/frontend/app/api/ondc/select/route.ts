import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

/**
 * ONDC Select API Route
 *
 * Handles ONDC order selection requests from BAPs
 * Integrated from ondc-seller-app package
 */

interface ONDCSelectRequest {
  context: {
    domain: string;
    country: string;
    city: string;
    action: string;
    core_version: string;
    bap_id: string;
    bap_uri: string;
    bpp_id: string;
    bpp_uri: string;
    transaction_id: string;
    message_id: string;
    timestamp: string;
  };
  message: {
    order: {
      provider: {
        id: string;
        locations: Array<{
          id: string;
        }>;
      };
      items: Array<{
        id: string;
        quantity: {
          count: number;
        };
      }>;
      billing: {
        name: string;
        address: {
          name: string;
          building: string;
          locality: string;
          city: string;
          state: string;
          country: string;
          area_code: string;
        };
        email: string;
        phone: string;
      };
      fulfillment: {
        type: string;
        end: {
          location: {
            gps: string;
            address: {
              name: string;
              building: string;
              locality: string;
              city: string;
              state: string;
              country: string;
              area_code: string;
            };
          };
          contact: {
            phone: string;
          };
        };
      };
    };
  };
}

export async function POST(request: NextRequest) {
  try {
    const body: ONDCSelectRequest = await request.json();
    const headersList = headers();
    const tenantId = headersList.get('x-tenant-id') || 'default';

    console.log('[ONDC Select] Processing select request:', {
      transactionId: body.context.transaction_id,
      messageId: body.context.message_id,
      tenantId,
    });

    // Extract order information with validation
    const order = body.message?.order;
    if (!order) {
      // If no order provided, create a default one for testing
      const defaultOrder = {
        provider: { id: tenantId, locations: [{ id: 'location_1' }] },
        items: [{ id: 'item_1', quantity: { count: 1 } }],
        billing: {
          name: 'Test Customer',
          address: {
            name: 'Test Address',
            building: 'Test Building',
            locality: 'Test Locality',
            city: 'Bangalore',
            state: 'Karnataka',
            country: 'IND',
            area_code: '560001',
          },
          email: '<EMAIL>',
          phone: '**********',
        },
        fulfillment: {
          type: 'Delivery',
          end: {
            location: {
              gps: '12.9716,77.5946',
              address: {
                name: 'Test Delivery Address',
                building: 'Test Building',
                locality: 'Test Locality',
                city: 'Bangalore',
                state: 'Karnataka',
                country: 'IND',
                area_code: '560001',
              },
            },
            contact: { phone: '**********' },
          },
        },
      };

      const orderInfo = {
        providerId: defaultOrder.provider.id,
        items: defaultOrder.items,
        billing: defaultOrder.billing,
        fulfillment: defaultOrder.fulfillment,
      };

      // Use default order for mock response
      body.message = { order: defaultOrder };
    } else {
      const orderInfo = {
        providerId: order.provider?.id || tenantId,
        items: order.items || [],
        billing: order.billing,
        fulfillment: order.fulfillment,
      };
    }

    // In development mode, return mock response
    if (process.env.NODE_ENV === 'development') {
      const mockResponse = {
        context: {
          ...body.context,
          action: 'on_select',
          timestamp: new Date().toISOString(),
        },
        message: {
          order: {
            provider: body.message.order.provider,
            items: body.message.order.items.map(item => ({
              ...item,
              price: {
                currency: 'INR',
                value: '999.00',
              },
              '@ondc/org/available_on_cod': false,
              '@ondc/org/cancellable': true,
              '@ondc/org/returnable': true,
            })),
            billing: body.message.order.billing,
            fulfillment: {
              ...body.message.order.fulfillment,
              id: 'fulfillment_1',
              '@ondc/org/provider_name': `Provider ${tenantId}`,
              tracking: false,
              '@ondc/org/category': 'Standard Delivery',
              '@ondc/org/TAT': 'PT24H',
              state: {
                descriptor: {
                  code: 'Serviceable',
                },
              },
            },
            quote: {
              price: {
                currency: 'INR',
                value: '1099.00',
              },
              breakup: [
                {
                  '@ondc/org/item_id': body.message.order.items[0]?.id || 'item_1',
                  '@ondc/org/item_quantity': {
                    count: body.message.order.items[0]?.quantity.count || 1,
                  },
                  title: 'Sample Product',
                  '@ondc/org/title_type': 'item',
                  price: {
                    currency: 'INR',
                    value: '999.00',
                  },
                  item: {
                    price: {
                      currency: 'INR',
                      value: '999.00',
                    },
                  },
                },
                {
                  '@ondc/org/item_id': 'fulfillment_1',
                  title: 'Delivery charges',
                  '@ondc/org/title_type': 'delivery',
                  price: {
                    currency: 'INR',
                    value: '100.00',
                  },
                },
              ],
            },
            payment: {
              '@ondc/org/buyer_app_finder_fee_type': 'percent',
              '@ondc/org/buyer_app_finder_fee_amount': '3.0',
              '@ondc/org/settlement_details': [
                {
                  settlement_counterparty: 'seller-app',
                  settlement_phase: 'sale-amount',
                  settlement_type: 'neft',
                  beneficiary_name: `Seller ${tenantId}`,
                  settlement_bank_account_no: '**********',
                  settlement_ifsc_code: 'HDFC0000123',
                  bank_name: 'HDFC Bank',
                  branch_name: 'Sample Branch',
                },
              ],
            },
          },
        },
      };

      return NextResponse.json(mockResponse);
    }

    // In production, integrate with actual ONDC service
    const response = await fetch(`${process.env.ONDC_SERVICE_URL}/select`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId,
      },
      body: JSON.stringify(body),
    });

    if (!response.ok) {
      throw new Error(`ONDC service error: ${response.statusText}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error: any) {
    console.error('[ONDC Select] Error processing select request:', error);

    return NextResponse.json(
      {
        context: {
          action: 'on_select',
          timestamp: new Date().toISOString(),
        },
        error: {
          type: 'INTERNAL_ERROR',
          code: '500',
          message: 'Internal server error',
        },
      },
      { status: 500 }
    );
  }
}
