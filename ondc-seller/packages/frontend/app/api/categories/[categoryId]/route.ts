import { NextResponse } from 'next/server';
import { getCategoryBySlug } from '@/lib/strapi-api';

export async function GET(
  request: Request,
  { params }: { params: { categoryId: string } }
) {
  try {
    const categoryId = params.categoryId;
    
    console.log('🚀 Category Detail API: Fetching category:', categoryId);
    
    // Fetch category from Strapi CMS by slug
    const category = await getCategoryBySlug(categoryId);

    if (!category) {
      return NextResponse.json(
        {
          success: false,
          error: 'Category not found',
          data: null,
        },
        { status: 404 }
      );
    }

    console.log('✅ Category Detail API: Successfully fetched category');
    console.log('📊 Category:', category.name);

    return NextResponse.json({
      success: true,
      data: category,
    });
  } catch (error) {
    console.error('❌ Category Detail API: Error fetching category:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch category',
        data: null,
      },
      { status: 500 }
    );
  }
}
