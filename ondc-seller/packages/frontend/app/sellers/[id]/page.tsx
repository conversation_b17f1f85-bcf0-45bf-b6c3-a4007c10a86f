'use client';

import React, { useState } from 'react';
import Image from '@/components/ui/Image';
import Link from 'next/link';

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  category: string;
  rating: number;
  description: string;
}

interface Review {
  id: string;
  user: {
    name: string;
    image: string;
  };
  rating: number;
  comment: string;
  date: string;
  product: {
    name: string;
    image: string;
  };
}

interface Seller {
  id: string;
  name: string;
  description: string;
  image: string;
  coverImage: string;
  rating: number;
  totalSales: number;
  verified: boolean;
  location: string;
  categories: string[];
  stats: {
    productsCount: number;
    customersServed: number;
    avgDeliveryTime: string;
    avgRating: number;
    totalReviews: number;
  };
  about: {
    description: string;
    established: string;
    specialties: string[];
    businessHours: string;
    contactEmail: string;
    contactPhone: string;
  };
}

const SellerDetailPage = ({ params }: { params: { id: string } }) => {
  const [activeTab, setActiveTab] = useState<'products' | 'reviews' | 'about'>('products');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [sortBy, setSortBy] = useState('featured');

  // This would typically come from an API
  const seller: Seller = {
    id: params.id,
    name: 'Tech World',
    description: 'Your one-stop shop for all things tech.',
    image: '/images/sellers/tech-world.jpg',
    coverImage: '/images/sellers/tech-world-cover.jpg',
    rating: 4.8,
    totalSales: 1500,
    verified: true,
    location: 'Mumbai, India',
    categories: ['Electronics', 'Accessories', 'Gadgets'],
    stats: {
      productsCount: 250,
      customersServed: 5000,
      avgDeliveryTime: '2-3 days',
      avgRating: 4.8,
      totalReviews: 850,
    },
    about: {
      description:
        'Tech World is a leading electronics retailer offering the latest gadgets and accessories. We pride ourselves on providing high-quality products and exceptional customer service.',
      established: '2015',
      specialties: [
        'Latest Smartphones',
        'Gaming Accessories',
        'Smart Home Devices',
        'Audio Equipment',
      ],
      businessHours: 'Mon-Sat: 9:00 AM - 8:00 PM',
      contactEmail: '<EMAIL>',
      contactPhone: '+91 1234567890',
    },
  };

  const products: Product[] = [
    {
      id: '1',
      name: 'Premium Wireless Headphones',
      price: 199.99,
      image: '/api/images/products/headphones.jpg',
      category: 'Audio',
      rating: 4.5,
      description: 'High-quality wireless headphones with noise cancellation.',
    },
    // Add more products...
  ];

  const reviews: Review[] = [
    {
      id: '1',
      user: {
        name: 'John Doe',
        image: '/images/users/john.jpg',
      },
      rating: 5,
      comment: 'Great service and fast delivery. The product quality is excellent!',
      date: '2024-03-15',
      product: {
        name: 'Premium Wireless Headphones',
        image: '/images/products/headphones.jpg',
      },
    },
    // Add more reviews...
  ];

  const filteredProducts = selectedCategory
    ? products.filter(product => product.category === selectedCategory)
    : products;

  const sortOptions = [
    { label: 'Featured', value: 'featured' },
    { label: 'Price: Low to High', value: 'price-asc' },
    { label: 'Price: High to Low', value: 'price-desc' },
    { label: 'Rating', value: 'rating' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="relative h-80">
        <Image
          src={seller.coverImage}
          alt={`${seller.name} cover`}
          fill
          className="object-cover"
          fallback="/images/placeholder.jpg"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
        <div className="absolute bottom-0 left-0 right-0">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
            <div className="flex items-end">
              <div className="relative w-32 h-32 rounded-xl overflow-hidden border-4 border-white">
                <Image
                  src={seller.image}
                  alt={seller.name}
                  fill
                  className="object-cover"
                  fallback="/images/placeholder.jpg"
                />
              </div>
              <div className="ml-6 pb-4">
                <h1 className="text-3xl font-bold text-white flex items-center">
                  {seller.name}
                  {seller.verified && (
                    <svg
                      className="ml-2 w-6 h-6 text-blue-500"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  )}
                </h1>
                <p className="mt-2 text-white/80">{seller.description}</p>
                <div className="mt-4 flex items-center space-x-6">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 text-yellow-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                    <span className="ml-2 text-white">{seller.rating} / 5</span>
                  </div>
                  <span className="text-white/80">{seller.totalSales}+ sales</span>
                  <span className="text-white/80">{seller.location}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-2 md:grid-cols-5 gap-8">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{seller.stats.productsCount}+</div>
              <div className="text-sm text-gray-600">Products</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">
                {seller.stats.customersServed}+
              </div>
              <div className="text-sm text-gray-600">Customers</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{seller.stats.avgDeliveryTime}</div>
              <div className="text-sm text-gray-600">Delivery Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{seller.stats.avgRating}</div>
              <div className="text-sm text-gray-600">Avg. Rating</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{seller.stats.totalReviews}+</div>
              <div className="text-sm text-gray-600">Reviews</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('products')}
              className={`pb-4 px-1 ${
                activeTab === 'products'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Products
            </button>
            <button
              onClick={() => setActiveTab('reviews')}
              className={`pb-4 px-1 ${
                activeTab === 'reviews'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              Reviews
            </button>
            <button
              onClick={() => setActiveTab('about')}
              className={`pb-4 px-1 ${
                activeTab === 'about'
                  ? 'border-b-2 border-blue-500 text-blue-600'
                  : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              About
            </button>
          </nav>
        </div>

        {/* Products Tab */}
        {activeTab === 'products' && (
          <div>
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setSelectedCategory(null)}
                  className={`px-4 py-2 rounded-full text-sm font-medium ${
                    !selectedCategory
                      ? 'bg-blue-100 text-blue-700'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  All
                </button>
                {seller.categories.map(category => (
                  <button
                    key={category}
                    onClick={() => setSelectedCategory(category)}
                    className={`px-4 py-2 rounded-full text-sm font-medium ${
                      selectedCategory === category
                        ? 'bg-blue-100 text-blue-700'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
              <select
                value={sortBy}
                onChange={e => setSortBy(e.target.value)}
                className="rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {filteredProducts.map(product => (
                <Link
                  key={product.id}
                  href={`/products/${product.id}`}
                  className="group bg-white rounded-lg shadow-sm hover:shadow-lg transition-shadow duration-200"
                >
                  <div className="aspect-w-1 aspect-h-1 w-full overflow-hidden rounded-t-lg">
                    <Image
                      src={product.image}
                      alt={product.name}
                      width={300}
                      height={300}
                      className="object-cover object-center group-hover:scale-105 transition-transform duration-200"
                      fallback="/images/placeholder.jpg"
                    />
                  </div>
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-gray-900">{product.name}</h3>
                    <p className="mt-1 text-sm text-gray-600">{product.description}</p>
                    <div className="mt-2 flex items-center justify-between">
                      <span className="text-lg font-bold text-gray-900">${product.price}</span>
                      <div className="flex items-center">
                        <svg
                          className="w-4 h-4 text-yellow-400"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="ml-1 text-sm text-gray-600">{product.rating}</span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Reviews Tab */}
        {activeTab === 'reviews' && (
          <div className="space-y-8">
            {reviews.map(review => (
              <div key={review.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex items-start">
                  <div className="relative w-12 h-12 rounded-full overflow-hidden">
                    <Image
                      src={review.user.image}
                      alt={review.user.name}
                      fill
                      className="object-cover"
                      fallback="/images/placeholder.jpg"
                    />
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900">{review.user.name}</h3>
                      <span className="text-sm text-gray-500">{review.date}</span>
                    </div>
                    <div className="mt-1 flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`w-5 h-5 ${
                            i < review.rating ? 'text-yellow-400' : 'text-gray-300'
                          }`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <p className="mt-4 text-gray-600">{review.comment}</p>
                    <div className="mt-4 flex items-center">
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden">
                        <Image
                          src={review.product.image}
                          alt={review.product.name}
                          fill
                          className="object-cover"
                          fallback="/images/placeholder.jpg"
                        />
                      </div>
                      <span className="ml-4 text-sm text-gray-500">
                        Purchased {review.product.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* About Tab */}
        {activeTab === 'about' && (
          <div className="bg-white rounded-lg shadow-sm p-8">
            <div className="space-y-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">About Us</h3>
                <p className="text-gray-600">{seller.about.description}</p>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-sm text-gray-500">Established</p>
                    <p className="mt-1 text-gray-900">{seller.about.established}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Business Hours</p>
                    <p className="mt-1 text-gray-900">{seller.about.businessHours}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Contact Email</p>
                    <p className="mt-1 text-gray-900">{seller.about.contactEmail}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Contact Phone</p>
                    <p className="mt-1 text-gray-900">{seller.about.contactPhone}</p>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Our Specialties</h3>
                <div className="flex flex-wrap gap-2">
                  {seller.about.specialties.map((specialty, index) => (
                    <span
                      key={index}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SellerDetailPage;
