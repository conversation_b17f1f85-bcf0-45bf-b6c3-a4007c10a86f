'use client';

import React from 'react';
import {
  ResponsiveContainer,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
} from 'recharts';

// Enhanced number formatting utility for better readability
const formatNumber = (value: number | undefined | null, compact: boolean = false): string => {
  // Handle undefined, null, or NaN values
  if (value === undefined || value === null || isNaN(value)) {
    return '0';
  }

  if (compact) {
    // For compact display (center text, tooltips)
    if (value >= 1000000) {
      return `${(value / 1000000).toFixed(1)}M`;
    } else if (value >= 1000) {
      return `${(value / 1000).toFixed(1)}K`;
    }
    return value.toString();
  } else {
    // For detailed display (full numbers with commas)
    return value.toLocaleString();
  }
};

// Enhanced percentage formatting
const formatPercentage = (value: number, total: number): string => {
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(1)}%`;
};

// Smart text truncation for labels
const truncateLabel = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

interface ChartCardProps {
  title: string;
  description?: string;
  children: React.ReactNode;
  className?: string;
  actions?: React.ReactNode;
}

export default function ChartCard({
  title,
  description,
  children,
  className = '',
  actions,
}: ChartCardProps) {
  return (
    <div className={`bg-white rounded-lg border border-gray-200 shadow-sm ${className}`}>
      <div className="flex items-center justify-between mb-[10px] px-6 pt-6">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 truncate" title={title}>
            {title}
          </h3>
          {description && (
            <p className="text-sm text-gray-600 mt-1" title={description}>
              {description}
            </p>
          )}
        </div>
        {actions && <div className="flex items-center space-x-2">{actions}</div>}
      </div>
      <div className="h-[calc(100%-76px)] overflow-hidden pr-3 py-2">
        <div className="w-full h-full">{children}</div>
      </div>
    </div>
  );
}

// Full-Screen Line Chart - No margins or spacing
interface LineChartProps {
  data: Array<{ label: string; value: number }>;
  color?: string;
  height?: number;
}

export function SimpleLineChart({ data, color = '#3B82F6', height = 320 }: LineChartProps) {
  const [hoveredPoint, setHoveredPoint] = React.useState<number | null>(null);

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">No data available</div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const minValue = Math.min(...data.map(d => d.value));
  const range = maxValue - minValue || 1;

  // Use percentage-based coordinates for true full-screen
  const chartHeight = height - 20; // Minimal space for labels

  return (
    <div className="w-full h-full relative overflow-hidden">
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full absolute inset-0"
        preserveAspectRatio="none"
        style={{ width: '100%', height: '100%' }}
      >
        {/* Background grid */}
        <defs>
          <pattern id="fullLineGrid" width="5" height="5" patternUnits="userSpaceOnUse">
            <path d="M 5 0 L 0 0 0 5" fill="none" stroke="#f8fafc" strokeWidth="0.3" />
          </pattern>
        </defs>
        <rect width="100" height="85" fill="url(#fullLineGrid)" />

        {/* Gradient for area */}
        <defs>
          <linearGradient id="fullLineGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={color} stopOpacity="0.4" />
            <stop offset="100%" stopColor={color} stopOpacity="0.05" />
          </linearGradient>
        </defs>

        {/* Create full-width area path */}
        <path
          d={`M 0,85 ${data
            .map((item, index) => {
              const x = (index / (data.length - 1)) * 100;
              const y = 85 - ((item.value - minValue) / range) * 80;
              return `L ${x},${y}`;
            })
            .join(' ')} L 100,85 Z`}
          fill="url(#fullLineGradient)"
          className="transition-all duration-300"
        />

        {/* Main line */}
        <polyline
          fill="none"
          stroke={color}
          strokeWidth="0.8"
          points={data
            .map((item, index) => {
              const x = (index / (data.length - 1)) * 100;
              const y = 85 - ((item.value - minValue) / range) * 80;
              return `${x},${y}`;
            })
            .join(' ')}
          className="transition-all duration-300"
        />

        {/* Data points */}
        {data.map((item, index) => {
          const x = (index / (data.length - 1)) * 100;
          const y = 85 - ((item.value - minValue) / range) * 80;
          const isHovered = hoveredPoint === index;

          return (
            <g key={index}>
              <circle
                cx={x}
                cy={y}
                r={isHovered ? '1.2' : '0.8'}
                fill="white"
                stroke={color}
                strokeWidth="0.6"
                className="transition-all duration-200 cursor-pointer"
                onMouseEnter={() => setHoveredPoint(index)}
                onMouseLeave={() => setHoveredPoint(null)}
              />
              {isHovered && (
                <g>
                  <rect
                    x={x - 8}
                    y={y - 8}
                    width="16"
                    height="6"
                    rx="1"
                    fill="rgba(0, 0, 0, 0.9)"
                  />
                  <text
                    x={x}
                    y={y - 4}
                    textAnchor="middle"
                    className="text-xs fill-white font-medium"
                    fontSize="3"
                  >
                    {formatNumber(item.value)}
                  </text>
                </g>
              )}
            </g>
          );
        })}

        {/* X-axis labels at bottom */}
        {data.map((item, index) => {
          const x = (index / (data.length - 1)) * 100;
          return (
            <text
              key={index}
              x={x}
              y="95"
              textAnchor="middle"
              className="text-xs fill-gray-600"
              fontSize="3"
            >
              {item.label}
            </text>
          );
        })}
      </svg>
    </div>
  );
}

// Full-Screen Bar Chart - No margins or spacing
interface BarChartProps {
  data: Array<{ label: string; value: number; color?: string }>;
  height?: number;
}

export function SimpleBarChart({ data, height = 320 }: BarChartProps) {
  const [hoveredBar, setHoveredBar] = React.useState<number | null>(null);

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">No data available</div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));

  return (
    <div className="w-full h-full relative overflow-hidden">
      <svg
        viewBox="0 0 100 100"
        className="w-full h-full absolute inset-0"
        preserveAspectRatio="none"
        style={{ width: '100%', height: '100%' }}
      >
        {/* Background grid */}
        <defs>
          <pattern id="fullBarGrid" width="4" height="4" patternUnits="userSpaceOnUse">
            <path d="M 4 0 L 0 0 0 4" fill="none" stroke="#f8fafc" strokeWidth="0.2" />
          </pattern>
        </defs>
        <rect width="100" height="85" fill="url(#fullBarGrid)" />

        {/* Bars */}
        {data.map((item, index) => {
          const barWidth = (100 / data.length) * 0.7; // 70% of available space per bar
          const barSpacing = (100 / data.length) * 0.15; // 15% spacing on each side
          const x = index * (100 / data.length) + barSpacing;
          const barHeight = (item.value / maxValue) * 80;
          const y = 85 - barHeight;
          const isHovered = hoveredBar === index;
          const barColor = item.color || '#3B82F6';

          return (
            <g key={index}>
              {/* Bar shadow */}
              <rect
                x={x + 0.2}
                y={y + 0.2}
                width={barWidth}
                height={barHeight}
                fill="rgba(0, 0, 0, 0.1)"
                rx="0.5"
              />

              {/* Main bar */}
              <rect
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill={barColor}
                rx="0.5"
                className={`transition-all duration-200 cursor-pointer ${
                  isHovered ? 'opacity-90' : 'opacity-100'
                }`}
                onMouseEnter={() => setHoveredBar(index)}
                onMouseLeave={() => setHoveredBar(null)}
              />

              {/* Hover tooltip */}
              {isHovered && (
                <g>
                  <rect
                    x={x + barWidth / 2 - 8}
                    y={y - 8}
                    width="16"
                    height="6"
                    rx="1"
                    fill="rgba(0, 0, 0, 0.9)"
                  />
                  <text
                    x={x + barWidth / 2}
                    y={y - 4}
                    textAnchor="middle"
                    className="text-xs fill-white font-medium"
                    fontSize="3"
                  >
                    {formatNumber(item.value)}
                  </text>
                </g>
              )}

              {/* X-axis labels */}
              <text
                x={x + barWidth / 2}
                y="95"
                textAnchor="middle"
                className="text-xs fill-gray-600"
                fontSize="3"
              >
                {item.label}
              </text>

              {/* Value labels on bars */}
              {!isHovered && barHeight > 8 && (
                <text
                  x={x + barWidth / 2}
                  y={y - 1}
                  textAnchor="middle"
                  className="text-xs fill-gray-900 font-medium"
                  fontSize="2.5"
                >
                  {item.value}
                </text>
              )}
            </g>
          );
        })}
      </svg>
    </div>
  );
}

// Simple Donut chart component
interface DonutChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  size?: number;
}

export function SimpleDonutChart({ data, size = 240 }: DonutChartProps) {
  const [hoveredSegment, setHoveredSegment] = React.useState<number | null>(null);

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">No data available</div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const center = size / 2;
  const radius = size / 2 - 30;
  const innerRadius = radius * 0.6;

  let cumulativePercentage = 0;

  return (
    <div className="flex items-center justify-center h-full w-full">
      <div className="relative w-full flex justify-center">
        <svg width={size} height={size} className="transform -rotate-90">
          {data.map((item, index) => {
            const percentage = (item.value / total) * 100;
            const strokeDasharray = `${percentage} ${100 - percentage}`;
            const strokeDashoffset = -cumulativePercentage;
            const isHovered = hoveredSegment === index;

            cumulativePercentage += percentage;

            return (
              <circle
                key={index}
                cx={center}
                cy={center}
                r={radius}
                fill="transparent"
                stroke={item.color}
                strokeWidth={radius - innerRadius}
                strokeDasharray={strokeDasharray}
                strokeDashoffset={strokeDashoffset}
                className={`transition-all duration-300 cursor-pointer ${
                  isHovered ? 'opacity-90 drop-shadow-lg' : 'opacity-100 hover:opacity-80'
                }`}
                style={{
                  strokeWidth: isHovered ? radius - innerRadius + 2 : radius - innerRadius,
                }}
                onMouseEnter={() => setHoveredSegment(index)}
                onMouseLeave={() => setHoveredSegment(null)}
              >
                <title>{`${item.label}: ${item.value} (${percentage.toFixed(1)}%)`}</title>
              </circle>
            );
          })}
        </svg>

        {/* Center text */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <div className="text-xl font-bold text-gray-900">{formatNumber(total)}</div>
            <div className="text-xs text-gray-500">Total</div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Stacked Area Chart Component
interface StackedAreaChartProps {
  data: Array<{ label: string; mobile: number; desktop: number }>;
  height?: number;
  mobileColor?: string;
  desktopColor?: string;
}

export function StackedAreaChart({
  data,
  height = 380,
  mobileColor = '#10B981',
  desktopColor = '#3B82F6',
}: StackedAreaChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">No data available</div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.mobile + d.desktop));
  const width = 100;
  const leftMargin = 2; // Minimal space for Y-axis labels
  const rightMargin = 0.5; // Minimal right margin
  const topMargin = 2; // Minimal top margin
  const bottomMargin = 15; // Reduced space for X-axis labels
  const chartWidth = width - leftMargin - rightMargin;
  const chartHeight = height - topMargin - bottomMargin;

  // Generate Y-axis ticks
  const yTicks = 5;
  const yTickValues = Array.from({ length: yTicks }, (_, i) =>
    Math.round((maxValue / (yTicks - 1)) * i)
  );

  // Create path for desktop area (bottom layer)
  const desktopPoints = data
    .map((item, index) => {
      const x =
        leftMargin +
        (data.length === 1 ? chartWidth / 2 : (index / (data.length - 1)) * chartWidth);
      const y = topMargin + chartHeight - (item.desktop / maxValue) * chartHeight;
      return `${x},${y}`;
    })
    .join(' ');

  // Create path for mobile area (top layer)
  const mobilePoints = data
    .map((item, index) => {
      const x =
        leftMargin +
        (data.length === 1 ? chartWidth / 2 : (index / (data.length - 1)) * chartWidth);
      const y = topMargin + chartHeight - ((item.mobile + item.desktop) / maxValue) * chartHeight;
      return `${x},${y}`;
    })
    .join(' ');

  // Create filled areas
  const desktopArea = `M ${leftMargin},${topMargin + chartHeight} L ${desktopPoints} L ${leftMargin + chartWidth},${topMargin + chartHeight} Z`;
  const mobileArea = `M ${leftMargin},${topMargin + chartHeight} L ${mobilePoints} L ${leftMargin + chartWidth},${topMargin + chartHeight} Z`;

  return (
    <div className="w-full h-full relative">
      <svg
        viewBox={`0 0 ${width} ${height}`}
        className="w-full h-full"
        preserveAspectRatio="xMidYMid meet"
      >
        {/* Grid lines */}
        <defs>
          <pattern id="areaGrid" width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#f3f4f6" strokeWidth="0.5" />
          </pattern>
          {/* Gradients for areas */}
          <linearGradient id="desktopGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={desktopColor} stopOpacity="0.8" />
            <stop offset="100%" stopColor={desktopColor} stopOpacity="0.3" />
          </linearGradient>
          <linearGradient id="mobileGradient" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" stopColor={mobileColor} stopOpacity="0.8" />
            <stop offset="100%" stopColor={mobileColor} stopOpacity="0.3" />
          </linearGradient>
        </defs>

        {/* Chart background */}
        <rect
          x={leftMargin}
          y={topMargin}
          width={chartWidth}
          height={chartHeight}
          fill="url(#areaGrid)"
        />

        {/* Y-axis labels */}
        {yTickValues.map((value, index) => {
          const y = topMargin + chartHeight - (value / maxValue) * chartHeight;
          return (
            <g key={index}>
              {/* Horizontal grid line */}
              <line
                x1={leftMargin}
                y1={y}
                x2={leftMargin + chartWidth}
                y2={y}
                stroke="#e5e7eb"
                strokeWidth="1"
                opacity="0.5"
              />
              {/* Y-axis label */}
              <text
                x={leftMargin - 0.5}
                y={y + 1}
                textAnchor="end"
                className="text-xs fill-gray-500"
                fontSize="9"
              >
                {value >= 1000 ? `${(value / 1000).toFixed(0)}k` : value}
              </text>
            </g>
          );
        })}

        {/* Desktop area (bottom layer) */}
        <path
          d={desktopArea}
          fill="url(#desktopGradient)"
          stroke={desktopColor}
          strokeWidth="2"
          className="transition-all duration-300"
        />

        {/* Mobile area (top layer) */}
        <path
          d={mobileArea}
          fill="url(#mobileGradient)"
          stroke={mobileColor}
          strokeWidth="2"
          className="transition-all duration-300"
        />

        {/* Data points with enhanced tooltips */}
        {data.map((item, index) => {
          const x =
            leftMargin +
            (data.length === 1 ? chartWidth / 2 : (index / (data.length - 1)) * chartWidth);
          const desktopY = topMargin + chartHeight - (item.desktop / maxValue) * chartHeight;
          const mobileY =
            topMargin + chartHeight - ((item.mobile + item.desktop) / maxValue) * chartHeight;

          return (
            <g key={index}>
              {/* Desktop point */}
              <circle
                cx={x}
                cy={desktopY}
                r="4"
                fill="white"
                stroke={desktopColor}
                strokeWidth="3"
                className="transition-all duration-200 cursor-pointer hover:r-6 hover:drop-shadow-lg"
              >
                <title>{`${item.label} - Desktop: ${item.desktop.toLocaleString()}`}</title>
              </circle>

              {/* Mobile point */}
              <circle
                cx={x}
                cy={mobileY}
                r="4"
                fill="white"
                stroke={mobileColor}
                strokeWidth="3"
                className="transition-all duration-200 cursor-pointer hover:r-6 hover:drop-shadow-lg"
              >
                <title>{`${item.label} - Mobile: ${item.mobile.toLocaleString()}, Total: ${(item.mobile + item.desktop).toLocaleString()}`}</title>
              </circle>
            </g>
          );
        })}

        {/* X-axis labels */}
        {data.map((item, index) => {
          const x =
            leftMargin +
            (data.length === 1 ? chartWidth / 2 : (index / (data.length - 1)) * chartWidth);
          return (
            <text
              key={index}
              x={x}
              y={height - 3}
              textAnchor="middle"
              className="text-xs fill-gray-600"
              fontSize="10"
            >
              {item.label}
            </text>
          );
        })}
      </svg>
    </div>
  );
}

// Enhanced Donut Chart with Center Text and Interactivity
interface EnhancedDonutChartProps {
  data: Array<{ label: string; value: number; color: string }>;
  centerText?: string;
  centerSubtext?: string;
  size?: number;
  onSegmentClick?: (segment: { label: string; value: number; color: string }) => void;
  onSegmentHover?: (segment: { label: string; value: number; color: string } | null) => void;
}

export function EnhancedDonutChart({
  data,
  centerText,
  centerSubtext,
  size,
  onSegmentClick,
  onSegmentHover,
}: EnhancedDonutChartProps) {
  const [hoveredSegment, setHoveredSegment] = React.useState<{
    label: string;
    value: number;
    color: string;
  } | null>(null);
  const [selectedSegment, setSelectedSegment] = React.useState<{
    label: string;
    value: number;
    color: string;
  } | null>(null);
  const [containerSize, setContainerSize] = React.useState(280);
  const containerRef = React.useRef<HTMLDivElement>(null);

  // Calculate responsive size based on container with significantly increased pie chart size
  React.useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const container = containerRef.current;
        const containerHeight = container.clientHeight;
        const containerWidth = container.clientWidth;
        // Use 90% of the smaller dimension for better space utilization
        const availableSize = Math.min(containerHeight - 30, containerWidth - 30);
        const newSize = Math.max(250, Math.min(availableSize, 450)); // Increased max from 380 to 450, min from 220 to 250
        setContainerSize(newSize);
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    return () => window.removeEventListener('resize', updateSize);
  }, []);

  const chartSize = size || containerSize;

  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">No data available</div>
    );
  }

  const total = data.reduce((sum, item) => sum + item.value, 0);
  const center = chartSize / 2;
  const radius = chartSize / 2 - 40;
  const innerRadius = radius * 0.55;

  // Calculate angles for each segment
  let cumulativeAngle = 0;
  const segments = data.map(item => {
    const percentage = (item.value / total) * 100;
    const angle = (item.value / total) * 360;
    const startAngle = cumulativeAngle;
    const endAngle = cumulativeAngle + angle;

    cumulativeAngle += angle;

    return {
      ...item,
      percentage,
      angle,
      startAngle,
      endAngle,
    };
  });

  // Create SVG path for each segment
  const createPath = (
    startAngle: number,
    endAngle: number,
    outerRadius: number,
    innerRadius: number
  ) => {
    const startAngleRad = (startAngle - 90) * (Math.PI / 180);
    const endAngleRad = (endAngle - 90) * (Math.PI / 180);

    const x1 = center + outerRadius * Math.cos(startAngleRad);
    const y1 = center + outerRadius * Math.sin(startAngleRad);
    const x2 = center + outerRadius * Math.cos(endAngleRad);
    const y2 = center + outerRadius * Math.sin(endAngleRad);

    const x3 = center + innerRadius * Math.cos(endAngleRad);
    const y3 = center + innerRadius * Math.sin(endAngleRad);
    const x4 = center + innerRadius * Math.cos(startAngleRad);
    const y4 = center + innerRadius * Math.sin(startAngleRad);

    const largeArcFlag = endAngle - startAngle <= 180 ? '0' : '1';

    return [
      'M',
      x1,
      y1,
      'A',
      outerRadius,
      outerRadius,
      0,
      largeArcFlag,
      1,
      x2,
      y2,
      'L',
      x3,
      y3,
      'A',
      innerRadius,
      innerRadius,
      0,
      largeArcFlag,
      0,
      x4,
      y4,
      'Z',
    ].join(' ');
  };

  const handleSegmentClick = (segment: (typeof segments)[0]) => {
    setSelectedSegment(segment);
    onSegmentClick?.(segment);
  };

  const handleSegmentHover = (segment: (typeof segments)[0] | null) => {
    setHoveredSegment(segment);
    onSegmentHover?.(segment);
  };

  const displaySegment = hoveredSegment || selectedSegment;
  const displayCenterText = displaySegment
    ? formatNumber(displaySegment.value, true) // Use compact format for center text
    : centerText || formatNumber(total, true);
  const displayCenterSubtext = displaySegment
    ? `${truncateLabel(displaySegment.label, 12)} (${formatPercentage(displaySegment.value, total)})`
    : centerSubtext || 'Total Visitors';

  // Enhanced responsive font sizing based on chart size and content length - optimized for larger pie charts
  const getFontSizes = () => {
    const textLength = displayCenterText.length;
    const subtextLength = displayCenterSubtext.length;

    if (chartSize <= 250) {
      return {
        main: textLength > 6 ? 'text-sm' : 'text-base',
        sub: 'text-xs',
        maxWidth: '100px',
        lineHeight: 'leading-tight',
      };
    } else if (chartSize <= 300) {
      return {
        main: textLength > 8 ? 'text-base' : 'text-lg',
        sub: subtextLength > 20 ? 'text-xs' : 'text-sm',
        maxWidth: '130px',
        lineHeight: 'leading-tight',
      };
    } else if (chartSize <= 350) {
      return {
        main: textLength > 10 ? 'text-lg' : 'text-xl',
        sub: subtextLength > 25 ? 'text-sm' : 'text-base',
        maxWidth: '160px',
        lineHeight: 'leading-snug',
      };
    } else if (chartSize <= 400) {
      return {
        main: textLength > 12 ? 'text-xl' : 'text-2xl',
        sub: subtextLength > 30 ? 'text-base' : 'text-lg',
        maxWidth: '190px',
        lineHeight: 'leading-snug',
      };
    } else {
      return {
        main: textLength > 14 ? 'text-2xl' : 'text-3xl',
        sub: subtextLength > 35 ? 'text-lg' : 'text-xl',
        maxWidth: '220px',
        lineHeight: 'leading-relaxed',
      };
    }
  };

  const fontSizes = getFontSizes();

  return (
    <div ref={containerRef} className="flex items-center justify-center h-full w-full p-2">
      {/* Enhanced Donut Chart - Larger with Better Spacing */}
      <div
        className="relative transition-all duration-300"
        style={{
          filter: hoveredSegment ? 'brightness(1.02)' : 'none',
        }}
      >
        <svg width={chartSize} height={chartSize}>
          {segments.map((segment, index) => {
            const isHovered = hoveredSegment?.label === segment.label;
            const isSelected = selectedSegment?.label === segment.label;
            const scale = isHovered || isSelected ? 1.08 : 1;
            const opacity = isHovered ? 0.85 : isSelected ? 0.9 : 1;

            return (
              <g
                key={index}
                style={{
                  transform: `scale(${scale})`,
                  transformOrigin: `${center}px ${center}px`,
                  filter: isHovered
                    ? 'drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2))'
                    : isSelected
                      ? 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))'
                      : 'none',
                }}
              >
                <path
                  d={createPath(segment.startAngle, segment.endAngle, radius, innerRadius)}
                  fill={segment.color}
                  stroke="white"
                  strokeWidth={isHovered ? '4' : '3'}
                  opacity={opacity}
                  className="transition-all duration-300 cursor-pointer"
                  onClick={() => handleSegmentClick(segment)}
                  onMouseEnter={() => handleSegmentHover(segment)}
                  onMouseLeave={() => handleSegmentHover(null)}
                >
                  <title>{`${segment.label}: ${formatNumber(segment.value, false)} (${formatPercentage(segment.value, total)})`}</title>
                </path>
              </g>
            );
          })}
        </svg>

        {/* Enhanced Center Text with Better Readability */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center px-3 py-2" style={{ maxWidth: fontSizes.maxWidth }}>
            <div
              className={`${fontSizes.main} font-bold text-gray-900 ${fontSizes.lineHeight} transition-all duration-300`}
              style={{
                wordBreak: 'break-word',
                hyphens: 'auto',
              }}
              title={
                displaySegment
                  ? formatNumber(displaySegment.value, false)
                  : formatNumber(total, false)
              }
            >
              {displayCenterText}
            </div>
            <div
              className={`${fontSizes.sub} text-gray-500 mt-1 ${fontSizes.lineHeight} transition-all duration-300`}
              style={{
                wordBreak: 'break-word',
                hyphens: 'auto',
              }}
              title={displayCenterSubtext}
            >
              {displayCenterSubtext}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// New Visitor Trends Chart using Recharts
interface VisitorTrendsChartProps {
  data: Array<{ label: string; mobile: number; desktop: number }>;
}

export function VisitorTrendsChart({ data }: VisitorTrendsChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">No data available</div>
    );
  }

  // Transform data for Recharts stacked area chart
  const chartData = data.map(item => ({
    name: item.label,
    desktop: item.desktop,
    mobile: item.mobile,
    total: item.desktop + item.mobile,
  }));

  // Enhanced custom tooltip with better number formatting
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const total = payload.reduce((sum: number, entry: any) => sum + entry.value, 0);

      return (
        <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg max-w-xs">
          <p className="font-semibold text-gray-900 mb-3 text-base">{label}</p>
          <div className="space-y-2">
            {payload.map((entry: any, index: number) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: entry.color }} />
                  <span className="text-sm font-medium text-gray-700">
                    {entry.dataKey === 'desktop' ? 'Desktop' : 'Mobile'}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold text-gray-900">
                    {formatNumber(entry.value, false)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatPercentage(entry.value, total)}
                  </div>
                </div>
              </div>
            ))}
          </div>
          <div className="border-t pt-3 mt-3 flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Total:</span>
            <div className="text-right">
              <div className="text-sm font-bold text-gray-900">{formatNumber(total, false)}</div>
              <div className="text-xs text-gray-500">{formatNumber(total, true)} visitors</div>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <div className="w-full h-full">
      {/* Professional Legend */}
      <div className="flex items-center justify-center mb-3 space-x-6 pt-4">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-blue-500 rounded"></div>
          <span className="text-sm text-gray-600">Desktop</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-green-500 rounded"></div>
          <span className="text-sm text-gray-600">Mobile</span>
        </div>
      </div>

      {/* Professional Recharts Implementation - Full Height Container */}
      <div className="h-[calc(100%-60px)] overflow-hidden">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={chartData} margin={{ top: 10, right: 10, left: 10, bottom: 20 }}>
            <defs>
              {/* Professional gradients matching shadcn standards */}
              <linearGradient id="desktopGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05} />
              </linearGradient>
              <linearGradient id="mobileGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#10B981" stopOpacity={0.3} />
                <stop offset="95%" stopColor="#10B981" stopOpacity={0.05} />
              </linearGradient>
            </defs>

            {/* Professional grid styling */}
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />

            {/* Modern X-axis styling */}
            <XAxis
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: '#6b7280' }}
            />

            {/* Enhanced Y-axis styling with better number formatting */}
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 11, fill: '#6b7280' }}
              tickFormatter={value => formatNumber(value, true)}
              width={50}
            />

            {/* Professional tooltip */}
            <Tooltip content={<CustomTooltip />} />

            {/* Desktop area (bottom layer) */}
            <Area
              type="monotone"
              dataKey="desktop"
              stackId="1"
              stroke="#3B82F6"
              strokeWidth={2}
              fill="url(#desktopGradient)"
              dot={false}
              activeDot={{ r: 4, stroke: '#3B82F6', strokeWidth: 2 }}
            />

            {/* Mobile area (top layer) */}
            <Area
              type="monotone"
              dataKey="mobile"
              stackId="1"
              stroke="#10B981"
              strokeWidth={2}
              fill="url(#mobileGradient)"
              dot={false}
              activeDot={{ r: 4, stroke: '#10B981', strokeWidth: 2 }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
