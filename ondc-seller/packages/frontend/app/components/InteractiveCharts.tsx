'use client';

import React, { useState, useMemo } from 'react';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
} from 'recharts';
import { format, subDays, subMonths, startOfMonth, endOfMonth } from 'date-fns';

// Types for our data structures
interface SalesDataPoint {
  date: string;
  sales: number;
  orders: number;
  revenue: number;
}

interface QuarterlySalesData {
  period: string;
  q1: number;
  q2: number;
  q3: number;
  q4: number;
  total: number;
}

// Time period options
const TIME_PERIODS = [
  { value: '7d', label: 'Last 7 days' },
  { value: '30d', label: 'Last 30 days' },
  { value: '3m', label: 'Last 3 months' },
  { value: '1y', label: 'Last year' },
] as const;

// Chart metric options
const CHART_METRICS = [
  { value: 'sales', label: 'Sales Volume' },
  { value: 'revenue', label: 'Revenue' },
  { value: 'orders', label: 'Order Count' },
] as const;

// Generate realistic mock data
const generateSalesData = (period: string): SalesDataPoint[] => {
  const data: SalesDataPoint[] = [];
  const now = new Date();

  let days: number;
  let dateFormat: string;

  switch (period) {
    case '7d':
      days = 7;
      dateFormat = 'MMM dd';
      break;
    case '30d':
      days = 30;
      dateFormat = 'MMM dd';
      break;
    case '3m':
      days = 90;
      dateFormat = 'MMM dd';
      break;
    case '1y':
      days = 365;
      dateFormat = 'MMM yyyy';
      break;
    default:
      days = 7;
      dateFormat = 'MMM dd';
  }

  for (let i = days - 1; i >= 0; i--) {
    const date = subDays(now, i);
    const baseValue = 1000 + Math.random() * 2000;
    const seasonalMultiplier = 1 + 0.3 * Math.sin((i / days) * Math.PI * 2);

    data.push({
      date: format(date, dateFormat),
      sales: Math.round(baseValue * seasonalMultiplier),
      orders: Math.round((baseValue * seasonalMultiplier) / 50),
      revenue: Math.round(baseValue * seasonalMultiplier * 25),
    });
  }

  return data;
};

// Generate quarterly sales data
const generateQuarterlySalesData = (): QuarterlySalesData[] => {
  const currentYear = new Date().getFullYear();
  const data: QuarterlySalesData[] = [];

  for (let i = 2; i >= 0; i--) {
    const year = currentYear - i;
    const q1 = 15000 + Math.random() * 10000;
    const q2 = 18000 + Math.random() * 12000;
    const q3 = 22000 + Math.random() * 8000;
    const q4 = 25000 + Math.random() * 15000;

    data.push({
      period: year.toString(),
      q1: Math.round(q1),
      q2: Math.round(q2),
      q3: Math.round(q3),
      q4: Math.round(q4),
      total: Math.round(q1 + q2 + q3 + q4),
    });
  }

  return data;
};

// Custom tooltip component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
        <p className="font-medium text-gray-900">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.value.toLocaleString()}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Sales Statistics Chart Component
interface SalesStatisticsChartProps {
  className?: string;
}

export function SalesStatisticsChart({ className = '' }: SalesStatisticsChartProps) {
  const [timePeriod, setTimePeriod] = useState<string>('30d');
  const [metric, setMetric] = useState<string>('sales');

  const data = useMemo(() => generateSalesData(timePeriod), [timePeriod]);

  const getMetricColor = (metric: string) => {
    switch (metric) {
      case 'sales':
        return '#3B82F6';
      case 'revenue':
        return '#10B981';
      case 'orders':
        return '#F59E0B';
      default:
        return '#3B82F6';
    }
  };

  return (
    <div className={`w-full h-full ${className}`}>
      {/* Controls */}
      <div className="flex flex-wrap gap-3 mb-4 px-6">
        <select
          value={timePeriod}
          onChange={e => setTimePeriod(e.target.value)}
          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {TIME_PERIODS.map(period => (
            <option key={period.value} value={period.value}>
              {period.label}
            </option>
          ))}
        </select>

        <select
          value={metric}
          onChange={e => setMetric(e.target.value)}
          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          {CHART_METRICS.map(metricOption => (
            <option key={metricOption.value} value={metricOption.value}>
              {metricOption.label}
            </option>
          ))}
        </select>
      </div>

      {/* Chart */}
      <div className="h-64 md:h-72 lg:h-80 overflow-hidden px-6">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={data} margin={{ top: 5, right: 0, left: 0, bottom: 5 }}>
            <defs>
              <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={getMetricColor(metric)} stopOpacity={0.3} />
                <stop offset="95%" stopColor={getMetricColor(metric)} stopOpacity={0.05} />
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              tickFormatter={value => value.toLocaleString()}
            />
            <Tooltip content={<CustomTooltip />} />
            <Area
              type="monotone"
              dataKey={metric}
              stroke={getMetricColor(metric)}
              strokeWidth={3}
              fill="url(#colorGradient)"
              dot={{ fill: getMetricColor(metric), strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6, stroke: getMetricColor(metric), strokeWidth: 2 }}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}

// Lifetime Sales Chart Component
interface LifetimeSalesChartProps {
  className?: string;
}

export function LifetimeSalesChart({ className = '' }: LifetimeSalesChartProps) {
  const [viewType, setViewType] = useState<string>('quarterly');

  const data = useMemo(() => generateQuarterlySalesData(), []);

  const chartData = useMemo(() => {
    if (viewType === 'quarterly') {
      return data.flatMap(item => [
        { period: `${item.period} Q1`, value: item.q1, year: item.period },
        { period: `${item.period} Q2`, value: item.q2, year: item.period },
        { period: `${item.period} Q3`, value: item.q3, year: item.period },
        { period: `${item.period} Q4`, value: item.q4, year: item.period },
      ]);
    } else {
      return data.map(item => ({ period: item.period, value: item.total, year: item.period }));
    }
  }, [data, viewType]);

  const getBarColor = (index: number) => {
    const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'];
    return colors[index % colors.length];
  };

  return (
    <div className={`w-full h-full ${className}`}>
      {/* Controls */}
      <div className="flex flex-wrap gap-3 mb-4 px-6">
        <select
          value={viewType}
          onChange={e => setViewType(e.target.value)}
          className="px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="quarterly">Quarterly View</option>
          <option value="yearly">Yearly View</option>
        </select>
      </div>

      {/* Chart */}
      <div className="h-64 md:h-72 lg:h-80 overflow-hidden px-6">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 5, right: 0, left: 0, bottom: 5 }}>
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis
              dataKey="period"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <YAxis
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              tickFormatter={value => `$${(value / 1000).toFixed(0)}k`}
            />
            <Tooltip
              content={<CustomTooltip />}
              formatter={(value: number) => [`$${value.toLocaleString()}`, 'Sales']}
            />
            <Bar dataKey="value" fill="#3B82F6" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}
