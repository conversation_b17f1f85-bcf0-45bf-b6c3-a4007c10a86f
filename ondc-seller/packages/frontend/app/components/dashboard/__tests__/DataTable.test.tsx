import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DataTable from '../DataTable';

interface TestData {
  id: string;
  name: string;
  value: number;
  status: string;
}

const mockData: TestData[] = [
  { id: '1', name: 'Item One', value: 100, status: 'Active' },
  { id: '2', name: 'Item Two', value: 200, status: 'Inactive' },
  { id: '3', name: 'Item Three', value: 150, status: 'Active' },
  { id: '4', name: 'Item Four', value: 75, status: 'Pending' },
  { id: '5', name: 'Item Five', value: 300, status: 'Active' },
];

const mockColumns = [
  { key: 'id' as const, header: 'ID', sortable: true },
  { key: 'name' as const, header: 'Name', sortable: true },
  { key: 'value' as const, header: 'Value', sortable: true },
  {
    key: 'status' as const,
    header: 'Status',
    render: (value: string) => <span className={`status-${value.toLowerCase()}`}>{value}</span>,
  },
];

describe('DataTable', () => {
  it('renders table with data and columns', () => {
    render(<DataTable data={mockData} columns={mockColumns} />);

    // Check headers
    expect(screen.getByText('ID')).toBeInTheDocument();
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Value')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();

    // Check data
    expect(screen.getByText('Item One')).toBeInTheDocument();
    expect(screen.getByText('Item Two')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('200')).toBeInTheDocument();
  });

  it('displays title and description when provided', () => {
    render(
      <DataTable
        data={mockData}
        columns={mockColumns}
        title="Test Table"
        description="This is a test table"
      />
    );

    expect(screen.getByText('Test Table')).toBeInTheDocument();
    expect(screen.getByText('This is a test table')).toBeInTheDocument();
  });

  it('implements text truncation with tooltip for long titles', () => {
    render(
      <DataTable
        data={mockData}
        columns={mockColumns}
        title="Very Long Table Title That Should Be Truncated"
        description="Very long description that should also be truncated properly"
      />
    );

    const titleElement = screen.getByText('Very Long Table Title That Should Be Truncated');
    expect(titleElement).toHaveClass('truncate');
    expect(titleElement).toHaveAttribute('title', 'Very Long Table Title That Should Be Truncated');

    const descElement = screen.getByText(
      'Very long description that should also be truncated properly'
    );
    expect(descElement).toHaveAttribute(
      'title',
      'Very long description that should also be truncated properly'
    );
  });

  it('renders search input when searchable is true', () => {
    render(<DataTable data={mockData} columns={mockColumns} searchable={true} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    expect(searchInput).toBeInTheDocument();
  });

  it('filters data based on search term', () => {
    render(<DataTable data={mockData} columns={mockColumns} searchable={true} />);

    const searchInput = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchInput, { target: { value: 'Item One' } });

    expect(screen.getByText('Item One')).toBeInTheDocument();
    expect(screen.queryByText('Item Two')).not.toBeInTheDocument();
  });

  it('sorts data when sortable column header is clicked', () => {
    render(<DataTable data={mockData} columns={mockColumns} />);

    const nameHeader = screen.getByText('Name');
    fireEvent.click(nameHeader);

    // After sorting by name ascending, "Item Five" should come first
    const rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('Item Five'); // First data row (index 1, header is 0)
  });

  it('toggles sort direction on repeated clicks', () => {
    render(<DataTable data={mockData} columns={mockColumns} />);

    const valueHeader = screen.getByText('Value');

    // First click - ascending
    fireEvent.click(valueHeader);
    let rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('75'); // Lowest value first

    // Second click - descending
    fireEvent.click(valueHeader);
    rows = screen.getAllByRole('row');
    expect(rows[1]).toHaveTextContent('300'); // Highest value first
  });

  it('renders custom cell content using render function', () => {
    render(<DataTable data={mockData} columns={mockColumns} />);

    // Check if custom status rendering is applied
    expect(screen.getAllByText('Active')[0]).toHaveClass('status-active');
    expect(screen.getByText('Inactive')).toHaveClass('status-inactive');
    expect(screen.getByText('Pending')).toHaveClass('status-pending');
  });

  it('displays pagination when enabled', () => {
    render(<DataTable data={mockData} columns={mockColumns} pagination={true} pageSize={2} />);

    expect(screen.getByText('Previous')).toBeInTheDocument();
    expect(screen.getByText('Next')).toBeInTheDocument();
    expect(screen.getByText(/Page 1 of/)).toBeInTheDocument();
  });

  it('navigates between pages correctly', () => {
    render(<DataTable data={mockData} columns={mockColumns} pagination={true} pageSize={2} />);

    // Initially shows first 2 items
    expect(screen.getByText('Item One')).toBeInTheDocument();
    expect(screen.getByText('Item Two')).toBeInTheDocument();
    expect(screen.queryByText('Item Three')).not.toBeInTheDocument();

    // Click next page
    const nextButton = screen.getByText('Next');
    fireEvent.click(nextButton);

    // Now shows next 2 items
    expect(screen.queryByText('Item One')).not.toBeInTheDocument();
    expect(screen.queryByText('Item Two')).not.toBeInTheDocument();
    expect(screen.getByText('Item Three')).toBeInTheDocument();
    expect(screen.getByText('Item Four')).toBeInTheDocument();
  });

  it('disables pagination buttons appropriately', () => {
    render(<DataTable data={mockData} columns={mockColumns} pagination={true} pageSize={2} />);

    const prevButton = screen.getByText('Previous');
    const nextButton = screen.getByText('Next');

    // On first page, previous should be disabled
    expect(prevButton).toBeDisabled();
    expect(nextButton).not.toBeDisabled();

    // Navigate to last page
    fireEvent.click(nextButton); // Page 2
    fireEvent.click(nextButton); // Page 3 (last page with pageSize=2 and 5 items)

    // On last page, next should be disabled
    expect(nextButton).toBeDisabled();
    expect(prevButton).not.toBeDisabled();
  });

  it('displays no data message when data array is empty', () => {
    render(<DataTable data={[]} columns={mockColumns} />);

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders actions when provided', () => {
    render(
      <DataTable
        data={mockData}
        columns={mockColumns}
        actions={<button data-testid="table-action">Action</button>}
      />
    );

    expect(screen.getByTestId('table-action')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const { container } = render(
      <DataTable data={mockData} columns={mockColumns} className="custom-table-class" />
    );

    const table = container.firstChild as HTMLElement;
    expect(table).toHaveClass('custom-table-class');
  });

  it('shows correct pagination info', () => {
    render(<DataTable data={mockData} columns={mockColumns} pagination={true} pageSize={3} />);

    expect(screen.getByText('Showing 1 to 3 of 5 results')).toBeInTheDocument();
  });
});
