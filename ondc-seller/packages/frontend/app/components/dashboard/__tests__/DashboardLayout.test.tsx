import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import DashboardLayout from '../DashboardLayout';

// Mock Next.js router
jest.mock('next/navigation', () => ({
  usePathname: () => '/dashboard',
}));

// Mock Next.js Link component
jest.mock('next/link', () => {
  return function MockLink({ children, href, ...props }: any) {
    return (
      <a href={href} {...props}>
        {children}
      </a>
    );
  };
});

describe('DashboardLayout', () => {
  it('renders dashboard layout with sidebar and main content', () => {
    render(
      <DashboardLayout>
        <div data-testid="dashboard-content">Dashboard Content</div>
      </DashboardLayout>
    );

    // Check if main content is rendered
    expect(screen.getByTestId('dashboard-content')).toBeInTheDocument();

    // Check if sidebar navigation items are present
    expect(screen.getAllByText('Overview')).toHaveLength(2); // Desktop and mobile
    expect(screen.getAllByText('Orders')).toHaveLength(2);
    expect(screen.getAllByText('Products')).toHaveLength(2);
    expect(screen.getAllByText('Analytics')).toHaveLength(2);
    expect(screen.getAllByText('ONDC Network')).toHaveLength(2);
  });

  it('displays ONDC Seller branding', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Check for brand elements
    expect(screen.getAllByText('ONDC Seller')).toHaveLength(2); // Desktop and mobile
    expect(screen.getAllByText('OS')).toHaveLength(2); // Logo initials
  });

  it('renders navigation links with correct hrefs', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    const overviewLinks = screen.getAllByRole('link', { name: /overview/i });
    expect(overviewLinks[0]).toHaveAttribute('href', '/dashboard');

    const ordersLinks = screen.getAllByRole('link', { name: /orders/i });
    expect(ordersLinks[0]).toHaveAttribute('href', '/dashboard/orders');

    const productsLinks = screen.getAllByRole('link', { name: /products/i });
    expect(productsLinks[0]).toHaveAttribute('href', '/dashboard/products');
  });

  it('highlights active navigation item', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Since we mocked usePathname to return '/dashboard', Overview should be active
    const overviewLinks = screen.getAllByRole('link', { name: /overview/i });
    expect(overviewLinks[0]).toHaveClass('bg-blue-100', 'text-blue-900');
  });

  it('renders secondary navigation items', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    expect(screen.getAllByText('Settings')).toHaveLength(2); // Desktop and mobile
    expect(screen.getAllByText('Help & Support')).toHaveLength(2);
    expect(screen.getAllByText('API Documentation')).toHaveLength(2);
  });

  it('renders header with search and user profile', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Check search input
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();

    // Check user profile elements
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('JD')).toBeInTheDocument(); // User initials
  });

  it('toggles mobile sidebar when hamburger menu is clicked', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Mobile sidebar should be hidden initially
    const mobileSidebarContainer = document.querySelector('.lg\\:hidden');
    expect(mobileSidebarContainer).toHaveClass('hidden');

    // Click hamburger menu
    const hamburgerButton = screen.getByLabelText('Open menu');
    fireEvent.click(hamburgerButton);

    // Mobile sidebar should now be visible
    expect(mobileSidebarContainer).toHaveClass('block');
  });

  it('closes mobile sidebar when close button is clicked', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Open mobile sidebar first
    const hamburgerButton = screen.getByLabelText('Open menu');
    fireEvent.click(hamburgerButton);

    const mobileSidebarContainer = document.querySelector('.lg\\:hidden');
    expect(mobileSidebarContainer).toHaveClass('block');

    // Click close button (X icon)
    const closeButton = screen.getByLabelText('Close sidebar');
    fireEvent.click(closeButton);
    expect(mobileSidebarContainer).toHaveClass('hidden');
  });

  it('closes mobile sidebar when overlay is clicked', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Open mobile sidebar first
    const hamburgerButton = screen.getByLabelText('Open menu');
    fireEvent.click(hamburgerButton);

    const mobileSidebarContainer = document.querySelector('.lg\\:hidden');
    expect(mobileSidebarContainer).toHaveClass('block');

    // Click overlay
    const overlay = document.querySelector('.fixed.inset-0.bg-gray-600');
    if (overlay) {
      fireEvent.click(overlay);
      expect(mobileSidebarContainer).toHaveClass('hidden');
    }
  });

  it('has responsive design classes', () => {
    const { container } = render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Check for responsive classes
    expect(container.querySelector('.lg\\:pl-64')).toBeInTheDocument(); // Main content offset
    expect(container.querySelector('.lg\\:hidden')).toBeInTheDocument(); // Mobile sidebar
    expect(container.querySelector('.hidden.lg\\:fixed')).toBeInTheDocument(); // Desktop sidebar
  });

  it('renders navigation icons', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Check if SVG icons are present (we can't easily test specific icons, but we can check for SVGs)
    const svgElements = document.querySelectorAll('svg');
    expect(svgElements.length).toBeGreaterThan(0);
  });

  it('has proper accessibility attributes', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Check for proper navigation structure
    const navElements = screen.getAllByRole('navigation');
    expect(navElements.length).toBeGreaterThan(0);

    // Check for proper button roles
    const buttons = screen.getAllByRole('button');
    expect(buttons.length).toBeGreaterThan(0);
  });

  it('maintains consistent styling across desktop and mobile', () => {
    render(
      <DashboardLayout>
        <div>Content</div>
      </DashboardLayout>
    );

    // Both desktop and mobile sidebars should have consistent navigation items
    const overviewLinks = screen.getAllByText('Overview');
    expect(overviewLinks).toHaveLength(2); // One for desktop, one for mobile

    const ordersLinks = screen.getAllByText('Orders');
    expect(ordersLinks).toHaveLength(2);
  });
});
