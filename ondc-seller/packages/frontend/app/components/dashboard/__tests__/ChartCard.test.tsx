import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import ChartCard, { Simple<PERSON><PERSON><PERSON><PERSON>, SimpleBarChart, SimpleDonut<PERSON>hart } from '../ChartCard';

// Mock SVG elements for testing
jest.mock('react', () => ({
  ...jest.requireActual('react'),
}));

describe('ChartCard', () => {
  it('renders chart card with title and children', () => {
    render(
      <ChartCard title="Test Chart">
        <div data-testid="chart-content">Chart Content</div>
      </ChartCard>
    );

    expect(screen.getByText('Test Chart')).toBeInTheDocument();
    expect(screen.getByTestId('chart-content')).toBeInTheDocument();
  });

  it('implements text truncation with tooltip for long titles', () => {
    render(
      <ChartCard title="Very Long Chart Title That Should Be Truncated">
        <div>Content</div>
      </ChartCard>
    );

    const titleElement = screen.getByText('Very Long Chart Title That Should Be Truncated');
    expect(titleElement).toHaveClass('truncate');
    expect(titleElement).toHaveAttribute('title', 'Very Long Chart Title That Should Be Truncated');
  });

  it('displays description when provided', () => {
    render(
      <ChartCard title="Test Chart" description="This is a test description">
        <div>Content</div>
      </ChartCard>
    );

    expect(screen.getByText('This is a test description')).toBeInTheDocument();
  });

  it('renders actions when provided', () => {
    render(
      <ChartCard title="Test Chart" actions={<button data-testid="chart-action">Action</button>}>
        <div>Content</div>
      </ChartCard>
    );

    expect(screen.getByTestId('chart-action')).toBeInTheDocument();
  });

  it('has consistent styling and layout', () => {
    const { container } = render(
      <ChartCard title="Test Chart">
        <div>Content</div>
      </ChartCard>
    );

    const card = container.firstChild as HTMLElement;
    expect(card).toHaveClass(
      'bg-card',
      'text-card-foreground',
      'rounded-lg',
      'border',
      'shadow-sm'
    );
  });

  it('has fixed height for chart content area', () => {
    render(
      <ChartCard title="Test Chart">
        <div data-testid="chart-content">Content</div>
      </ChartCard>
    );

    const chartArea = screen.getByTestId('chart-content').parentElement;
    expect(chartArea).toHaveClass('w-full', 'h-full');
  });
});

describe('SimpleLineChart', () => {
  const mockData = [
    { label: 'Jan', value: 100 },
    { label: 'Feb', value: 150 },
    { label: 'Mar', value: 120 },
  ];

  it('renders line chart with data', () => {
    render(<SimpleLineChart data={mockData} />);

    // Check if SVG is rendered
    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('displays no data message when data is empty', () => {
    render(<SimpleLineChart data={[]} />);

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders with custom color', () => {
    render(<SimpleLineChart data={mockData} color="#FF0000" />);

    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('handles single data point', () => {
    const singleData = [{ label: 'Jan', value: 100 }];
    render(<SimpleLineChart data={singleData} />);

    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });
});

describe('SimpleBarChart', () => {
  const mockData = [
    { label: 'A', value: 100 },
    { label: 'B', value: 150 },
    { label: 'C', value: 120 },
  ];

  it('renders bar chart with data', () => {
    render(<SimpleBarChart data={mockData} />);

    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('displays no data message when data is empty', () => {
    render(<SimpleBarChart data={[]} />);

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders bars with custom colors', () => {
    const coloredData = [
      { label: 'A', value: 100, color: '#FF0000' },
      { label: 'B', value: 150, color: '#00FF00' },
    ];
    render(<SimpleBarChart data={coloredData} />);

    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });
});

describe('SimpleDonutChart', () => {
  const mockData = [
    { label: 'Category A', value: 30, color: '#FF0000' },
    { label: 'Category B', value: 50, color: '#00FF00' },
    { label: 'Category C', value: 20, color: '#0000FF' },
  ];

  it('renders donut chart with data', () => {
    render(<SimpleDonutChart data={mockData} />);

    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });

  it('displays no data message when data is empty', () => {
    render(<SimpleDonutChart data={[]} />);

    expect(screen.getByText('No data available')).toBeInTheDocument();
  });

  it('renders legend with labels and values', () => {
    render(<SimpleDonutChart data={mockData} />);

    expect(screen.getByText('Category A')).toBeInTheDocument();
    expect(screen.getByText('Category B')).toBeInTheDocument();
    expect(screen.getByText('Category C')).toBeInTheDocument();
    // Values are now displayed with percentages in the format "30 (30.0%)"
    expect(screen.getByText('30 (30.0%)')).toBeInTheDocument();
    expect(screen.getByText('50 (50.0%)')).toBeInTheDocument();
    expect(screen.getByText('20 (20.0%)')).toBeInTheDocument();
  });

  it('renders with custom size', () => {
    render(<SimpleDonutChart data={mockData} size={300} />);

    const svg = document.querySelector('svg');
    expect(svg).toHaveAttribute('width', '300');
    expect(svg).toHaveAttribute('height', '300');
  });

  it('handles single data point', () => {
    const singleData = [{ label: 'Single', value: 100, color: '#FF0000' }];
    render(<SimpleDonutChart data={singleData} />);

    const svg = document.querySelector('svg');
    expect(svg).toBeInTheDocument();
  });
});
