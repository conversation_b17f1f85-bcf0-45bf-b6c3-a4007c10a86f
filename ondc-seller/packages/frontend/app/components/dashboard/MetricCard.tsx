'use client';

import React from 'react';

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    type: 'increase' | 'decrease';
  };
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

export default function MetricCard({
  title,
  value,
  change,
  description,
  icon,
  className = '',
}: MetricCardProps) {
  return (
    <div
      className={`h-64 bg-card text-card-foreground rounded-lg border shadow-sm p-6 flex flex-col ${className}`}
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-muted-foreground truncate" title={title}>
          {title}
        </h3>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </div>

      <div className="flex-1 flex flex-col justify-center">
        <div className="text-3xl font-bold text-foreground mb-2">{value}</div>

        {change && (
          <div className="flex items-center mb-2">
            <span
              className={`inline-flex items-center text-sm font-medium ${
                change.type === 'increase'
                  ? 'text-green-600 dark:text-green-400'
                  : 'text-red-600 dark:text-red-400'
              }`}
            >
              {change.type === 'increase' ? (
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"
                  />
                </svg>
              ) : (
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6"
                  />
                </svg>
              )}
              {change.value}
            </span>
          </div>
        )}

        {description && (
          <p className="text-sm text-muted-foreground line-clamp-2" title={description}>
            {description}
          </p>
        )}
      </div>
    </div>
  );
}

// Specific metric card components for ONDC Seller dashboard
export function RevenueCard({
  revenue,
  change,
}: {
  revenue: string;
  change?: MetricCardProps['change'];
}) {
  return (
    <MetricCard
      title="Total Revenue"
      value={revenue}
      change={change}
      description="Revenue from ONDC network sales"
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
          />
        </svg>
      }
    />
  );
}

export function OrdersCard({
  orders,
  change,
}: {
  orders: string | number;
  change?: MetricCardProps['change'];
}) {
  return (
    <MetricCard
      title="Total Orders"
      value={orders}
      change={change}
      description="Orders received this month"
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
          />
        </svg>
      }
    />
  );
}

export function CustomersCard({
  customers,
  change,
}: {
  customers: string | number;
  change?: MetricCardProps['change'];
}) {
  return (
    <MetricCard
      title="Active Customers"
      value={customers}
      change={change}
      description="Customers who made purchases"
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
          />
        </svg>
      }
    />
  );
}

export function ConversionCard({
  rate,
  change,
}: {
  rate: string;
  change?: MetricCardProps['change'];
}) {
  return (
    <MetricCard
      title="Conversion Rate"
      value={rate}
      change={change}
      description="Visitors to customers conversion"
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
          />
        </svg>
      }
    />
  );
}

export function ONDCNetworkCard({
  connections,
  change,
}: {
  connections: string | number;
  change?: MetricCardProps['change'];
}) {
  return (
    <MetricCard
      title="ONDC Network Reach"
      value={connections}
      change={change}
      description="Connected buyer apps"
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"
          />
        </svg>
      }
    />
  );
}

export function InventoryCard({
  products,
  change,
}: {
  products: string | number;
  change?: MetricCardProps['change'];
}) {
  return (
    <MetricCard
      title="Active Products"
      value={products}
      change={change}
      description="Products listed on ONDC"
      icon={
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
          />
        </svg>
      }
    />
  );
}
