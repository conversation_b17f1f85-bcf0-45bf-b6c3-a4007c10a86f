'use client';

import React, { Suspense } from 'react';
import { AdminDashboardWrapper } from '@/components/admin/AdminSuspenseWrapper';
import { useLoading } from '@/contexts/LoadingContext';
import DashboardSkeleton from '@/components/skeletons/DashboardSkeleton';
import Link from 'next/link';
import Image from 'next/image';
import {
  ShoppingBagIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  PlusIcon,
} from '@heroicons/react/24/outline';
import ChartCard, { EnhancedDonutChart, VisitorTrendsChart } from '../components/ChartCard';
import { SalesStatisticsChart, LifetimeSalesChart } from '../components/InteractiveCharts';

interface StatCard {
  name: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease';
  icon: React.ComponentType<any>;
}

const stats: StatCard[] = [
  {
    name: 'Total Revenue',
    value: '₹2,45,678',
    change: '+12.5%',
    changeType: 'increase',
    icon: CurrencyDollarIcon,
  },
  {
    name: 'Total Orders',
    value: '1,234',
    change: '+8.2%',
    changeType: 'increase',
    icon: ClipboardDocumentListIcon,
  },
  {
    name: 'Total Products',
    value: '456',
    change: '+3.1%',
    changeType: 'increase',
    icon: ShoppingBagIcon,
  },
  {
    name: 'Total Customers',
    value: '2,890',
    change: '-2.4%',
    changeType: 'decrease',
    icon: UsersIcon,
  },
];

const recentOrders = [
  {
    id: '#ORD-001',
    customer: 'John Doe',
    email: '<EMAIL>',
    total: '₹1,299',
    status: 'Completed',
    date: '2024-01-15',
  },
  {
    id: '#ORD-002',
    customer: 'Jane Smith',
    email: '<EMAIL>',
    total: '₹2,499',
    status: 'Processing',
    date: '2024-01-15',
  },
  {
    id: '#ORD-003',
    customer: 'Mike Johnson',
    email: '<EMAIL>',
    total: '₹899',
    status: 'Shipped',
    date: '2024-01-14',
  },
  {
    id: '#ORD-004',
    customer: 'Sarah Wilson',
    email: '<EMAIL>',
    total: '₹3,299',
    status: 'Pending',
    date: '2024-01-14',
  },
  {
    id: '#ORD-005',
    customer: 'David Brown',
    email: '<EMAIL>',
    total: '₹1,799',
    status: 'Completed',
    date: '2024-01-13',
  },
];

const topProducts = [
  {
    id: 1,
    name: 'Nike Air Max 270',
    image: '/images/placeholder.png',
    sales: 156,
    revenue: '₹2,34,000',
    stock: 45,
  },
  {
    id: 2,
    name: 'Adidas Ultraboost 22',
    image: '/images/placeholder.png',
    sales: 134,
    revenue: '₹2,01,000',
    stock: 23,
  },
  {
    id: 3,
    name: 'Puma RS-X',
    image: '/images/placeholder.png',
    sales: 98,
    revenue: '₹1,47,000',
    stock: 67,
  },
  {
    id: 4,
    name: 'New Balance 990v5',
    image: '/images/placeholder.png',
    sales: 87,
    revenue: '₹1,30,500',
    stock: 12,
  },
  {
    id: 5,
    name: 'Converse Chuck Taylor',
    image: '/images/placeholder.png',
    sales: 76,
    revenue: '₹91,200',
    stock: 89,
  },
];

// Mock data is now handled within the InteractiveCharts components

// Visitor data for different time periods
const visitorData = {
  '7days': [
    { label: 'Mon', mobile: 1250, desktop: 2100 },
    { label: 'Tue', mobile: 1400, desktop: 2300 },
    { label: 'Wed', mobile: 1600, desktop: 2500 },
    { label: 'Thu', mobile: 1350, desktop: 2200 },
    { label: 'Fri', mobile: 1800, desktop: 2800 },
    { label: 'Sat', mobile: 2200, desktop: 3200 },
    { label: 'Sun', mobile: 1900, desktop: 2900 },
  ],
  '30days': [
    { label: 'Week 1', mobile: 8500, desktop: 15200 },
    { label: 'Week 2', mobile: 9200, desktop: 16800 },
    { label: 'Week 3', mobile: 8800, desktop: 15900 },
    { label: 'Week 4', mobile: 10100, desktop: 18500 },
  ],
  '3months': [
    { label: 'Month 1', mobile: 35000, desktop: 62000 },
    { label: 'Month 2', mobile: 38000, desktop: 68000 },
    { label: 'Month 3', mobile: 42000, desktop: 75000 },
  ],
  '6months': [
    { label: 'Jan', mobile: 32000, desktop: 58000 },
    { label: 'Feb', mobile: 35000, desktop: 62000 },
    { label: 'Mar', mobile: 38000, desktop: 68000 },
    { label: 'Apr', mobile: 42000, desktop: 75000 },
    { label: 'May', mobile: 45000, desktop: 82000 },
    { label: 'Jun', mobile: 48000, desktop: 88000 },
  ],
  yearly: [
    { label: '2020', mobile: 420000, desktop: 780000 },
    { label: '2021', mobile: 480000, desktop: 890000 },
    { label: '2022', mobile: 520000, desktop: 950000 },
    { label: '2023', mobile: 580000, desktop: 1020000 },
  ],
};

// Browser data for different time periods
const browserData = {
  today: [
    { label: 'Chrome', value: 2450, color: '#4285F4' },
    { label: 'Safari', value: 1200, color: '#FF9500' },
    { label: 'Firefox', value: 800, color: '#FF7139' },
    { label: 'Edge', value: 650, color: '#0078D4' },
    { label: 'Others', value: 400, color: '#6B7280' },
  ],
  '7days': [
    { label: 'Chrome', value: 18500, color: '#4285F4' },
    { label: 'Safari', value: 9200, color: '#FF9500' },
    { label: 'Firefox', value: 6800, color: '#FF7139' },
    { label: 'Edge', value: 4200, color: '#0078D4' },
    { label: 'Others', value: 2800, color: '#6B7280' },
  ],
  '30days': [
    { label: 'Chrome', value: 78500, color: '#4285F4' },
    { label: 'Safari', value: 42000, color: '#FF9500' },
    { label: 'Firefox', value: 28500, color: '#FF7139' },
    { label: 'Edge', value: 18200, color: '#0078D4' },
    { label: 'Others', value: 12800, color: '#6B7280' },
  ],
  '3months': [
    { label: 'Chrome', value: 245000, color: '#4285F4' },
    { label: 'Safari', value: 128000, color: '#FF9500' },
    { label: 'Firefox', value: 89000, color: '#FF7139' },
    { label: 'Edge', value: 56000, color: '#0078D4' },
    { label: 'Others', value: 38000, color: '#6B7280' },
  ],
  '6months': [
    { label: 'Chrome', value: 520000, color: '#4285F4' },
    { label: 'Safari', value: 280000, color: '#FF9500' },
    { label: 'Firefox', value: 195000, color: '#FF7139' },
    { label: 'Edge', value: 125000, color: '#0078D4' },
    { label: 'Others', value: 85000, color: '#6B7280' },
  ],
  yearly: [
    { label: 'Chrome', value: 1250000, color: '#4285F4' },
    { label: 'Safari', value: 680000, color: '#FF9500' },
    { label: 'Firefox', value: 480000, color: '#FF7139' },
    { label: 'Edge', value: 320000, color: '#0078D4' },
    { label: 'Others', value: 220000, color: '#6B7280' },
  ],
};

// Dashboard content component
function DashboardContent() {
  const [visitorPeriod, setVisitorPeriod] = React.useState<keyof typeof visitorData>('7days');
  const [browserPeriod, setBrowserPeriod] = React.useState<keyof typeof browserData>('7days');
  const { loading } = useLoading();

  // Simulate data loading
  React.useEffect(() => {
    // This would be replaced with actual data fetching
    const timer = setTimeout(() => {
      // Data loaded
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="md:flex md:items-center md:justify-between px-4 sm:px-6 md:px-8">
        <div className="flex-1 min-w-0">
          <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
            Dashboard
          </h2>
          <p className="mt-1 text-sm text-gray-500">
            Welcome back! Here's what's happening with your store today.
          </p>
        </div>
        <div className="mt-4 flex md:mt-0 md:ml-4">
          <Link
            href="/admin/products/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="-ml-1 mr-2 h-5 w-5" />
            Add Product
          </Link>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 px-4 sm:px-6 md:px-8">
        {stats.map(stat => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <stat.icon className="h-6 w-6 text-gray-400" />
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">{stat.name}</dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">{stat.value}</div>
                      <div
                        className={`ml-2 flex items-baseline text-sm font-semibold ${
                          stat.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                        }`}
                      >
                        {stat.changeType === 'increase' ? (
                          <ArrowTrendingUpIcon className="self-center flex-shrink-0 h-4 w-4 text-green-500" />
                        ) : (
                          <ArrowTrendingDownIcon className="self-center flex-shrink-0 h-4 w-4 text-red-500" />
                        )}
                        <span className="ml-1">{stat.change}</span>
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Section - Responsive Grid */}
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-2 2xl:grid-cols-4 px-4 sm:px-6 md:px-8">
        {/* Sales Statistics Chart */}
        <div className="xl:col-span-1 2xl:col-span-2">
          <ChartCard
            title="Sales Statistics"
            description="Interactive sales performance with dynamic time periods"
          >
            <SalesStatisticsChart />
          </ChartCard>
        </div>

        {/* Lifetime Sales Chart */}
        <div className="xl:col-span-1 2xl:col-span-2">
          <ChartCard
            title="Lifetime Sales"
            description="Interactive quarterly and yearly sales performance"
          >
            <LifetimeSalesChart />
          </ChartCard>
        </div>
      </div>

      {/* Enhanced Charts Section - Responsive Grid */}
      <div className="grid grid-cols-1 gap-6 xl:grid-cols-2 2xl:grid-cols-4 px-4 sm:px-6 md:px-8">
        {/* Visitor Trends - Stacked Area Chart */}
        <div className="xl:col-span-1 2xl:col-span-2">
          <ChartCard
            title="Visitor Trends"
            description="Visitor distribution by device type over time"
            className="h-96"
            actions={
              <select
                className="text-sm border border-gray-300 rounded-md px-3 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={visitorPeriod}
                onChange={e => setVisitorPeriod(e.target.value as keyof typeof visitorData)}
              >
                <option value="7days">Last 7 days</option>
                <option value="30days">Last 30 days</option>
                <option value="3months">Last 3 months</option>
                <option value="6months">Last 6 months</option>
                <option value="yearly">Yearly</option>
              </select>
            }
          >
            <VisitorTrendsChart data={visitorData[visitorPeriod]} />
          </ChartCard>
        </div>

        {/* Browser Distribution - Enhanced Donut Chart */}
        <div className="xl:col-span-1 2xl:col-span-2">
          <ChartCard
            title="Browser Distribution"
            description="Visitor breakdown by browser type"
            className="h-96"
            actions={
              <select
                className="text-sm border border-gray-300 rounded-md px-3 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={browserPeriod}
                onChange={e => setBrowserPeriod(e.target.value as keyof typeof browserData)}
              >
                <option value="today">Today</option>
                <option value="7days">Last 7 days</option>
                <option value="30days">Last 30 days</option>
                <option value="3months">Last 3 months</option>
                <option value="6months">Last 6 months</option>
                <option value="yearly">Yearly</option>
              </select>
            }
          >
            <EnhancedDonutChart
              data={browserData[browserPeriod]}
              centerSubtext={`${
                browserPeriod === 'today'
                  ? 'Today'
                  : browserPeriod === '7days'
                    ? 'Last 7 days'
                    : browserPeriod === '30days'
                      ? 'Last 30 days'
                      : browserPeriod === '3months'
                        ? 'Last 3 months'
                        : browserPeriod === '6months'
                          ? 'Last 6 months'
                          : 'Yearly'
              }`}
              onSegmentClick={segment => {
                console.log('Browser segment clicked:', segment);
              }}
              onSegmentHover={segment => {
                console.log('Browser segment hovered:', segment);
              }}
            />
          </ChartCard>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 px-4 sm:px-6 md:px-8">
        {/* Recent Orders */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Recent Orders</h3>
              <Link href="/admin/orders" className="text-sm text-blue-600 hover:text-blue-500">
                View all
              </Link>
            </div>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {recentOrders.map(order => (
                  <li key={order.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {order.id} - {order.customer}
                        </p>
                        <p className="text-sm text-gray-500 truncate">{order.email}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">{order.total}</p>
                        <p
                          className={`text-xs ${
                            order.status === 'Completed'
                              ? 'text-green-600'
                              : order.status === 'Processing'
                                ? 'text-yellow-600'
                                : order.status === 'Shipped'
                                  ? 'text-blue-600'
                                  : 'text-gray-600'
                          }`}
                        >
                          {order.status}
                        </p>
                      </div>
                      <div>
                        <Link
                          href={`/admin/orders/${order.id}`}
                          className="text-gray-400 hover:text-gray-500"
                        >
                          <EyeIcon className="h-5 w-5" />
                        </Link>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg leading-6 font-medium text-gray-900">Top Products</h3>
              <Link href="/admin/products" className="text-sm text-blue-600 hover:text-blue-500">
                View all
              </Link>
            </div>
            <div className="flow-root">
              <ul className="-my-5 divide-y divide-gray-200">
                {topProducts.map(product => (
                  <li key={product.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className="relative h-10 w-10 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                          <Image
                            src="/images/products/placeholder.svg"
                            alt={product.name}
                            width={40}
                            height={40}
                            className="object-cover rounded-lg"
                            priority={false}
                          />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">{product.name}</p>
                        <p className="text-sm text-gray-500">
                          {product.sales} sales • Stock: {product.stock}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-gray-900">{product.revenue}</p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Main dashboard component with loading wrapper
export default function AdminDashboard() {
  return (
    <Suspense fallback={<DashboardSkeleton />}>
      <DashboardContent />
    </Suspense>
  );
}
