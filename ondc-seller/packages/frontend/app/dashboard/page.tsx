'use client';

import React from 'react';
import DashboardLayout from '../components/dashboard/DashboardLayout';
import {
  RevenueCard,
  OrdersCard,
  CustomersCard,
  ConversionCard,
  ONDCNetworkCard,
  InventoryCard,
} from '../components/dashboard/MetricCard';
import ChartCard, {
  Simple<PERSON>ine<PERSON>hart,
  SimpleBar<PERSON>hart,
  SimpleDonutChart,
} from '../components/dashboard/ChartCard';
import DataTable from '../components/dashboard/DataTable';

// Mock data for demonstration
const revenueData = [
  { label: 'Jan', value: 45000 },
  { label: 'Feb', value: 52000 },
  { label: 'Mar', value: 48000 },
  { label: 'Apr', value: 61000 },
  { label: 'May', value: 55000 },
  { label: 'Jun', value: 67000 },
];

const categoryData = [
  { label: 'Electronics', value: 35, color: '#3B82F6' },
  { label: 'Fashion', value: 28, color: '#10B981' },
  { label: 'Home & Garden', value: 20, color: '#F59E0B' },
  { label: 'Books', value: 12, color: '#EF4444' },
  { label: 'Sports', value: 5, color: '#8B5CF6' },
];

const topProductsData = [
  { label: 'Smartphone', value: 145 },
  { label: 'Laptop', value: 98 },
  { label: 'Headphones', value: 76 },
  { label: 'Watch', value: 54 },
  { label: 'Tablet', value: 32 },
];

const recentOrdersData = [
  {
    id: 'ORD-001',
    customer: 'John Doe',
    product: 'Wireless Headphones',
    amount: '₹2,499',
    status: 'Delivered',
    date: '2024-01-15',
    buyerApp: 'Paytm',
  },
  {
    id: 'ORD-002',
    customer: 'Jane Smith',
    product: 'Smartphone Case',
    amount: '₹599',
    status: 'Processing',
    date: '2024-01-14',
    buyerApp: 'PhonePe',
  },
  {
    id: 'ORD-003',
    customer: 'Mike Johnson',
    product: 'Bluetooth Speaker',
    amount: '₹1,299',
    status: 'Shipped',
    date: '2024-01-14',
    buyerApp: 'Flipkart',
  },
  {
    id: 'ORD-004',
    customer: 'Sarah Wilson',
    product: 'Laptop Stand',
    amount: '₹899',
    status: 'Delivered',
    date: '2024-01-13',
    buyerApp: 'Amazon',
  },
  {
    id: 'ORD-005',
    customer: 'David Brown',
    product: 'USB Cable',
    amount: '₹299',
    status: 'Processing',
    date: '2024-01-13',
    buyerApp: 'Myntra',
  },
];

const orderColumns = [
  {
    key: 'id' as const,
    header: 'Order ID',
    sortable: true,
  },
  {
    key: 'customer' as const,
    header: 'Customer',
    sortable: true,
  },
  {
    key: 'product' as const,
    header: 'Product',
    sortable: true,
    render: (value: string) => (
      <span className="truncate" title={value}>
        {value}
      </span>
    ),
  },
  {
    key: 'amount' as const,
    header: 'Amount',
    sortable: true,
    className: 'font-medium',
  },
  {
    key: 'status' as const,
    header: 'Status',
    render: (value: string) => (
      <span
        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
          value === 'Delivered'
            ? 'bg-green-100 text-green-800'
            : value === 'Shipped'
              ? 'bg-blue-100 text-blue-800'
              : value === 'Processing'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-gray-100 text-gray-800'
        }`}
      >
        {value}
      </span>
    ),
  },
  {
    key: 'buyerApp' as const,
    header: 'Buyer App',
    sortable: true,
  },
  {
    key: 'date' as const,
    header: 'Date',
    sortable: true,
  },
];

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Page Header */}
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Dashboard Overview</h1>
          <p className="text-gray-600 mt-2 text-sm sm:text-base">
            Welcome to your ONDC Seller dashboard. Monitor your sales, orders, and network
            performance.
          </p>
        </div>

        {/* Metrics Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          <RevenueCard revenue="₹3,24,500" change={{ value: '+12.5%', type: 'increase' }} />
          <OrdersCard orders={1234} change={{ value: '+8.2%', type: 'increase' }} />
          <CustomersCard customers={856} change={{ value: '+15.3%', type: 'increase' }} />
          <ConversionCard rate="4.2%" change={{ value: '+0.8%', type: 'increase' }} />
          <ONDCNetworkCard connections={12} change={{ value: '+2', type: 'increase' }} />
          <InventoryCard products={245} change={{ value: '+18', type: 'increase' }} />
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
          <ChartCard
            title="Revenue Trend"
            description="Monthly revenue for the last 6 months"
            actions={
              <select className="text-sm border border-gray-300 rounded-md px-3 py-1 bg-white">
                <option>Last 6 months</option>
                <option>Last 3 months</option>
                <option>Last month</option>
              </select>
            }
          >
            <SimpleLineChart data={revenueData} />
          </ChartCard>

          <ChartCard title="Sales by Category" description="Product category distribution">
            <SimpleDonutChart data={categoryData} />
          </ChartCard>
        </div>

        {/* Top Products Chart */}
        <ChartCard title="Top Selling Products" description="Best performing products this month">
          <SimpleBarChart data={topProductsData} />
        </ChartCard>

        {/* Recent Orders Table */}
        <DataTable
          data={recentOrdersData}
          columns={orderColumns}
          title="Recent Orders"
          description="Latest orders from ONDC network"
          searchable={true}
          pagination={true}
          pageSize={5}
          actions={
            <button className="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700">
              View All Orders
            </button>
          }
        />
      </div>
    </DashboardLayout>
  );
}
