'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import Breadcrumbs from '../../../../../components/Breadcrumbs';
import ProductGrid from '../../../../../components/ProductGrid';
import CategoryFilters from '../../../../../components/CategoryFilters';
import Pagination from '../../../../../components/Pagination';
import NoProductsFound from '../../../../../components/NoProductsFound';
import {
  getSubcategoryProducts,
  getSubcategoryInfo,
  ProductsQueryParams,
} from '../../../../../lib/category-api';
import { getCurrentTenantId } from '../../../../../lib/tenant';
import { productCategories } from '../../../../../data/categories';

// Define filter groups for subcategories
const SUBCATEGORY_FILTER_GROUPS = {
  // Fashion subcategories
  mens: [
    {
      id: 'type',
      name: 'Type',
      options: [
        { id: 'shirts', name: 'Shirts' },
        { id: 'tshirts', name: 'T-Shirts' },
        { id: 'jeans', name: 'Jeans' },
        { id: 'trousers', name: 'Trousers' },
        { id: 'suits', name: 'Suits' },
      ],
    },
    {
      id: 'size',
      name: 'Size',
      options: [
        { id: 's', name: 'S' },
        { id: 'm', name: 'M' },
        { id: 'l', name: 'L' },
        { id: 'xl', name: 'XL' },
        { id: 'xxl', name: 'XXL' },
      ],
    },
    {
      id: 'color',
      name: 'Color',
      options: [
        { id: 'black', name: 'Black' },
        { id: 'white', name: 'White' },
        { id: 'blue', name: 'Blue' },
        { id: 'grey', name: 'Grey' },
      ],
    },
  ],
  womens: [
    {
      id: 'type',
      name: 'Type',
      options: [
        { id: 'dresses', name: 'Dresses' },
        { id: 'tops', name: 'Tops' },
        { id: 'jeans', name: 'Jeans' },
        { id: 'skirts', name: 'Skirts' },
        { id: 'sarees', name: 'Sarees' },
      ],
    },
    {
      id: 'size',
      name: 'Size',
      options: [
        { id: 'xs', name: 'XS' },
        { id: 's', name: 'S' },
        { id: 'm', name: 'M' },
        { id: 'l', name: 'L' },
        { id: 'xl', name: 'XL' },
      ],
    },
    {
      id: 'color',
      name: 'Color',
      options: [
        { id: 'black', name: 'Black' },
        { id: 'white', name: 'White' },
        { id: 'red', name: 'Red' },
        { id: 'pink', name: 'Pink' },
      ],
    },
  ],
  kids: [
    {
      id: 'type',
      name: 'Type',
      options: [
        { id: 'tshirts', name: 'T-Shirts' },
        { id: 'jeans', name: 'Jeans' },
        { id: 'dresses', name: 'Dresses' },
        { id: 'shorts', name: 'Shorts' },
      ],
    },
    {
      id: 'age',
      name: 'Age',
      options: [
        { id: '0-2', name: '0-2 years' },
        { id: '2-5', name: '2-5 years' },
        { id: '5-8', name: '5-8 years' },
        { id: '8-12', name: '8-12 years' },
      ],
    },
  ],
  footwear: [
    {
      id: 'type',
      name: 'Type',
      options: [
        { id: 'casual', name: 'Casual' },
        { id: 'formal', name: 'Formal' },
        { id: 'sports', name: 'Sports' },
        { id: 'sandals', name: 'Sandals' },
      ],
    },
    {
      id: 'size',
      name: 'Size',
      options: [
        { id: '6', name: 'UK 6' },
        { id: '7', name: 'UK 7' },
        { id: '8', name: 'UK 8' },
        { id: '9', name: 'UK 9' },
        { id: '10', name: 'UK 10' },
      ],
    },
  ],
  // Electronics subcategories
  smartphones: [
    {
      id: 'brand',
      name: 'Brand',
      options: [
        { id: 'apple', name: 'Apple' },
        { id: 'samsung', name: 'Samsung' },
        { id: 'xiaomi', name: 'Xiaomi' },
        { id: 'oneplus', name: 'OnePlus' },
        { id: 'google', name: 'Google' },
      ],
    },
    {
      id: 'features',
      name: 'Features',
      options: [
        { id: '5g', name: '5G' },
        { id: 'wireless-charging', name: 'Wireless Charging' },
        { id: 'fast-charging', name: 'Fast Charging' },
        { id: 'waterproof', name: 'Waterproof' },
      ],
    },
  ],
  laptops: [
    {
      id: 'brand',
      name: 'Brand',
      options: [
        { id: 'apple', name: 'Apple' },
        { id: 'dell', name: 'Dell' },
        { id: 'hp', name: 'HP' },
        { id: 'lenovo', name: 'Lenovo' },
        { id: 'asus', name: 'Asus' },
      ],
    },
    {
      id: 'processor',
      name: 'Processor',
      options: [
        { id: 'intel-i3', name: 'Intel i3' },
        { id: 'intel-i5', name: 'Intel i5' },
        { id: 'intel-i7', name: 'Intel i7' },
        { id: 'amd-ryzen', name: 'AMD Ryzen' },
        { id: 'apple-m1', name: 'Apple M1' },
      ],
    },
  ],
};

// Define price ranges (same as category page)
const PRICE_RANGES = [
  { min: 0, max: 1000 },
  { min: 1000, max: 5000 },
  { min: 5000, max: 10000 },
  { min: 10000, max: 20000 },
  { min: 20000, max: Infinity },
];

interface SubcategoryPageProps {
  params: {
    categoryId: string;
    subcategoryId: string;
  };
  searchParams: {
    page?: string;
    sort?: string;
  };
}

export default function SubcategoryPage({ params, searchParams }: SubcategoryPageProps) {
  const { categoryId, subcategoryId } = params;
  console.log(`[SubcategoryPage] Component rendered for ${categoryId}/${subcategoryId}`);

  const router = useRouter();
  const pathname = usePathname();

  // State
  const [products, setProducts] = useState<any[]>([]);
  const [category, setCategory] = useState<any>(null);
  const [subcategory, setSubcategory] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: parseInt(searchParams.page || '1', 10),
    pageSize: 12,
    pageCount: 1,
    total: 0,
  });

  // Query parameters
  const [queryParams, setQueryParams] = useState<ProductsQueryParams>({
    page: parseInt(searchParams.page || '1', 10),
    pageSize: 12,
    sortBy: searchParams.sort || 'newest',
    filters: {},
    priceRange: null,
    inStockOnly: false,
  });

  // Get filter groups for this subcategory
  const filterGroups =
    SUBCATEGORY_FILTER_GROUPS[subcategoryId as keyof typeof SUBCATEGORY_FILTER_GROUPS] || [];

  // Fetch products
  useEffect(() => {
    console.log(`[SubcategoryPage] useEffect triggered for ${categoryId}/${subcategoryId}`);
    console.log(`[SubcategoryPage] useEffect queryParams:`, queryParams);

    const fetchProducts = async () => {
      console.log(`[SubcategoryPage] fetchProducts called for ${categoryId}/${subcategoryId}`);
      setLoading(true);

      try {
        const tenantId = getCurrentTenantId();
        console.log(
          `[SubcategoryPage] Fetching products for ${categoryId}/${subcategoryId} with tenant ID: ${tenantId}`
        );
        console.log(`[SubcategoryPage] Query params:`, queryParams);

        // Get subcategory info first to verify it exists
        const subcategoryInfo = getSubcategoryInfo(categoryId, subcategoryId);
        console.log(`[SubcategoryPage] Subcategory info:`, subcategoryInfo);

        if (!subcategoryInfo) {
          console.error(
            `[SubcategoryPage] Subcategory ${subcategoryId} not found in category ${categoryId}`
          );
          setError(`Subcategory ${subcategoryId} not found in category ${categoryId}`);
          setLoading(false);
          return;
        }

        console.log(`[SubcategoryPage] About to call getSubcategoryProducts`);
        const result = await getSubcategoryProducts(
          categoryId,
          subcategoryId,
          queryParams,
          tenantId
        );

        console.log(`[SubcategoryPage] API result:`, result);

        if (result.error) {
          console.error(`[SubcategoryPage] Error:`, result.error);
          setError(result.error);
        } else {
          setProducts(result.products);
          setCategory(result.category);
          setSubcategory(result.subcategory);
          if (result.pagination) {
            setPagination(result.pagination);
          }
          setError(null);
        }
      } catch (err) {
        console.error(`[SubcategoryPage] Exception:`, err);
        setError(err instanceof Error ? err.message : 'Failed to load products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId, subcategoryId, queryParams]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({ ...prev, page }));

    // Update URL with new page parameter
    const url = new URL(window.location.href);
    url.searchParams.set('page', page.toString());
    router.push(url.pathname + url.search);
  };

  // Handle sort change
  const handleSortChange = (sortOption: string) => {
    setQueryParams(prev => ({ ...prev, sortBy: sortOption, page: 1 }));

    // Update URL with new sort parameter
    const url = new URL(window.location.href);
    url.searchParams.set('sort', sortOption);
    url.searchParams.delete('page'); // Reset page when sorting changes
    router.push(url.pathname + url.search);
  };

  // Handle filter change
  const handleFilterChange = (filters: Record<string, string[]>) => {
    setQueryParams(prev => ({ ...prev, filters, page: 1 }));
  };

  // Handle price range change
  const handlePriceRangeChange = (priceRange: { min: number; max: number } | null) => {
    setQueryParams(prev => ({ ...prev, priceRange, page: 1 }));
  };

  // Handle availability change
  const handleAvailabilityChange = (inStockOnly: boolean) => {
    setQueryParams(prev => ({ ...prev, inStockOnly, page: 1 }));
  };

  // Clear all filters
  const clearAllFilters = () => {
    setQueryParams({
      page: 1,
      pageSize: 12,
      sortBy: 'newest',
      filters: {},
      priceRange: null,
      inStockOnly: false,
    });

    // Update URL to remove all filter parameters
    router.push(pathname || `/products/category/${categoryId}/${subcategoryId}`);
  };

  // If subcategory doesn't exist, return 404
  if (!loading && !subcategory && !error) {
    return notFound();
  }

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    // { label: 'Products', href: '/products' },
    { label: category?.name || categoryId, href: `/products/category/${categoryId}` },
    {
      label: subcategory?.name || subcategoryId,
      href: `/products/category/${categoryId}/${subcategoryId}`,
      active: true,
    },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      <div className="flex flex-col space-y-6">
        {/* Subcategory header */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {subcategory?.name || subcategoryId}
          </h1>
          <p className="text-gray-600">
            {subcategory?.description ||
              `Browse our ${subcategoryId} collection in the ${category?.name || categoryId} category.`}
          </p>
        </div>

        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar with filters */}
          <div className="lg:col-span-1">
            {filterGroups.length > 0 ? (
              <CategoryFilters
                filterGroups={filterGroups}
                priceRanges={PRICE_RANGES}
                onFilterChange={handleFilterChange}
                onPriceRangeChange={handlePriceRangeChange}
                onAvailabilityChange={handleAvailabilityChange}
              />
            ) : (
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 mb-4">Filters</h2>
                <div className="flex flex-col items-center justify-center py-6">
                  <Image
                    src="/images/no-filters.svg"
                    alt="No filters available"
                    width={120}
                    height={120}
                    className="mb-4"
                  />
                  <p className="text-gray-500 text-center">
                    No specific filters available for this category.
                  </p>
                </div>
                <div className="mt-4 border-t border-gray-200 pt-4">
                  <h3 className="text-md font-medium text-gray-900 mb-3">Price</h3>
                  <div className="space-y-2">
                    {PRICE_RANGES.map((range, index) => (
                      <div key={`price-${index}`} className="flex items-center">
                        <input
                          id={`price-${index}`}
                          name="price-range"
                          type="radio"
                          className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                          onChange={() => handlePriceRangeChange(range)}
                        />
                        <label htmlFor={`price-${index}`} className="ml-3 text-sm text-gray-700">
                          {range.min === 0
                            ? `Under ₹${range.max.toLocaleString()}`
                            : range.max === Infinity
                              ? `₹${range.min.toLocaleString()}+`
                              : `₹${range.min.toLocaleString()} - ₹${range.max.toLocaleString()}`}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="mt-4 border-t border-gray-200 pt-4">
                  <h3 className="text-md font-medium text-gray-900 mb-3">Availability</h3>
                  <div className="flex items-center">
                    <input
                      id="in-stock"
                      name="in-stock"
                      type="checkbox"
                      className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      onChange={e => handleAvailabilityChange(e.target.checked)}
                    />
                    <label htmlFor="in-stock" className="ml-3 text-sm text-gray-700">
                      In Stock Only
                    </label>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Product grid */}
          <div className="lg:col-span-3">
            <ProductGrid
              products={products}
              isLoading={loading}
              error={error ? new Error(error) : null}
              onSortChange={handleSortChange}
              emptyStateMessage={`No products found in ${subcategory?.name || subcategoryId}.`}
              emptyStateAction={
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <button
                    onClick={clearAllFilters}
                    className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                  >
                    Clear Filters
                  </button>
                  <Link
                    href={`/products/category/${categoryId}`}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    View All {category?.name || categoryId}
                  </Link>
                </div>
              }
            />

            {/* Pagination */}
            {!loading && !error && products.length > 0 && (
              <div className="mt-8">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.pageCount}
                  onPageChange={handlePageChange}
                  totalItems={pagination.total}
                  itemsPerPage={pagination.pageSize}
                />
              </div>
            )}

            {/* No products with filters applied */}
            {!loading && !error && products.length === 0 && (
              <NoProductsFound
                message="No products match your selected filters."
                showHomeLink={false}
                showClearFiltersButton={true}
                onClearFilters={clearAllFilters}
                customActions={
                  <Link
                    href={`/products/category/${categoryId}`}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    View All {category?.name || categoryId}
                  </Link>
                }
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
