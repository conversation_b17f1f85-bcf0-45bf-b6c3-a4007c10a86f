import { Metadata, ResolvingMetadata } from 'next';
import { getSubcategoryById, getCategoryById } from '../../../../../data/categories';

interface SubcategoryLayoutProps {
  children: React.ReactNode;
  params: {
    categoryId: string;
    subcategoryId: string;
  };
}

export async function generateMetadata(
  { params }: SubcategoryLayoutProps,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const { categoryId, subcategoryId } = params;

  // Get subcategory information
  const subcategoryInfo = getSubcategoryById(categoryId, subcategoryId);
  const categoryInfo = getCategoryById(categoryId);

  // If subcategory doesn't exist, use fallback metadata
  if (!subcategoryInfo) {
    return {
      title: `Products | ONDC Seller Platform`,
      description: 'Browse our product catalog on the ONDC Seller Platform.',
    };
  }

  // Generate SEO metadata based on subcategory
  const categoryName = categoryInfo?.name || '';
  return {
    title: `${subcategoryInfo.name} | ${categoryName} | ONDC Seller Platform`,
    description:
      subcategoryInfo.description ||
      `Explore our ${subcategoryInfo.name} collection in the ${categoryName} category.`,
    openGraph: {
      title: `${subcategoryInfo.name} | ${categoryName} | ONDC Seller Platform`,
      description:
        subcategoryInfo.description ||
        `Explore our ${subcategoryInfo.name} collection in the ${categoryName} category.`,
      type: 'website',
      locale: 'en_IN',
      siteName: 'ONDC Seller Platform',
    },
  };
}

export default function SubcategoryLayout({ children }: SubcategoryLayoutProps) {
  return <>{children}</>;
}
