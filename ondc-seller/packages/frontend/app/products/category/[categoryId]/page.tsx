'use client';

import { useState, useEffect } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import Breadcrumbs from '../../../../components/Breadcrumbs';
import ProductGrid from '../../../../components/ProductGrid';
import CategoryFilters from '../../../../components/CategoryFilters';
import Pagination from '../../../../components/Pagination';
import NoProductsFound from '../../../../components/NoProductsFound';
import {
  getCategoryProducts,
  getCategoryInfo,
  ProductsQueryParams,
} from '../../../../lib/category-api';
import { getCurrentTenantId } from '../../../../lib/tenant';
import { productCategories } from '../../../../data/categories';

// Define filter groups for each category
const FILTER_GROUPS = {
  electronics: [
    {
      id: 'brand',
      name: 'Brand',
      options: [
        { id: 'apple', name: 'Apple' },
        { id: 'samsung', name: 'Samsung' },
        { id: 'sony', name: 'Sony' },
        { id: 'lg', name: 'LG' },
        { id: 'xiaomi', name: 'Xiaomi' },
      ],
    },
    {
      id: 'features',
      name: 'Features',
      options: [
        { id: 'wireless', name: 'Wireless' },
        { id: 'bluetooth', name: 'Bluetooth' },
        { id: 'touchscreen', name: 'Touchscreen' },
        { id: 'waterproof', name: 'Waterproof' },
      ],
    },
  ],
  fashion: [
    {
      id: 'brand',
      name: 'Brand',
      options: [
        { id: 'nike', name: 'Nike' },
        { id: 'adidas', name: 'Adidas' },
        { id: 'puma', name: 'Puma' },
        { id: 'levis', name: "Levi's" },
        { id: 'zara', name: 'Zara' },
      ],
    },
    {
      id: 'size',
      name: 'Size',
      options: [
        { id: 's', name: 'Small' },
        { id: 'm', name: 'Medium' },
        { id: 'l', name: 'Large' },
        { id: 'xl', name: 'X-Large' },
      ],
    },
    {
      id: 'color',
      name: 'Color',
      options: [
        { id: 'black', name: 'Black' },
        { id: 'white', name: 'White' },
        { id: 'blue', name: 'Blue' },
        { id: 'red', name: 'Red' },
        { id: 'green', name: 'Green' },
      ],
    },
  ],
  home: [
    {
      id: 'brand',
      name: 'Brand',
      options: [
        { id: 'ikea', name: 'IKEA' },
        { id: 'homecenter', name: 'Home Center' },
        { id: 'urban', name: 'Urban Ladder' },
        { id: 'godrej', name: 'Godrej' },
      ],
    },
    {
      id: 'material',
      name: 'Material',
      options: [
        { id: 'wood', name: 'Wood' },
        { id: 'metal', name: 'Metal' },
        { id: 'plastic', name: 'Plastic' },
        { id: 'glass', name: 'Glass' },
        { id: 'fabric', name: 'Fabric' },
      ],
    },
  ],
  beauty: [
    {
      id: 'brand',
      name: 'Brand',
      options: [
        { id: 'loreal', name: "L'Oreal" },
        { id: 'maybelline', name: 'Maybelline' },
        { id: 'lakme', name: 'Lakme' },
        { id: 'mac', name: 'MAC' },
        { id: 'forest', name: 'Forest Essentials' },
      ],
    },
    {
      id: 'concern',
      name: 'Skin Concern',
      options: [
        { id: 'dry', name: 'Dry Skin' },
        { id: 'oily', name: 'Oily Skin' },
        { id: 'sensitive', name: 'Sensitive Skin' },
        { id: 'acne', name: 'Acne-Prone' },
        { id: 'aging', name: 'Anti-Aging' },
      ],
    },
  ],
  grocery: [
    {
      id: 'dietary',
      name: 'Dietary',
      options: [
        { id: 'vegetarian', name: 'Vegetarian' },
        { id: 'vegan', name: 'Vegan' },
        { id: 'glutenfree', name: 'Gluten-Free' },
        { id: 'organic', name: 'Organic' },
      ],
    },
    {
      id: 'packaging',
      name: 'Packaging',
      options: [
        { id: 'ecofriendly', name: 'Eco-Friendly' },
        { id: 'recyclable', name: 'Recyclable' },
        { id: 'plastic-free', name: 'Plastic-Free' },
      ],
    },
  ],
};

// Define price ranges
const PRICE_RANGES = [
  { min: 0, max: 1000 },
  { min: 1000, max: 5000 },
  { min: 5000, max: 10000 },
  { min: 10000, max: 20000 },
  { min: 20000, max: Infinity },
];

interface CategoryPageProps {
  params: {
    categoryId: string;
  };
  searchParams: {
    page?: string;
    sort?: string;
  };
}

export default function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { categoryId } = params;
  const router = useRouter();
  const pathname = usePathname();

  // State
  const [products, setProducts] = useState<any[]>([]);
  const [category, setCategory] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: parseInt(searchParams.page || '1', 10),
    pageSize: 12,
    pageCount: 1,
    total: 0,
  });

  // Query parameters
  const [queryParams, setQueryParams] = useState<ProductsQueryParams>({
    page: parseInt(searchParams.page || '1', 10),
    pageSize: 12,
    sortBy: searchParams.sort || 'newest',
    filters: {},
    priceRange: null,
    inStockOnly: false,
  });

  // Get filter groups for this category
  const filterGroups = FILTER_GROUPS[categoryId as keyof typeof FILTER_GROUPS] || [];

  // Fetch products
  useEffect(() => {
    const fetchProducts = async () => {
      setLoading(true);

      try {
        const tenantId = getCurrentTenantId();
        const result = await getCategoryProducts(categoryId, queryParams, tenantId);
        console.log({ result });
        if (result.error) {
          setError(result.error);
        } else {
          setProducts(result.products);
          setCategory(result.category);
          if (result.pagination) {
            setPagination(result.pagination);
          }
          setError(null);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load products');
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [categoryId, queryParams]);

  // Handle page change
  const handlePageChange = (page: number) => {
    setQueryParams(prev => ({ ...prev, page }));

    // Update URL with new page parameter
    const url = new URL(window.location.href);
    url.searchParams.set('page', page.toString());
    router.push(url.pathname + url.search);
  };

  // Handle sort change
  const handleSortChange = (sortOption: string) => {
    setQueryParams(prev => ({ ...prev, sortBy: sortOption, page: 1 }));

    // Update URL with new sort parameter
    const url = new URL(window.location.href);
    url.searchParams.set('sort', sortOption);
    url.searchParams.delete('page'); // Reset page when sorting changes
    router.push(url.pathname + url.search);
  };

  // Handle filter change
  const handleFilterChange = (filters: Record<string, string[]>) => {
    setQueryParams(prev => ({ ...prev, filters, page: 1 }));
  };

  // Handle price range change
  const handlePriceRangeChange = (priceRange: { min: number; max: number } | null) => {
    setQueryParams(prev => ({ ...prev, priceRange, page: 1 }));
  };

  // Handle availability change
  const handleAvailabilityChange = (inStockOnly: boolean) => {
    setQueryParams(prev => ({ ...prev, inStockOnly, page: 1 }));
  };

  // Clear all filters
  const clearAllFilters = () => {
    setQueryParams({
      page: 1,
      pageSize: 12,
      sortBy: 'newest',
      filters: {},
      priceRange: null,
      inStockOnly: false,
    });

    // Update URL to remove all filter parameters
    router.push(pathname || `/products/category/${categoryId}`);
  };

  // If category doesn't exist, return 404
  if (!loading && !category && !error) {
    return notFound();
  }

  // Get subcategories for this category
  const categoryData = productCategories.find(cat => cat.id === categoryId);
  const subcategories = categoryData?.subcategories || [];

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    // { label: 'Products', href: '/products' },
    { label: category?.name || categoryId, href: `/products/category/${categoryId}`, active: true },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      <div className="flex flex-col space-y-6">
        {/* Category header */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{category?.name || categoryId}</h1>
          <p className="text-gray-600">
            {category?.description || `Browse our ${categoryId} collection.`}
          </p>

          {/* Subcategories */}
          {subcategories.length > 0 && (
            <div className="mt-4">
              <h2 className="text-lg font-medium text-gray-900 mb-2">Browse by Category</h2>
              <div className="flex flex-wrap gap-2">
                {subcategories.map(subcategory => (
                  <Link
                    key={subcategory.id}
                    href={`/products/category/${categoryId}/${subcategory.id}`}
                    className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-full text-sm font-medium transition-colors duration-200"
                  >
                    {subcategory.name}
                  </Link>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Main content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar with filters */}
          <div className="lg:col-span-1">
            <CategoryFilters
              filterGroups={filterGroups}
              priceRanges={PRICE_RANGES}
              onFilterChange={handleFilterChange}
              onPriceRangeChange={handlePriceRangeChange}
              onAvailabilityChange={handleAvailabilityChange}
            />
          </div>

          {/* Product grid */}
          <div className="lg:col-span-3">
            <ProductGrid
              products={products}
              isLoading={loading}
              error={error ? new Error(error) : null}
              onSortChange={handleSortChange}
              emptyStateMessage="No products found in this category."
              emptyStateAction={
                <button
                  onClick={clearAllFilters}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                >
                  Clear Filters
                </button>
              }
            />

            {/* Pagination */}
            {!loading && !error && products.length > 0 && (
              <div className="mt-8">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.pageCount}
                  onPageChange={handlePageChange}
                  totalItems={pagination.total}
                  itemsPerPage={pagination.pageSize}
                />
              </div>
            )}

            {/* No products with filters applied */}
            {!loading && !error && products.length === 0 && (
              <NoProductsFound
                message="No products match your selected filters."
                showHomeLink={false}
                showClearFiltersButton={true}
                onClearFilters={clearAllFilters}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
