'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Breadcrumbs from '@/components/Breadcrumbs';
import { productCategories } from '@/data/categories';
import { getCurrentTenantId } from '@/lib/tenant';

export default function CategoriesPage() {
  const [tenantId, setTenantId] = useState<string>('default');

  // Get current tenant ID
  useEffect(() => {
    setTenantId(getCurrentTenantId());
  }, []);

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Products', href: '/products' },
    { label: 'Categories', href: '/products/category', active: true },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      <div className="flex flex-col space-y-6">
        {/* Page header */}
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Product Categories</h1>
          <p className="text-gray-600">
            Browse our product categories to find what you're looking for.
          </p>
        </div>

        {/* Categories grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {productCategories.map(category => (
            <Link
              key={category.id}
              href={`/products/category/${category.id}`}
              className="group bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-md"
            >
              {/* Category image */}
              <div className="relative h-48 bg-gray-100 overflow-hidden">
                {category.image && (
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                    onError={e => {
                      e.currentTarget.src = '/images/placeholder.png';
                    }}
                  />
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              </div>

              {/* Category details */}
              <div className="p-6">
                <h2 className="text-xl font-bold text-gray-900 mb-2">{category.name}</h2>
                <p className="text-gray-600 mb-4">
                  {`Explore our ${category.name} collection featuring top brands and latest products.`}
                </p>

                {/* Subcategories */}
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Popular Subcategories:</h3>
                  <div className="flex flex-wrap gap-2">
                    {category.subcategories.slice(0, 3).map(subcategory => (
                      <Link
                        key={subcategory.id}
                        href={`/products/category/${category.id}/${subcategory.id}`}
                        className="px-3 py-1 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-full text-xs font-medium transition-colors duration-200"
                        onClick={e => e.stopPropagation()}
                      >
                        {subcategory.name}
                      </Link>
                    ))}
                    {category.subcategories.length > 3 && (
                      <span className="px-3 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium">
                        +{category.subcategories.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* View category button */}
                <div className="mt-6 flex justify-end">
                  <span className="inline-flex items-center text-blue-600 font-medium group-hover:text-blue-800 transition-colors duration-200">
                    View Category
                    <svg
                      className="ml-2 h-5 w-5"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </span>
                </div>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
