'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import {
  ArrowLeftIcon,
  HeartIcon,
  ShareIcon,
  StarIcon,
  ShoppingCartIcon,
  TruckIcon,
  ShieldCheckIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import { getProductByHandle, type MockProduct } from '@/data/msw-data';
import { useCart } from '@/context/CartContext';

interface ProductVariant {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  stock: number;
  sku: string;
}

interface ProductImage {
  id: string;
  src: string;
  alt: string;
}

interface Product {
  id: string;
  name: string;
  slug: string;
  description: string;
  longDescription: string;
  price: number;
  originalPrice?: number;
  images: ProductImage[];
  variants: ProductVariant[];
  category: string;
  brand: string;
  rating: number;
  reviewCount: number;
  features: string[];
  specifications: { [key: string]: string };
  isWishlisted: boolean;
  badge?: string;
  inStock: boolean;
}

// Removed mockProduct - now using real MSW data
const _unusedMockProduct: Product = {
  id: '1',
  name: 'Premium Wireless Headphones',
  slug: 'premium-wireless-headphones',
  description:
    'High-quality wireless headphones with active noise cancellation and premium sound quality.',
  longDescription:
    'Experience exceptional audio quality with our Premium Wireless Headphones. Featuring advanced active noise cancellation technology, these headphones deliver crystal-clear sound while blocking out unwanted ambient noise. The comfortable over-ear design ensures hours of listening comfort, while the long-lasting battery provides up to 30 hours of playback time.',
  price: 299.99,
  originalPrice: 399.99,
  images: [
    {
      id: '1',
      src: '/images/products/headphones.jpg',
      alt: 'Premium Wireless Headphones - Front View',
    },
    {
      id: '2',
      src: '/images/products/headphones.jpg',
      alt: 'Premium Wireless Headphones - Side View',
    },
    {
      id: '3',
      src: '/images/products/headphones.jpg',
      alt: 'Premium Wireless Headphones - Back View',
    },
  ],
  variants: [
    {
      id: 'black',
      name: 'Midnight Black',
      price: 299.99,
      originalPrice: 399.99,
      stock: 15,
      sku: 'HP-001-BLK',
    },
    {
      id: 'white',
      name: 'Pearl White',
      price: 299.99,
      originalPrice: 399.99,
      stock: 8,
      sku: 'HP-001-WHT',
    },
    {
      id: 'silver',
      name: 'Space Silver',
      price: 319.99,
      originalPrice: 419.99,
      stock: 12,
      sku: 'HP-001-SLV',
    },
  ],
  category: 'Electronics',
  brand: 'AudioTech',
  rating: 4.8,
  reviewCount: 324,
  features: [
    'Active Noise Cancellation',
    '30-hour battery life',
    'Quick charge (15 min = 3 hours)',
    'Premium leather ear cushions',
    'Bluetooth 5.0 connectivity',
    'Built-in microphone',
    'Foldable design',
    'Carrying case included',
  ],
  specifications: {
    'Driver Size': '40mm',
    'Frequency Response': '20Hz - 20kHz',
    Impedance: '32 Ohms',
    Sensitivity: '110dB',
    'Battery Life': '30 hours (ANC on), 40 hours (ANC off)',
    'Charging Time': '2 hours',
    Weight: '250g',
    Connectivity: 'Bluetooth 5.0, 3.5mm jack',
    Warranty: '2 years',
  },
  isWishlisted: false,
  badge: 'Best Seller',
  inStock: true,
};

// Helper function to convert MSW product to Product interface
function convertMSWToProduct(mswProduct: MockProduct): Product {
  return {
    id: mswProduct.id,
    name: mswProduct.title,
    slug: mswProduct.handle,
    description: mswProduct.description,
    longDescription:
      mswProduct.description + ' This product offers exceptional quality and value for your needs.',
    price: mswProduct.price / 100, // Convert from cents to dollars
    originalPrice: mswProduct.price > 2000 ? (mswProduct.price * 1.25) / 100 : undefined,
    images: mswProduct.images.map(img => ({
      id: img.id,
      src: img.src,
      alt: img.alt || mswProduct.title,
    })),
    variants: mswProduct.variants.map(variant => ({
      id: variant.id,
      name: variant.title,
      price: variant.price / 100,
      originalPrice: variant.price > 2000 ? (variant.price * 1.25) / 100 : undefined,
      stock: variant.inventory_quantity || 10,
      sku: `SKU-${variant.id}`,
    })),
    category: mswProduct.category || 'General',
    brand: mswProduct.vendor || 'ONDC Store',
    rating: mswProduct.rating || 4.0,
    reviewCount: mswProduct.reviews || 0,
    features: [
      'High quality materials',
      'Excellent craftsmanship',
      'Fast shipping',
      'Customer satisfaction guaranteed',
    ],
    specifications: {
      Brand: mswProduct.vendor || 'ONDC Store',
      Category: mswProduct.category || 'General',
      'Product Type': mswProduct.product_type || 'Standard',
      SKU: mswProduct.id,
      Availability: mswProduct.variants[0]?.available ? 'In Stock' : 'Out of Stock',
    },
    isWishlisted: false,
    badge: mswProduct.variants.some(v => !v.available) ? 'Limited Stock' : undefined,
    inStock: mswProduct.variants.some(v => v.available),
  };
}

export default function ProductPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { addItem } = useCart();
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [isLoading, setIsLoading] = useState(false); // Start with false for immediate loading
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews'>(
    'description'
  );

  // Hardcoded products for testing - matching category page products
  const getProductBySlug = (productSlug: string): Product | null => {
    const allProducts: Product[] = [
      // Electronics
      {
        id: 'test-electronics-1',
        name: 'Premium Wireless Headphones',
        slug: 'premium-wireless-headphones',
        description:
          'High-quality wireless headphones with active noise cancellation and premium sound quality.',
        longDescription:
          'Experience exceptional audio quality with our Premium Wireless Headphones. Featuring advanced active noise cancellation technology, these headphones deliver crystal-clear sound while blocking out unwanted ambient noise.',
        price: 299.99,
        originalPrice: 399.99,
        images: [
          { id: '1', src: '/images/products/headphones.jpg', alt: 'Premium Wireless Headphones' },
        ],
        variants: [
          {
            id: 'black',
            name: 'Midnight Black',
            price: 299.99,
            originalPrice: 399.99,
            stock: 15,
            sku: 'HP-001-BLK',
          },
          {
            id: 'white',
            name: 'Pearl White',
            price: 299.99,
            originalPrice: 399.99,
            stock: 8,
            sku: 'HP-001-WHT',
          },
        ],
        category: 'Electronics',
        brand: 'AudioTech',
        rating: 4.8,
        reviewCount: 324,
        features: [
          'Active Noise Cancellation',
          '30-hour battery life',
          'Quick charge',
          'Premium leather cushions',
        ],
        specifications: {
          'Driver Size': '40mm',
          'Frequency Response': '20Hz - 20kHz',
          'Battery Life': '30 hours',
          Weight: '250g',
        },
        isWishlisted: false,
        badge: 'Best Seller',
        inStock: true,
      },
      {
        id: 'test-electronics-2',
        name: 'Smart Fitness Watch',
        slug: 'smart-fitness-watch',
        description: 'Advanced fitness tracking watch with heart rate monitoring and GPS.',
        longDescription:
          'Track your fitness goals with precision using our Smart Fitness Watch. Features include heart rate monitoring, GPS tracking, sleep analysis, and 7-day battery life.',
        price: 199.99,
        images: [{ id: '1', src: '/images/products/watch.jpg', alt: 'Smart Fitness Watch' }],
        variants: [
          { id: 'black', name: 'Space Black', price: 199.99, stock: 12, sku: 'SW-001-BLK' },
          { id: 'silver', name: 'Silver', price: 199.99, stock: 8, sku: 'SW-001-SLV' },
        ],
        category: 'Electronics',
        brand: 'FitTech',
        rating: 4.6,
        reviewCount: 156,
        features: ['Heart Rate Monitor', 'GPS Tracking', '7-day battery', 'Water resistant'],
        specifications: {
          Display: '1.4" AMOLED',
          'Battery Life': '7 days',
          'Water Rating': '5ATM',
          Weight: '45g',
        },
        isWishlisted: false,
        inStock: true,
      },
      // Fashion
      {
        id: 'test-fashion-1',
        name: 'Organic Cotton T-Shirt',
        slug: 'organic-cotton-t-shirt',
        description: 'Comfortable organic cotton t-shirt made from sustainable materials.',
        longDescription:
          'Our Organic Cotton T-Shirt is made from 100% certified organic cotton, providing ultimate comfort while being environmentally responsible.',
        price: 29.99,
        images: [{ id: '1', src: '/images/products/tshirt.jpg', alt: 'Organic Cotton T-Shirt' }],
        variants: [
          { id: 's', name: 'Small', price: 29.99, stock: 20, sku: 'TS-001-S' },
          { id: 'm', name: 'Medium', price: 29.99, stock: 25, sku: 'TS-001-M' },
          { id: 'l', name: 'Large', price: 29.99, stock: 18, sku: 'TS-001-L' },
        ],
        category: 'Fashion',
        brand: 'EcoWear',
        rating: 4.5,
        reviewCount: 127,
        features: [
          '100% Organic Cotton',
          'Sustainable Production',
          'Soft & Comfortable',
          'Machine Washable',
        ],
        specifications: {
          Material: '100% Organic Cotton',
          Fit: 'Regular',
          Care: 'Machine wash cold',
          Origin: 'Made in USA',
        },
        isWishlisted: false,
        inStock: true,
      },
      {
        id: 'test-electronics-3',
        name: 'Professional Camera Lens',
        slug: 'professional-camera-lens',
        description: 'High-performance 85mm f/1.4 lens perfect for portrait photography.',
        longDescription:
          'Capture stunning portraits with our Professional Camera Lens. This 85mm f/1.4 lens delivers exceptional bokeh and sharp image quality for professional photographers.',
        price: 599.99,
        originalPrice: 699.99,
        images: [
          { id: '1', src: '/images/products/camera-lens.jpg', alt: 'Professional Camera Lens' },
        ],
        variants: [
          {
            id: 'canon',
            name: 'Canon Mount',
            price: 599.99,
            originalPrice: 699.99,
            stock: 8,
            sku: 'CL-001-CAN',
          },
          {
            id: 'nikon',
            name: 'Nikon Mount',
            price: 599.99,
            originalPrice: 699.99,
            stock: 5,
            sku: 'CL-001-NIK',
          },
        ],
        category: 'Electronics',
        brand: 'PhotoPro',
        rating: 4.9,
        reviewCount: 89,
        features: [
          '85mm f/1.4 aperture',
          'Professional bokeh',
          'Weather sealed',
          'Ultra-sharp optics',
        ],
        specifications: {
          'Focal Length': '85mm',
          'Maximum Aperture': 'f/1.4',
          'Minimum Focus': '0.85m',
          Weight: '560g',
        },
        isWishlisted: false,
        badge: 'Pro',
        inStock: true,
      },
      {
        id: 'test-electronics-4',
        name: 'Gaming Mechanical Keyboard',
        slug: 'gaming-mechanical-keyboard',
        description: 'RGB backlit mechanical keyboard with Cherry MX switches.',
        longDescription:
          'Elevate your gaming experience with our Gaming Mechanical Keyboard. Features Cherry MX switches, customizable RGB lighting, and programmable keys for competitive gaming.',
        price: 149.99,
        images: [
          { id: '1', src: '/images/products/keyboard.jpg', alt: 'Gaming Mechanical Keyboard' },
        ],
        variants: [
          { id: 'red', name: 'Cherry MX Red', price: 149.99, stock: 15, sku: 'KB-001-RED' },
          { id: 'blue', name: 'Cherry MX Blue', price: 149.99, stock: 12, sku: 'KB-001-BLU' },
          { id: 'brown', name: 'Cherry MX Brown', price: 149.99, stock: 10, sku: 'KB-001-BRN' },
        ],
        category: 'Electronics',
        brand: 'GameGear',
        rating: 4.7,
        reviewCount: 203,
        features: ['Cherry MX switches', 'RGB backlighting', 'Programmable keys', 'Anti-ghosting'],
        specifications: {
          'Switch Type': 'Cherry MX Mechanical',
          Backlighting: 'RGB',
          'Key Layout': 'Full-size (104 keys)',
          Connection: 'USB-C',
        },
        isWishlisted: false,
        inStock: true,
      },
      // Home & Garden
      {
        id: 'test-home-1',
        name: 'Premium Coffee Maker',
        slug: 'premium-coffee-maker',
        description: 'Professional-grade coffee maker with programmable features.',
        longDescription:
          'Brew the perfect cup every time with our Premium Coffee Maker. Features programmable brewing, thermal carafe, and built-in grinder.',
        price: 199.99,
        images: [
          { id: '1', src: '/images/products/coffee-maker.jpg', alt: 'Premium Coffee Maker' },
        ],
        variants: [
          { id: 'standard', name: 'Standard', price: 199.99, stock: 10, sku: 'CM-001-STD' },
        ],
        category: 'Home & Garden',
        brand: 'BrewMaster',
        rating: 4.7,
        reviewCount: 156,
        features: ['Programmable Timer', 'Built-in Grinder', 'Thermal Carafe', '12-cup capacity'],
        specifications: {
          Capacity: '12 cups',
          Power: '1200W',
          Dimensions: '14" x 10" x 16"',
          Warranty: '2 years',
        },
        isWishlisted: false,
        inStock: true,
      },
    ];

    return allProducts.find(p => p.slug === productSlug) || null;
  };

  // Load product immediately
  const product = getProductBySlug(slug);

  // Initialize selected variant when product loads
  React.useEffect(() => {
    console.log('ProductPage loaded:', { slug, product: !!product });
    if (product && product.variants.length > 0) {
      setSelectedVariant(product.variants[0]);
      setIsWishlisted(product.isWishlisted);
    }
  }, [slug, product]);

  const handleAddToCart = () => {
    if (!product || !selectedVariant) return;

    try {
      const cartItem = {
        id: `${product.id}-${selectedVariant.id}`,
        productId: product.id,
        name: `${product.name} - ${selectedVariant.name}`,
        price: selectedVariant.price,
        image: product.images[0]?.src || '/images/placeholder-product.jpg',
        variant: {
          id: selectedVariant.id,
          name: selectedVariant.name,
          sku: selectedVariant.sku,
        },
        maxQuantity: selectedVariant.stock,
        sellerId: 'ondc-seller',
        sellerName: product.brand,
      };

      addItem(cartItem, quantity);

      console.log('Successfully added to cart:', {
        product: product.name,
        variant: selectedVariant.name,
        quantity,
        price: selectedVariant.price,
      });

      // Show success message (you could replace this with a toast notification)
      alert(`Added ${quantity} x ${product.name} (${selectedVariant.name}) to cart!`);
    } catch (error) {
      console.error('Error adding to cart:', error);
      alert('Failed to add item to cart. Please try again.');
    }
  };

  const handleWishlistToggle = () => {
    setIsWishlisted(!isWishlisted);
    // Here you would typically call your wishlist API
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="animate-pulse">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-8"></div>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-gray-200 rounded-lg h-96"></div>
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/4"></div>
                <div className="h-20 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
          <p className="text-gray-600 mb-8">The product you're looking for doesn't exist.</p>
          <Link
            href="/products"
            className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Products
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-blue-600">
            Home
          </Link>
          <span>/</span>
          <Link href="/products" className="hover:text-blue-600">
            Products
          </Link>
          <span>/</span>
          <Link
            href={`/categories/${product.category.toLowerCase()}`}
            className="hover:text-blue-600"
          >
            {product.category}
          </Link>
          <span>/</span>
          <span className="text-gray-900">{product.name}</span>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative aspect-square bg-white rounded-lg overflow-hidden">
              <Image
                src={product.images[selectedImageIndex].src}
                alt={product.images[selectedImageIndex].alt}
                fill
                className="object-cover"
                sizes="(max-width: 1024px) 100vw, 50vw"
              />
              {product.badge && (
                <div className="absolute top-4 left-4 bg-blue-600 text-white text-sm px-3 py-1 rounded">
                  {product.badge}
                </div>
              )}
            </div>

            {/* Thumbnail Images */}
            <div className="flex space-x-2">
              {product.images.map((image, index) => (
                <button
                  key={image.id}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`relative w-20 h-20 rounded-lg overflow-hidden border-2 ${
                    selectedImageIndex === index ? 'border-blue-600' : 'border-gray-200'
                  }`}
                >
                  <Image
                    src={image.src}
                    alt={image.alt}
                    fill
                    className="object-cover"
                    sizes="80px"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-blue-600 font-medium">{product.brand}</span>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={handleWishlistToggle}
                    className="p-2 rounded-full hover:bg-gray-100 transition-colors"
                  >
                    {isWishlisted ? (
                      <HeartSolidIcon className="h-6 w-6 text-red-500" />
                    ) : (
                      <HeartIcon className="h-6 w-6 text-gray-400" />
                    )}
                  </button>
                  <button className="p-2 rounded-full hover:bg-gray-100 transition-colors">
                    <ShareIcon className="h-6 w-6 text-gray-400" />
                  </button>
                </div>
              </div>

              <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>

              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`h-5 w-5 ${i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                    />
                  ))}
                  <span className="ml-2 text-sm text-gray-600">
                    {product.rating} ({product.reviewCount} reviews)
                  </span>
                </div>
              </div>

              <p className="text-gray-600 mb-6">{product.description}</p>
            </div>

            {/* Price */}
            <div className="border-t border-b border-gray-200 py-6">
              <div className="flex items-center space-x-4">
                <span className="text-3xl font-bold text-gray-900">
                  ${selectedVariant?.price || product.price}
                </span>
                {(selectedVariant?.originalPrice || product.originalPrice) && (
                  <span className="text-xl text-gray-500 line-through">
                    ${selectedVariant?.originalPrice || product.originalPrice}
                  </span>
                )}
                {(selectedVariant?.originalPrice || product.originalPrice) && (
                  <span className="bg-red-100 text-red-800 text-sm px-2 py-1 rounded">
                    Save $
                    {(
                      (selectedVariant?.originalPrice || product.originalPrice!) -
                      (selectedVariant?.price || product.price)
                    ).toFixed(2)}
                  </span>
                )}
              </div>
            </div>

            {/* Variants */}
            {product.variants.length > 1 && (
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">Color</h3>
                <div className="flex space-x-3">
                  {product.variants.map(variant => (
                    <button
                      key={variant.id}
                      onClick={() => setSelectedVariant(variant)}
                      className={`px-4 py-2 border rounded-md text-sm font-medium transition-colors ${
                        selectedVariant?.id === variant.id
                          ? 'border-blue-600 bg-blue-50 text-blue-600'
                          : 'border-gray-300 text-gray-700 hover:border-gray-400'
                      }`}
                    >
                      {variant.name}
                    </button>
                  ))}
                </div>
                {selectedVariant && (
                  <p className="text-sm text-gray-600 mt-2">
                    {selectedVariant.stock > 0 ? (
                      `${selectedVariant.stock} in stock`
                    ) : (
                      <span className="text-red-600">Out of stock</span>
                    )}
                  </p>
                )}
              </div>
            )}

            {/* Quantity and Add to Cart */}
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-900 mb-2 block">Quantity</label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-10 h-10 border border-gray-300 rounded-md flex items-center justify-center hover:bg-gray-50"
                  >
                    -
                  </button>
                  <span className="w-12 text-center font-medium">{quantity}</span>
                  <button
                    onClick={() => setQuantity(quantity + 1)}
                    className="w-10 h-10 border border-gray-300 rounded-md flex items-center justify-center hover:bg-gray-50"
                  >
                    +
                  </button>
                </div>
              </div>

              <div className="flex space-x-4">
                <button
                  onClick={handleAddToCart}
                  disabled={!selectedVariant?.stock}
                  className="flex-1 bg-blue-600 text-white py-3 px-6 rounded-md font-medium hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors flex items-center justify-center"
                >
                  <ShoppingCartIcon className="h-5 w-5 mr-2" />
                  Add to Cart
                </button>
                <button className="px-6 py-3 border border-gray-300 rounded-md font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                  Buy Now
                </button>
              </div>
            </div>

            {/* Shipping Info */}
            <div className="border-t border-gray-200 pt-6">
              <div className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <TruckIcon className="h-5 w-5 mr-3 text-green-600" />
                  Free shipping on orders over $50
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <ArrowPathIcon className="h-5 w-5 mr-3 text-blue-600" />
                  30-day return policy
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <ShieldCheckIcon className="h-5 w-5 mr-3 text-purple-600" />
                  2-year warranty included
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {[
                { id: 'description', label: 'Description' },
                { id: 'specifications', label: 'Specifications' },
                { id: 'reviews', label: 'Reviews' },
              ].map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-600 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                <p className="text-gray-600 mb-6">{product.longDescription}</p>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Features</h3>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <span className="w-2 h-2 bg-blue-600 rounded-full mr-3"></span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {Object.entries(product.specifications).map(([key, value]) => (
                  <div key={key} className="flex justify-between py-2 border-b border-gray-200">
                    <span className="font-medium text-gray-900">{key}</span>
                    <span className="text-gray-600">{value}</span>
                  </div>
                ))}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="text-center py-12">
                <p className="text-gray-500">Reviews coming soon...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
