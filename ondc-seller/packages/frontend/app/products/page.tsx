'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Breadcrumbs from '@/components/Breadcrumbs';
import Image from '@/components/ui/Image';
import ErrorPage from '@/components/ErrorPage';
import { handleApiError, shouldShowErrorPage, getErrorPageStatusCode, AppError } from '@/lib/error';
import { ApiError } from '@/types/api';

interface Product {
  id: string;
  name: string;
  price: {
    amount: number;
    currency_code: string;
  };
  image: string;
  category: string;
  rating: number;
  seller: {
    name: string;
    verified: boolean;
  };
  description: string;
  variants?: Array<{
    id: string;
    prices: Array<{
      amount: number;
      currency_code: string;
    }>;
    inventory_quantity: number;
  }>;
  inventory_quantity?: number;
}

// Define price ranges
const priceRanges = [
  { label: 'All Prices', value: 'all' },
  { label: 'Under ₹50', value: 'under-50' },
  { label: '₹50 - ₹100', value: '50-100' },
  { label: '₹100 - ₹200', value: '100-200' },
  { label: 'Over ₹200', value: 'over-200' },
];

// Define sort options
const sortOptions = [
  { label: 'Featured', value: 'featured' },
  { label: 'Price: Low to High', value: 'price-asc' },
  { label: 'Price: High to Low', value: 'price-desc' },
  { label: 'Rating', value: 'rating' },
];

// Define product categories
const productCategories = [
  'Electronics',
  'Fashion',
  'Home & Living',
  'Beauty',
  'Sports',
  'Books',
] as const;

type Category = (typeof productCategories)[number];

const ProductsPage = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ApiError | null>(null);
  const [sortBy, setSortBy] = useState('featured');
  const [priceRange, setPriceRange] = useState('all');
  const [selectedCategories, setSelectedCategories] = useState<Category[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        // Set a timeout for the fetch request to handle connection issues more gracefully
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        // Get the current tenant ID (or use default)
        const tenantId = localStorage.getItem('current_tenant_id') || 'default';
        console.log(`Fetching products for tenant: ${tenantId}`);

        // Use MSW-intercepted URL when MSW is enabled, otherwise use local API routes
        const isMSWActive = process.env.NEXT_PUBLIC_MSW_ENABLED === 'true';
        const apiUrl = isMSWActive ? 'http://localhost:9000/store/products' : '/api/store/products';

        console.log(
          `[Products] Using API: ${apiUrl} (MSW: ${isMSWActive ? 'enabled' : 'disabled'})`
        );

        const response = await fetch(apiUrl, {
          signal: controller.signal,
          method: 'GET',
          mode: 'cors',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'x-tenant-id': tenantId,
            Accept: 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Failed to fetch products: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Products fetched successfully:', data);

        // Transform the API response to match the expected format
        const transformedProducts = (data.products || []).map((product: any) => {
          // Get the first variant's price or use a default
          const firstVariant = product.variants && product.variants[0];
          const firstPrice = firstVariant && firstVariant.prices && firstVariant.prices[0];

          return {
            id: product.id,
            name: product.title || product.name,
            description: product.description || '',
            image: product.thumbnail || '/api/images/products/default.jpg',
            category: product.category || 'Uncategorized',
            rating: product.rating || 0,
            seller: {
              name: product.seller?.name || 'Unknown Seller',
              verified: product.seller?.verified || false,
            },
            // Add a price object that matches the expected format
            price: {
              amount: firstPrice ? firstPrice.amount : 0,
              currency_code: firstPrice ? firstPrice.currency_code : 'inr',
            },
            // Use the first variant's inventory if product doesn't have it directly
            inventory_quantity:
              product.inventory_quantity !== undefined
                ? product.inventory_quantity
                : firstVariant
                  ? firstVariant.inventory_quantity
                  : 0,
          };
        });

        console.log('Transformed products:', transformedProducts);
        setProducts(transformedProducts || []);
        setLoading(false);
        setError(null); // Clear any previous errors
      } catch (err) {
        console.error('Error fetching products:', err);

        // Handle the error using our standardized error handler
        let apiError = handleApiError(err);

        // For fetch errors that don't have status codes, check if it's a network error
        if (!apiError.statusCode && err instanceof Error) {
          if (err.name === 'AbortError' || err.message.includes('aborted')) {
            apiError = new AppError(
              'Connection timeout - Backend server may not be running',
              0,
              'ECONNABORTED'
            );
          } else if (
            err.message.includes('fetch') ||
            err.message.includes('Failed to fetch') ||
            err.message.includes('NetworkError') ||
            err.message.includes('ERR_NETWORK') ||
            err.message.includes('ECONNREFUSED') ||
            err.message.includes('connection')
          ) {
            apiError = new AppError('Cannot connect to the backend server', 0, 'NETWORK_ERROR');
          } else {
            // For any other error without status, treat as network error
            apiError = new AppError('Backend service is currently unavailable', 0, 'NETWORK_ERROR');
          }
        }

        setError(apiError);
        setLoading(false);

        // Don't use mock data - let the error page handle it
        setProducts([]);
      }
    };

    fetchProducts();
  }, []);

  const formatPrice = (amount?: number, currency?: string) => {
    if (amount === undefined || currency === undefined) {
      return 'Price not available';
    }
    try {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: (currency || 'INR').toUpperCase(),
      }).format(amount / 100);
    } catch (error) {
      console.error('Error formatting price:', error);
      return `₹${(amount / 100).toFixed(2)}`;
    }
  };

  // Filter products with safety checks for missing inventory data
  const filteredProducts = products.filter(product => {
    if (priceRange === 'all') return true;
    const price = product.price.amount / 100;
    if (priceRange === 'under-50') return price < 50;
    if (priceRange === '50-100') return price >= 50 && price < 100;
    if (priceRange === '100-200') return price >= 100 && price < 200;
    if (priceRange === 'over-200') return price >= 200;
    return true;
  });

  // Sort products with safety checks for missing data
  const sortedProducts = [...filteredProducts].sort((a: Product, b: Product) => {
    if (sortBy === 'featured') {
      return 0;
    }
    if (sortBy === 'price-asc') {
      return a.price.amount - b.price.amount;
    }
    if (sortBy === 'price-desc') {
      return b.price.amount - a.price.amount;
    }
    if (sortBy === 'rating') {
      return (b.rating || 0) - (a.rating || 0);
    }
    return 0;
  });

  if (loading) {
    return (
      <div>
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'Featured Products', href: '/products', active: true },
          ]}
        />
        <div className="text-center py-10">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  // Show error page for critical errors
  if (error && shouldShowErrorPage(error)) {
    const handleRetry = () => {
      setLoading(true);
      setError(null);
      // Trigger a re-fetch by calling the effect again
      window.location.reload();
    };

    return (
      <ErrorPage
        statusCode={getErrorPageStatusCode(error)}
        title="Products Unavailable"
        message={error.message}
        showRetry={true}
        onRetry={handleRetry}
        showHome={true}
      />
    );
  }

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Featured Products', href: '/products', active: true },
  ];

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      <div className="flex flex-col md:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="w-full md:w-64 space-y-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Categories</h3>
            <div className="space-y-2">
              {productCategories.map(category => (
                <label key={category} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedCategories.includes(category)}
                    onChange={() => {
                      if (selectedCategories.includes(category)) {
                        setSelectedCategories(prev => prev.filter(c => c !== category));
                      } else {
                        setSelectedCategories(prev => [...prev, category]);
                      }
                    }}
                    className="h-4 w-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-600">{category}</span>
                </label>
              ))}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Price Range</h3>
            <div className="space-y-2">
              {priceRanges.map(range => (
                <label key={range.value} className="flex items-center">
                  <input
                    type="radio"
                    name="price-range"
                    value={range.value}
                    checked={priceRange === range.value}
                    onChange={e => setPriceRange(e.target.value)}
                    className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                  />
                  <span className="ml-2 text-gray-600">{range.label}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">All Products</h2>
            <select
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
              className="rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {sortedProducts.map(product => (
              <div
                key={product.id}
                className="group relative bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200 h-96 flex flex-col"
              >
                {/* Fixed height image container */}
                <div className="relative h-48 w-full overflow-hidden">
                  <Image
                    src={product.image}
                    alt={product.name}
                    width={400}
                    height={300}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                    fallback="/images/placeholder.jpg"
                  />
                  {/* Category badge */}
                  <div className="absolute top-2 left-2">
                    <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full font-medium">
                      {product.category}
                    </span>
                  </div>
                </div>

                {/* Content container with flex-grow */}
                <div className="p-4 flex-1 flex flex-col">
                  {/* Product title - fixed 2 lines */}
                  <div className="mb-2">
                    <h3
                      className="text-lg font-semibold text-gray-900 line-clamp-2 h-14 leading-7"
                      title={product.name}
                    >
                      {product.name}
                    </h3>
                  </div>

                  {/* Description - fixed 2 lines */}
                  <div className="mb-3">
                    <p
                      className="text-sm text-gray-600 line-clamp-2 h-10 leading-5"
                      title={product.description}
                    >
                      {product.description ||
                        'High-quality product with excellent features and reliable performance.'}
                    </p>
                  </div>

                  {/* Seller info */}
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center">
                      <span className="text-xs text-gray-500">by</span>
                      <span className="ml-1 text-xs font-medium text-gray-900 truncate max-w-20">
                        {product.seller.name !== 'Unknown Seller'
                          ? product.seller.name
                          : 'ONDC Seller'}
                      </span>
                      {(product.seller.verified || product.seller.name === 'Unknown Seller') && (
                        <svg
                          className="ml-1 w-3 h-3 text-blue-500 flex-shrink-0"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clipRule="evenodd"
                          />
                        </svg>
                      )}
                    </div>
                    <div className="flex items-center">
                      <svg
                        className="w-3 h-3 text-yellow-400"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                      <span className="ml-1 text-xs text-gray-600">{product.rating || 4.5}</span>
                    </div>
                  </div>

                  {/* Stock status */}
                  <div className="mb-3">
                    <span
                      className={`text-xs px-2 py-1 rounded-full ${
                        (product.inventory_quantity || 0) > 0
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}
                    >
                      {(product.inventory_quantity || 0) > 0
                        ? `In Stock (${product.inventory_quantity || 10})`
                        : 'Out of Stock'}
                    </span>
                  </div>

                  {/* Price and button - pushed to bottom */}
                  <div className="mt-auto">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-lg font-bold text-blue-600">
                        {formatPrice(product.price.amount, product.price.currency_code)}
                      </span>
                    </div>

                    <div className="space-y-2">
                      <button
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors duration-200 text-sm font-medium"
                        disabled={(product.inventory_quantity || 0) === 0}
                      >
                        {(product.inventory_quantity || 0) > 0 ? 'Add to Cart' : 'Out of Stock'}
                      </button>

                      <Link
                        href={`/products/${product.id}`}
                        className="block w-full text-center bg-gray-100 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-200 transition-colors duration-200 text-sm font-medium"
                      >
                        View Details
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;
