'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { ClockIcon, FireIcon, TagIcon, FunnelIcon } from '@heroicons/react/24/outline';

interface Deal {
  id: number;
  name: string;
  slug: string;
  originalPrice: number;
  salePrice: number;
  discount: number;
  image: string;
  category: string;
  endTime: string;
  soldCount: number;
  totalStock: number;
  isLimitedTime: boolean;
  badge?: string;
  rating: number;
  reviewCount: number;
}

const mockDeals: Deal[] = [
  {
    id: 1,
    name: 'Premium Wireless Headphones',
    slug: 'premium-wireless-headphones',
    originalPrice: 399.99,
    salePrice: 299.99,
    discount: 25,
    image: 'https://picsum.photos/400/400?random=1',
    category: 'Electronics',
    endTime: '2024-12-31T23:59:59',
    soldCount: 145,
    totalStock: 200,
    isLimitedTime: true,
    badge: 'Hot Deal',
    rating: 4.8,
    reviewCount: 324,
  },
  {
    id: 2,
    name: 'Smart Fitness Watch',
    slug: 'smart-fitness-watch',
    originalPrice: 249.99,
    salePrice: 149.99,
    discount: 40,
    image: 'https://picsum.photos/400/400?random=2',
    category: 'Electronics',
    endTime: '2024-12-25T23:59:59',
    soldCount: 278,
    totalStock: 350,
    isLimitedTime: true,
    badge: 'Flash Sale',
    rating: 4.7,
    reviewCount: 567,
  },
  {
    id: 3,
    name: 'Premium Coffee Maker',
    slug: 'premium-coffee-maker',
    originalPrice: 199.99,
    salePrice: 119.99,
    discount: 40,
    image: 'https://picsum.photos/400/400?random=3',
    category: 'Kitchen',
    endTime: '2024-12-30T23:59:59',
    soldCount: 89,
    totalStock: 150,
    isLimitedTime: false,
    badge: 'Best Value',
    rating: 4.5,
    reviewCount: 234,
  },
  {
    id: 4,
    name: 'Organic Cotton T-Shirt',
    slug: 'organic-cotton-tshirt',
    originalPrice: 39.99,
    salePrice: 24.99,
    discount: 38,
    image: 'https://picsum.photos/400/400?random=4',
    category: 'Fashion',
    endTime: '2024-12-28T23:59:59',
    soldCount: 167,
    totalStock: 300,
    isLimitedTime: true,
    badge: 'Eco Deal',
    rating: 4.3,
    reviewCount: 445,
  },
  {
    id: 5,
    name: 'Professional Camera Lens',
    slug: 'professional-camera-lens',
    originalPrice: 1199.99,
    salePrice: 899.99,
    discount: 25,
    image: 'https://picsum.photos/400/400?random=5',
    category: 'Photography',
    endTime: '2024-12-26T23:59:59',
    soldCount: 34,
    totalStock: 80,
    isLimitedTime: true,
    badge: 'Pro Deal',
    rating: 4.9,
    reviewCount: 78,
  },
  {
    id: 6,
    name: 'Ergonomic Office Chair',
    slug: 'ergonomic-office-chair',
    originalPrice: 599.99,
    salePrice: 399.99,
    discount: 33,
    image: 'https://picsum.photos/400/400?random=6',
    category: 'Furniture',
    endTime: '2024-12-29T23:59:59',
    soldCount: 156,
    totalStock: 250,
    isLimitedTime: false,
    badge: 'Comfort Deal',
    rating: 4.6,
    reviewCount: 189,
  },
];

export default function DealsPage() {
  const [deals, setDeals] = useState<Deal[]>([]);
  const [filteredDeals, setFilteredDeals] = useState<Deal[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('discount');
  const [timeLeft, setTimeLeft] = useState<{ [key: number]: string }>({});
  const [isLoading, setIsLoading] = useState(true);

  // Get unique categories
  const categories = ['all', ...Array.from(new Set(mockDeals.map(deal => deal.category)))];

  // Calculate time left for deals
  const calculateTimeLeft = (endTime: string): string => {
    const now = new Date().getTime();
    const end = new Date(endTime).getTime();
    const difference = end - now;

    if (difference <= 0) return 'Expired';

    const days = Math.floor(difference / (1000 * 60 * 60 * 24));
    const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));

    if (days > 0) return `${days}d ${hours}h`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  // Calculate progress percentage
  const getProgressPercentage = (sold: number, total: number): number => {
    return Math.min((sold / total) * 100, 100);
  };

  useEffect(() => {
    // Simulate API call
    const loadDeals = async () => {
      setIsLoading(true);

      setTimeout(() => {
        setDeals(mockDeals);
        setFilteredDeals(mockDeals);
        setIsLoading(false);
      }, 500);
    };

    loadDeals();
  }, []);

  useEffect(() => {
    // Update time left every minute
    const timer = setInterval(() => {
      const newTimeLeft: { [key: number]: string } = {};
      deals.forEach(deal => {
        if (deal.isLimitedTime) {
          newTimeLeft[deal.id] = calculateTimeLeft(deal.endTime);
        }
      });
      setTimeLeft(newTimeLeft);
    }, 60000);

    // Initial calculation
    const initialTimeLeft: { [key: number]: string } = {};
    deals.forEach(deal => {
      if (deal.isLimitedTime) {
        initialTimeLeft[deal.id] = calculateTimeLeft(deal.endTime);
      }
    });
    setTimeLeft(initialTimeLeft);

    return () => clearInterval(timer);
  }, [deals]);

  useEffect(() => {
    // Filter and sort deals
    let filtered = [...deals];

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(deal => deal.category === selectedCategory);
    }

    // Sort deals
    switch (sortBy) {
      case 'discount':
        filtered.sort((a, b) => b.discount - a.discount);
        break;
      case 'price-low':
        filtered.sort((a, b) => a.salePrice - b.salePrice);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.salePrice - a.salePrice);
        break;
      case 'ending-soon':
        filtered.sort((a, b) => {
          if (!a.isLimitedTime && !b.isLimitedTime) return 0;
          if (!a.isLimitedTime) return 1;
          if (!b.isLimitedTime) return -1;
          return new Date(a.endTime).getTime() - new Date(b.endTime).getTime();
        });
        break;
      case 'popular':
        filtered.sort((a, b) => b.soldCount - a.soldCount);
        break;
      default:
        break;
    }

    setFilteredDeals(filtered);
  }, [deals, selectedCategory, sortBy]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <div className="flex items-center justify-center mb-4">
              <FireIcon className="h-12 w-12 mr-3" />
              <h1 className="text-4xl md:text-5xl font-bold">Hot Deals</h1>
            </div>
            <p className="text-xl md:text-2xl mb-8">Incredible savings on your favorite products</p>
            <div className="flex items-center justify-center space-x-8 text-lg">
              <div className="flex items-center">
                <TagIcon className="h-6 w-6 mr-2" />
                <span>Up to 70% Off</span>
              </div>
              <div className="flex items-center">
                <ClockIcon className="h-6 w-6 mr-2" />
                <span>Limited Time</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Filters and Controls */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-8 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <FunnelIcon className="h-5 w-5 mr-2 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filter by:</span>
            </div>

            <select
              value={selectedCategory}
              onChange={e => setSelectedCategory(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              {categories.map(category => (
                <option key={category} value={category}>
                  {category === 'all' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700">Sort by:</span>
            <select
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm"
            >
              <option value="discount">Highest Discount</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="ending-soon">Ending Soon</option>
              <option value="popular">Most Popular</option>
            </select>
          </div>
        </div>

        {/* Deals Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, index) => (
              <div
                key={index}
                className="bg-white rounded-lg border-2 border-gray-100 p-4 animate-pulse"
              >
                <div className="bg-gray-200 rounded-lg aspect-square mb-4"></div>
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-4 bg-gray-200 rounded w-2/3 mb-4"></div>
                <div className="h-2 bg-gray-200 rounded mb-2"></div>
                <div className="h-10 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredDeals.map(deal => (
              <div
                key={deal.id}
                className="bg-white rounded-lg border-2 border-blue-100 hover:border-blue-200 hover:shadow-xl transition-all duration-300 overflow-hidden group"
              >
                {/* Deal Image */}
                <div className="relative aspect-square overflow-hidden bg-gray-100">
                  <Image
                    src={deal.image}
                    alt={deal.name}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                    sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    onError={e => {
                      const target = e.target as HTMLImageElement;
                      target.src = `https://via.placeholder.com/400x400/3B82F6/FFFFFF?text=${encodeURIComponent(deal.name.split(' ')[0])}`;
                    }}
                  />

                  {/* Discount Badge */}
                  <div className="absolute top-3 left-3 bg-green-500 text-white text-sm font-bold px-3 py-1 rounded-full shadow-md">
                    -{deal.discount}%
                  </div>

                  {/* Deal Badge */}
                  {deal.badge && (
                    <div className="absolute top-3 right-3 bg-blue-500 text-white text-xs font-semibold px-2 py-1 rounded shadow-md">
                      {deal.badge}
                    </div>
                  )}
                </div>

                {/* Deal Info */}
                <div className="p-4">
                  <div className="text-sm text-gray-500 mb-1">{deal.category}</div>
                  <h3 className="font-semibold text-gray-900 mb-3 line-clamp-2">{deal.name}</h3>

                  {/* Rating */}
                  <div className="flex items-center mb-3">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <svg
                          key={i}
                          className={`h-4 w-4 ${i < Math.floor(deal.rating) ? 'text-yellow-400' : 'text-gray-300'}`}
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                      ))}
                    </div>
                    <span className="text-sm text-gray-500 ml-2">({deal.reviewCount})</span>
                  </div>

                  {/* Prices */}
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="text-xl font-bold text-blue-600">
                      ₹{(deal.salePrice * 83).toFixed(0)}
                    </span>
                    <span className="text-sm text-gray-500 line-through">
                      ₹{(deal.originalPrice * 83).toFixed(0)}
                    </span>
                    <span className="text-sm text-green-600 font-medium">
                      Save ₹{((deal.originalPrice - deal.salePrice) * 83).toFixed(0)}
                    </span>
                  </div>

                  {/* Progress Bar */}
                  <div className="mb-3">
                    <div className="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Sold: {deal.soldCount}</span>
                      <span>Available: {deal.totalStock - deal.soldCount}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${getProgressPercentage(deal.soldCount, deal.totalStock)}%`,
                        }}
                      ></div>
                    </div>
                  </div>

                  {/* Countdown Timer */}
                  {deal.isLimitedTime && timeLeft[deal.id] && timeLeft[deal.id] !== 'Expired' && (
                    <div className="flex items-center text-sm text-orange-600 mb-4 bg-orange-50 px-2 py-1 rounded">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      <span className="font-medium">Ends in: {timeLeft[deal.id]}</span>
                    </div>
                  )}

                  {/* Shop Now Button */}
                  <Link
                    href={`/products/${deal.slug}`}
                    className="w-full bg-gradient-to-r from-blue-600 to-blue-700 text-white text-center py-3 rounded-md hover:from-blue-700 hover:to-blue-800 transition-all duration-200 block font-semibold shadow-md hover:shadow-lg transform hover:-translate-y-0.5"
                  >
                    Shop Now
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* No deals found */}
        {!isLoading && filteredDeals.length === 0 && (
          <div className="text-center py-12">
            <TagIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No deals found</h3>
            <p className="text-gray-500 mb-6">
              Try adjusting your filters or check back later for new deals.
            </p>
            <button
              onClick={() => {
                setSelectedCategory('all');
                setSortBy('discount');
              }}
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 transition-colors"
            >
              Reset Filters
            </button>
          </div>
        )}

        {/* Load More Button */}
        {!isLoading && filteredDeals.length > 0 && (
          <div className="text-center mt-12">
            <button className="bg-gray-200 text-gray-700 px-8 py-3 rounded-md hover:bg-gray-300 transition-colors">
              Load More Deals
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
