'use client';

import React, { useState, useEffect } from 'react';
import Image from '@/components/ui/Image';
import Link from 'next/link';
import { useParams } from 'next/navigation';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  sale_price?: number;
  image: string;
  category: string;
  subcategory?: string;
}

interface Category {
  id: string;
  name: string;
  description: string;
  slug: string;
}

interface Subcategory {
  id: string;
  name: string;
  description: string;
  slug: string;
  parent: Category;
}

const CategoryDetailPage = () => {
  const params = useParams();
  const categoryId = params.categoryId as string;
  
  const [category, setCategory] = useState<Category | null>(null);
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch category details and subcategories
  useEffect(() => {
    const fetchCategoryData = async () => {
      try {
        console.log('🚀 CategoryDetail: Fetching category data for:', categoryId);
        setIsLoading(true);
        setError(null);

        // Fetch category details
        const categoryResponse = await fetch(`/api/categories/${categoryId}`);
        if (!categoryResponse.ok) {
          throw new Error(`Failed to fetch category: ${categoryResponse.status}`);
        }
        const categoryResult = await categoryResponse.json();
        
        if (categoryResult.success && categoryResult.data) {
          setCategory(categoryResult.data);
          console.log('✅ CategoryDetail: Category loaded:', categoryResult.data);
          
          // Fetch subcategories for this parent category
          const subcategoriesResponse = await fetch(`/api/categories/${categoryResult.data.id}/subcategories`);
          if (subcategoriesResponse.ok) {
            const subcategoriesResult = await subcategoriesResponse.json();
            if (subcategoriesResult.success && subcategoriesResult.data) {
              setSubcategories(subcategoriesResult.data);
              console.log('✅ CategoryDetail: Subcategories loaded:', subcategoriesResult.data);
            }
          }
          
          // Fetch products for this category (all subcategories)
          const productsResponse = await fetch(`/api/products?category=${categoryId}&pageSize=50`);
          if (productsResponse.ok) {
            const productsResult = await productsResponse.json();
            if (productsResult.success && productsResult.data) {
              setProducts(productsResult.data);
              console.log('✅ CategoryDetail: Products loaded:', productsResult.data);
            }
          }
        } else {
          throw new Error('Category not found');
        }
      } catch (error) {
        console.error('❌ CategoryDetail: Error fetching category data:', error);
        setError(error instanceof Error ? error.message : 'Failed to load category');
      } finally {
        setIsLoading(false);
      }
    };

    if (categoryId) {
      fetchCategoryData();
    }
  }, [categoryId]);

  // Filter products by selected subcategory
  const filteredProducts = selectedSubcategory
    ? products.filter(product => product.subcategory === selectedSubcategory)
    : products;

  if (isLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-8"></div>
          <div className="flex gap-4 mb-8">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-10 bg-gray-200 rounded-full w-24"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[1, 2, 3, 4, 5, 6, 7, 8].map(i => (
              <div key={i} className="bg-gray-200 rounded-lg h-80"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error || !category) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Category Not Found</h1>
          <p className="text-gray-600 mb-8">{error || 'The requested category could not be found.'}</p>
          <Link
            href="/categories"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            Back to Categories
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb Navigation */}
      <nav className="flex mb-6" aria-label="Breadcrumb">
        <ol className="inline-flex items-center space-x-1 md:space-x-3">
          <li className="inline-flex items-center">
            <Link href="/" className="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
              Home
            </Link>
          </li>
          <li>
            <div className="flex items-center">
              <svg className="w-3 h-3 text-gray-400 mx-1" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4" />
              </svg>
              <Link href="/categories" className="ml-1 text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2">
                Categories
              </Link>
            </div>
          </li>
          <li aria-current="page">
            <div className="flex items-center">
              <svg className="w-3 h-3 text-gray-400 mx-1" fill="none" viewBox="0 0 6 10">
                <path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="m1 9 4-4-4-4" />
              </svg>
              <span className="ml-1 text-sm font-medium text-gray-500 md:ml-2">{category.name}</span>
            </div>
          </li>
        </ol>
      </nav>

      {/* Category Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">{category.name}</h1>
        <p className="text-lg text-gray-600">{category.description}</p>
      </div>

      {/* Subcategory Filter Badges */}
      {subcategories.length > 0 && (
        <div className="mb-8">
          <h3 className="text-sm font-medium text-gray-900 mb-4">Filter by Subcategory:</h3>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedSubcategory(null)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedSubcategory === null
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All ({products.length})
            </button>
            {subcategories.map(subcategory => {
              const count = products.filter(p => p.subcategory === subcategory.slug).length;
              return (
                <button
                  key={subcategory.id}
                  onClick={() => setSelectedSubcategory(subcategory.slug)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedSubcategory === subcategory.slug
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {subcategory.name} ({count})
                </button>
              );
            })}
          </div>
        </div>
      )}

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredProducts.length > 0 ? (
          filteredProducts.map(product => (
            <div key={product.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="aspect-square relative">
                <Image
                  src={product.image}
                  alt={product.name}
                  fill
                  className="object-cover"
                  fallback="/images/placeholder.svg"
                />
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
                <p className="text-gray-600 text-sm mb-3 line-clamp-2">{product.description}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {product.sale_price ? (
                      <>
                        <span className="text-lg font-bold text-red-600">₹{product.sale_price}</span>
                        <span className="text-sm text-gray-500 line-through">₹{product.price}</span>
                      </>
                    ) : (
                      <span className="text-lg font-bold text-gray-900">₹{product.price}</span>
                    )}
                  </div>
                  <button className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-colors">
                    Add to Cart
                  </button>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="col-span-full text-center py-12">
            <p className="text-gray-500 text-lg">No products found in this category.</p>
            {selectedSubcategory && (
              <button
                onClick={() => setSelectedSubcategory(null)}
                className="mt-4 text-blue-600 hover:text-blue-700 font-medium"
              >
                Show all products
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryDetailPage;
