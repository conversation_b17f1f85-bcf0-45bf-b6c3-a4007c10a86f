'use client';

import { useState } from 'react';
import McpProductList from '@/components/McpProductList';
import Breadcrumbs from '@/components/Breadcrumbs';

// Sample categories for the filter
const SAMPLE_CATEGORIES = [
  { id: 'all', name: 'All Categories' },
  { id: 'electronics', name: 'Electronics' },
  { id: 'fashion', name: 'Fashion & Apparel' },
  { id: 'home', name: 'Home & Kitchen' },
  { id: 'beauty', name: 'Beauty & Personal Care' },
  { id: 'sports', name: 'Sports & Outdoors' },
];

export default function McpProductsPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Breadcrumbs */}
      <Breadcrumbs
        items={[
          { label: 'Home', href: '/' },
          { label: 'Products', href: '/products' },
          { label: 'MCP Products', href: '/mcp-products', active: true },
        ]}
      />

      <div className="flex flex-col md:flex-row md:items-center md:justify-between mt-6 mb-8">
        <h1 className="text-3xl font-bold text-gray-900">MCP Products</h1>

        <div className="mt-4 md:mt-0 flex items-center space-x-2">
          <span className="text-sm font-medium text-gray-700">Sort by:</span>
          <select className="block w-40 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
            <option value="newest">Newest</option>
            <option value="price-asc">Price: Low to High</option>
            <option value="price-desc">Price: High to Low</option>
            <option value="popular">Most Popular</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar with filters */}
        <div className="lg:col-span-1">
          <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Categories</h2>
            <div className="space-y-2">
              {SAMPLE_CATEGORIES.map(category => (
                <div key={category.id} className="flex items-center">
                  <input
                    id={`category-${category.id}`}
                    name="category"
                    type="radio"
                    checked={selectedCategory === category.id}
                    onChange={() => setSelectedCategory(category.id)}
                    className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor={`category-${category.id}`}
                    className="ml-3 text-sm text-gray-700 cursor-pointer"
                  >
                    {category.name}
                  </label>
                </div>
              ))}
            </div>

            <div className="border-t border-gray-200 pt-4 mt-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Price Range</h2>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="min-price" className="block text-sm font-medium text-gray-700">
                    Min
                  </label>
                  <input
                    type="number"
                    id="min-price"
                    placeholder="0"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="max-price" className="block text-sm font-medium text-gray-700">
                    Max
                  </label>
                  <input
                    type="number"
                    id="max-price"
                    placeholder="10000"
                    className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                  />
                </div>
              </div>
              <button
                type="button"
                className="mt-4 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Apply Filters
              </button>
            </div>

            <div className="border-t border-gray-200 pt-4 mt-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Availability</h2>
              <div className="space-y-2">
                <div className="flex items-center">
                  <input
                    id="in-stock"
                    name="availability"
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="in-stock" className="ml-3 text-sm text-gray-700">
                    In Stock Only
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="lg:col-span-3">
          <McpProductList category={selectedCategory} />
        </div>
      </div>
    </div>
  );
}
