'use client';

import React, { useState } from 'react';

const TrackOrderPage = () => {
  const [orderNumber, setOrderNumber] = useState('');
  const [email, setEmail] = useState('');
  const [trackingResult, setTrackingResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleTrackOrder = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      // Mock tracking data
      setTrackingResult({
        orderNumber: orderNumber,
        status: 'In Transit',
        estimatedDelivery: '2024-01-25',
        trackingSteps: [
          {
            status: 'Order Placed',
            date: '2024-01-20',
            time: '10:30 AM',
            description: 'Your order has been successfully placed',
            completed: true
          },
          {
            status: 'Order Confirmed',
            date: '2024-01-20',
            time: '11:15 AM',
            description: 'Order confirmed by seller',
            completed: true
          },
          {
            status: 'Packed',
            date: '2024-01-21',
            time: '02:45 PM',
            description: 'Your order has been packed and ready for shipment',
            completed: true
          },
          {
            status: 'Shipped',
            date: '2024-01-22',
            time: '09:20 AM',
            description: 'Your order has been shipped',
            completed: true
          },
          {
            status: 'In Transit',
            date: '2024-01-23',
            time: '08:00 AM',
            description: 'Package is on the way to your location',
            completed: true
          },
          {
            status: 'Out for Delivery',
            date: '2024-01-25',
            time: 'Expected',
            description: 'Package will be out for delivery',
            completed: false
          },
          {
            status: 'Delivered',
            date: '2024-01-25',
            time: 'Expected',
            description: 'Package will be delivered',
            completed: false
          }
        ],
        items: [
          {
            name: 'Wireless Bluetooth Headphones',
            quantity: 1,
            price: '₹2,999',
            image: '/api/placeholder/80/80'
          },
          {
            name: 'Phone Case - Clear',
            quantity: 2,
            price: '₹599',
            image: '/api/placeholder/80/80'
          }
        ],
        shippingAddress: {
          name: 'John Doe',
          address: '123 Main Street, Apartment 4B',
          city: 'Mumbai',
          state: 'Maharashtra',
          pincode: '400001'
        }
      });
      setIsLoading(false);
    }, 1500);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white">
        <div className="w-full px-4 py-16">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              Track Your Order
            </h1>
            <p className="text-xl text-blue-100 max-w-2xl mx-auto">
              Enter your order details to get real-time tracking information
            </p>
          </div>
        </div>
      </div>

      {/* Tracking Form */}
      <div className="w-full px-4 py-16">
        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-sm p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Enter Order Details</h2>
            <form onSubmit={handleTrackOrder} className="space-y-6">
              <div>
                <label htmlFor="orderNumber" className="block text-sm font-medium text-gray-700 mb-2">
                  Order Number *
                </label>
                <input
                  type="text"
                  id="orderNumber"
                  value={orderNumber}
                  onChange={(e) => setOrderNumber(e.target.value)}
                  placeholder="Enter your order number (e.g., ORD123456789)"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email address"
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  required
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white font-medium py-3 px-6 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                    Tracking...
                  </div>
                ) : (
                  'Track Order'
                )}
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Tracking Results */}
      {trackingResult && (
        <div className="w-full px-4 pb-16">
          <div className="max-w-4xl mx-auto">
            {/* Order Summary */}
            <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
              <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Order #{trackingResult.orderNumber}
                  </h2>
                  <p className="text-gray-600">
                    Status: <span className="font-semibold text-blue-600">{trackingResult.status}</span>
                  </p>
                </div>
                <div className="mt-4 md:mt-0 text-right">
                  <p className="text-sm text-gray-600">Estimated Delivery</p>
                  <p className="text-lg font-semibold text-gray-900">{trackingResult.estimatedDelivery}</p>
                </div>
              </div>

              {/* Progress Bar */}
              <div className="mb-8">
                <div className="flex justify-between items-center mb-4">
                  <span className="text-sm text-gray-600">Order Progress</span>
                  <span className="text-sm text-gray-600">
                    {trackingResult.trackingSteps.filter((step: any) => step.completed).length} of {trackingResult.trackingSteps.length} steps completed
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                    style={{ 
                      width: `${(trackingResult.trackingSteps.filter((step: any) => step.completed).length / trackingResult.trackingSteps.length) * 100}%` 
                    }}
                  ></div>
                </div>
              </div>

              {/* Tracking Steps */}
              <div className="space-y-4">
                {trackingResult.trackingSteps.map((step: any, index: number) => (
                  <div key={index} className="flex items-start">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 flex-shrink-0 ${
                      step.completed 
                        ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white' 
                        : 'bg-gray-200 text-gray-400'
                    }`}>
                      {step.completed ? (
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      ) : (
                        <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                      )}
                    </div>
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center">
                        <h3 className={`font-semibold ${step.completed ? 'text-gray-900' : 'text-gray-500'}`}>
                          {step.status}
                        </h3>
                        <span className={`text-sm ${step.completed ? 'text-gray-600' : 'text-gray-400'}`}>
                          {step.date} {step.time !== 'Expected' && `at ${step.time}`}
                        </span>
                      </div>
                      <p className={`text-sm mt-1 ${step.completed ? 'text-gray-600' : 'text-gray-400'}`}>
                        {step.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Items */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Items</h3>
                <div className="space-y-4">
                  {trackingResult.items.map((item: any, index: number) => (
                    <div key={index} className="flex items-center">
                      <img
                        src={item.image}
                        alt={item.name}
                        className="w-16 h-16 object-cover rounded-lg mr-4"
                      />
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.name}</h4>
                        <p className="text-sm text-gray-600">Quantity: {item.quantity}</p>
                        <p className="text-sm font-semibold text-gray-900">{item.price}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Shipping Address */}
              <div className="bg-white rounded-lg shadow-sm p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                <div className="text-gray-600">
                  <p className="font-medium text-gray-900">{trackingResult.shippingAddress.name}</p>
                  <p>{trackingResult.shippingAddress.address}</p>
                  <p>{trackingResult.shippingAddress.city}, {trackingResult.shippingAddress.state}</p>
                  <p>{trackingResult.shippingAddress.pincode}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrackOrderPage;
