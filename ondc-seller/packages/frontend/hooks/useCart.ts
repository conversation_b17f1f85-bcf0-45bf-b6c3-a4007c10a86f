'use client';

import { useState, useEffect, useCallback } from 'react';

export interface CartItem {
  id: string;
  variant_id: string;
  product_id: string;
  title: string;
  variant_title?: string;
  price: number;
  quantity: number;
  total: number;
  image?: string;
  handle?: string;
}

export interface Cart {
  id: string;
  items: CartItem[];
  subtotal: number;
  total: number;
  item_count: number;
  created_at: string;
  updated_at: string;
}

interface UseCartReturn {
  cart: Cart | null;
  loading: boolean;
  error: string | null;
  addToCart: (productId: string, variantId: string, quantity?: number) => Promise<void>;
  updateCartItem: (lineItemId: string, quantity: number) => Promise<void>;
  removeFromCart: (lineItemId: string) => Promise<void>;
  clearCart: () => Promise<void>;
  refreshCart: () => Promise<void>;
}

const CART_STORAGE_KEY = 'ondc_seller_cart';

export function useCart(): UseCartReturn {
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize cart from localStorage
  useEffect(() => {
    const savedCart = localStorage.getItem(CART_STORAGE_KEY);
    if (savedCart) {
      try {
        setCart(JSON.parse(savedCart));
      } catch (err) {
        console.error('Failed to parse saved cart:', err);
        localStorage.removeItem(CART_STORAGE_KEY);
      }
    }
  }, []);

  // Save cart to localStorage whenever it changes
  useEffect(() => {
    if (cart) {
      localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(cart));
    }
  }, [cart]);

  const createEmptyCart = (): Cart => ({
    id: `cart_${Date.now()}`,
    items: [],
    subtotal: 0,
    total: 0,
    item_count: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  });

  const calculateCartTotals = (items: CartItem[]): { subtotal: number; total: number; item_count: number } => {
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const item_count = items.reduce((sum, item) => sum + item.quantity, 0);
    
    // For now, total equals subtotal (no taxes, shipping, etc.)
    const total = subtotal;
    
    return { subtotal, total, item_count };
  };

  const addToCart = useCallback(async (productId: string, variantId: string, quantity: number = 1) => {
    setLoading(true);
    setError(null);

    try {
      // Mock product data - in a real app, this would come from an API
      const mockProduct = {
        id: productId,
        title: `Product ${productId}`,
        price: 999,
        image: 'https://via.placeholder.com/150x150/3B82F6/FFFFFF?text=Product',
        handle: `product-${productId}`,
      };

      const currentCart = cart || createEmptyCart();
      
      // Check if item already exists
      const existingItemIndex = currentCart.items.findIndex(
        item => item.variant_id === variantId
      );

      let updatedItems: CartItem[];

      if (existingItemIndex >= 0) {
        // Update existing item
        updatedItems = [...currentCart.items];
        updatedItems[existingItemIndex] = {
          ...updatedItems[existingItemIndex],
          quantity: updatedItems[existingItemIndex].quantity + quantity,
          total: (updatedItems[existingItemIndex].quantity + quantity) * updatedItems[existingItemIndex].price,
        };
      } else {
        // Add new item
        const newItem: CartItem = {
          id: `item_${Date.now()}`,
          variant_id: variantId,
          product_id: productId,
          title: mockProduct.title,
          price: mockProduct.price,
          quantity,
          total: mockProduct.price * quantity,
          image: mockProduct.image,
          handle: mockProduct.handle,
        };
        updatedItems = [...currentCart.items, newItem];
      }

      const totals = calculateCartTotals(updatedItems);
      
      const updatedCart: Cart = {
        ...currentCart,
        items: updatedItems,
        ...totals,
        updated_at: new Date().toISOString(),
      };

      setCart(updatedCart);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add item to cart');
    } finally {
      setLoading(false);
    }
  }, [cart]);

  const updateCartItem = useCallback(async (lineItemId: string, quantity: number) => {
    if (!cart) return;

    setLoading(true);
    setError(null);

    try {
      const updatedItems = cart.items.map(item => {
        if (item.id === lineItemId) {
          return {
            ...item,
            quantity,
            total: item.price * quantity,
          };
        }
        return item;
      }).filter(item => item.quantity > 0); // Remove items with 0 quantity

      const totals = calculateCartTotals(updatedItems);
      
      const updatedCart: Cart = {
        ...cart,
        items: updatedItems,
        ...totals,
        updated_at: new Date().toISOString(),
      };

      setCart(updatedCart);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update cart item');
    } finally {
      setLoading(false);
    }
  }, [cart]);

  const removeFromCart = useCallback(async (lineItemId: string) => {
    if (!cart) return;

    setLoading(true);
    setError(null);

    try {
      const updatedItems = cart.items.filter(item => item.id !== lineItemId);
      const totals = calculateCartTotals(updatedItems);
      
      const updatedCart: Cart = {
        ...cart,
        items: updatedItems,
        ...totals,
        updated_at: new Date().toISOString(),
      };

      setCart(updatedCart);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to remove item from cart');
    } finally {
      setLoading(false);
    }
  }, [cart]);

  const clearCart = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      setCart(createEmptyCart());
      localStorage.removeItem(CART_STORAGE_KEY);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear cart');
    } finally {
      setLoading(false);
    }
  }, []);

  const refreshCart = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // In a real app, this would fetch the latest cart from the server
      // For now, we'll just recalculate totals
      if (cart) {
        const totals = calculateCartTotals(cart.items);
        const updatedCart: Cart = {
          ...cart,
          ...totals,
          updated_at: new Date().toISOString(),
        };
        setCart(updatedCart);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh cart');
    } finally {
      setLoading(false);
    }
  }, [cart]);

  return {
    cart,
    loading,
    error,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    refreshCart,
  };
}
