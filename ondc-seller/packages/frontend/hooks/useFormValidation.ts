import { useState, useCallback, useEffect, useMemo } from 'react';
import {
  validateForm,
  validateField,
  ValidationRule,
  FIELD_VALIDATION_RULES,
} from '@/lib/validation';

export interface FormValidationOptions {
  initialData?: Record<string, any>;
  validationRules?: Record<string, ValidationRule>;
  validateOnChange?: boolean;
  validateOnBlur?: boolean;
  onSubmit?: (data: Record<string, any>) => Promise<void> | void;
  onError?: (errors: Record<string, string>) => void;
}

export interface FormValidationReturn {
  data: Record<string, any>;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
  handleChange: (e: React.ChangeEvent<any>) => void;
  handleBlur: (e: React.FocusEvent<any>) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  setFieldValue: (name: string, value: any) => void;
  setFieldError: (name: string, error: string) => void;
  setErrors: (errors: Record<string, string>) => void;
  resetForm: () => void;
  validateField: (name: string) => void;
  validateForm: () => boolean;
  clearErrors: () => void;
}

/**
 * Comprehensive form validation hook with Material-UI integration
 */
export function useFormValidation({
  initialData = {},
  validationRules = {},
  validateOnChange = true,
  validateOnBlur = true,
  onSubmit,
  onError,
}: FormValidationOptions): FormValidationReturn {
  const [data, setData] = useState<Record<string, any>>(initialData);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [touched, setTouched] = useState<Record<string, boolean>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate if form is valid - check both errors and required fields
  const isValid = useMemo(() => {
    // Check if there are any actual errors (non-empty error messages)
    const actualErrors = Object.entries(errors).filter(
      ([key, value]) => value && value.trim() !== ''
    );
    if (actualErrors.length > 0) {
      return false;
    }

    // Only check fields that are explicitly defined in validationRules
    // If no validationRules provided, check all fields in data
    const rulesToCheck =
      Object.keys(validationRules).length > 0
        ? validationRules
        : Object.keys(data).reduce(
            (acc, key) => {
              const rule = FIELD_VALIDATION_RULES[key];
              if (rule) acc[key] = rule;
              return acc;
            },
            {} as Record<string, any>
          );

    for (const [fieldName, rules] of Object.entries(rulesToCheck)) {
      if (rules.required) {
        const value = data[fieldName];
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          return false;
        }
      }
    }

    return true;
  }, [errors, data, validationRules]);

  // Handle input changes
  const handleChange = useCallback(
    (e: React.ChangeEvent<any>) => {
      const { name, value, type, checked } = e.target;
      const fieldValue = type === 'checkbox' ? checked : value;

      setData(prev => ({ ...prev, [name]: fieldValue }));

      // Auto-generate SKU from product name
      if (name === 'name' && !data.sku) {
        const sku = value
          .toUpperCase()
          .replace(/[^A-Z0-9]/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        setData(prev => ({ ...prev, sku }));
      }

      // Auto-generate slug from title
      if (name === 'title' && !data.slug) {
        const slug = value
          .toLowerCase()
          .replace(/[^a-z0-9]/g, '-')
          .replace(/-+/g, '-')
          .replace(/^-|-$/g, '');
        setData(prev => ({ ...prev, slug }));
      }

      // Validate on change if enabled and field is touched
      if (validateOnChange && touched[name]) {
        const rules = validationRules[name] || FIELD_VALIDATION_RULES[name];
        if (rules) {
          const error = validateField(name, fieldValue, rules);
          setErrors(prev => {
            const newErrors = { ...prev };
            if (error) {
              newErrors[name] = error;
            } else {
              delete newErrors[name];
            }
            return newErrors;
          });
        }
      }
    },
    [data, touched, validateOnChange, validationRules]
  );

  // Handle input blur
  const handleBlur = useCallback(
    (e: React.FocusEvent<any>) => {
      const { name, value, type, checked } = e.target;
      const fieldValue = type === 'checkbox' ? checked : value;

      setTouched(prev => ({ ...prev, [name]: true }));

      // Validate on blur if enabled
      if (validateOnBlur) {
        const rules = validationRules[name] || FIELD_VALIDATION_RULES[name];
        if (rules) {
          const error = validateField(name, fieldValue, rules);
          setErrors(prev => {
            const newErrors = { ...prev };
            if (error) {
              newErrors[name] = error;
            } else {
              delete newErrors[name];
            }
            return newErrors;
          });
        }
      }
    },
    [validateOnBlur, validationRules]
  );

  // Handle form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();
      setIsSubmitting(true);

      try {
        // Validate all fields
        const allRules = { ...FIELD_VALIDATION_RULES, ...validationRules };
        const validation = validateForm(data, allRules);

        if (!validation.isValid) {
          setErrors(validation.errors);
          setTouched(Object.keys(data).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
          if (onError) {
            onError(validation.errors);
          }
          return;
        }

        // Clear errors and submit
        setErrors({});
        if (onSubmit) {
          await onSubmit(data);
        }
      } catch (error) {
        console.error('Form submission error:', error);
        const errorMessage =
          error instanceof Error ? error.message : 'An unexpected error occurred';
        setErrors({ submit: errorMessage });
        if (onError) {
          onError({ submit: errorMessage });
        }
      } finally {
        setIsSubmitting(false);
      }
    },
    [data, validationRules, onSubmit, onError]
  );

  // Set field value programmatically
  const setFieldValue = useCallback((name: string, value: any) => {
    setData(prev => ({ ...prev, [name]: value }));
  }, []);

  // Set field error programmatically
  const setFieldError = useCallback((name: string, error: string) => {
    setErrors(prev => ({ ...prev, [name]: error }));
  }, []);

  // Validate a specific field
  const validateFieldManually = useCallback(
    (name: string) => {
      const rules = validationRules[name] || FIELD_VALIDATION_RULES[name];
      if (rules) {
        const error = validateField(name, data[name], rules);
        setErrors(prev => {
          const newErrors = { ...prev };
          if (error) {
            newErrors[name] = error;
          } else {
            delete newErrors[name];
          }
          return newErrors;
        });
      }
    },
    [data, validationRules]
  );

  // Validate entire form
  const validateFormManually = useCallback(() => {
    const allRules = { ...FIELD_VALIDATION_RULES, ...validationRules };
    const validation = validateForm(data, allRules);
    setErrors(validation.errors);
    setTouched(Object.keys(data).reduce((acc, key) => ({ ...acc, [key]: true }), {}));
    return validation.isValid;
  }, [data, validationRules]);

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setData(initialData);
    setErrors({});
    setTouched({});
    setIsSubmitting(false);
  }, [initialData]);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  // Update data when initialData changes (only on mount)
  useEffect(() => {
    setData(initialData);
  }, []); // Only run on mount

  return {
    data,
    errors,
    touched,
    isValid,
    isSubmitting,
    handleChange,
    handleBlur,
    handleSubmit,
    setFieldValue,
    setFieldError,
    setErrors,
    resetForm,
    validateField: validateFieldManually,
    validateForm: validateFormManually,
    clearErrors,
  };
}

/**
 * Simplified form validation hook for basic forms
 */
export function useSimpleFormValidation(initialData: Record<string, any> = {}) {
  return useFormValidation({
    initialData,
    validateOnChange: true,
    validateOnBlur: true,
  });
}

/**
 * Product form validation hook with specific rules
 */
export function useProductFormValidation(options: FormValidationOptions = {}) {
  return useFormValidation({
    ...options,
    validationRules: {
      name: FIELD_VALIDATION_RULES.productName,
      sku: FIELD_VALIDATION_RULES.sku,
      price: FIELD_VALIDATION_RULES.price,
      description: FIELD_VALIDATION_RULES.description,
      shortDescription: FIELD_VALIDATION_RULES.shortDescription,
      quantity: FIELD_VALIDATION_RULES.quantity,
      category: FIELD_VALIDATION_RULES.category,
      ...options.validationRules,
    },
    validateOnChange: options.validateOnChange ?? true,
    validateOnBlur: options.validateOnBlur ?? true,
  });
}

/**
 * Coupon form validation hook with specific rules
 */
export function useCouponFormValidation(options: FormValidationOptions = {}) {
  return useFormValidation({
    ...options,
    validationRules: {
      code: FIELD_VALIDATION_RULES.couponCode,
      name: FIELD_VALIDATION_RULES.couponName,
      value: FIELD_VALIDATION_RULES.discountValue,
      minimumAmount: FIELD_VALIDATION_RULES.minimumAmount,
      maximumDiscount: FIELD_VALIDATION_RULES.maximumDiscount,
      startDate: FIELD_VALIDATION_RULES.startDate,
      endDate: FIELD_VALIDATION_RULES.endDate,
      ...options.validationRules,
    },
    validateOnChange: options.validateOnChange ?? true,
    validateOnBlur: options.validateOnBlur ?? true,
  });
}

/**
 * Category form validation hook with specific rules
 */
export function useCategoryFormValidation(options: FormValidationOptions = {}) {
  return useFormValidation({
    ...options,
    validationRules: {
      name: FIELD_VALIDATION_RULES.categoryName,
      slug: FIELD_VALIDATION_RULES.categorySlug,
      description: FIELD_VALIDATION_RULES.categoryDescription,
      ...options.validationRules,
    },
    validateOnChange: options.validateOnChange ?? true,
    validateOnBlur: options.validateOnBlur ?? true,
  });
}

/**
 * Collection form validation hook with specific rules
 */
export function useCollectionFormValidation(options: FormValidationOptions = {}) {
  return useFormValidation({
    ...options,
    validationRules: {
      name: FIELD_VALIDATION_RULES.collectionName,
      slug: FIELD_VALIDATION_RULES.collectionSlug,
      description: FIELD_VALIDATION_RULES.collectionDescription,
      ...options.validationRules,
    },
    validateOnChange: options.validateOnChange ?? true,
    validateOnBlur: options.validateOnBlur ?? true,
  });
}

/**
 * Attribute form validation hook with specific rules
 */
export function useAttributeFormValidation(options: FormValidationOptions = {}) {
  return useFormValidation({
    ...options,
    validationRules: {
      name: FIELD_VALIDATION_RULES.attributeName,
      code: FIELD_VALIDATION_RULES.attributeCode,
      ...options.validationRules,
    },
    validateOnChange: options.validateOnChange ?? true,
    validateOnBlur: options.validateOnBlur ?? true,
  });
}

/**
 * Settings form validation hook with specific rules
 */
export function useSettingsFormValidation(options: FormValidationOptions = {}) {
  return useFormValidation({
    ...options,
    validationRules: {
      storeName: FIELD_VALIDATION_RULES.storeName,
      storeDescription: FIELD_VALIDATION_RULES.storeDescription,
      storeEmail: FIELD_VALIDATION_RULES.contactEmail,
      storePhone: FIELD_VALIDATION_RULES.contactPhone,
      storeAddress: FIELD_VALIDATION_RULES.address,
      storeCity: FIELD_VALIDATION_RULES.city,
      storeState: FIELD_VALIDATION_RULES.state,
      storeZipCode: FIELD_VALIDATION_RULES.postalCode,
      storeCountry: FIELD_VALIDATION_RULES.country,
      freeShippingThreshold: FIELD_VALIDATION_RULES.minimumAmount,
      shippingRate: FIELD_VALIDATION_RULES.minimumAmount,
      sessionTimeout: {
        required: true,
        min: 5,
        max: 1440, // 24 hours in minutes
      },
      ...options.validationRules,
    },
    validateOnChange: options.validateOnChange ?? true,
    validateOnBlur: options.validateOnBlur ?? true,
  });
}
