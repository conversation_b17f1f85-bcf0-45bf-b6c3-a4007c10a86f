'use client';

import { useState, useEffect } from 'react';

export interface TenantConfig {
  id: string;
  name: string;
  domain: string;
  logo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  theme?: 'light' | 'dark';
  features?: string[];
  settings?: Record<string, any>;
}

export interface UseTenantReturn {
  tenantId: string | null;
  tenantConfig: TenantConfig | null;
  loading: boolean;
  error: string | null;
  setTenantId: (id: string) => void;
  refreshTenant: () => Promise<void>;
}

// Mock tenant configurations
const mockTenantConfigs: Record<string, TenantConfig> = {
  'demo': {
    id: 'demo',
    name: 'Demo Seller',
    domain: 'demo.ondcseller.com',
    logo: '/images/demo-logo.png',
    primaryColor: '#3B82F6',
    secondaryColor: '#10B981',
    theme: 'light',
    features: ['products', 'orders', 'analytics', 'inventory'],
    settings: {
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      language: 'en',
    },
  },
  'seller1': {
    id: 'seller1',
    name: 'Seller One',
    domain: 'seller1.ondcseller.com',
    logo: '/images/seller1-logo.png',
    primaryColor: '#059669',
    secondaryColor: '#DC2626',
    theme: 'light',
    features: ['products', 'orders', 'analytics'],
    settings: {
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      language: 'en',
    },
  },
  'seller2': {
    id: 'seller2',
    name: 'Seller Two',
    domain: 'seller2.ondcseller.com',
    logo: '/images/seller2-logo.png',
    primaryColor: '#7C3AED',
    secondaryColor: '#F59E0B',
    theme: 'dark',
    features: ['products', 'orders', 'inventory', 'reports'],
    settings: {
      currency: 'INR',
      timezone: 'Asia/Kolkata',
      language: 'hi',
    },
  },
};

const TENANT_STORAGE_KEY = 'ondc_seller_tenant_id';

export function useTenant(): UseTenantReturn {
  const [tenantId, setTenantIdState] = useState<string | null>(null);
  const [tenantConfig, setTenantConfig] = useState<TenantConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize tenant from various sources
  useEffect(() => {
    const initializeTenant = async () => {
      setLoading(true);
      setError(null);

      try {
        let detectedTenantId: string | null = null;

        // 1. Check URL subdomain (if running on custom domain)
        if (typeof window !== 'undefined') {
          const hostname = window.location.hostname;
          const subdomain = hostname.split('.')[0];
          
          if (subdomain && subdomain !== 'localhost' && subdomain !== 'www') {
            detectedTenantId = subdomain;
          }
        }

        // 2. Check URL search params
        if (!detectedTenantId && typeof window !== 'undefined') {
          const urlParams = new URLSearchParams(window.location.search);
          const tenantParam = urlParams.get('tenant');
          if (tenantParam) {
            detectedTenantId = tenantParam;
          }
        }

        // 3. Check localStorage
        if (!detectedTenantId) {
          try {
            const storedTenantId = localStorage.getItem(TENANT_STORAGE_KEY);
            if (storedTenantId) {
              detectedTenantId = storedTenantId;
            }
          } catch (err) {
            console.warn('Failed to read tenant from localStorage:', err);
          }
        }

        // 4. Default to demo tenant
        if (!detectedTenantId) {
          detectedTenantId = 'demo';
        }

        // Fetch tenant configuration
        const config = await fetchTenantConfig(detectedTenantId);
        
        setTenantIdState(detectedTenantId);
        setTenantConfig(config);

        // Store in localStorage for future visits
        try {
          localStorage.setItem(TENANT_STORAGE_KEY, detectedTenantId);
        } catch (err) {
          console.warn('Failed to store tenant in localStorage:', err);
        }

      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize tenant');
        console.error('Tenant initialization error:', err);
        
        // Fallback to demo tenant
        setTenantIdState('demo');
        setTenantConfig(mockTenantConfigs['demo']);
      } finally {
        setLoading(false);
      }
    };

    initializeTenant();
  }, []);

  const setTenantId = (id: string) => {
    setLoading(true);
    setError(null);

    fetchTenantConfig(id)
      .then((config) => {
        setTenantIdState(id);
        setTenantConfig(config);
        
        try {
          localStorage.setItem(TENANT_STORAGE_KEY, id);
        } catch (err) {
          console.warn('Failed to store tenant in localStorage:', err);
        }
      })
      .catch((err) => {
        setError(err instanceof Error ? err.message : 'Failed to set tenant');
        console.error('Set tenant error:', err);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const refreshTenant = async () => {
    if (!tenantId) return;

    setLoading(true);
    setError(null);

    try {
      const config = await fetchTenantConfig(tenantId);
      setTenantConfig(config);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh tenant');
      console.error('Refresh tenant error:', err);
    } finally {
      setLoading(false);
    }
  };

  return {
    tenantId,
    tenantConfig,
    loading,
    error,
    setTenantId,
    refreshTenant,
  };
}

// Helper function to fetch tenant configuration
async function fetchTenantConfig(tenantId: string): Promise<TenantConfig> {
  // In development, use mock data
  if (process.env.NODE_ENV === 'development' || !process.env.NEXT_PUBLIC_API_URL) {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const config = mockTenantConfigs[tenantId];
    if (!config) {
      throw new Error(`Tenant configuration not found for: ${tenantId}`);
    }
    
    return config;
  }

  // In production, fetch from API
  try {
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/api/tenants/${tenantId}/config`, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch tenant config: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data.config;
  } catch (error) {
    console.warn('Failed to fetch tenant config from API, using fallback:', error);
    
    // Fallback to mock data
    const config = mockTenantConfigs[tenantId] || mockTenantConfigs['demo'];
    return config;
  }
}

// Utility functions
export function getTenantFromUrl(): string | null {
  if (typeof window === 'undefined') return null;
  
  const hostname = window.location.hostname;
  const subdomain = hostname.split('.')[0];
  
  if (subdomain && subdomain !== 'localhost' && subdomain !== 'www') {
    return subdomain;
  }
  
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('tenant');
}

export function getStoredTenantId(): string | null {
  if (typeof window === 'undefined') return null;
  
  try {
    return localStorage.getItem(TENANT_STORAGE_KEY);
  } catch {
    return null;
  }
}

export function clearStoredTenantId(): void {
  if (typeof window === 'undefined') return;
  
  try {
    localStorage.removeItem(TENANT_STORAGE_KEY);
  } catch (err) {
    console.warn('Failed to clear stored tenant ID:', err);
  }
}
