openapi: 3.0.3
info:
  title: ONDC Seller Platform API
  description: |
    Comprehensive API specification for the ONDC (Open Network for Digital Commerce) Seller Platform.
    This API provides complete CRUD operations for managing products, categories, orders, customers, 
    collections, coupons, and analytics for e-commerce sellers on the ONDC network.

    ## Features
    - **Product Management**: Full catalog management with variants, inventory, and pricing
    - **Order Processing**: Complete order lifecycle management with status tracking
    - **Customer Management**: Customer profiles, addresses, and order history
    - **Category Management**: Hierarchical category structure with SEO optimization
    - **Collection Management**: Product collections with automatic and manual rules
    - **Coupon Management**: Flexible discount and promotion system
    - **Analytics**: Comprehensive business intelligence and reporting

    ## Authentication
    All admin endpoints require authentication. Use Bearer token authentication.

    ## Rate Limiting
    API requests are rate-limited to 1000 requests per hour per API key.

    ## Error Handling
    The API uses standard HTTP status codes and returns detailed error messages in JSON format.
  version: 1.0.0
  contact:
    name: ONDC Seller Platform Team
    email: <EMAIL>
    url: https://ondc-seller.com/support
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://ondc-seller.com/terms

servers:
  - url: http://localhost:3000/api
    description: Development server
  - url: https://api.ondc-seller.com
    description: Production server
  - url: https://staging-api.ondc-seller.com
    description: Staging server

security:
  - bearerAuth: []

tags:
  - name: Products
    description: Product catalog management
  - name: Categories
    description: Product category management
  - name: Orders
    description: Order processing and management
  - name: Customers
    description: Customer relationship management
  - name: Collections
    description: Product collection management
  - name: Coupons
    description: Discount and promotion management
  - name: Analytics
    description: Business intelligence and reporting
  - name: Public
    description: Public endpoints (no authentication required)

paths:
  # Products Endpoints
  /admin/products:
    get:
      tags: [Products]
      summary: List all products
      description: Retrieve a paginated list of products with filtering and sorting options
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: search
          in: query
          description: Search term for product name, SKU, or description
          schema:
            type: string
        - name: status
          in: query
          description: Filter by product status
          schema:
            type: string
            enum: [active, inactive, draft]
        - name: category
          in: query
          description: Filter by category name
          schema:
            type: string
        - name: sortBy
          in: query
          description: Field to sort by
          schema:
            type: string
            enum: [name, price, quantity, status, createdAt, updatedAt]
            default: updatedAt
        - name: sortOrder
          in: query
          description: Sort order
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
                  filters:
                    $ref: '#/components/schemas/FilterInfo'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      tags: [Products]
      summary: Create a new product
      description: Create a new product in the catalog
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductRequest'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product created successfully
                  product:
                    $ref: '#/components/schemas/Product'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/products/{id}:
    get:
      tags: [Products]
      summary: Get product by ID
      description: Retrieve detailed information about a specific product
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      tags: [Products]
      summary: Update product
      description: Update an existing product
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateProductRequest'
      responses:
        '200':
          description: Product updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product updated successfully
                  product:
                    $ref: '#/components/schemas/Product'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags: [Products]
      summary: Delete product
      description: Delete a product from the catalog
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
      responses:
        '200':
          description: Product deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product deleted successfully
                  product:
                    $ref: '#/components/schemas/Product'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Categories Endpoints
  /admin/categories:
    get:
      tags: [Categories]
      summary: List all categories
      description: Retrieve a paginated list of categories with filtering and sorting options
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: search
          in: query
          description: Search term for category name, slug, or description
          schema:
            type: string
        - name: status
          in: query
          description: Filter by category status
          schema:
            type: string
            enum: [active, inactive]
        - name: parentId
          in: query
          description: Filter by parent category ID
          schema:
            type: string
        - name: sortBy
          in: query
          description: Field to sort by
          schema:
            type: string
            enum: [name, sortOrder, productCount, createdAt, updatedAt]
            default: sortOrder
        - name: sortOrder
          in: query
          description: Sort order
          schema:
            type: string
            enum: [asc, desc]
            default: asc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  categories:
                    type: array
                    items:
                      $ref: '#/components/schemas/Category'
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
                  filters:
                    $ref: '#/components/schemas/FilterInfo'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      tags: [Categories]
      summary: Create a new category
      description: Create a new product category
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCategoryRequest'
      responses:
        '201':
          description: Category created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Category created successfully
                  category:
                    $ref: '#/components/schemas/Category'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /admin/categories/{id}:
    get:
      tags: [Categories]
      summary: Get category by ID
      description: Retrieve detailed information about a specific category
      parameters:
        - name: id
          in: path
          required: true
          description: Category ID
          schema:
            type: string
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  category:
                    $ref: '#/components/schemas/Category'
        '404':
          $ref: '#/components/responses/NotFound'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      tags: [Categories]
      summary: Update category
      description: Update an existing category
      parameters:
        - name: id
          in: path
          required: true
          description: Category ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateCategoryRequest'
      responses:
        '200':
          description: Category updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Category updated successfully
                  category:
                    $ref: '#/components/schemas/Category'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          $ref: '#/components/responses/Conflict'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags: [Categories]
      summary: Delete category
      description: Delete a category (only if it has no child categories)
      parameters:
        - name: id
          in: path
          required: true
          description: Category ID
          schema:
            type: string
      responses:
        '200':
          description: Category deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Category deleted successfully
                  category:
                    $ref: '#/components/schemas/Category'
        '400':
          description: Cannot delete category with child categories
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Orders Endpoints
  /admin/orders:
    get:
      tags: [Orders]
      summary: List all orders
      description: Retrieve a paginated list of orders with filtering and sorting options
      parameters:
        - name: page
          in: query
          description: Page number for pagination
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 10
        - name: search
          in: query
          description: Search term for order number, customer name, or email
          schema:
            type: string
        - name: status
          in: query
          description: Filter by order status
          schema:
            type: string
            enum: [pending, processing, shipped, delivered, cancelled]
        - name: paymentStatus
          in: query
          description: Filter by payment status
          schema:
            type: string
            enum: [pending, paid, failed, refunded]
        - name: fulfillmentStatus
          in: query
          description: Filter by fulfillment status
          schema:
            type: string
            enum: [unfulfilled, partial, fulfilled, cancelled]
        - name: sortBy
          in: query
          description: Field to sort by
          schema:
            type: string
            enum: [orderNumber, total, status, createdAt, updatedAt]
            default: createdAt
        - name: sortOrder
          in: query
          description: Sort order
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
                  filters:
                    $ref: '#/components/schemas/FilterInfo'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Analytics Endpoints
  /admin/analytics:
    get:
      tags: [Analytics]
      summary: Get dashboard analytics
      description: Retrieve comprehensive analytics data for the admin dashboard
      parameters:
        - name: period
          in: query
          description: Time period for analytics
          schema:
            type: string
            enum: [7d, 30d, 90d, 1y]
            default: 30d
        - name: metric
          in: query
          description: Specific metric to filter
          schema:
            type: string
            enum: [all, revenue, orders, customers, products]
            default: all
        - name: segment
          in: query
          description: Customer segment to filter
          schema:
            type: string
            enum: [all, new, repeat, vip]
            default: all
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalyticsData'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/InternalServerError'

  # Public Endpoints (No authentication required)
  /products:
    get:
      tags: [Public]
      summary: List public products
      description: Retrieve a list of active products for public consumption
      security: []
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 12
        - name: search
          in: query
          schema:
            type: string
        - name: category
          in: query
          schema:
            type: string
        - name: sortBy
          in: query
          schema:
            type: string
            enum: [name, price, createdAt]
            default: createdAt
        - name: sortOrder
          in: query
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/PublicProduct'
                  pagination:
                    $ref: '#/components/schemas/PaginationInfo'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  schemas:
    # Product Schemas
    Product:
      type: object
      required:
        - id
        - name
        - sku
        - price
        - category
        - status
      properties:
        id:
          type: string
          description: Unique product identifier
          example: '1'
        name:
          type: string
          description: Product name
          example: 'Nike Air Max 270'
        sku:
          type: string
          description: Stock Keeping Unit
          example: 'NIKE-AM270-001'
        price:
          type: integer
          description: Product price in smallest currency unit (paise for INR)
          example: 12999
        comparePrice:
          type: integer
          description: Compare at price for discounts
          example: 14999
          nullable: true
        quantity:
          type: integer
          description: Available stock quantity
          example: 45
        category:
          type: string
          description: Product category
          example: 'Shoes'
        status:
          type: string
          enum: [active, inactive, draft]
          description: Product status
          example: 'active'
        image:
          type: string
          description: Product image URL
          example: '/images/products/placeholder.svg'
        description:
          type: string
          description: Product description
          example: 'Premium running shoes with advanced cushioning technology'
        weight:
          type: number
          description: Product weight in kg
          example: 0.8
        dimensions:
          type: object
          properties:
            length:
              type: number
              example: 30
            width:
              type: number
              example: 20
            height:
              type: number
              example: 12
        tags:
          type: array
          items:
            type: string
          example: ['nike', 'running', 'sports']
        vendor:
          type: string
          description: Product vendor/brand
          example: 'Nike Inc.'
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: '2024-01-10T10:00:00Z'
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: '2024-01-15T14:30:00Z'

    CreateProductRequest:
      type: object
      required:
        - name
        - sku
        - price
        - category
      properties:
        name:
          type: string
          description: Product name
          example: 'Nike Air Max 270'
        sku:
          type: string
          description: Stock Keeping Unit (must be unique)
          example: 'NIKE-AM270-001'
        price:
          type: integer
          description: Product price in smallest currency unit
          minimum: 0
          example: 12999
        comparePrice:
          type: integer
          description: Compare at price for discounts
          minimum: 0
          example: 14999
        quantity:
          type: integer
          description: Initial stock quantity
          minimum: 0
          default: 0
          example: 45
        category:
          type: string
          description: Product category
          example: 'Shoes'
        status:
          type: string
          enum: [active, inactive, draft]
          default: draft
          example: 'active'
        image:
          type: string
          description: Product image URL
          example: '/images/products/placeholder.svg'
        description:
          type: string
          description: Product description
          example: 'Premium running shoes with advanced cushioning technology'
        weight:
          type: number
          description: Product weight in kg
          minimum: 0
          example: 0.8
        dimensions:
          type: object
          properties:
            length:
              type: number
              minimum: 0
            width:
              type: number
              minimum: 0
            height:
              type: number
              minimum: 0
        tags:
          type: array
          items:
            type: string
          example: ['nike', 'running', 'sports']
        vendor:
          type: string
          description: Product vendor/brand
          example: 'Nike Inc.'

    UpdateProductRequest:
      type: object
      properties:
        name:
          type: string
          description: Product name
        sku:
          type: string
          description: Stock Keeping Unit (must be unique)
        price:
          type: integer
          description: Product price in smallest currency unit
          minimum: 0
        comparePrice:
          type: integer
          description: Compare at price for discounts
          minimum: 0
        quantity:
          type: integer
          description: Stock quantity
          minimum: 0
        category:
          type: string
          description: Product category
        status:
          type: string
          enum: [active, inactive, draft]
        image:
          type: string
          description: Product image URL
        description:
          type: string
          description: Product description
        weight:
          type: number
          description: Product weight in kg
          minimum: 0
        dimensions:
          type: object
          properties:
            length:
              type: number
              minimum: 0
            width:
              type: number
              minimum: 0
            height:
              type: number
              minimum: 0
        tags:
          type: array
          items:
            type: string
        vendor:
          type: string
          description: Product vendor/brand

    PublicProduct:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        sku:
          type: string
        price:
          type: integer
        comparePrice:
          type: integer
          nullable: true
        category:
          type: string
        image:
          type: string
        description:
          type: string
        tags:
          type: array
          items:
            type: string
        vendor:
          type: string

    # Category Schemas
    Category:
      type: object
      required:
        - id
        - name
        - slug
        - status
      properties:
        id:
          type: string
          description: Unique category identifier
          example: '1'
        name:
          type: string
          description: Category name
          example: 'Electronics'
        slug:
          type: string
          description: URL-friendly category identifier
          example: 'electronics'
        description:
          type: string
          description: Category description
          example: 'Electronic devices and gadgets'
        image:
          type: string
          description: Category image URL
          example: '/images/categories/electronics.jpg'
        status:
          type: string
          enum: [active, inactive]
          description: Category status
          example: 'active'
        parentId:
          type: string
          description: Parent category ID for hierarchical structure
          nullable: true
          example: null
        sortOrder:
          type: integer
          description: Display order
          example: 1
        productCount:
          type: integer
          description: Number of products in this category
          example: 156
        metaTitle:
          type: string
          description: SEO meta title
          example: 'Electronics - ONDC Seller'
        metaDescription:
          type: string
          description: SEO meta description
          example: 'Browse our wide selection of electronic devices'
        createdAt:
          type: string
          format: date-time
          example: '2024-01-01T00:00:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2024-01-15T10:30:00Z'

    CreateCategoryRequest:
      type: object
      required:
        - name
        - slug
      properties:
        name:
          type: string
          description: Category name
          example: 'Electronics'
        slug:
          type: string
          description: URL-friendly identifier (must be unique)
          example: 'electronics'
        description:
          type: string
          description: Category description
          example: 'Electronic devices and gadgets'
        image:
          type: string
          description: Category image URL
          example: '/images/categories/electronics.jpg'
        status:
          type: string
          enum: [active, inactive]
          default: active
          example: 'active'
        parentId:
          type: string
          description: Parent category ID
          nullable: true
        sortOrder:
          type: integer
          description: Display order
          minimum: 0
          example: 1
        metaTitle:
          type: string
          description: SEO meta title
        metaDescription:
          type: string
          description: SEO meta description

    UpdateCategoryRequest:
      type: object
      properties:
        name:
          type: string
          description: Category name
        slug:
          type: string
          description: URL-friendly identifier (must be unique)
        description:
          type: string
          description: Category description
        image:
          type: string
          description: Category image URL
        status:
          type: string
          enum: [active, inactive]
        parentId:
          type: string
          description: Parent category ID
          nullable: true
        sortOrder:
          type: integer
          description: Display order
          minimum: 0
        metaTitle:
          type: string
          description: SEO meta title
        metaDescription:
          type: string
          description: SEO meta description

    # Order Schemas
    Order:
      type: object
      required:
        - id
        - orderNumber
        - customerId
        - status
        - paymentStatus
        - fulfillmentStatus
        - total
        - items
      properties:
        id:
          type: string
          description: Unique order identifier
          example: 'ORD-001'
        orderNumber:
          type: string
          description: Human-readable order number
          example: '#10001'
        customerId:
          type: string
          description: Customer identifier
          example: 'CUST-001'
        customerName:
          type: string
          description: Customer name
          example: 'John Doe'
        customerEmail:
          type: string
          description: Customer email
          example: '<EMAIL>'
        status:
          type: string
          enum: [pending, processing, shipped, delivered, cancelled]
          description: Order status
          example: 'pending'
        paymentStatus:
          type: string
          enum: [pending, paid, failed, refunded]
          description: Payment status
          example: 'paid'
        fulfillmentStatus:
          type: string
          enum: [unfulfilled, partial, fulfilled, cancelled]
          description: Fulfillment status
          example: 'unfulfilled'
        total:
          type: integer
          description: Total order amount in smallest currency unit
          example: 25999
        subtotal:
          type: integer
          description: Subtotal before tax and shipping
          example: 23999
        tax:
          type: integer
          description: Tax amount
          example: 1440
        shipping:
          type: integer
          description: Shipping cost
          example: 560
        discount:
          type: integer
          description: Discount amount
          example: 0
        currency:
          type: string
          description: Currency code
          example: 'INR'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
        shippingAddress:
          $ref: '#/components/schemas/Address'
        billingAddress:
          $ref: '#/components/schemas/Address'
        notes:
          type: string
          description: Order notes
          example: 'Please deliver between 10 AM - 6 PM'
        tags:
          type: array
          items:
            type: string
          example: ['priority', 'new-customer']
        createdAt:
          type: string
          format: date-time
          example: '2024-01-15T10:30:00Z'
        updatedAt:
          type: string
          format: date-time
          example: '2024-01-15T14:45:00Z'

    OrderItem:
      type: object
      required:
        - id
        - productId
        - productName
        - sku
        - quantity
        - price
        - total
      properties:
        id:
          type: string
          description: Order item identifier
          example: 'ITEM-001'
        productId:
          type: string
          description: Product identifier
          example: '1'
        productName:
          type: string
          description: Product name at time of order
          example: 'Nike Air Max 270'
        sku:
          type: string
          description: Product SKU at time of order
          example: 'NIKE-AM270-001'
        quantity:
          type: integer
          description: Quantity ordered
          minimum: 1
          example: 2
        price:
          type: integer
          description: Unit price at time of order
          example: 12999
        total:
          type: integer
          description: Total price for this item (price * quantity)
          example: 25998
        image:
          type: string
          description: Product image URL
          example: '/images/products/placeholder.svg'

    Address:
      type: object
      required:
        - name
        - address1
        - city
        - state
        - postalCode
        - country
      properties:
        name:
          type: string
          description: Recipient name
          example: 'John Doe'
        address1:
          type: string
          description: Address line 1
          example: '123 Main Street'
        address2:
          type: string
          description: Address line 2
          example: 'Apt 4B'
        city:
          type: string
          description: City
          example: 'Mumbai'
        state:
          type: string
          description: State/Province
          example: 'Maharashtra'
        postalCode:
          type: string
          description: Postal/ZIP code
          example: '400001'
        country:
          type: string
          description: Country
          example: 'India'
        phone:
          type: string
          description: Phone number
          example: '+91 9876543210'

    CreateOrderRequest:
      type: object
      required:
        - customerId
        - items
      properties:
        customerId:
          type: string
          description: Customer identifier
          example: 'CUST-001'
        customerName:
          type: string
          description: Customer name
          example: 'John Doe'
        customerEmail:
          type: string
          description: Customer email
          example: '<EMAIL>'
        items:
          type: array
          items:
            type: object
            required:
              - productId
              - productName
              - sku
              - quantity
              - price
            properties:
              productId:
                type: string
              productName:
                type: string
              sku:
                type: string
              quantity:
                type: integer
                minimum: 1
              price:
                type: integer
                minimum: 0
              image:
                type: string
        shippingAddress:
          $ref: '#/components/schemas/Address'
        billingAddress:
          $ref: '#/components/schemas/Address'
        shipping:
          type: integer
          description: Shipping cost
          minimum: 0
          default: 0
        discount:
          type: integer
          description: Discount amount
          minimum: 0
          default: 0
        currency:
          type: string
          description: Currency code
          default: 'INR'
        notes:
          type: string
          description: Order notes
        tags:
          type: array
          items:
            type: string

    # Common Response Schemas
    PaginationInfo:
      type: object
      properties:
        page:
          type: integer
          description: Current page number
          example: 1
        limit:
          type: integer
          description: Items per page
          example: 10
        total:
          type: integer
          description: Total number of items
          example: 156
        totalPages:
          type: integer
          description: Total number of pages
          example: 16
        hasNext:
          type: boolean
          description: Whether there are more pages
          example: true
        hasPrev:
          type: boolean
          description: Whether there are previous pages
          example: false

    FilterInfo:
      type: object
      properties:
        search:
          type: string
          description: Applied search term
        status:
          type: string
          description: Applied status filter
        category:
          type: string
          description: Applied category filter
        sortBy:
          type: string
          description: Sort field
        sortOrder:
          type: string
          description: Sort order

    AnalyticsData:
      type: object
      properties:
        overview:
          type: object
          properties:
            totalRevenue:
              type: integer
              example: 2456789
            totalOrders:
              type: integer
              example: 1234
            totalCustomers:
              type: integer
              example: 567
            totalProducts:
              type: integer
              example: 89
            averageOrderValue:
              type: integer
              example: 1991
            conversionRate:
              type: number
              example: 3.2
            returnRate:
              type: number
              example: 2.1
            customerLifetimeValue:
              type: integer
              example: 4335
        revenueByMonth:
          type: array
          items:
            type: object
            properties:
              month:
                type: string
                example: 'Jan'
              revenue:
                type: integer
                example: 185000
              orders:
                type: integer
                example: 95
        topProducts:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
              name:
                type: string
              sku:
                type: string
              revenue:
                type: integer
              unitsSold:
                type: integer
              conversionRate:
                type: number
        metadata:
          type: object
          properties:
            period:
              type: string
              example: '30d'
            metric:
              type: string
              example: 'all'
            segment:
              type: string
              example: 'all'
            generatedAt:
              type: string
              format: date-time

    Error:
      type: object
      required:
        - error
      properties:
        error:
          type: string
          description: Error message
          example: 'Product not found'
        details:
          type: string
          description: Additional error details
          example: 'No product found with ID: 999'
        code:
          type: string
          description: Error code
          example: 'PRODUCT_NOT_FOUND'

  responses:
    BadRequest:
      description: Bad request - Invalid input parameters
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: 'Missing required field: name'
            details: "The 'name' field is required for creating a product"

    Unauthorized:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: 'Unauthorized'
            details: 'Valid authentication token required'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: 'Product not found'
            details: 'No product found with ID: 999'

    Conflict:
      description: Conflict - Resource already exists
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: 'Product with this SKU already exists'
            details: "A product with SKU 'NIKE-AM270-001' already exists"

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
          example:
            error: 'Internal server error'
            details: 'An unexpected error occurred while processing your request'
