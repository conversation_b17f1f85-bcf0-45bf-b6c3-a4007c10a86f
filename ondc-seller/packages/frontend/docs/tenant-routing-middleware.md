# Tenant Routing Middleware

This document describes the tenant routing middleware implementation for the ONDC Seller Platform.

## Overview

The tenant routing middleware is a Next.js middleware that extracts the tenant identifier from various sources and makes it available to the application. It ensures that all API requests include the correct tenant ID, enabling multi-tenancy support throughout the application.

## Implementation

The middleware is implemented in `src/middleware.ts` and follows these steps:

1. Extract the tenant identifier using one of these methods (in order of priority):
   - From the subdomain of the request URL (e.g., `tenant1.example.com` → tenant ID is "tenant1")
   - From the `x-tenant-id` header if present in the incoming request
   - From the `x-tenant-id` cookie if present
   - Falls back to a "default" tenant ID if none of the above are available

2. Add the tenant ID to:
   - The request headers for server-side access
   - A cookie for client-side access

3. Forward the tenant ID in the `x-tenant-id` header for all outgoing API requests

## Key Components

### 1. Middleware (`src/middleware.ts`)

```typescript
import { NextRequest, NextResponse } from 'next/server';

// Default tenant ID to use if no tenant is specified
const DEFAULT_TENANT_ID = 'default';

// Cookie name for storing the tenant ID
const TENANT_COOKIE_NAME = 'x-tenant-id';

export async function middleware(request: NextRequest) {
  // Clone the request headers to modify them
  const requestHeaders = new Headers(request.headers);
  
  // Get the URL and hostname
  const hostname = request.headers.get('host') || 'localhost';
  
  // Try to get tenant ID from different sources (in order of priority)
  let tenantId: string | null = null;
  
  // 1. Try to get from subdomain
  tenantId = extractTenantFromSubdomain(hostname);
  
  // 2. If not found in subdomain, try to get from header
  if (!tenantId) {
    tenantId = request.headers.get('x-tenant-id');
  }
  
  // 3. If not found in header, try to get from cookie
  if (!tenantId) {
    tenantId = request.cookies.get(TENANT_COOKIE_NAME)?.value || null;
  }
  
  // 4. If still not found, use default tenant ID
  if (!tenantId) {
    tenantId = DEFAULT_TENANT_ID;
  }
  
  // Set the tenant ID in the request headers
  requestHeaders.set('x-tenant-id', tenantId);
  
  // Create a new response with the modified headers
  const response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
  
  // Set the tenant ID in a cookie for client-side access
  response.cookies.set(TENANT_COOKIE_NAME, tenantId, COOKIE_OPTIONS);
  
  return response;
}
```

### 2. Server-Side Tenant Utilities (`src/lib/server-tenant.ts`)

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

// Default tenant ID for server-side rendering
const DEFAULT_TENANT_ID = 'default';

// Cookie name for storing the tenant ID
const TENANT_COOKIE_NAME = 'x-tenant-id';

export function getTenantIdFromRequest(): string {
  try {
    // Get headers from the request
    const headersList = headers();
    
    // Try to get tenant ID from the x-tenant-id header (set by middleware)
    const tenantId = headersList.get('x-tenant-id');
    
    return tenantId || DEFAULT_TENANT_ID;
  } catch (error) {
    console.error('Error getting tenant ID from request:', error);
    return DEFAULT_TENANT_ID;
  }
}
```

### 3. Client-Side Tenant Utilities (`src/lib/tenant.ts`)

```typescript
// Cookie name for storing the tenant ID
const TENANT_COOKIE_NAME = 'x-tenant-id';

export const getCurrentTenantId = (): string => {
  // Always return default tenant ID during server-side rendering
  if (typeof window === 'undefined') {
    return DEFAULT_TENANT_ID;
  }

  try {
    // First try to get from cookie (set by middleware)
    const cookies = document.cookie.split(';');
    const tenantCookie = cookies.find(cookie => cookie.trim().startsWith(`${TENANT_COOKIE_NAME}=`));
    
    if (tenantCookie) {
      const tenantId = tenantCookie.split('=')[1].trim();
      if (tenantId) {
        return tenantId;
      }
    }
    
    // If not in cookie, try localStorage
    if ('localStorage' in window) {
      const storedTenantId = localStorage.getItem('currentTenantId');
      if (storedTenantId) {
        return storedTenantId;
      }
    }
    
    return DEFAULT_TENANT_ID;
  } catch (error) {
    console.error('Error accessing tenant ID:', error);
    return DEFAULT_TENANT_ID;
  }
};
```

### 4. API Client Integration (`src/lib/api.ts`)

```typescript
// Add request interceptor for authentication and multi-tenancy
const addAuthAndTenantHeaders = (config: AxiosRequestConfig) => {
  // Add tenant ID header to every request
  config.headers = config.headers || {};
  
  // Get tenant ID from cookie first (set by middleware)
  let tenantId = null;
  
  // Try to get from cookie (client-side only)
  if (typeof document !== 'undefined') {
    const cookies = document.cookie.split(';');
    const tenantCookie = cookies.find(cookie => cookie.trim().startsWith('x-tenant-id='));
    
    if (tenantCookie) {
      tenantId = tenantCookie.split('=')[1].trim();
    }
  }
  
  // If not found in cookie, use the tenant context
  if (!tenantId) {
    tenantId = getCurrentTenantId();
  }
  
  // Set the tenant ID header
  config.headers['x-tenant-id'] = tenantId;
  
  // Authentication logic...
  
  return config;
};
```

## Usage

### 1. Server Components

In server components, you can get the tenant ID using the `getTenantIdFromRequest` function:

```typescript
import { getTenantIdFromRequest } from '../lib/server-tenant';

export default async function ServerComponent() {
  const tenantId = getTenantIdFromRequest();
  
  // Use the tenant ID to fetch tenant-specific data
  const data = await fetchTenantData(tenantId);
  
  return (
    <div>
      <h1>Tenant: {tenantId}</h1>
      {/* Render tenant-specific data */}
    </div>
  );
}
```

### 2. API Routes

In API routes, you can get the tenant ID from the request headers:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { getTenantIdFromNextRequest } from '../../../lib/server-tenant';

export async function GET(req: NextRequest) {
  const tenantId = getTenantIdFromNextRequest(req);
  
  // Use the tenant ID to fetch tenant-specific data
  const data = await fetchTenantData(tenantId);
  
  return NextResponse.json(data);
}
```

### 3. Client Components

In client components, you can get the tenant ID using the `getCurrentTenantId` function:

```typescript
import { useEffect, useState } from 'react';
import { getCurrentTenantId } from '../lib/tenant';

export default function ClientComponent() {
  const [tenantId, setTenantId] = useState('');
  
  useEffect(() => {
    setTenantId(getCurrentTenantId());
  }, []);
  
  return (
    <div>
      <h1>Tenant: {tenantId}</h1>
      {/* Render tenant-specific UI */}
    </div>
  );
}
```

## Testing

To test the tenant routing middleware:

1. Access the application using different subdomains (e.g., `tenant1.localhost:3000`, `tenant2.localhost:3000`)
2. Check the `x-tenant-id` cookie in the browser's developer tools
3. Verify that API requests include the correct `x-tenant-id` header
4. Test switching between tenants using the tenant selector UI
