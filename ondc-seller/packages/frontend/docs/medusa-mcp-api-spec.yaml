openapi: 3.0.3
info:
  title: Medusa-MCP Integration API
  description: |
    Comprehensive API specification for the Medusa Model Context Protocol (MCP) integration endpoints 
    that complement the ONDC Seller Platform. This API provides seamless integration between 
    ONDC commerce operations and Medusa's e-commerce engine through the MCP protocol.

    ## Key Features
    - **Product Synchronization**: Bi-directional sync between ONDC and Medusa catalogs
    - **Order Fulfillment**: Complete order lifecycle management through MCP
    - **Inventory Management**: Real-time inventory synchronization across platforms
    - **Customer Data Sync**: Unified customer profiles between ONDC and Medusa
    - **Payment Processing**: Integrated payment workflows with multiple providers
    - **Shipping & Logistics**: Coordinated shipping and logistics management
    - **Real-time Webhooks**: Event-driven synchronization and notifications

    ## MCP Protocol
    The Model Context Protocol (MCP) enables AI-powered automation and intelligent service 
    orchestration between ONDC and Medusa systems. This API supports JSON-RPC communication
    for high-throughput, reliable data exchange.

    ## Multi-Tenant Support
    All endpoints support multi-tenant architecture with tenant isolation through 
    the `x-tenant-id` header for secure multi-store operations.

    ## Authentication
    Supports dual authentication schemes compatible with both ONDC and Medusa systems.
    Use Bearer tokens for API access and MCP tokens for protocol-level communication.

    ## Rate Limiting
    API requests are rate-limited to 2000 requests per hour per tenant for MCP endpoints.
    Standard ONDC rate limits apply to synchronization endpoints.
  version: 1.0.0
  contact:
    name: Medusa-MCP Integration Team
    email: <EMAIL>
    url: https://ondc-seller.com/mcp-docs
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://ondc-seller.com/mcp-terms

servers:
  - url: http://localhost:3000/api/mcp
    description: Development MCP server
  - url: http://localhost:9000/admin
    description: Medusa backend server
  - url: https://mcp-api.ondc-seller.com
    description: Production MCP server
  - url: https://staging-mcp.ondc-seller.com
    description: Staging MCP server

security:
  - bearerAuth: []
  - mcpAuth: []

tags:
  - name: Product Sync
    description: Product synchronization between ONDC and Medusa
  - name: Order Fulfillment
    description: Order processing and fulfillment workflows
  - name: Inventory Management
    description: Real-time inventory synchronization
  - name: Customer Sync
    description: Customer data synchronization
  - name: Payment Integration
    description: Payment processing coordination
  - name: Shipping & Logistics
    description: Shipping and logistics management
  - name: Webhooks
    description: Real-time event notifications
  - name: MCP Tools
    description: Model Context Protocol tool endpoints
  - name: Health & Monitoring
    description: System health and monitoring endpoints

paths:
  # Product Synchronization Endpoints
  /sync/products:
    post:
      tags: [Product Sync]
      summary: Synchronize products from ONDC to Medusa
      description: |
        Synchronizes product catalog from ONDC platform to Medusa backend.
        Supports bulk synchronization with conflict resolution and rollback capabilities.
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          description: Tenant identifier for multi-store support
          schema:
            type: string
            example: 'store_001'
        - name: sync-mode
          in: header
          description: Synchronization mode
          schema:
            type: string
            enum: [full, incremental, force]
            default: incremental
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ProductSyncRequest'
      responses:
        '200':
          description: Products synchronized successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProductSyncResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '409':
          description: Synchronization conflict detected
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncConflictError'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /sync/products/status/{syncId}:
    get:
      tags: [Product Sync]
      summary: Get product synchronization status
      description: Retrieve the status of a product synchronization operation
      parameters:
        - name: syncId
          in: path
          required: true
          description: Synchronization operation ID
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Synchronization status retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SyncStatus'
        '404':
          $ref: '#/components/responses/NotFound'

  /sync/products/rollback/{syncId}:
    post:
      tags: [Product Sync]
      summary: Rollback product synchronization
      description: Rollback a product synchronization operation to previous state
      parameters:
        - name: syncId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Synchronization rolled back successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RollbackResponse'

  # Order Fulfillment Endpoints
  /orders/fulfill:
    post:
      tags: [Order Fulfillment]
      summary: Process order fulfillment through MCP
      description: |
        Processes order fulfillment workflow through Medusa-MCP integration.
        Handles inventory allocation, payment processing, and shipping coordination.
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderFulfillmentRequest'
      responses:
        '201':
          description: Order fulfillment initiated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderFulfillmentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '402':
          description: Payment required or failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentError'
        '409':
          description: Inventory conflict or insufficient stock
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryConflictError'

  /orders/{orderId}/status:
    get:
      tags: [Order Fulfillment]
      summary: Get order fulfillment status
      description: Retrieve detailed fulfillment status for an order
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Order status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderStatus'

    put:
      tags: [Order Fulfillment]
      summary: Update order fulfillment status
      description: Update the fulfillment status of an order through MCP
      parameters:
        - name: orderId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/OrderStatusUpdate'
      responses:
        '200':
          description: Order status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OrderStatus'

  # Inventory Management Endpoints
  /inventory/sync:
    post:
      tags: [Inventory Management]
      summary: Synchronize inventory levels
      description: |
        Synchronizes inventory levels between ONDC and Medusa systems.
        Supports real-time updates and conflict resolution.
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InventorySyncRequest'
      responses:
        '200':
          description: Inventory synchronized successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventorySyncResponse'

  /inventory/reserve:
    post:
      tags: [Inventory Management]
      summary: Reserve inventory for order
      description: Reserve inventory items for order processing
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InventoryReservationRequest'
      responses:
        '201':
          description: Inventory reserved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryReservationResponse'

  /inventory/release:
    post:
      tags: [Inventory Management]
      summary: Release reserved inventory
      description: Release previously reserved inventory items
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InventoryReleaseRequest'
      responses:
        '200':
          description: Inventory released successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryReleaseResponse'

  # Customer Synchronization Endpoints
  /customers/sync:
    post:
      tags: [Customer Sync]
      summary: Synchronize customer data
      description: |
        Synchronizes customer profiles between ONDC and Medusa systems.
        Maintains unified customer experience across platforms.
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CustomerSyncRequest'
      responses:
        '200':
          description: Customers synchronized successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CustomerSyncResponse'

  /customers/{customerId}/profile:
    get:
      tags: [Customer Sync]
      summary: Get unified customer profile
      description: Retrieve unified customer profile from both ONDC and Medusa
      parameters:
        - name: customerId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Customer profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UnifiedCustomerProfile'

  # Payment Integration Endpoints
  /payments/process:
    post:
      tags: [Payment Integration]
      summary: Process payment through MCP
      description: |
        Processes payments through integrated payment providers.
        Supports multiple payment methods and currencies.
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentProcessRequest'
      responses:
        '201':
          description: Payment processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentProcessResponse'
        '402':
          description: Payment failed
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentError'

  /payments/{paymentId}/status:
    get:
      tags: [Payment Integration]
      summary: Get payment status
      description: Retrieve current status of a payment transaction
      parameters:
        - name: paymentId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Payment status retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentStatus'

  # Shipping & Logistics Endpoints
  /shipping/calculate:
    post:
      tags: [Shipping & Logistics]
      summary: Calculate shipping costs
      description: Calculate shipping costs for order items across multiple carriers
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShippingCalculationRequest'
      responses:
        '200':
          description: Shipping costs calculated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShippingCalculationResponse'

  /shipping/book:
    post:
      tags: [Shipping & Logistics]
      summary: Book shipping for order
      description: Book shipping service for order fulfillment
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShippingBookingRequest'
      responses:
        '201':
          description: Shipping booked successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShippingBookingResponse'

  /shipping/{shipmentId}/track:
    get:
      tags: [Shipping & Logistics]
      summary: Track shipment
      description: Get real-time tracking information for a shipment
      parameters:
        - name: shipmentId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Tracking information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShipmentTracking'

  # MCP Tools Endpoints
  /tools/execute:
    post:
      tags: [MCP Tools]
      summary: Execute MCP tool
      description: |
        Execute a specific MCP tool with provided parameters.
        Supports all Medusa SDK operations through the MCP protocol.
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/MCPToolRequest'
      responses:
        '200':
          description: Tool executed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MCPToolResponse'
        '400':
          description: Invalid tool or parameters
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MCPToolError'

  /tools/list:
    get:
      tags: [MCP Tools]
      summary: List available MCP tools
      description: Get list of all available MCP tools and their capabilities
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Available tools retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/MCPToolsList'

  # Webhooks Endpoints
  /webhooks/register:
    post:
      tags: [Webhooks]
      summary: Register webhook endpoint
      description: |
        Register a webhook endpoint for real-time event notifications.
        Supports various event types for synchronization and monitoring.
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookRegistrationRequest'
      responses:
        '201':
          description: Webhook registered successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookRegistrationResponse'

  /webhooks/{webhookId}:
    get:
      tags: [Webhooks]
      summary: Get webhook details
      description: Retrieve details of a registered webhook
      parameters:
        - name: webhookId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Webhook details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookDetails'

    put:
      tags: [Webhooks]
      summary: Update webhook configuration
      description: Update webhook endpoint configuration
      parameters:
        - name: webhookId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/WebhookUpdateRequest'
      responses:
        '200':
          description: Webhook updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/WebhookDetails'

    delete:
      tags: [Webhooks]
      summary: Unregister webhook
      description: Remove a registered webhook endpoint
      parameters:
        - name: webhookId
          in: path
          required: true
          schema:
            type: string
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '204':
          description: Webhook unregistered successfully

  # Health & Monitoring Endpoints
  /health:
    get:
      tags: [Health & Monitoring]
      summary: Get system health status
      description: |
        Retrieve comprehensive health status of MCP integration components.
        Includes connectivity to ONDC, Medusa, and external services.
      security: []
      responses:
        '200':
          description: System health status retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
        '503':
          description: Service unavailable
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'

  /health/detailed:
    get:
      tags: [Health & Monitoring]
      summary: Get detailed health metrics
      description: Retrieve detailed health metrics and performance data
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Detailed health metrics retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedHealthMetrics'

  /metrics:
    get:
      tags: [Health & Monitoring]
      summary: Get system metrics
      description: Retrieve system performance and usage metrics
      parameters:
        - name: x-tenant-id
          in: header
          required: true
          schema:
            type: string
        - name: period
          in: query
          description: Time period for metrics
          schema:
            type: string
            enum: [1h, 24h, 7d, 30d]
            default: 24h
      responses:
        '200':
          description: System metrics retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemMetrics'

components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for API authentication

    mcpAuth:
      type: apiKey
      in: header
      name: X-MCP-Token
      description: MCP protocol authentication token

  schemas:
    # Product Synchronization Schemas
    ProductSyncRequest:
      type: object
      required:
        - products
        - source
      properties:
        products:
          type: array
          items:
            $ref: '#/components/schemas/ProductSyncItem'
          description: List of products to synchronize
        source:
          type: string
          enum: [ondc, medusa]
          description: Source system for synchronization
        target:
          type: string
          enum: [ondc, medusa]
          description: Target system for synchronization
        options:
          type: object
          properties:
            skipConflicts:
              type: boolean
              default: false
            updateExisting:
              type: boolean
              default: true
            validateInventory:
              type: boolean
              default: true

    ProductSyncItem:
      type: object
      required:
        - id
        - title
        - handle
      properties:
        id:
          type: string
          description: Product identifier
          example: 'prod_01'
        title:
          type: string
          description: Product title
          example: 'Premium Wireless Headphones'
        handle:
          type: string
          description: URL handle
          example: 'premium-wireless-headphones'
        description:
          type: string
          description: Product description
        thumbnail:
          type: string
          description: Product thumbnail URL
        status:
          type: string
          enum: [draft, proposed, published, rejected]
          default: draft
        variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariant'
        images:
          type: array
          items:
            $ref: '#/components/schemas/ProductImage'
        metadata:
          type: object
          additionalProperties: true
          description: Additional product metadata
        tenant_id:
          type: string
          description: Tenant identifier

    ProductVariant:
      type: object
      required:
        - id
        - title
      properties:
        id:
          type: string
          example: 'var_01'
        title:
          type: string
          example: 'Default Variant'
        sku:
          type: string
          example: 'PWH-001'
        prices:
          type: array
          items:
            $ref: '#/components/schemas/ProductPrice'
        inventory_quantity:
          type: integer
          minimum: 0
          example: 100
        weight:
          type: number
          description: Weight in grams
        dimensions:
          $ref: '#/components/schemas/ProductDimensions'

    ProductPrice:
      type: object
      required:
        - amount
        - currency_code
      properties:
        amount:
          type: integer
          description: Price in smallest currency unit
          example: 15999
        currency_code:
          type: string
          example: 'inr'
        region_id:
          type: string
          description: Region identifier

    ProductImage:
      type: object
      required:
        - id
        - url
      properties:
        id:
          type: string
          example: 'img_01'
        url:
          type: string
          format: uri
          example: 'https://example.com/image.jpg'
        alt_text:
          type: string
          description: Alternative text for accessibility

    ProductDimensions:
      type: object
      properties:
        length:
          type: number
          description: Length in cm
        width:
          type: number
          description: Width in cm
        height:
          type: number
          description: Height in cm

    ProductSyncResponse:
      type: object
      properties:
        sync_id:
          type: string
          description: Synchronization operation ID
          example: 'sync_12345'
        status:
          type: string
          enum: [initiated, in_progress, completed, failed]
          example: 'initiated'
        total_products:
          type: integer
          example: 150
        processed_products:
          type: integer
          example: 0
        successful_syncs:
          type: integer
          example: 0
        failed_syncs:
          type: integer
          example: 0
        conflicts:
          type: array
          items:
            $ref: '#/components/schemas/SyncConflict'
        estimated_completion:
          type: string
          format: date-time
          example: '2024-01-15T14:30:00Z'
        created_at:
          type: string
          format: date-time
          example: '2024-01-15T14:00:00Z'

    SyncConflict:
      type: object
      properties:
        product_id:
          type: string
          example: 'prod_01'
        conflict_type:
          type: string
          enum: [duplicate_handle, price_mismatch, inventory_conflict, metadata_conflict]
          example: 'duplicate_handle'
        description:
          type: string
          example: 'Product handle already exists in target system'
        resolution_options:
          type: array
          items:
            type: string
          example: ['skip', 'overwrite', 'merge', 'rename']
        current_value:
          type: object
          description: Current value in target system
        new_value:
          type: object
          description: New value from source system

    SyncStatus:
      type: object
      properties:
        sync_id:
          type: string
          example: 'sync_12345'
        status:
          type: string
          enum: [initiated, in_progress, completed, failed, cancelled]
          example: 'in_progress'
        progress:
          type: object
          properties:
            total:
              type: integer
              example: 150
            processed:
              type: integer
              example: 75
            successful:
              type: integer
              example: 70
            failed:
              type: integer
              example: 5
            percentage:
              type: number
              format: float
              example: 50.0
        errors:
          type: array
          items:
            $ref: '#/components/schemas/SyncError'
        started_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time
          nullable: true
        estimated_completion:
          type: string
          format: date-time
          nullable: true

    SyncError:
      type: object
      properties:
        product_id:
          type: string
          example: 'prod_01'
        error_code:
          type: string
          example: 'VALIDATION_FAILED'
        error_message:
          type: string
          example: 'Product title is required'
        timestamp:
          type: string
          format: date-time

    # Order Fulfillment Schemas
    OrderFulfillmentRequest:
      type: object
      required:
        - order_id
        - items
      properties:
        order_id:
          type: string
          example: 'order_01'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderFulfillmentItem'
        shipping_address:
          $ref: '#/components/schemas/Address'
        billing_address:
          $ref: '#/components/schemas/Address'
        payment_method:
          type: string
          enum: [card, upi, netbanking, wallet, cod]
          example: 'upi'
        shipping_method:
          type: string
          example: 'standard'
        notes:
          type: string
          description: Special instructions

    OrderFulfillmentItem:
      type: object
      required:
        - variant_id
        - quantity
      properties:
        variant_id:
          type: string
          example: 'var_01'
        quantity:
          type: integer
          minimum: 1
          example: 2
        unit_price:
          type: integer
          description: Price per unit in smallest currency unit
          example: 15999

    OrderFulfillmentResponse:
      type: object
      properties:
        fulfillment_id:
          type: string
          example: 'fulfill_12345'
        order_id:
          type: string
          example: 'order_01'
        status:
          type: string
          enum: [pending, processing, shipped, delivered, cancelled]
          example: 'pending'
        tracking_number:
          type: string
          example: 'TRK123456789'
        estimated_delivery:
          type: string
          format: date-time
          example: '2024-01-20T18:00:00Z'
        payment_status:
          type: string
          enum: [pending, authorized, captured, failed, refunded]
          example: 'authorized'
        created_at:
          type: string
          format: date-time

    OrderStatus:
      type: object
      properties:
        order_id:
          type: string
          example: 'order_01'
        fulfillment_status:
          type: string
          enum: [pending, processing, shipped, delivered, cancelled]
          example: 'processing'
        payment_status:
          type: string
          enum: [pending, authorized, captured, failed, refunded]
          example: 'captured'
        shipping_status:
          type: string
          enum: [not_shipped, shipped, in_transit, delivered, returned]
          example: 'shipped'
        tracking_info:
          $ref: '#/components/schemas/TrackingInfo'
        timeline:
          type: array
          items:
            $ref: '#/components/schemas/OrderStatusEvent'
        updated_at:
          type: string
          format: date-time

    TrackingInfo:
      type: object
      properties:
        tracking_number:
          type: string
          example: 'TRK123456789'
        carrier:
          type: string
          example: 'BlueDart'
        current_location:
          type: string
          example: 'Mumbai Distribution Center'
        estimated_delivery:
          type: string
          format: date-time
        delivery_attempts:
          type: integer
          example: 0

    OrderStatusEvent:
      type: object
      properties:
        status:
          type: string
          example: 'shipped'
        description:
          type: string
          example: 'Order has been shipped'
        timestamp:
          type: string
          format: date-time
        location:
          type: string
          example: 'Mumbai Warehouse'

    # Common Schemas
    Address:
      type: object
      required:
        - first_name
        - last_name
        - address_1
        - city
        - postal_code
        - country_code
      properties:
        first_name:
          type: string
          example: 'John'
        last_name:
          type: string
          example: 'Doe'
        company:
          type: string
          example: 'Acme Corp'
        address_1:
          type: string
          example: '123 Main Street'
        address_2:
          type: string
          example: 'Apt 4B'
        city:
          type: string
          example: 'Mumbai'
        province:
          type: string
          example: 'Maharashtra'
        postal_code:
          type: string
          example: '400001'
        country_code:
          type: string
          example: 'IN'
        phone:
          type: string
          example: '+91 9876543210'

    # Error Response Schemas
    SyncConflictError:
      type: object
      properties:
        error:
          type: string
          example: 'Synchronization conflict detected'
        conflicts:
          type: array
          items:
            $ref: '#/components/schemas/SyncConflict'
        resolution_required:
          type: boolean
          example: true
        sync_id:
          type: string
          example: 'sync_12345'

    PaymentError:
      type: object
      properties:
        error:
          type: string
          example: 'Payment processing failed'
        payment_id:
          type: string
          example: 'pay_12345'
        error_code:
          type: string
          example: 'INSUFFICIENT_FUNDS'
        provider_error:
          type: string
          example: 'Transaction declined by bank'
        retry_possible:
          type: boolean
          example: true

    InventoryConflictError:
      type: object
      properties:
        error:
          type: string
          example: 'Insufficient inventory'
        conflicts:
          type: array
          items:
            type: object
            properties:
              variant_id:
                type: string
              requested_quantity:
                type: integer
              available_quantity:
                type: integer
        suggested_alternatives:
          type: array
          items:
            type: string
          description: Alternative variant IDs with sufficient stock

    # Health & Monitoring Schemas
    HealthStatus:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
          example: 'healthy'
        timestamp:
          type: string
          format: date-time
        services:
          type: object
          properties:
            ondc_api:
              $ref: '#/components/schemas/ServiceHealth'
            medusa_backend:
              $ref: '#/components/schemas/ServiceHealth'
            mcp_server:
              $ref: '#/components/schemas/ServiceHealth'
            database:
              $ref: '#/components/schemas/ServiceHealth'
            redis:
              $ref: '#/components/schemas/ServiceHealth'
        version:
          type: string
          example: '1.0.0'
        uptime:
          type: integer
          description: Uptime in seconds
          example: 86400

    ServiceHealth:
      type: object
      properties:
        status:
          type: string
          enum: [healthy, degraded, unhealthy]
          example: 'healthy'
        response_time:
          type: number
          description: Response time in milliseconds
          example: 45.2
        last_check:
          type: string
          format: date-time
        error_message:
          type: string
          nullable: true

    DetailedHealthMetrics:
      type: object
      properties:
        system:
          type: object
          properties:
            cpu_usage:
              type: number
              example: 45.2
            memory_usage:
              type: number
              example: 67.8
            disk_usage:
              type: number
              example: 23.1
        api_metrics:
          type: object
          properties:
            requests_per_minute:
              type: integer
              example: 150
            average_response_time:
              type: number
              example: 120.5
            error_rate:
              type: number
              example: 0.02
        sync_metrics:
          type: object
          properties:
            active_syncs:
              type: integer
              example: 3
            completed_syncs_24h:
              type: integer
              example: 45
            failed_syncs_24h:
              type: integer
              example: 2

    SystemMetrics:
      type: object
      properties:
        period:
          type: string
          example: '24h'
        api_calls:
          type: object
          properties:
            total:
              type: integer
              example: 12450
            successful:
              type: integer
              example: 12398
            failed:
              type: integer
              example: 52
            by_endpoint:
              type: object
              additionalProperties:
                type: integer
        sync_operations:
          type: object
          properties:
            product_syncs:
              type: integer
              example: 45
            inventory_syncs:
              type: integer
              example: 120
            customer_syncs:
              type: integer
              example: 23
        performance:
          type: object
          properties:
            average_response_time:
              type: number
              example: 145.2
            p95_response_time:
              type: number
              example: 450.1
            throughput:
              type: number
              example: 8.5

    # MCP Tool Schemas
    MCPToolRequest:
      type: object
      required:
        - tool_name
        - parameters
      properties:
        tool_name:
          type: string
          example: 'medusa_store_list_products'
          description: Name of the MCP tool to execute
        parameters:
          type: object
          additionalProperties: true
          description: Tool-specific parameters
        context:
          type: object
          properties:
            tenant_id:
              type: string
            user_id:
              type: string
            session_id:
              type: string

    MCPToolResponse:
      type: object
      properties:
        tool_name:
          type: string
          example: 'medusa_store_list_products'
        execution_id:
          type: string
          example: 'exec_12345'
        status:
          type: string
          enum: [success, error, timeout]
          example: 'success'
        result:
          type: object
          additionalProperties: true
          description: Tool execution result
        execution_time:
          type: number
          description: Execution time in milliseconds
          example: 234.5
        timestamp:
          type: string
          format: date-time

    MCPToolError:
      type: object
      properties:
        error:
          type: string
          example: 'Tool execution failed'
        tool_name:
          type: string
          example: 'medusa_store_list_products'
        error_code:
          type: string
          example: 'INVALID_PARAMETERS'
        details:
          type: string
          example: "Required parameter 'limit' is missing"

    MCPToolsList:
      type: object
      properties:
        tools:
          type: array
          items:
            $ref: '#/components/schemas/MCPToolDefinition'
        total_count:
          type: integer
          example: 25

    MCPToolDefinition:
      type: object
      properties:
        name:
          type: string
          example: 'medusa_store_list_products'
        description:
          type: string
          example: 'List products from Medusa store'
        category:
          type: string
          enum: [store, admin, sync, utility]
          example: 'store'
        parameters:
          type: object
          description: JSON Schema for tool parameters
        examples:
          type: array
          items:
            type: object

  responses:
    BadRequest:
      description: Bad request - Invalid input parameters
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: 'Invalid request parameters'
              details:
                type: string
                example: "The 'tenant_id' header is required"

    Unauthorized:
      description: Unauthorized - Authentication required
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: 'Unauthorized'
              details:
                type: string
                example: 'Valid authentication token required'

    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: 'Resource not found'
              details:
                type: string
                example: 'Synchronization operation not found'

    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              error:
                type: string
                example: 'Internal server error'
              details:
                type: string
                example: 'An unexpected error occurred while processing your request'
