{"info": {"name": "Medusa-MCP Integration API", "description": "Complete API collection for Medusa Model Context Protocol integration with ONDC Seller Platform", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "mcp_base_url", "value": "http://localhost:3000/api/mcp", "type": "string"}, {"key": "medusa_base_url", "value": "http://localhost:9000/admin", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}, {"key": "mcp_token", "value": "your_mcp_token_here", "type": "string"}, {"key": "tenant_id", "value": "default", "type": "string"}], "item": [{"name": "Product Synchronization", "item": [{"name": "Sync Products ONDC to Medusa", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}, {"key": "sync-mode", "value": "incremental"}], "body": {"mode": "raw", "raw": "{\n  \"products\": [\n    {\n      \"id\": \"prod_01\",\n      \"title\": \"Premium Wireless Headphones\",\n      \"handle\": \"premium-wireless-headphones\",\n      \"description\": \"High-quality wireless headphones with noise cancellation\",\n      \"status\": \"published\",\n      \"variants\": [\n        {\n          \"id\": \"var_01\",\n          \"title\": \"Default Variant\",\n          \"sku\": \"PWH-001\",\n          \"prices\": [\n            {\n              \"amount\": 15999,\n              \"currency_code\": \"inr\"\n            }\n          ],\n          \"inventory_quantity\": 100\n        }\n      ],\n      \"tenant_id\": \"{{tenant_id}}\"\n    }\n  ],\n  \"source\": \"ondc\",\n  \"target\": \"medusa\",\n  \"options\": {\n    \"skipConflicts\": false,\n    \"updateExisting\": true,\n    \"validateInventory\": true\n  }\n}"}, "url": {"raw": "{{mcp_base_url}}/sync/products", "host": ["{{mcp_base_url}}"], "path": ["sync", "products"]}}}, {"name": "Get Sync Status", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}"}], "url": {"raw": "{{mcp_base_url}}/sync/products/status/sync_12345", "host": ["{{mcp_base_url}}"], "path": ["sync", "products", "status", "sync_12345"]}}}, {"name": "Roll<PERSON> Sync", "request": {"method": "POST", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}"}], "url": {"raw": "{{mcp_base_url}}/sync/products/rollback/sync_12345", "host": ["{{mcp_base_url}}"], "path": ["sync", "products", "rollback", "sync_12345"]}}}]}, {"name": "Order Fulfillment", "item": [{"name": "Process Order Fulfillment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"order_01\",\n  \"items\": [\n    {\n      \"variant_id\": \"var_01\",\n      \"quantity\": 2,\n      \"unit_price\": 15999\n    }\n  ],\n  \"shipping_address\": {\n    \"first_name\": \"<PERSON>\",\n    \"last_name\": \"<PERSON><PERSON>\",\n    \"address_1\": \"123 Main Street\",\n    \"city\": \"Mumbai\",\n    \"province\": \"Maharashtra\",\n    \"postal_code\": \"400001\",\n    \"country_code\": \"IN\",\n    \"phone\": \"+91 9876543210\"\n  },\n  \"payment_method\": \"upi\",\n  \"shipping_method\": \"standard\"\n}"}, "url": {"raw": "{{mcp_base_url}}/orders/fulfill", "host": ["{{mcp_base_url}}"], "path": ["orders", "fulfill"]}}}, {"name": "Get Order Status", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}"}], "url": {"raw": "{{mcp_base_url}}/orders/order_01/status", "host": ["{{mcp_base_url}}"], "path": ["orders", "order_01", "status"]}}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"fulfillment_status\": \"processing\",\n  \"payment_status\": \"captured\",\n  \"notes\": \"Order is being processed\"\n}"}, "url": {"raw": "{{mcp_base_url}}/orders/order_01/status", "host": ["{{mcp_base_url}}"], "path": ["orders", "order_01", "status"]}}}]}, {"name": "Inventory Management", "item": [{"name": "Sync Inventory", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"items\": [\n    {\n      \"variant_id\": \"var_01\",\n      \"quantity\": 150,\n      \"location_id\": \"loc_warehouse_01\"\n    },\n    {\n      \"variant_id\": \"var_02\",\n      \"quantity\": 75,\n      \"location_id\": \"loc_warehouse_01\"\n    }\n  ],\n  \"sync_mode\": \"absolute\"\n}"}, "url": {"raw": "{{mcp_base_url}}/inventory/sync", "host": ["{{mcp_base_url}}"], "path": ["inventory", "sync"]}}}, {"name": "Reserve Inventory", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"order_id\": \"order_01\",\n  \"items\": [\n    {\n      \"variant_id\": \"var_01\",\n      \"quantity\": 2,\n      \"location_id\": \"loc_warehouse_01\"\n    }\n  ],\n  \"reservation_duration\": 1800\n}"}, "url": {"raw": "{{mcp_base_url}}/inventory/reserve", "host": ["{{mcp_base_url}}"], "path": ["inventory", "reserve"]}}}, {"name": "Release Inventory", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"reservation_id\": \"res_12345\",\n  \"reason\": \"order_cancelled\"\n}"}, "url": {"raw": "{{mcp_base_url}}/inventory/release", "host": ["{{mcp_base_url}}"], "path": ["inventory", "release"]}}}]}, {"name": "Customer Synchronization", "item": [{"name": "Sync Customers", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}], "body": {"mode": "raw", "raw": "{\n  \"customers\": [\n    {\n      \"id\": \"cust_01\",\n      \"email\": \"<EMAIL>\",\n      \"first_name\": \"<PERSON>\",\n      \"last_name\": \"<PERSON><PERSON>\",\n      \"phone\": \"+91 9876543210\",\n      \"addresses\": [\n        {\n          \"first_name\": \"<PERSON>\",\n          \"last_name\": \"<PERSON><PERSON>\",\n          \"address_1\": \"123 Main Street\",\n          \"city\": \"Mumbai\",\n          \"province\": \"Maharashtra\",\n          \"postal_code\": \"400001\",\n          \"country_code\": \"IN\"\n        }\n      ]\n    }\n  ],\n  \"source\": \"ondc\",\n  \"target\": \"medusa\"\n}"}, "url": {"raw": "{{mcp_base_url}}/customers/sync", "host": ["{{mcp_base_url}}"], "path": ["customers", "sync"]}}}, {"name": "Get Unified Customer Profile", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}"}], "url": {"raw": "{{mcp_base_url}}/customers/cust_01/profile", "host": ["{{mcp_base_url}}"], "path": ["customers", "cust_01", "profile"]}}}]}, {"name": "MCP Tools", "item": [{"name": "Execute MCP Tool", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "x-tenant-id", "value": "{{tenant_id}}"}, {"key": "X-MCP-Token", "value": "{{mcp_token}}"}], "body": {"mode": "raw", "raw": "{\n  \"tool_name\": \"medusa_store_list_products\",\n  \"parameters\": {\n    \"limit\": 10,\n    \"offset\": 0,\n    \"expand\": \"variants,images\"\n  },\n  \"context\": {\n    \"tenant_id\": \"{{tenant_id}}\",\n    \"user_id\": \"user_01\",\n    \"session_id\": \"session_12345\"\n  }\n}"}, "url": {"raw": "{{mcp_base_url}}/tools/execute", "host": ["{{mcp_base_url}}"], "path": ["tools", "execute"]}}}, {"name": "List Available MCP Tools", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}"}], "url": {"raw": "{{mcp_base_url}}/tools/list", "host": ["{{mcp_base_url}}"], "path": ["tools", "list"]}}}]}, {"name": "Health & Monitoring", "item": [{"name": "System Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{mcp_base_url}}/health", "host": ["{{mcp_base_url}}"], "path": ["health"]}}, "auth": {"type": "<PERSON><PERSON><PERSON>"}}, {"name": "Detailed Health Metrics", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}"}], "url": {"raw": "{{mcp_base_url}}/health/detailed", "host": ["{{mcp_base_url}}"], "path": ["health", "detailed"]}}}, {"name": "System Metrics", "request": {"method": "GET", "header": [{"key": "x-tenant-id", "value": "{{tenant_id}}"}], "url": {"raw": "{{mcp_base_url}}/metrics?period=24h", "host": ["{{mcp_base_url}}"], "path": ["metrics"], "query": [{"key": "period", "value": "24h"}]}}}]}]}