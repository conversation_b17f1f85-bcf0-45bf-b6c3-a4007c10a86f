# Medusa API Integration

This document describes the integration between the Next.js frontend and the Medusa backend in the ONDC Seller Platform.

## Overview

The frontend communicates with the Medusa backend through a set of API adapter functions. These functions handle:

- Authentication
- Multi-tenancy
- Error handling
- Retrying failed requests
- Type safety

## API Client

The main API client is defined in `src/lib/api.ts`. It creates an Axios instance with the Medusa backend URL and adds interceptors for authentication and multi-tenancy.

```typescript
// Create axios instance for Medusa API
export const medusaApi = axios.create({
  baseURL: MEDUSA_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  timeout: 10000, // 10 seconds
});
```

## Multi-Tenancy Support

Every request to the Medusa backend includes the `x-tenant-id` header to support multi-tenancy. This is handled by a request interceptor:

```typescript
// Add request interceptor for authentication and multi-tenancy
const addAuthAndTenantHeaders = (config: AxiosRequestConfig) => {
  // Add tenant ID header to every request
  config.headers = config.headers || {};
  config.headers['x-tenant-id'] = getCurrentTenantId();
  
  // Authentication logic...
  
  return config;
};

// Add request interceptor to both API instances
medusaApi.interceptors.request.use(addAuthAndTenantHeaders, error => Promise.reject(error));
```

## Key API Functions

### fetchProducts

Fetches products from the Medusa backend.

```typescript
export const fetchProducts = async (params?: ProductQueryParams): Promise<ProductResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.get('/store/products', { params });
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error fetching products:', apiError);
      throw apiError;
    }
  });
};
```

### createOrder

Creates an order in the Medusa backend.

```typescript
export const createOrder = async (orderData: OrderCreateData): Promise<OrderResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.post('/store/orders', orderData);
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error creating order:', apiError);
      throw apiError;
    }
  });
};
```

### updateInventory

Updates inventory in the Medusa backend.

```typescript
export const updateInventory = async (
  inventoryId: string,
  updateData: InventoryUpdateData
): Promise<InventoryLevelResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.post(`/admin/inventories/${inventoryId}`, updateData);
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error updating inventory:', apiError);
      throw apiError;
    }
  });
};
```

## API Utilities

### retryWithBackoff

A utility function that retries failed requests with exponential backoff.

```typescript
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  initialDelay = 300
): Promise<T> => {
  let retries = 0;
  
  while (true) {
    try {
      return await fn();
    } catch (error) {
      if (retries >= maxRetries) {
        throw error;
      }
      
      const delay = initialDelay * Math.pow(2, retries);
      await new Promise(resolve => setTimeout(resolve, delay));
      retries++;
    }
  }
};
```

### handleApiError

A utility function that standardizes error handling for API requests.

```typescript
export const handleApiError = (error: any): ApiError => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    return {
      message: error.response.data.message || 'An error occurred',
      code: error.response.data.code,
      status: error.response.status,
      details: error.response.data.details,
    };
  } else if (error.request) {
    // The request was made but no response was received
    return {
      message: 'No response received from server',
      code: 'NETWORK_ERROR',
    };
  } else {
    // Something happened in setting up the request that triggered an Error
    return {
      message: error.message || 'An error occurred',
      code: 'REQUEST_SETUP_ERROR',
    };
  }
};
```

## Testing

You can test the API integration using the test script in `src/tests/api-test.ts`. This script tests the `fetchProducts`, `createOrder`, and `updateInventory` functions.

To run the test:

```bash
npx ts-node src/tests/api-test.ts
```

## Troubleshooting

If you encounter issues with the API integration:

1. Check that the Medusa backend is running and accessible
2. Verify that the `NEXT_PUBLIC_MEDUSA_URL` environment variable is set correctly
3. Check the browser console for error messages
4. Verify that the tenant ID is being set correctly
5. Check the network tab in the browser developer tools to see the actual requests and responses
