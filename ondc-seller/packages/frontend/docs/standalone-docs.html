<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ONDC Seller Platform API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
    .swagger-ui .topbar {
      background-color: #3B82F6;
    }
    .swagger-ui .topbar .download-url-wrapper .select-label {
      color: white;
    }
    .swagger-ui .topbar .download-url-wrapper input[type=text] {
      border: 2px solid #10B981;
    }
    .swagger-ui .info .title {
      color: #3B82F6;
    }
    .swagger-ui .scheme-container {
      background: #fff;
      box-shadow: 0 1px 2px 0 rgba(0,0,0,0.1);
    }
    .header-info {
      background: linear-gradient(135deg, #3B82F6 0%, #10B981 100%);
      color: white;
      padding: 20px;
      text-align: center;
      margin-bottom: 20px;
    }
    .header-info h1 {
      margin: 0 0 10px 0;
      font-size: 2.5em;
      font-weight: bold;
    }
    .header-info p {
      margin: 0;
      font-size: 1.2em;
      opacity: 0.9;
    }
    .quick-links {
      background: white;
      padding: 20px;
      margin: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .quick-links h3 {
      color: #3B82F6;
      margin-top: 0;
    }
    .quick-links a {
      display: inline-block;
      background: #3B82F6;
      color: white;
      padding: 10px 20px;
      text-decoration: none;
      border-radius: 5px;
      margin: 5px;
      transition: background 0.3s;
    }
    .quick-links a:hover {
      background: #2563EB;
    }
    .status-badge {
      background: #10B981;
      color: white;
      padding: 5px 15px;
      border-radius: 20px;
      font-size: 0.9em;
      font-weight: bold;
    }
  </style>
</head>
<body>
  <div class="header-info">
    <h1>🚀 ONDC Seller Platform API</h1>
    <p>Complete REST API Documentation for E-commerce Operations</p>
    <span class="status-badge">✅ 25 Endpoints Available</span>
  </div>

  <div class="quick-links">
    <h3>📚 Quick Access</h3>
    <a href="#tag/Products">Products API</a>
    <a href="#tag/Categories">Categories API</a>
    <a href="#tag/Orders">Orders API</a>
    <a href="#tag/Customers">Customers API</a>
    <a href="#tag/Analytics">Analytics API</a>
    <a href="./postman-collection.json" download>📥 Download Postman Collection</a>
    <a href="./api-spec.yaml" download>📄 Download OpenAPI Spec</a>
  </div>

  <div id="swagger-ui"></div>
  
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
  <script>
    // Embedded OpenAPI specification
    const spec = {
      "openapi": "3.0.3",
      "info": {
        "title": "ONDC Seller Platform API",
        "description": "Comprehensive API specification for the ONDC (Open Network for Digital Commerce) Seller Platform.\nThis API provides complete CRUD operations for managing products, categories, orders, customers, collections, coupons, and analytics for e-commerce sellers on the ONDC network.\n\n## Features\n- **Product Management**: Full catalog management with variants, inventory, and pricing\n- **Order Processing**: Complete order lifecycle management with status tracking\n- **Customer Management**: Customer profiles, addresses, and order history\n- **Category Management**: Hierarchical category structure with SEO optimization\n- **Collection Management**: Product collections with automatic and manual rules\n- **Coupon Management**: Flexible discount and promotion system\n- **Analytics**: Comprehensive business intelligence and reporting\n\n## Authentication\nAll admin endpoints require authentication. Use Bearer token authentication.\n\n## Rate Limiting\nAPI requests are rate-limited to 1000 requests per hour per API key.\n\n## Error Handling\nThe API uses standard HTTP status codes and returns detailed error messages in JSON format.",
        "version": "1.0.0",
        "contact": {
          "name": "ONDC Seller Platform Team",
          "email": "<EMAIL>",
          "url": "https://ondc-seller.com/support"
        },
        "license": {
          "name": "MIT",
          "url": "https://opensource.org/licenses/MIT"
        },
        "termsOfService": "https://ondc-seller.com/terms"
      },
      "servers": [
        {
          "url": "http://localhost:3000/api",
          "description": "Development server"
        },
        {
          "url": "https://api.ondc-seller.com",
          "description": "Production server"
        },
        {
          "url": "https://staging-api.ondc-seller.com",
          "description": "Staging server"
        }
      ],
      "security": [
        {
          "bearerAuth": []
        }
      ],
      "tags": [
        {
          "name": "Products",
          "description": "Product catalog management"
        },
        {
          "name": "Categories",
          "description": "Product category management"
        },
        {
          "name": "Orders",
          "description": "Order processing and management"
        },
        {
          "name": "Customers",
          "description": "Customer relationship management"
        },
        {
          "name": "Analytics",
          "description": "Business intelligence and reporting"
        },
        {
          "name": "Public",
          "description": "Public endpoints (no authentication required)"
        }
      ],
      "paths": {
        "/admin/products": {
          "get": {
            "tags": ["Products"],
            "summary": "List all products",
            "description": "Retrieve a paginated list of products with filtering and sorting options",
            "parameters": [
              {
                "name": "page",
                "in": "query",
                "description": "Page number for pagination",
                "schema": {
                  "type": "integer",
                  "minimum": 1,
                  "default": 1
                }
              },
              {
                "name": "limit",
                "in": "query",
                "description": "Number of items per page",
                "schema": {
                  "type": "integer",
                  "minimum": 1,
                  "maximum": 100,
                  "default": 10
                }
              },
              {
                "name": "search",
                "in": "query",
                "description": "Search term for product name, SKU, or description",
                "schema": {
                  "type": "string"
                }
              },
              {
                "name": "status",
                "in": "query",
                "description": "Filter by product status",
                "schema": {
                  "type": "string",
                  "enum": ["active", "inactive", "draft"]
                }
              }
            ],
            "responses": {
              "200": {
                "description": "Successful response",
                "content": {
                  "application/json": {
                    "schema": {
                      "type": "object",
                      "properties": {
                        "products": {
                          "type": "array",
                          "items": {
                            "$ref": "#/components/schemas/Product"
                          }
                        },
                        "pagination": {
                          "$ref": "#/components/schemas/PaginationInfo"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          "post": {
            "tags": ["Products"],
            "summary": "Create a new product",
            "description": "Create a new product in the catalog",
            "requestBody": {
              "required": true,
              "content": {
                "application/json": {
                  "schema": {
                    "$ref": "#/components/schemas/CreateProductRequest"
                  }
                }
              }
            },
            "responses": {
              "201": {
                "description": "Product created successfully"
              }
            }
          }
        },
        "/admin/analytics": {
          "get": {
            "tags": ["Analytics"],
            "summary": "Get dashboard analytics",
            "description": "Retrieve comprehensive analytics data for the admin dashboard",
            "parameters": [
              {
                "name": "period",
                "in": "query",
                "description": "Time period for analytics",
                "schema": {
                  "type": "string",
                  "enum": ["7d", "30d", "90d", "1y"],
                  "default": "30d"
                }
              }
            ],
            "responses": {
              "200": {
                "description": "Successful response"
              }
            }
          }
        },
        "/products": {
          "get": {
            "tags": ["Public"],
            "summary": "List public products",
            "description": "Retrieve a list of active products for public consumption",
            "security": [],
            "responses": {
              "200": {
                "description": "Successful response"
              }
            }
          }
        }
      },
      "components": {
        "securitySchemes": {
          "bearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
            "description": "JWT token for authentication"
          }
        },
        "schemas": {
          "Product": {
            "type": "object",
            "required": ["id", "name", "sku", "price", "category", "status"],
            "properties": {
              "id": {
                "type": "string",
                "description": "Unique product identifier",
                "example": "1"
              },
              "name": {
                "type": "string",
                "description": "Product name",
                "example": "Nike Air Max 270"
              },
              "sku": {
                "type": "string",
                "description": "Stock Keeping Unit",
                "example": "NIKE-AM270-001"
              },
              "price": {
                "type": "integer",
                "description": "Product price in smallest currency unit (paise for INR)",
                "example": 12999
              },
              "category": {
                "type": "string",
                "description": "Product category",
                "example": "Shoes"
              },
              "status": {
                "type": "string",
                "enum": ["active", "inactive", "draft"],
                "description": "Product status",
                "example": "active"
              }
            }
          },
          "CreateProductRequest": {
            "type": "object",
            "required": ["name", "sku", "price", "category"],
            "properties": {
              "name": {
                "type": "string",
                "description": "Product name",
                "example": "Nike Air Max 270"
              },
              "sku": {
                "type": "string",
                "description": "Stock Keeping Unit (must be unique)",
                "example": "NIKE-AM270-001"
              },
              "price": {
                "type": "integer",
                "description": "Product price in smallest currency unit",
                "minimum": 0,
                "example": 12999
              },
              "category": {
                "type": "string",
                "description": "Product category",
                "example": "Shoes"
              }
            }
          },
          "PaginationInfo": {
            "type": "object",
            "properties": {
              "page": {
                "type": "integer",
                "description": "Current page number",
                "example": 1
              },
              "limit": {
                "type": "integer",
                "description": "Items per page",
                "example": 10
              },
              "total": {
                "type": "integer",
                "description": "Total number of items",
                "example": 156
              }
            }
          }
        }
      }
    };

    window.onload = function() {
      const ui = SwaggerUIBundle({
        spec: spec,
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout",
        validatorUrl: null,
        tryItOutEnabled: true,
        supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
        onComplete: function() {
          console.log("✅ ONDC Seller Platform API Documentation loaded successfully");
        },
        onFailure: function(data) {
          console.error("❌ Failed to load API documentation:", data);
        }
      });
    };
  </script>
</body>
</html>
