# TDD Implementation Guide
## ONDC E-Commerce Platform - Practical Testing Examples

### Quick Start Guide

This document provides practical examples and step-by-step implementation of TDD for the ONDC e-commerce platform.

---

## 1. Setting Up the Testing Environment

### 1.1 Install Testing Dependencies

```bash
# Core testing libraries
npm install --save-dev jest @testing-library/react @testing-library/jest-dom
npm install --save-dev @testing-library/user-event msw

# TypeScript support
npm install --save-dev @types/jest ts-jest

# E2E testing
npm install --save-dev @playwright/test

# Additional utilities
npm install --save-dev jest-environment-jsdom
```

### 1.2 Jest Configuration

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/index.tsx',
    '!src/reportWebVitals.ts',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
};
```

### 1.3 Test Setup File

```typescript
// src/setupTests.ts
import '@testing-library/jest-dom';
import { server } from './mocks/server';

// Establish API mocking before all tests
beforeAll(() => server.listen());

// Reset any request handlers that we may add during the tests
afterEach(() => server.resetHandlers());

// Clean up after the tests are finished
afterAll(() => server.close());

// Mock Next.js router
jest.mock('next/router', () => ({
  useRouter() {
    return {
      route: '/',
      pathname: '/',
      query: '',
      asPath: '',
      push: jest.fn(),
      pop: jest.fn(),
      reload: jest.fn(),
      back: jest.fn(),
      prefetch: jest.fn().mockResolvedValue(undefined),
      beforePopState: jest.fn(),
      events: {
        on: jest.fn(),
        off: jest.fn(),
        emit: jest.fn(),
      },
    };
  },
}));
```

---

## 2. TDD Example: Product Card Component

### Step 1: Write the Test (Red Phase)

```typescript
// src/components/ProductCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { ProductCard } from './ProductCard';

const mockProduct = {
  id: '1',
  name: 'Premium Wireless Headphones',
  price: 299.99,
  originalPrice: 399.99,
  image: '/images/products/headphones.jpg',
  rating: 4.8,
  reviewCount: 324,
  category: 'Electronics',
  isWishlisted: false,
  badge: 'Best Seller',
  inStock: true,
};

describe('ProductCard', () => {
  it('should display product information correctly', () => {
    // Red: This test will fail initially
    render(<ProductCard product={mockProduct} />);
    
    expect(screen.getByText('Premium Wireless Headphones')).toBeInTheDocument();
    expect(screen.getByText('$299.99')).toBeInTheDocument();
    expect(screen.getByText('$399.99')).toBeInTheDocument();
    expect(screen.getByText('Best Seller')).toBeInTheDocument();
    expect(screen.getByAltText('Premium Wireless Headphones')).toBeInTheDocument();
  });

  it('should show rating and review count', () => {
    render(<ProductCard product={mockProduct} />);
    
    expect(screen.getByText('4.8')).toBeInTheDocument();
    expect(screen.getByText('(324 reviews)')).toBeInTheDocument();
  });

  it('should handle add to cart click', () => {
    const mockOnAddToCart = jest.fn();
    render(<ProductCard product={mockProduct} onAddToCart={mockOnAddToCart} />);
    
    const addToCartButton = screen.getByText('Add to Cart');
    fireEvent.click(addToCartButton);
    
    expect(mockOnAddToCart).toHaveBeenCalledWith(mockProduct);
  });

  it('should show out of stock state', () => {
    const outOfStockProduct = { ...mockProduct, inStock: false };
    render(<ProductCard product={outOfStockProduct} />);
    
    expect(screen.getByText('Out of Stock')).toBeInTheDocument();
    expect(screen.getByText('Add to Cart')).toBeDisabled();
  });
});
```

### Step 2: Implement the Component (Green Phase)

```typescript
// src/components/ProductCard.tsx
import React from 'react';
import Image from 'next/image';
import { StarIcon } from '@heroicons/react/24/solid';

interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviewCount: number;
  category: string;
  isWishlisted: boolean;
  badge?: string;
  inStock: boolean;
}

interface ProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  onAddToCart 
}) => {
  const handleAddToCart = () => {
    if (onAddToCart && product.inStock) {
      onAddToCart(product);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow">
      {/* Product Image */}
      <div className="relative h-48">
        <Image
          src={product.image}
          alt={product.name}
          fill
          className="object-cover"
        />
        {product.badge && (
          <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs rounded">
            {product.badge}
          </span>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-2">{product.name}</h3>
        
        {/* Rating */}
        <div className="flex items-center mb-2">
          <StarIcon className="h-4 w-4 text-yellow-400" />
          <span className="ml-1 text-sm text-gray-600">{product.rating}</span>
          <span className="ml-1 text-sm text-gray-500">
            ({product.reviewCount} reviews)
          </span>
        </div>

        {/* Price */}
        <div className="flex items-center mb-4">
          <span className="text-lg font-bold text-gray-900">
            ${product.price}
          </span>
          {product.originalPrice && (
            <span className="ml-2 text-sm text-gray-500 line-through">
              ${product.originalPrice}
            </span>
          )}
        </div>

        {/* Stock Status */}
        {!product.inStock && (
          <p className="text-red-500 text-sm mb-2">Out of Stock</p>
        )}

        {/* Add to Cart Button */}
        <button
          onClick={handleAddToCart}
          disabled={!product.inStock}
          className={`w-full py-2 px-4 rounded-md font-medium ${
            product.inStock
              ? 'bg-blue-600 text-white hover:bg-blue-700'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          Add to Cart
        </button>
      </div>
    </div>
  );
};
```

### Step 3: Refactor (Blue Phase)

```typescript
// src/components/ProductCard.tsx (Refactored)
import React from 'react';
import Image from 'next/image';
import { StarIcon } from '@heroicons/react/24/solid';
import { Product } from '@/types/product';
import { formatPrice } from '@/utils/formatters';

interface ProductCardProps {
  product: Product;
  onAddToCart?: (product: Product) => void;
  className?: string;
}

export const ProductCard: React.FC<ProductCardProps> = ({ 
  product, 
  onAddToCart,
  className = ''
}) => {
  const handleAddToCart = () => {
    if (onAddToCart && product.inStock) {
      onAddToCart(product);
    }
  };

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${className}`}>
      <ProductImage product={product} />
      <ProductInfo product={product} onAddToCart={handleAddToCart} />
    </div>
  );
};

// Extracted components for better maintainability
const ProductImage: React.FC<{ product: Product }> = ({ product }) => (
  <div className="relative h-48">
    <Image
      src={product.image}
      alt={product.name}
      fill
      className="object-cover"
    />
    {product.badge && <ProductBadge badge={product.badge} />}
  </div>
);

const ProductBadge: React.FC<{ badge: string }> = ({ badge }) => (
  <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 text-xs rounded">
    {badge}
  </span>
);

const ProductInfo: React.FC<{ 
  product: Product; 
  onAddToCart: () => void; 
}> = ({ product, onAddToCart }) => (
  <div className="p-4">
    <h3 className="font-semibold text-gray-900 mb-2">{product.name}</h3>
    <ProductRating rating={product.rating} reviewCount={product.reviewCount} />
    <ProductPrice price={product.price} originalPrice={product.originalPrice} />
    <ProductStock inStock={product.inStock} />
    <AddToCartButton inStock={product.inStock} onClick={onAddToCart} />
  </div>
);
```

---

## 3. TDD Example: Shopping Cart Hook

### Step 1: Write the Test (Red Phase)

```typescript
// src/hooks/useCart.test.tsx
import { renderHook, act } from '@testing-library/react';
import { useCart } from './useCart';

const mockProduct = {
  id: '1',
  name: 'Test Product',
  price: 99.99,
  image: '/test.jpg',
  variant: { id: 'v1', name: 'Default' },
  maxQuantity: 10,
  sellerId: 'seller1',
  sellerName: 'Test Seller'
};

describe('useCart', () => {
  it('should start with empty cart', () => {
    const { result } = renderHook(() => useCart());
    
    expect(result.current.items).toEqual([]);
    expect(result.current.totalItems).toBe(0);
    expect(result.current.totalPrice).toBe(0);
  });

  it('should add item to cart', () => {
    const { result } = renderHook(() => useCart());
    
    act(() => {
      result.current.addItem(mockProduct, 2);
    });
    
    expect(result.current.items).toHaveLength(1);
    expect(result.current.items[0].quantity).toBe(2);
    expect(result.current.totalItems).toBe(2);
    expect(result.current.totalPrice).toBe(199.98);
  });

  it('should update quantity when adding existing item', () => {
    const { result } = renderHook(() => useCart());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
      result.current.addItem(mockProduct, 2);
    });
    
    expect(result.current.items).toHaveLength(1);
    expect(result.current.items[0].quantity).toBe(3);
  });

  it('should remove item from cart', () => {
    const { result } = renderHook(() => useCart());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
      result.current.removeItem(mockProduct.id);
    });
    
    expect(result.current.items).toHaveLength(0);
    expect(result.current.totalItems).toBe(0);
  });

  it('should clear entire cart', () => {
    const { result } = renderHook(() => useCart());
    
    act(() => {
      result.current.addItem(mockProduct, 1);
      result.current.clearCart();
    });
    
    expect(result.current.items).toHaveLength(0);
  });
});
```

### Step 2: Implement the Hook (Green Phase)

```typescript
// src/hooks/useCart.tsx
import { useState, useCallback, useMemo } from 'react';

interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  image: string;
  variant: {
    id: string;
    name: string;
  };
  quantity: number;
  maxQuantity: number;
  sellerId: string;
  sellerName: string;
}

export const useCart = () => {
  const [items, setItems] = useState<CartItem[]>([]);

  const addItem = useCallback((product: Omit<CartItem, 'quantity'>, quantity: number = 1) => {
    setItems(currentItems => {
      const existingItem = currentItems.find(item => item.id === product.id);
      
      if (existingItem) {
        return currentItems.map(item =>
          item.id === product.id
            ? { ...item, quantity: item.quantity + quantity }
            : item
        );
      }
      
      return [...currentItems, { ...product, quantity }];
    });
  }, []);

  const removeItem = useCallback((itemId: string) => {
    setItems(currentItems => currentItems.filter(item => item.id !== itemId));
  }, []);

  const updateQuantity = useCallback((itemId: string, quantity: number) => {
    setItems(currentItems =>
      currentItems.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
    );
  }, []);

  const clearCart = useCallback(() => {
    setItems([]);
  }, []);

  const totalItems = useMemo(() => {
    return items.reduce((total, item) => total + item.quantity, 0);
  }, [items]);

  const totalPrice = useMemo(() => {
    return items.reduce((total, item) => total + (item.price * item.quantity), 0);
  }, [items]);

  return {
    items,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    totalItems,
    totalPrice,
  };
};
```

---

## 4. TDD Example: API Integration Test

### Step 1: Write the Test (Red Phase)

```typescript
// src/services/productService.test.ts
import { productService } from './productService';
import { server } from '../mocks/server';
import { rest } from 'msw';

describe('ProductService', () => {
  it('should fetch products by category', async () => {
    // Red: This will fail until we implement the service
    const products = await productService.getProductsByCategory('electronics');
    
    expect(products).toHaveLength(4);
    expect(products[0]).toHaveProperty('id');
    expect(products[0]).toHaveProperty('name');
    expect(products[0]).toHaveProperty('price');
    expect(products[0].category).toBe('electronics');
  });

  it('should handle API errors gracefully', async () => {
    // Mock API error
    server.use(
      rest.get('/api/products', (req, res, ctx) => {
        return res(ctx.status(500), ctx.json({ error: 'Server error' }));
      })
    );

    await expect(productService.getProductsByCategory('electronics'))
      .rejects.toThrow('Failed to fetch products');
  });

  it('should search products by query', async () => {
    const products = await productService.searchProducts('headphones');
    
    expect(products.length).toBeGreaterThan(0);
    expect(products[0].name.toLowerCase()).toContain('headphones');
  });
});
```

### Step 2: Implement the Service (Green Phase)

```typescript
// src/services/productService.ts
interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  image: string;
  description: string;
}

class ProductService {
  private baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';

  async getProductsByCategory(category: string): Promise<Product[]> {
    try {
      const response = await fetch(`${this.baseUrl}/products?category=${category}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }
      
      const data = await response.json();
      return data.products || [];
    } catch (error) {
      console.error('Error fetching products:', error);
      throw new Error('Failed to fetch products');
    }
  }

  async searchProducts(query: string): Promise<Product[]> {
    try {
      const response = await fetch(`${this.baseUrl}/products/search?q=${encodeURIComponent(query)}`);
      
      if (!response.ok) {
        throw new Error('Failed to search products');
      }
      
      const data = await response.json();
      return data.products || [];
    } catch (error) {
      console.error('Error searching products:', error);
      throw new Error('Failed to search products');
    }
  }

  async getProductById(id: string): Promise<Product | null> {
    try {
      const response = await fetch(`${this.baseUrl}/products/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error('Failed to fetch product');
      }
      
      const data = await response.json();
      return data.product;
    } catch (error) {
      console.error('Error fetching product:', error);
      throw new Error('Failed to fetch product');
    }
  }
}

export const productService = new ProductService();
```

---

## 5. E2E Testing Example

### Complete Shopping Flow Test

```typescript
// tests/e2e/shopping-flow.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Complete Shopping Flow', () => {
  test('user can browse products and complete purchase', async ({ page }) => {
    // Navigate to home page
    await page.goto('http://localhost:3000');
    
    // Verify home page loads
    await expect(page.locator('h1')).toContainText('Welcome');
    
    // Browse to electronics category
    await page.click('text=Electronics');
    await expect(page).toHaveURL(/.*categories\/electronics/);
    
    // Verify products are displayed
    await expect(page.locator('[data-testid="product-card"]')).toHaveCount(4);
    
    // Click on first product
    await page.click('[data-testid="product-card"]:first-child');
    
    // Verify product detail page
    await expect(page.locator('h1')).toContainText('Premium Wireless Headphones');
    
    // Add to cart
    await page.click('text=Add to Cart');
    
    // Verify cart count updated
    await expect(page.locator('[data-testid="cart-count"]')).toContainText('1');
    
    // Go to cart
    await page.click('[data-testid="cart-icon"]');
    
    // Verify cart page
    await expect(page).toHaveURL(/.*cart/);
    await expect(page.locator('[data-testid="cart-item"]')).toHaveCount(1);
    
    // Proceed to checkout
    await page.click('text=Proceed to Checkout');
    
    // Fill checkout form
    await page.fill('[name="email"]', '<EMAIL>');
    await page.fill('[name="firstName"]', 'John');
    await page.fill('[name="lastName"]', 'Doe');
    await page.fill('[name="address"]', '123 Main St');
    await page.fill('[name="city"]', 'New York');
    await page.fill('[name="zipCode"]', '10001');
    
    // Complete order
    await page.click('text=Place Order');
    
    // Verify success page
    await expect(page.locator('h1')).toContainText('Order Confirmed');
    await expect(page.locator('[data-testid="order-number"]')).toBeVisible();
  });

  test('user can filter products by price', async ({ page }) => {
    await page.goto('http://localhost:3000/categories/electronics');
    
    // Set price filter
    await page.fill('[data-testid="price-filter-max"]', '200');
    await page.click('[data-testid="apply-filters"]');
    
    // Verify filtered results
    const productPrices = await page.locator('[data-testid="product-price"]').allTextContents();
    productPrices.forEach(priceText => {
      const price = parseFloat(priceText.replace('$', ''));
      expect(price).toBeLessThanOrEqual(200);
    });
  });
});
```

---

## 6. Running Tests

### Test Scripts in package.json

```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "playwright test",
    "test:e2e:ui": "playwright test --ui",
    "test:all": "npm run test && npm run test:e2e"
  }
}
```

### Running Tests

```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Run all tests
npm run test:all
```

This implementation guide provides practical examples of how to apply TDD principles to build a robust e-commerce platform. Each example follows the Red-Green-Refactor cycle and demonstrates real-world testing scenarios.
