#!/usr/bin/env node

const axios = require('axios');

const MCP_BASE_URL = 'http://localhost:3000/api/mcp';
const TENANT_ID = 'default';

async function testMCPAPI() {
  console.log('🔧 Testing Medusa-MCP Integration API Endpoints...\n');
  
  const results = {
    passed: 0,
    failed: 0,
    endpoints: []
  };

  // Helper function to test an endpoint
  async function testEndpoint(name, method, url, data = null, headers = {}, expectedStatus = 200) {
    try {
      const config = {
        method,
        url: `${MCP_BASE_URL}${url}`,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': TENANT_ID,
          ...headers
        }
      };
      
      if (data) {
        config.data = data;
      }
      
      const response = await axios(config);
      
      if (response.status === expectedStatus) {
        console.log(`✅ ${name}: ${method} ${url} - Status ${response.status}`);
        results.passed++;
        results.endpoints.push({ 
          name, 
          status: 'PASS', 
          method, 
          url, 
          responseStatus: response.status,
          responseSize: JSON.stringify(response.data).length 
        });
        return response.data;
      } else {
        console.log(`❌ ${name}: Expected ${expectedStatus}, got ${response.status}`);
        results.failed++;
        results.endpoints.push({ name, status: 'FAIL', method, url, responseStatus: response.status });
        return null;
      }
    } catch (error) {
      const status = error.response?.status;
      const message = error.response?.data?.error || error.message;
      
      // Some endpoints are expected to fail (not implemented yet)
      if (status === 404 || status === 501 || error.code === 'ECONNREFUSED') {
        console.log(`⚠️  ${name}: ${method} ${url} - Not implemented yet (${status || error.code})`);
        results.endpoints.push({ 
          name, 
          status: 'NOT_IMPLEMENTED', 
          method, 
          url, 
          error: status || error.code 
        });
      } else {
        console.log(`❌ ${name}: ${method} ${url} - Error: ${status || error.code} - ${message}`);
        results.failed++;
        results.endpoints.push({ 
          name, 
          status: 'FAIL', 
          method, 
          url, 
          error: status || error.code,
          message: message
        });
      }
      return null;
    }
  }

  console.log('🔄 Testing Product Synchronization Endpoints...');
  
  // Test product sync
  const productSyncData = {
    products: [
      {
        id: "mcp_prod_01",
        title: "MCP Test Product",
        handle: "mcp-test-product",
        description: "Product created for MCP API testing",
        status: "published",
        variants: [
          {
            id: "mcp_var_01",
            title: "Default Variant",
            sku: "MCP-TEST-001",
            prices: [
              {
                amount: 9999,
                currency_code: "inr"
              }
            ],
            inventory_quantity: 50
          }
        ],
        tenant_id: TENANT_ID
      }
    ],
    source: "ondc",
    target: "medusa",
    options: {
      skipConflicts: false,
      updateExisting: true,
      validateInventory: true
    }
  };

  const syncResponse = await testEndpoint(
    'Sync Products ONDC to Medusa', 
    'POST', 
    '/sync/products', 
    productSyncData,
    { 'sync-mode': 'incremental' },
    201
  );

  if (syncResponse?.sync_id) {
    await testEndpoint('Get Sync Status', 'GET', `/sync/products/status/${syncResponse.sync_id}`);
    await testEndpoint('Rollback Sync', 'POST', `/sync/products/rollback/${syncResponse.sync_id}`);
  } else {
    await testEndpoint('Get Sync Status (Mock)', 'GET', '/sync/products/status/sync_12345');
    await testEndpoint('Rollback Sync (Mock)', 'POST', '/sync/products/rollback/sync_12345');
  }

  console.log('\n📦 Testing Order Fulfillment Endpoints...');
  
  // Test order fulfillment
  const orderFulfillmentData = {
    order_id: "mcp_order_01",
    items: [
      {
        variant_id: "mcp_var_01",
        quantity: 2,
        unit_price: 9999
      }
    ],
    shipping_address: {
      first_name: "John",
      last_name: "Doe",
      address_1: "123 Test Street",
      city: "Mumbai",
      province: "Maharashtra",
      postal_code: "400001",
      country_code: "IN",
      phone: "+91 9876543210"
    },
    payment_method: "upi",
    shipping_method: "standard"
  };

  const fulfillmentResponse = await testEndpoint(
    'Process Order Fulfillment', 
    'POST', 
    '/orders/fulfill', 
    orderFulfillmentData,
    {},
    201
  );

  await testEndpoint('Get Order Status', 'GET', '/orders/mcp_order_01/status');
  
  const statusUpdateData = {
    fulfillment_status: "processing",
    payment_status: "captured",
    notes: "Order is being processed via MCP"
  };
  
  await testEndpoint('Update Order Status', 'PUT', '/orders/mcp_order_01/status', statusUpdateData);

  console.log('\n📊 Testing Inventory Management Endpoints...');
  
  // Test inventory sync
  const inventorySyncData = {
    items: [
      {
        variant_id: "mcp_var_01",
        quantity: 100,
        location_id: "loc_warehouse_01"
      }
    ],
    sync_mode: "absolute"
  };

  await testEndpoint('Sync Inventory', 'POST', '/inventory/sync', inventorySyncData);

  // Test inventory reservation
  const reservationData = {
    order_id: "mcp_order_01",
    items: [
      {
        variant_id: "mcp_var_01",
        quantity: 2,
        location_id: "loc_warehouse_01"
      }
    ],
    reservation_duration: 1800
  };

  const reservationResponse = await testEndpoint('Reserve Inventory', 'POST', '/inventory/reserve', reservationData, {}, 201);

  // Test inventory release
  const releaseData = {
    reservation_id: reservationResponse?.reservation_id || "res_12345",
    reason: "order_cancelled"
  };

  await testEndpoint('Release Inventory', 'POST', '/inventory/release', releaseData);

  console.log('\n👥 Testing Customer Synchronization Endpoints...');
  
  // Test customer sync
  const customerSyncData = {
    customers: [
      {
        id: "mcp_cust_01",
        email: "<EMAIL>",
        first_name: "MCP",
        last_name: "Test",
        phone: "+91 9999999999",
        addresses: [
          {
            first_name: "MCP",
            last_name: "Test",
            address_1: "123 MCP Street",
            city: "Mumbai",
            province: "Maharashtra",
            postal_code: "400001",
            country_code: "IN"
          }
        ]
      }
    ],
    source: "ondc",
    target: "medusa"
  };

  await testEndpoint('Sync Customers', 'POST', '/customers/sync', customerSyncData);
  await testEndpoint('Get Unified Customer Profile', 'GET', '/customers/mcp_cust_01/profile');

  console.log('\n💳 Testing Payment Integration Endpoints...');
  
  // Test payment processing
  const paymentData = {
    order_id: "mcp_order_01",
    amount: 19998,
    currency: "INR",
    payment_method: "upi",
    customer_id: "mcp_cust_01"
  };

  const paymentResponse = await testEndpoint('Process Payment', 'POST', '/payments/process', paymentData, {}, 201);
  
  if (paymentResponse?.payment_id) {
    await testEndpoint('Get Payment Status', 'GET', `/payments/${paymentResponse.payment_id}/status`);
  } else {
    await testEndpoint('Get Payment Status (Mock)', 'GET', '/payments/pay_12345/status');
  }

  console.log('\n🚚 Testing Shipping & Logistics Endpoints...');
  
  // Test shipping calculation
  const shippingCalcData = {
    items: [
      {
        variant_id: "mcp_var_01",
        quantity: 2,
        weight: 500,
        dimensions: { length: 20, width: 15, height: 10 }
      }
    ],
    destination: {
      postal_code: "400001",
      country_code: "IN"
    }
  };

  await testEndpoint('Calculate Shipping', 'POST', '/shipping/calculate', shippingCalcData);

  // Test shipping booking
  const shippingBookingData = {
    order_id: "mcp_order_01",
    carrier: "BlueDart",
    service_type: "standard",
    pickup_address: {
      address_1: "Warehouse 1",
      city: "Mumbai",
      postal_code: "400001",
      country_code: "IN"
    },
    delivery_address: shippingCalcData.destination
  };

  const shippingResponse = await testEndpoint('Book Shipping', 'POST', '/shipping/book', shippingBookingData, {}, 201);
  
  if (shippingResponse?.shipment_id) {
    await testEndpoint('Track Shipment', 'GET', `/shipping/${shippingResponse.shipment_id}/track`);
  } else {
    await testEndpoint('Track Shipment (Mock)', 'GET', '/shipping/ship_12345/track');
  }

  console.log('\n🔧 Testing MCP Tools Endpoints...');
  
  // Test MCP tool execution
  const toolRequest = {
    tool_name: "medusa_store_list_products",
    parameters: {
      limit: 5,
      offset: 0,
      expand: "variants,images"
    },
    context: {
      tenant_id: TENANT_ID,
      user_id: "user_01",
      session_id: "session_12345"
    }
  };

  await testEndpoint('Execute MCP Tool', 'POST', '/tools/execute', toolRequest, { 'X-MCP-Token': 'test_mcp_token' });
  await testEndpoint('List Available MCP Tools', 'GET', '/tools/list');

  console.log('\n🏥 Testing Health & Monitoring Endpoints...');
  
  // Test health endpoints (no auth required)
  await testEndpoint('System Health Check', 'GET', '/health', null, {}, 200);
  await testEndpoint('Detailed Health Metrics', 'GET', '/health/detailed');
  await testEndpoint('System Metrics', 'GET', '/metrics?period=24h');

  console.log('\n🔗 Testing Webhook Endpoints...');
  
  // Test webhook registration
  const webhookData = {
    url: "https://example.com/webhook",
    events: ["product.created", "order.updated", "inventory.changed"],
    secret: "webhook_secret_123"
  };

  const webhookResponse = await testEndpoint('Register Webhook', 'POST', '/webhooks/register', webhookData, {}, 201);
  
  if (webhookResponse?.webhook_id) {
    await testEndpoint('Get Webhook Details', 'GET', `/webhooks/${webhookResponse.webhook_id}`);
    await testEndpoint('Update Webhook', 'PUT', `/webhooks/${webhookResponse.webhook_id}`, { ...webhookData, events: ["product.created"] });
    await testEndpoint('Unregister Webhook', 'DELETE', `/webhooks/${webhookResponse.webhook_id}`, null, {}, 204);
  } else {
    await testEndpoint('Get Webhook Details (Mock)', 'GET', '/webhooks/webhook_12345');
    await testEndpoint('Update Webhook (Mock)', 'PUT', '/webhooks/webhook_12345', webhookData);
    await testEndpoint('Unregister Webhook (Mock)', 'DELETE', '/webhooks/webhook_12345', null, {}, 204);
  }

  // Summary
  console.log('\n' + '='.repeat(70));
  console.log('🔧 MEDUSA-MCP API TEST SUMMARY');
  console.log('='.repeat(70));
  
  const notImplemented = results.endpoints.filter(e => e.status === 'NOT_IMPLEMENTED').length;
  const totalTests = results.passed + results.failed + notImplemented;
  
  console.log(`✅ Passed: ${results.passed}`);
  console.log(`❌ Failed: ${results.failed}`);
  console.log(`⚠️  Not Implemented: ${notImplemented}`);
  console.log(`📊 Total Tests: ${totalTests}`);
  
  if (results.passed > 0) {
    console.log(`🎯 Implementation Rate: ${((results.passed / totalTests) * 100).toFixed(1)}%`);
  }
  
  if (results.failed > 0) {
    console.log('\n❌ Failed Endpoints:');
    results.endpoints
      .filter(e => e.status === 'FAIL')
      .forEach(e => console.log(`   - ${e.name}: ${e.method} ${e.url} (${e.error || e.responseStatus})`));
  }
  
  if (notImplemented > 0) {
    console.log('\n⚠️  Not Implemented Endpoints:');
    results.endpoints
      .filter(e => e.status === 'NOT_IMPLEMENTED')
      .forEach(e => console.log(`   - ${e.name}: ${e.method} ${e.url}`));
  }
  
  console.log('\n🎉 Medusa-MCP API testing completed!');
  console.log('📝 Note: Not implemented endpoints are expected as MCP integration is still in development.');
  
  return results;
}

// Run the test
if (require.main === module) {
  testMCPAPI().catch(console.error);
}

module.exports = testMCPAPI;
