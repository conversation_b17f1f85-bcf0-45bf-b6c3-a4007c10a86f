# TDD Quick Reference Card
## ONDC E-Commerce Platform

### 🔄 TDD Cycle
```
🔴 RED → 🟢 GREEN → 🔵 REFACTOR
```

---

## ⚡ Quick Commands

### Testing Commands
```bash
# Run all tests
npm run test

# Watch mode
npm run test:watch

# Coverage report
npm run test:coverage

# E2E tests
npm run test:e2e

# Specific test file
npm test ProductCard.test.tsx

# Debug mode
npm run test:debug
```

### Test File Creation
```bash
# Component test
touch src/components/ProductCard/ProductCard.test.tsx

# Hook test
touch src/hooks/useCart.test.ts

# Utility test
touch src/utils/formatPrice.test.ts
```

---

## 📝 Test Templates

### Component Test Template
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { ComponentName } from './ComponentName';

const mockProps = {
  // Define mock props
};

describe('ComponentName', () => {
  it('should render correctly', () => {
    render(<ComponentName {...mockProps} />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('should handle user interaction', () => {
    const mockHandler = jest.fn();
    render(<ComponentName {...mockProps} onAction={mockHandler} />);
    
    fireEvent.click(screen.getByText('Button'));
    expect(mockHandler).toHaveBeenCalled();
  });
});
```

### Hook Test Template
```typescript
import { renderHook, act } from '@testing-library/react';
import { useHookName } from './useHookName';

describe('useHookName', () => {
  it('should initialize with default values', () => {
    const { result } = renderHook(() => useHookName());
    
    expect(result.current.value).toBe(defaultValue);
  });

  it('should update value when action is called', () => {
    const { result } = renderHook(() => useHookName());
    
    act(() => {
      result.current.updateValue(newValue);
    });
    
    expect(result.current.value).toBe(newValue);
  });
});
```

### API Service Test Template
```typescript
import { serviceName } from './serviceName';
import { server } from '../mocks/server';
import { rest } from 'msw';

describe('serviceName', () => {
  it('should fetch data successfully', async () => {
    const data = await serviceName.getData();
    
    expect(data).toHaveLength(expectedLength);
    expect(data[0]).toHaveProperty('expectedProperty');
  });

  it('should handle API errors', async () => {
    server.use(
      rest.get('/api/endpoint', (req, res, ctx) => {
        return res(ctx.status(500));
      })
    );

    await expect(serviceName.getData()).rejects.toThrow();
  });
});
```

---

## 🎯 Common Test Patterns

### Testing User Interactions
```typescript
// Click events
fireEvent.click(screen.getByText('Button'));

// Form inputs
fireEvent.change(screen.getByLabelText('Email'), {
  target: { value: '<EMAIL>' }
});

// Keyboard events
fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });
```

### Async Testing
```typescript
// Wait for element to appear
await waitFor(() => {
  expect(screen.getByText('Loaded')).toBeInTheDocument();
});

// Wait for element to disappear
await waitForElementToBeRemoved(screen.getByText('Loading...'));

// Find by text (async)
const element = await screen.findByText('Async Content');
```

### Mock Functions
```typescript
// Create mock
const mockFn = jest.fn();

// Mock return value
mockFn.mockReturnValue('mocked value');

// Mock resolved promise
mockFn.mockResolvedValue({ data: 'success' });

// Mock rejected promise
mockFn.mockRejectedValue(new Error('API Error'));

// Check if called
expect(mockFn).toHaveBeenCalled();
expect(mockFn).toHaveBeenCalledWith(expectedArgs);
expect(mockFn).toHaveBeenCalledTimes(2);
```

---

## 🔍 Common Queries

### Finding Elements
```typescript
// By text content
screen.getByText('Button Text');
screen.queryByText('Optional Text'); // Returns null if not found
screen.findByText('Async Text'); // Returns promise

// By role
screen.getByRole('button', { name: 'Submit' });
screen.getByRole('textbox', { name: 'Email' });

// By label
screen.getByLabelText('Password');

// By test id
screen.getByTestId('product-card');

// By placeholder
screen.getByPlaceholderText('Enter email...');
```

### Assertions
```typescript
// Element presence
expect(element).toBeInTheDocument();
expect(element).not.toBeInTheDocument();

// Element state
expect(button).toBeDisabled();
expect(input).toHaveValue('expected value');
expect(element).toHaveClass('active');

// Text content
expect(element).toHaveTextContent('Expected text');

// Attributes
expect(link).toHaveAttribute('href', '/expected-url');
```

---

## 🎭 Mocking Patterns

### Mock Next.js Components
```typescript
// Mock Next.js Image
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props) => <img {...props} />,
}));

// Mock Next.js Link
jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ children, href }) => <a href={href}>{children}</a>,
}));

// Mock Next.js Router
const mockPush = jest.fn();
jest.mock('next/router', () => ({
  useRouter: () => ({
    push: mockPush,
    pathname: '/',
    query: {},
  }),
}));
```

### Mock Context
```typescript
// Mock context provider
const MockProvider = ({ children, value }) => (
  <Context.Provider value={value}>
    {children}
  </Context.Provider>
);

// Use in tests
render(
  <MockProvider value={mockContextValue}>
    <Component />
  </MockProvider>
);
```

### Mock API Calls
```typescript
// MSW handler
rest.get('/api/products', (req, res, ctx) => {
  return res(
    ctx.status(200),
    ctx.json({ products: mockProducts })
  );
});

// Fetch mock
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    json: () => Promise.resolve(mockData),
  })
);
```

---

## 🚀 E2E Test Patterns

### Page Navigation
```typescript
// Navigate to page
await page.goto('/products');

// Click navigation
await page.click('text=Electronics');

// Wait for navigation
await page.waitForURL('**/categories/electronics');
```

### Form Interactions
```typescript
// Fill form fields
await page.fill('[name="email"]', '<EMAIL>');
await page.fill('[name="password"]', 'password123');

// Select dropdown
await page.selectOption('[name="country"]', 'US');

// Check checkbox
await page.check('[name="terms"]');

// Submit form
await page.click('button[type="submit"]');
```

### Assertions
```typescript
// Element visibility
await expect(page.locator('text=Success')).toBeVisible();

// Text content
await expect(page.locator('h1')).toContainText('Welcome');

// URL
await expect(page).toHaveURL(/.*\/success/);

// Count
await expect(page.locator('.product-card')).toHaveCount(4);
```

---

## 📊 Coverage Commands

### Generate Reports
```bash
# HTML coverage report
npm run test:coverage

# Open coverage in browser
open coverage/lcov-report/index.html

# Coverage summary
npm run test -- --coverage --coverageReporters=text-summary
```

### Coverage Thresholds
```javascript
// jest.config.js
coverageThreshold: {
  global: {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80,
  },
  './src/components/cart/': {
    branches: 90,
    functions: 90,
    lines: 90,
    statements: 90,
  },
}
```

---

## 🐛 Debugging Tests

### Debug Commands
```bash
# Debug specific test
npm run test:debug -- --testNamePattern="should add item to cart"

# Run single test file
npm test -- ProductCard.test.tsx

# Verbose output
npm test -- --verbose

# Watch mode with coverage
npm test -- --watch --coverage
```

### Debug Techniques
```typescript
// Console log in tests
console.log(screen.debug()); // Prints DOM

// Pause execution
await page.pause(); // Playwright

// Screenshot on failure
await page.screenshot({ path: 'failure.png' });

// Step through test
debugger; // Add breakpoint
```

---

## ✅ Test Checklist

### Before Writing Tests
- [ ] Understand the requirement
- [ ] Identify user behavior to test
- [ ] Plan test scenarios (happy path, edge cases, errors)
- [ ] Set up necessary mocks

### Writing Tests
- [ ] Write descriptive test names
- [ ] Follow Arrange-Act-Assert pattern
- [ ] Test user behavior, not implementation
- [ ] Keep tests independent
- [ ] Use appropriate queries and assertions

### After Writing Tests
- [ ] Ensure tests pass
- [ ] Check coverage impact
- [ ] Refactor if needed
- [ ] Update documentation
- [ ] Review with team

---

## 🎯 Quick Tips

### Do's ✅
- Test user behavior, not implementation details
- Use descriptive test names
- Keep tests simple and focused
- Mock external dependencies
- Test error scenarios
- Maintain test independence

### Don'ts ❌
- Don't test implementation details
- Don't write overly complex tests
- Don't ignore failing tests
- Don't skip edge cases
- Don't forget to clean up mocks
- Don't test third-party libraries

---

## 📚 Quick Links

- [Main TDD Document](./TDD-E-Commerce-Website.md)
- [Implementation Guide](./TDD-Implementation-Guide.md)
- [Test Configuration](./TDD-Test-Configuration.md)
- [Testing Library Docs](https://testing-library.com/)
- [Jest Docs](https://jestjs.io/)
- [Playwright Docs](https://playwright.dev/)

---

**Remember**: Good tests give you confidence to refactor and deploy. If you're not confident in your tests, improve them before shipping!
