#!/bin/bash

# ONDC Seller Platform API Documentation Server
# This script serves the API documentation using a simple HTTP server

echo "🚀 Starting ONDC Seller Platform API Documentation Server..."
echo ""

# Check if we're in the docs directory
if [ ! -f "api-spec.yaml" ]; then
    echo "❌ Error: api-spec.yaml not found. Please run this script from the docs directory."
    exit 1
fi

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Error: Python 3 is required but not installed."
    exit 1
fi

# Display available documentation options
echo "📚 Available Documentation Options:"
echo ""
echo "1. 🌐 Interactive Swagger UI (Recommended)"
echo "   URL: http://localhost:8080/swagger-ui.html"
echo ""
echo "2. 📱 Standalone Documentation (No server required)"
echo "   File: standalone-docs.html (can be opened directly in browser)"
echo ""
echo "3. 📄 Raw OpenAPI Specification"
echo "   File: api-spec.yaml"
echo ""
echo "4. 🧪 Postman Collection"
echo "   File: postman-collection.json"
echo ""

# Start the HTTP server
echo "🔧 Starting HTTP server on port 8080..."
echo "📖 Open your browser and navigate to: http://localhost:8080/swagger-ui.html"
echo ""
echo "💡 Tips:"
echo "   - Use Ctrl+C to stop the server"
echo "   - The server serves all files in the docs directory"
echo "   - For testing APIs, import postman-collection.json into Postman"
echo ""
echo "🎯 Quick Links:"
echo "   - Main Documentation: http://localhost:8080/swagger-ui.html"
echo "   - Standalone Version: http://localhost:8080/standalone-docs.html"
echo "   - OpenAPI Spec: http://localhost:8080/api-spec.yaml"
echo "   - Postman Collection: http://localhost:8080/postman-collection.json"
echo ""
echo "⚡ Server starting..."
echo "----------------------------------------"

# Start Python HTTP server
python3 -m http.server 8080
