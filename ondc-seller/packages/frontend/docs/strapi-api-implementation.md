# Strapi API Implementation for ONDC Seller Platform

This document provides an overview of the Strapi API implementation for the ONDC Seller Platform, including the features implemented, usage examples, and testing procedures.

## Table of Contents

1. [Overview](#overview)
2. [Features Implemented](#features-implemented)
3. [File Structure](#file-structure)
4. [Usage Examples](#usage-examples)
5. [Testing](#testing)
6. [Multi-Tenant Support](#multi-tenant-support)
7. [Error Handling](#error-handling)
8. [Performance Considerations](#performance-considerations)
9. [Future Improvements](#future-improvements)

## Overview

The Strapi API implementation provides a comprehensive set of features for interacting with the Strapi CMS, including:

- Enhanced error handling and fallbacks
- Caching for frequently accessed data
- Pagination for large collections
- Content management (create, update, delete)
- Media upload functionality
- Authentication and authorization
- GraphQL support for efficient data fetching

These features are implemented in a modular way, allowing for easy maintenance and extension.

## Features Implemented

### 1. Enhanced Error Handling and Fallbacks

The enhanced error handling provides detailed error information and fallbacks for when the Strapi API is unavailable or returns errors. This ensures that the application continues to function even when the CMS is down.

**Key Files:**
- `src/lib/strapi-enhanced.ts`

### 2. Caching for Frequently Accessed Data

The caching layer improves performance by storing frequently accessed data in memory, reducing the number of API calls to Strapi. The cache has a configurable TTL (Time To Live) and can be cleared manually.

**Key Files:**
- `src/lib/strapi-cache.ts`

### 3. Pagination for Large Collections

The pagination utilities make it easy to fetch large collections of data in smaller chunks, improving performance and reducing memory usage. The pagination controls can be used to build UI components for navigating through paginated data.

**Key Files:**
- `src/lib/strapi-pagination.ts`

### 4. Content Management Features

The content management features provide functions for creating, updating, and deleting content in Strapi. These functions are used to manage banners, pages, product categories, and other content types.

**Key Files:**
- `src/lib/strapi-content-management.ts`

### 5. Media Upload Functionality

The media upload functionality provides functions for uploading files to the Strapi media library, including single files, multiple files, and files from URLs. It also provides functions for getting information about files and deleting files.

**Key Files:**
- `src/lib/strapi-media.ts`

### 6. Authentication and Authorization

The authentication and authorization features provide functions for logging in, registering, and managing user sessions. They also provide functions for getting authentication headers and checking if a user is authenticated.

**Key Files:**
- `src/lib/strapi-auth.ts`

### 7. GraphQL Support

The GraphQL support provides functions for executing GraphQL queries against the Strapi API, allowing for more efficient data fetching by reducing over-fetching and under-fetching.

**Key Files:**
- `src/lib/strapi-graphql.ts`

## File Structure

The Strapi API implementation is organized into the following files:

- `src/lib/strapi-enhanced.ts`: Enhanced error handling and fallbacks
- `src/lib/strapi-cache.ts`: Caching for frequently accessed data
- `src/lib/strapi-pagination.ts`: Pagination for large collections
- `src/lib/strapi-content-management.ts`: Content management features
- `src/lib/strapi-media.ts`: Media upload functionality
- `src/lib/strapi-auth.ts`: Authentication and authorization
- `src/lib/strapi-graphql.ts`: GraphQL support
- `src/lib/strapi-api.ts`: Main export file that combines all features

Each file has corresponding test files in the `src/lib/__tests__` directory.

## Usage Examples

### Basic Usage

```typescript
import strapiApi from '../lib/strapi-api';

// Get banners with error handling and fallbacks
const banners = await strapiApi.getBanners();

// Get banners with caching
const cachedBanners = await strapiApi.getCachedBanners();

// Get paginated products
const products = await strapiApi.getPaginatedProducts({ page: 1, pageSize: 10 });

// Create a new banner
const newBanner = await strapiApi.createBanner({
  title: 'New Banner',
  subtitle: 'Banner subtitle',
  cta_text: 'Click here',
  cta_link: '/products'
});

// Upload a file
const file = new File(['file content'], 'test.jpg', { type: 'image/jpeg' });
const uploadResult = await strapiApi.uploadMedia(file);

// Login
const loginResult = await strapiApi.login('<EMAIL>', 'password');

// Get products with GraphQL
const graphqlProducts = await strapiApi.getProductsWithRelations();
```

### Advanced Usage

```typescript
import strapiApi from '../lib/strapi-api';

// Use specific modules
import { cache, pagination, contentManagement } from '../lib/strapi-api';

// Clear cache for a specific collection
cache.clearCollectionCache('banners');

// Generate pagination controls
const paginationControls = pagination.generatePaginationControls({
  page: 2,
  pageSize: 10,
  pageCount: 5,
  total: 50
});

// Create a new item with specific options
const newItem = await contentManagement.createItem('custom-collection', {
  title: 'Custom Item',
  description: 'Custom description'
});
```

## Testing

The Strapi API implementation includes comprehensive tests for all features. The tests use Jest and mock external dependencies to ensure that the tests are reliable and fast.

To run the tests:

```bash
npm test
```

## Multi-Tenant Support

The Strapi API implementation includes support for multi-tenancy, allowing the application to serve multiple tenants (sellers) from a single instance. The tenant ID is automatically included in all API requests to Strapi.

The tenant ID is obtained from the `getCurrentTenantId()` function, which gets the tenant ID from various sources (URL, localStorage, etc.) and falls back to a default tenant ID if none is found.

## Error Handling

The error handling in the Strapi API implementation is designed to be robust and informative. It provides detailed error information and fallbacks for when the Strapi API is unavailable or returns errors.

The `processStrapiError()` function processes errors from the Strapi API and provides a standardized error object with status, statusText, and message properties. This makes it easier to handle errors consistently throughout the application.

## Performance Considerations

The Strapi API implementation includes several features to improve performance:

- **Caching**: Frequently accessed data is cached in memory to reduce API calls.
- **Pagination**: Large collections are fetched in smaller chunks to improve performance and reduce memory usage.
- **GraphQL**: GraphQL queries are used to reduce over-fetching and under-fetching of data.

## Future Improvements

Potential future improvements to the Strapi API implementation include:

- **Persistent Caching**: Implement persistent caching using localStorage or IndexedDB for improved performance across page loads.
- **Offline Support**: Add support for offline mode, allowing the application to function when the Strapi API is unavailable.
- **Real-time Updates**: Implement real-time updates using WebSockets or Server-Sent Events for live content updates.
- **Batch Operations**: Add support for batch operations (create, update, delete multiple items at once) for improved performance.
- **Field-level Validation**: Implement field-level validation for content management operations to ensure data integrity.
