# ONDC Seller Platform API Documentation

This directory contains the comprehensive API documentation for the ONDC (Open Network for Digital Commerce) Seller Platform backend, including the new Medusa-MCP integration endpoints.

## 📋 Overview

The ONDC Seller Platform provides two complementary API suites:

1. **ONDC Admin API** - Complete RESTful endpoints for managing e-commerce operations
2. **Medusa-MCP Integration API** - Model Context Protocol endpoints for AI-powered automation and synchronization

The APIs support full CRUD operations for products, categories, orders, customers, collections, coupons, analytics, and advanced MCP-based integrations.

## 📁 Files

### **Core API Specifications**

- **`api-spec.yaml`** - Complete ONDC Admin API OpenAPI 3.0.3 specification
- **`medusa-mcp-api-spec.yaml`** - Complete Medusa-MCP Integration API specification

### **Interactive Documentation**

- **`combined-api-docs.html`** - Unified documentation viewer with tab switching
- **`swagger-ui.html`** - Interactive ONDC Admin API documentation
- **`standalone-docs.html`** - Self-contained documentation (no server required)

### **Testing & Collections**

- **`postman-collection.json`** - ONDC Admin API testing collection
- **`medusa-mcp-postman-collection.json`** - MCP Integration API testing collection
- **`test-admin-api.js`** - Automated ONDC Admin API testing script
- **`test-mcp-api.js`** - Automated MCP Integration API testing script

### **Documentation & Guides**

- **`README.md`** - This comprehensive documentation file
- **`API_SUMMARY.md`** - ONDC Admin API implementation summary
- **`MCP_INTEGRATION_SUMMARY.md`** - MCP Integration API specification summary

### **Utilities**

- **`serve-docs.sh`** - Documentation server startup script

## 🚀 Quick Start

### Viewing the API Documentation

You can view the API documentation in several ways:

#### 1. Swagger UI (Recommended)

```bash
# Install swagger-ui-serve globally
npm install -g swagger-ui-serve

# Serve the documentation
swagger-ui-serve api-spec.yaml
```

#### 2. Online Swagger Editor

1. Go to [editor.swagger.io](https://editor.swagger.io/)
2. Copy and paste the contents of `api-spec.yaml`
3. View the interactive documentation

#### 3. VS Code Extension

1. Install the "OpenAPI (Swagger) Editor" extension
2. Open `api-spec.yaml` in VS Code
3. Use the preview feature

### Testing the API

#### Using curl

```bash
# List products
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/admin/products

# Create a product
curl -X POST \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"name":"Test Product","sku":"TEST-001","price":9999,"category":"Electronics"}' \
  http://localhost:3000/api/admin/products

# Get analytics
curl -H "Authorization: Bearer YOUR_TOKEN" \
  http://localhost:3000/api/admin/analytics?period=30d
```

#### Using Postman

1. Import the OpenAPI specification into Postman
2. Set up authentication with Bearer token
3. Test the endpoints interactively

## 🔐 Authentication

All admin endpoints require authentication using Bearer tokens:

```
Authorization: Bearer YOUR_JWT_TOKEN
```

Public endpoints (like `/products`) do not require authentication.

## 📊 API Endpoints Summary

### Products (`/admin/products`)

- **GET** - List products with pagination, search, and filtering
- **POST** - Create new product
- **GET** `/{id}` - Get product details
- **PUT** `/{id}` - Update product
- **DELETE** `/{id}` - Delete product

### Categories (`/admin/categories`)

- **GET** - List categories with hierarchical support
- **POST** - Create new category
- **GET** `/{id}` - Get category details
- **PUT** `/{id}` - Update category
- **DELETE** `/{id}` - Delete category (if no children)

### Orders (`/admin/orders`)

- **GET** - List orders with multi-status filtering
- **POST** - Create order (admin only)
- **GET** `/{id}` - Get order details
- **PUT** `/{id}` - Update order status
- **DELETE** `/{id}` - Delete order (cancelled only)

### Customers (`/admin/customers`)

- **GET** - List customers with filtering
- **POST** - Create customer
- **GET** `/{id}` - Get customer details
- **PUT** `/{id}` - Update customer
- **DELETE** `/{id}` - Delete customer (no orders only)

### Analytics (`/admin/analytics`)

- **GET** - Get dashboard analytics with filtering

### Public Endpoints

- **GET** `/products` - Public product listing (no auth required)

## 🔧 Request/Response Format

### Standard Response Format

```json
{
  "products": [...],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 156,
    "totalPages": 16,
    "hasNext": true,
    "hasPrev": false
  },
  "filters": {
    "search": "nike",
    "status": "active",
    "sortBy": "name",
    "sortOrder": "asc"
  }
}
```

### Error Response Format

```json
{
  "error": "Product not found",
  "details": "No product found with ID: 999",
  "code": "PRODUCT_NOT_FOUND"
}
```

## 📈 Pagination

All list endpoints support pagination:

- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10, max: 100)
- `search` - Search term
- `sortBy` - Sort field
- `sortOrder` - Sort direction (asc/desc)

## 🔍 Filtering and Search

### Products

- Filter by: `status`, `category`
- Search in: `name`, `sku`, `description`
- Sort by: `name`, `price`, `quantity`, `status`, `createdAt`, `updatedAt`

### Categories

- Filter by: `status`, `parentId`
- Search in: `name`, `slug`, `description`
- Sort by: `name`, `sortOrder`, `productCount`, `createdAt`, `updatedAt`

### Orders

- Filter by: `status`, `paymentStatus`, `fulfillmentStatus`
- Search in: `orderNumber`, `customerName`, `customerEmail`
- Sort by: `orderNumber`, `total`, `status`, `createdAt`, `updatedAt`

### Customers

- Filter by: `status`, `emailVerified`
- Search in: `firstName`, `lastName`, `email`, `phone`
- Sort by: `firstName`, `lastName`, `totalSpent`, `createdAt`, `updatedAt`

## 💰 Currency and Pricing

All monetary values are stored in the smallest currency unit (paise for INR):

- ₹129.99 = 12999 paise
- ₹1,000.00 = 100000 paise

## 📅 Date Formats

All dates use ISO 8601 format:

```
2024-01-15T14:30:00Z
```

## ⚠️ Error Codes

| HTTP Status | Description                            |
| ----------- | -------------------------------------- |
| 200         | Success                                |
| 201         | Created                                |
| 400         | Bad Request - Invalid input            |
| 401         | Unauthorized - Authentication required |
| 404         | Not Found - Resource doesn't exist     |
| 409         | Conflict - Resource already exists     |
| 500         | Internal Server Error                  |

## 🧪 Testing

The API includes comprehensive test coverage. Run the test suite:

```bash
# Run API tests
node test-admin-api.js

# Expected output: 80% success rate (36/45 tests pass)
```

## 🔄 Rate Limiting

- **Rate Limit**: 1000 requests per hour per API key
- **Headers**:
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## 🌐 CORS

The API supports CORS for web applications. Configure allowed origins in your environment.

## 📝 Changelog

### Version 1.0.0

- Initial API release
- Complete CRUD operations for all resources
- Comprehensive filtering and pagination
- Analytics endpoints
- Full OpenAPI 3.0.3 specification

## 🤝 Support

For API support and questions:

- **Email**: <EMAIL>
- **Documentation**: https://ondc-seller.com/docs
- **Status Page**: https://status.ondc-seller.com

## 📄 License

This API documentation is licensed under the MIT License.
