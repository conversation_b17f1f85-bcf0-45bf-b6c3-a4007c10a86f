{"info": {"name": "ONDC Seller Platform API", "description": "Complete API collection for the ONDC Seller Platform with all admin endpoints", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "variable": [{"key": "base_url", "value": "http://localhost:3000/api", "type": "string"}, {"key": "auth_token", "value": "your_jwt_token_here", "type": "string"}], "item": [{"name": "Products", "item": [{"name": "List Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/products?page=1&limit=10", "host": ["{{base_url}}"], "path": ["admin", "products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "search", "value": "", "disabled": true}, {"key": "status", "value": "", "disabled": true}, {"key": "category", "value": "", "disabled": true}, {"key": "sortBy", "value": "updatedAt", "disabled": true}, {"key": "sortOrder", "value": "desc", "disabled": true}]}}}, {"name": "Create Product", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Product\",\n  \"sku\": \"TEST-001\",\n  \"price\": 9999,\n  \"category\": \"Electronics\",\n  \"description\": \"A test product created via API\",\n  \"quantity\": 100,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/admin/products", "host": ["{{base_url}}"], "path": ["admin", "products"]}}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/products/1", "host": ["{{base_url}}"], "path": ["admin", "products", "1"]}}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Product Name\",\n  \"price\": 11999,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/admin/products/1", "host": ["{{base_url}}"], "path": ["admin", "products", "1"]}}}, {"name": "Delete Product", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{base_url}}/admin/products/1", "host": ["{{base_url}}"], "path": ["admin", "products", "1"]}}}]}, {"name": "Categories", "item": [{"name": "List Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/categories?page=1&limit=10", "host": ["{{base_url}}"], "path": ["admin", "categories"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Create Category", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Test Category\",\n  \"slug\": \"test-category\",\n  \"description\": \"A test category created via API\",\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{base_url}}/admin/categories", "host": ["{{base_url}}"], "path": ["admin", "categories"]}}}, {"name": "Get Category by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/categories/1", "host": ["{{base_url}}"], "path": ["admin", "categories", "1"]}}}]}, {"name": "Orders", "item": [{"name": "List Orders", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/orders?page=1&limit=10", "host": ["{{base_url}}"], "path": ["admin", "orders"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "", "disabled": true}, {"key": "paymentStatus", "value": "", "disabled": true}, {"key": "fulfillmentStatus", "value": "", "disabled": true}]}}}, {"name": "Get Order by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/orders/ORD-001", "host": ["{{base_url}}"], "path": ["admin", "orders", "ORD-001"]}}}, {"name": "Update Order Status", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"processing\",\n  \"fulfillmentStatus\": \"partial\"\n}"}, "url": {"raw": "{{base_url}}/admin/orders/ORD-001", "host": ["{{base_url}}"], "path": ["admin", "orders", "ORD-001"]}}}]}, {"name": "Customers", "item": [{"name": "List Customers", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/customers?page=1&limit=10", "host": ["{{base_url}}"], "path": ["admin", "customers"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}]}}}, {"name": "Create Customer", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"firstName\": \"Test\",\n  \"lastName\": \"Customer\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+91 9999999999\"\n}"}, "url": {"raw": "{{base_url}}/admin/customers", "host": ["{{base_url}}"], "path": ["admin", "customers"]}}}, {"name": "Get Customer by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/customers/CUST-001", "host": ["{{base_url}}"], "path": ["admin", "customers", "CUST-001"]}}}]}, {"name": "Analytics", "item": [{"name": "Get Dashboard Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/analytics?period=30d&metric=all", "host": ["{{base_url}}"], "path": ["admin", "analytics"], "query": [{"key": "period", "value": "30d"}, {"key": "metric", "value": "all"}, {"key": "segment", "value": "all", "disabled": true}]}}}, {"name": "Get Revenue Analytics", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/admin/analytics?period=30d&metric=revenue", "host": ["{{base_url}}"], "path": ["admin", "analytics"], "query": [{"key": "period", "value": "30d"}, {"key": "metric", "value": "revenue"}]}}}]}, {"name": "Public Endpoints", "item": [{"name": "List Public Products", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/products?page=1&limit=12", "host": ["{{base_url}}"], "path": ["products"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "12"}, {"key": "search", "value": "", "disabled": true}, {"key": "category", "value": "", "disabled": true}]}}, "auth": {"type": "<PERSON><PERSON><PERSON>"}}]}]}