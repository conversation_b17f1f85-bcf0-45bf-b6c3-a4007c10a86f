<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ONDC Seller Platform - Complete API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .header-section {
      background: linear-gradient(135deg, #3B82F6 0%, #10B981 100%);
      color: white;
      padding: 30px 20px;
      text-align: center;
      margin-bottom: 30px;
    }
    .header-section h1 {
      margin: 0 0 15px 0;
      font-size: 3em;
      font-weight: bold;
      text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    .header-section p {
      margin: 0 0 20px 0;
      font-size: 1.3em;
      opacity: 0.95;
    }
    .api-selector {
      background: white;
      padding: 25px;
      margin: 20px;
      border-radius: 12px;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      text-align: center;
    }
    .api-selector h2 {
      color: #3B82F6;
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 1.8em;
    }
    .api-tabs {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin-bottom: 25px;
      flex-wrap: wrap;
    }
    .api-tab {
      background: #f8fafc;
      border: 2px solid #e2e8f0;
      color: #64748b;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      font-weight: 600;
      min-width: 200px;
    }
    .api-tab:hover {
      background: #e2e8f0;
      border-color: #cbd5e1;
    }
    .api-tab.active {
      background: #3B82F6;
      border-color: #3B82F6;
      color: white;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    .api-description {
      background: #f1f5f9;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      border-left: 4px solid #3B82F6;
    }
    .api-description h3 {
      margin: 0 0 10px 0;
      color: #1e293b;
      font-size: 1.2em;
    }
    .api-description p {
      margin: 0;
      color: #475569;
      line-height: 1.6;
    }
    .quick-links {
      display: flex;
      justify-content: center;
      gap: 15px;
      flex-wrap: wrap;
    }
    .quick-link {
      background: #10B981;
      color: white;
      padding: 12px 20px;
      text-decoration: none;
      border-radius: 6px;
      font-weight: 600;
      transition: all 0.3s ease;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
    .quick-link:hover {
      background: #059669;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      color: white;
      text-decoration: none;
    }
    .status-badges {
      display: flex;
      justify-content: center;
      gap: 15px;
      margin: 20px 0;
      flex-wrap: wrap;
    }
    .status-badge {
      background: #10B981;
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-size: 0.9em;
      font-weight: bold;
      display: inline-flex;
      align-items: center;
      gap: 6px;
    }
    .status-badge.mcp {
      background: #8B5CF6;
    }
    .swagger-ui .topbar {
      background-color: #3B82F6;
    }
    .swagger-ui .topbar .download-url-wrapper .select-label {
      color: white;
    }
    .swagger-ui .info .title {
      color: #3B82F6;
    }
    #swagger-ui {
      margin-top: 20px;
    }
    .loading-spinner {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px;
    }
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #e2e8f0;
      border-top: 4px solid #3B82F6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div class="header-section">
    <h1>🚀 ONDC Seller Platform</h1>
    <p>Complete API Documentation Suite</p>
    <div class="status-badges">
      <span class="status-badge">✅ 25 Admin Endpoints</span>
      <span class="status-badge mcp">🔧 15 MCP Integration Endpoints</span>
      <span class="status-badge">📊 Real-time Sync</span>
    </div>
  </div>

  <div class="api-selector">
    <h2>📚 API Documentation</h2>
    <div class="api-tabs">
      <div class="api-tab active" data-spec="ondc-admin">
        🏪 ONDC Admin API
      </div>
      <div class="api-tab" data-spec="medusa-mcp">
        🔧 Medusa-MCP Integration
      </div>
    </div>
    
    <div class="api-description" id="api-description">
      <h3>ONDC Admin API</h3>
      <p>Complete CRUD operations for managing products, categories, orders, customers, collections, and analytics. Includes comprehensive filtering, pagination, and real-time data management for ONDC seller operations.</p>
    </div>

    <div class="quick-links">
      <a href="./postman-collection.json" download class="quick-link">
        📥 Download Postman Collection
      </a>
      <a href="./api-spec.yaml" download class="quick-link">
        📄 ONDC OpenAPI Spec
      </a>
      <a href="./medusa-mcp-api-spec.yaml" download class="quick-link">
        🔧 MCP OpenAPI Spec
      </a>
      <a href="./README.md" class="quick-link">
        📖 Documentation Guide
      </a>
    </div>
  </div>

  <div id="swagger-ui"></div>
  
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@5.9.0/swagger-ui-standalone-preset.js"></script>
  <script>
    let currentUI = null;
    
    const apiDescriptions = {
      'ondc-admin': {
        title: 'ONDC Admin API',
        description: 'Complete CRUD operations for managing products, categories, orders, customers, collections, and analytics. Includes comprehensive filtering, pagination, and real-time data management for ONDC seller operations.'
      },
      'medusa-mcp': {
        title: 'Medusa-MCP Integration API',
        description: 'Model Context Protocol integration for seamless synchronization between ONDC and Medusa e-commerce systems. Supports product sync, order fulfillment, inventory management, and AI-powered automation workflows.'
      }
    };

    function updateDescription(specType) {
      const descriptionEl = document.getElementById('api-description');
      const info = apiDescriptions[specType];
      descriptionEl.innerHTML = `
        <h3>${info.title}</h3>
        <p>${info.description}</p>
      `;
    }

    function showLoading() {
      document.getElementById('swagger-ui').innerHTML = `
        <div class="loading-spinner">
          <div class="spinner"></div>
        </div>
      `;
    }

    function loadSwaggerUI(specUrl) {
      showLoading();
      
      // Destroy existing UI
      if (currentUI) {
        try {
          currentUI = null;
        } catch (e) {
          console.log('Error destroying previous UI:', e);
        }
      }

      // Clear the container
      setTimeout(() => {
        document.getElementById('swagger-ui').innerHTML = '';
        
        // Initialize new UI
        currentUI = SwaggerUIBundle({
          url: specUrl,
          dom_id: '#swagger-ui',
          deepLinking: true,
          presets: [
            SwaggerUIBundle.presets.apis,
            SwaggerUIStandalonePreset
          ],
          plugins: [
            SwaggerUIBundle.plugins.DownloadUrl
          ],
          layout: "StandaloneLayout",
          validatorUrl: null,
          tryItOutEnabled: true,
          supportedSubmitMethods: ['get', 'post', 'put', 'delete', 'patch'],
          onComplete: function() {
            console.log(`✅ ${specUrl} loaded successfully`);
          },
          onFailure: function(data) {
            console.error(`❌ Failed to load ${specUrl}:`, data);
            document.getElementById('swagger-ui').innerHTML = `
              <div style="text-align: center; padding: 40px; color: #ef4444;">
                <h3>Failed to load API specification</h3>
                <p>Please check the console for more details.</p>
                <button onclick="location.reload()" style="background: #3B82F6; color: white; padding: 10px 20px; border: none; border-radius: 6px; cursor: pointer;">
                  Retry
                </button>
              </div>
            `;
          }
        });
      }, 100);
    }

    // Tab switching functionality
    document.addEventListener('DOMContentLoaded', function() {
      const tabs = document.querySelectorAll('.api-tab');
      
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // Remove active class from all tabs
          tabs.forEach(t => t.classList.remove('active'));
          
          // Add active class to clicked tab
          this.classList.add('active');
          
          // Get the spec type
          const specType = this.getAttribute('data-spec');
          
          // Update description
          updateDescription(specType);
          
          // Load appropriate spec
          if (specType === 'ondc-admin') {
            loadSwaggerUI('./api-spec.yaml');
          } else if (specType === 'medusa-mcp') {
            loadSwaggerUI('./medusa-mcp-api-spec.yaml');
          }
        });
      });

      // Load default spec (ONDC Admin API)
      loadSwaggerUI('./api-spec.yaml');
    });
  </script>
</body>
</html>
