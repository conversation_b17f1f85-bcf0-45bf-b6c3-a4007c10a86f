'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'customer' | 'seller';
  avatar?: string;
  permissions?: string[];
  lastLogin?: Date;
  isActive: boolean;
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  hasPermission: (permission: string) => boolean;
  isAdmin: boolean;
  isCustomer: boolean;
  isSeller: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: 'customer' | 'seller';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Mock users for development
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Admin User',
    role: 'admin',
    avatar: '/images/avatars/admin.jpg',
    permissions: ['*'],
    lastLogin: new Date(),
    isActive: true,
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Seller User',
    role: 'seller',
    avatar: '/images/avatars/seller.jpg',
    permissions: ['products.manage', 'orders.view', 'analytics.view'],
    lastLogin: new Date(),
    isActive: true,
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Customer User',
    role: 'customer',
    avatar: '/images/avatars/customer.jpg',
    permissions: ['orders.view', 'profile.edit'],
    lastLogin: new Date(),
    isActive: true,
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Demo Admin',
    role: 'admin',
    avatar: '/images/avatars/demo.jpg',
    permissions: ['*'],
    lastLogin: new Date(),
    isActive: true,
  },
];

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for stored authentication
    const initAuth = async () => {
      try {
        const storedUser = localStorage.getItem('ondc_auth_user');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          setUser(userData);
        } else {
          // Auto-login as demo admin for development
          const demoAdmin = mockUsers.find(u => u.email === '<EMAIL>');
          if (demoAdmin) {
            setUser(demoAdmin);
            localStorage.setItem('ondc_auth_user', JSON.stringify(demoAdmin));
            console.log('🔧 Development mode: Auto-logged in as demo admin');
          }
        }
      } catch (error) {
        console.error('Failed to initialize auth:', error);
        localStorage.removeItem('ondc_auth_user');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find user in mock data
      const foundUser = mockUsers.find(u => u.email === email);

      if (!foundUser) {
        throw new Error('User not found');
      }

      // In a real app, you'd verify the password here
      if (password !== '123456' && password !== 'demo') {
        throw new Error('Invalid password');
      }

      // Update last login
      const userWithLogin = {
        ...foundUser,
        lastLogin: new Date(),
      };

      setUser(userWithLogin);
      localStorage.setItem('ondc_auth_user', JSON.stringify(userWithLogin));
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('ondc_auth_user');
  };

  const register = async (userData: RegisterData): Promise<void> => {
    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === userData.email);
      if (existingUser) {
        throw new Error('User already exists');
      }

      // Create new user
      const newUser: User = {
        id: Date.now().toString(),
        email: userData.email,
        name: userData.name,
        role: userData.role || 'customer',
        avatar: '/images/avatars/default.jpg',
        permissions:
          userData.role === 'seller'
            ? ['products.manage', 'orders.view', 'analytics.view']
            : ['orders.view', 'profile.edit'],
        lastLogin: new Date(),
        isActive: true,
      };

      // Add to mock users (in real app, this would be an API call)
      mockUsers.push(newUser);

      setUser(newUser);
      localStorage.setItem('ondc_auth_user', JSON.stringify(newUser));
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProfile = async (userData: Partial<User>): Promise<void> => {
    if (!user) throw new Error('No user logged in');

    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedUser = { ...user, ...userData };
      setUser(updatedUser);
      localStorage.setItem('ondc_auth_user', JSON.stringify(updatedUser));
    } catch (error) {
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    if (user.permissions?.includes('*')) return true;
    return user.permissions?.includes(permission) || false;
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated: !!user,
    login,
    logout,
    register,
    updateProfile,
    hasPermission,
    isAdmin: user?.role === 'admin',
    isCustomer: user?.role === 'customer',
    isSeller: user?.role === 'seller',
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Helper hooks for specific roles
export function useAdminAuth() {
  const auth = useAuth();
  if (!auth.isAdmin) {
    throw new Error('Admin access required');
  }
  return auth;
}

export function useCustomerAuth() {
  const auth = useAuth();
  if (!auth.isCustomer) {
    throw new Error('Customer access required');
  }
  return auth;
}

export function useSellerAuth() {
  const auth = useAuth();
  if (!auth.isSeller) {
    throw new Error('Seller access required');
  }
  return auth;
}
