'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export interface CartItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  image: string;
  variant?: {
    size?: string;
    color?: string;
    [key: string]: any;
  };
  maxQuantity?: number;
  sellerId: string;
  sellerName: string;
}

export interface CartContextType {
  items: CartItem[];
  totalItems: number;
  totalPrice: number;
  isLoading: boolean;
  addItem: (item: Omit<CartItem, 'quantity'>, quantity?: number) => void;
  removeItem: (itemId: string) => void;
  updateQuantity: (itemId: string, quantity: number) => void;
  clearCart: () => void;
  getItemQuantity: (productId: string, variant?: any) => number;
  isInCart: (productId: string, variant?: any) => boolean;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

const CART_STORAGE_KEY = 'ondc_cart_items';

interface CartProviderProps {
  children: ReactNode;
}

export function CartProvider({ children }: CartProviderProps) {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Load cart from localStorage on mount
  useEffect(() => {
    try {
      const storedCart = localStorage.getItem(CART_STORAGE_KEY);
      if (storedCart) {
        const cartItems = JSON.parse(storedCart);
        setItems(cartItems);
      } else {
        // Initialize with demo data for testing if no cart exists
        const demoItems: CartItem[] = [
          {
            id: 'demo-1',
            productId: 'demo-product-1',
            name: 'Premium Wireless Headphones',
            price: 199.99,
            quantity: 1,
            image:
              'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop',
            sellerId: 'tech-world',
            sellerName: 'Tech World',
            maxQuantity: 10,
          },
          {
            id: 'demo-2',
            productId: 'demo-product-2',
            name: 'Professional Laptop Stand',
            price: 299.99,
            quantity: 2,
            image:
              'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500&h=500&fit=crop',
            sellerId: 'office-pro',
            sellerName: 'Office Pro',
            maxQuantity: 5,
          },
        ];
        setItems(demoItems);
      }
    } catch (error) {
      console.error('Failed to load cart from localStorage:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save cart to localStorage whenever items change
  useEffect(() => {
    if (!isLoading) {
      try {
        localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items));
      } catch (error) {
        console.error('Failed to save cart to localStorage:', error);
      }
    }
  }, [items, isLoading]);

  const generateItemId = (productId: string, variant?: any): string => {
    if (!variant) return productId;
    const variantKey = Object.keys(variant)
      .sort()
      .map(key => `${key}:${variant[key]}`)
      .join('|');
    return `${productId}__${variantKey}`;
  };

  const addItem = (newItem: Omit<CartItem, 'quantity'>, quantity: number = 1) => {
    const itemId = generateItemId(newItem.productId, newItem.variant);

    setItems(currentItems => {
      const existingItemIndex = currentItems.findIndex(item => item.id === itemId);

      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const updatedItems = [...currentItems];
        const existingItem = updatedItems[existingItemIndex];
        const newQuantity = existingItem.quantity + quantity;
        const maxQuantity = existingItem.maxQuantity || 999;

        updatedItems[existingItemIndex] = {
          ...existingItem,
          quantity: Math.min(newQuantity, maxQuantity),
        };

        return updatedItems;
      } else {
        // Add new item
        const cartItem: CartItem = {
          ...newItem,
          id: itemId,
          quantity: Math.min(quantity, newItem.maxQuantity || 999),
        };

        return [...currentItems, cartItem];
      }
    });
  };

  const removeItem = (itemId: string) => {
    setItems(currentItems => currentItems.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId: string, quantity: number) => {
    if (quantity <= 0) {
      removeItem(itemId);
      return;
    }

    setItems(currentItems =>
      currentItems.map(item => {
        if (item.id === itemId) {
          const maxQuantity = item.maxQuantity || 999;
          return {
            ...item,
            quantity: Math.min(quantity, maxQuantity),
          };
        }
        return item;
      })
    );
  };

  const clearCart = () => {
    setItems([]);
  };

  const getItemQuantity = (productId: string, variant?: any): number => {
    const itemId = generateItemId(productId, variant);
    const item = items.find(item => item.id === itemId);
    return item?.quantity || 0;
  };

  const isInCart = (productId: string, variant?: any): boolean => {
    return getItemQuantity(productId, variant) > 0;
  };

  const totalItems = items.reduce((total, item) => total + item.quantity, 0);
  const totalPrice = items.reduce((total, item) => total + item.price * item.quantity, 0);

  const value: CartContextType = {
    items,
    totalItems,
    totalPrice,
    isLoading,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    getItemQuantity,
    isInCart,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}

export function useCart(): CartContextType {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}

// Helper hooks for cart operations
export function useCartItem(productId: string, variant?: any) {
  const cart = useCart();
  const itemId = cart.items.find(
    item => item.productId === productId && JSON.stringify(item.variant) === JSON.stringify(variant)
  )?.id;

  return {
    quantity: cart.getItemQuantity(productId, variant),
    isInCart: cart.isInCart(productId, variant),
    addToCart: (item: Omit<CartItem, 'quantity'>, qty?: number) => cart.addItem(item, qty),
    removeFromCart: () => itemId && cart.removeItem(itemId),
    updateQuantity: (qty: number) => itemId && cart.updateQuantity(itemId, qty),
  };
}

export function useCartSummary() {
  const cart = useCart();

  const subtotal = cart.totalPrice;
  const tax = subtotal * 0.1; // 10% tax
  const shipping = subtotal > 500 ? 0 : 50; // Free shipping over ₹500
  const total = subtotal + tax + shipping;

  return {
    subtotal,
    tax,
    shipping,
    total,
    itemCount: cart.totalItems,
    isEmpty: cart.items.length === 0,
  };
}
