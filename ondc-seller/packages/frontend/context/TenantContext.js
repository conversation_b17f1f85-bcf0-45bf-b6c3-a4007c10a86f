import React, { createContext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import api from '../lib/api';

// Create context
const TenantContext = createContext();

/**
 * Provider component for tenant information
 * @param {Object} props - Component props
 * @returns {JSX.Element} Provider component
 */
export const TenantProvider = ({ children }) => {
  const [currentTenant, setCurrentTenant] = useState(null);
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const router = useRouter();

  // Load all tenants from Strapi
  useEffect(() => {
    let isMounted = true;

    const loadTenants = async () => {
      if (!isMounted) return;

      try {
        setLoading(true);
        setError(null);

        // Define mock tenants once to avoid duplication
        const mockTenants = [
          {
            id: 1,
            name: 'Organic Farms',
            description: 'Premium organic produce directly from farms',
            ondc_seller_id: 'ONDC-ORG-001'
          },
          {
            id: 2,
            name: 'Tech Gadgets',
            description: 'Latest technology gadgets and accessories',
            ondc_seller_id: 'ONDC-TECH-002'
          }
        ];

        // Use a timeout to prevent excessive API calls
        const response = await Promise.race([
          api.strapi.getSellers(),
          new Promise(resolve => setTimeout(() => resolve(null), 3000)) // 3 second timeout
        ]);

        if (!isMounted) return;

        if (response && response.items && response.items.length > 0) {
          setTenants(response.items);

          // Set default tenant if none is selected
          if (!currentTenant) {
            const defaultTenantId = process.env.NEXT_PUBLIC_DEFAULT_TENANT_ID;
            const defaultTenant = defaultTenantId
              ? response.items.find(t => t.ondc_seller_id === defaultTenantId)
              : response.items[0];

            if (defaultTenant && isMounted) {
              setCurrentTenant(defaultTenant);
              api.setTenantId(defaultTenant.ondc_seller_id);
            }
          }
        } else {
          // If no tenants are returned, use mock tenants for development
          if (isMounted) {
            setTenants(mockTenants);

            if (!currentTenant) {
              setCurrentTenant(mockTenants[0]);
              api.setTenantId(mockTenants[0].ondc_seller_id);
            }
          }
        }
      } catch (error) {
        if (!isMounted) return;

        console.error('Error loading tenants:', error);
        setError('Failed to load sellers. Please try again later.');

        // Use mock tenants for development
        if (process.env.NODE_ENV === 'development' && isMounted) {
          const mockTenants = [
            {
              id: 1,
              name: 'Organic Farms',
              description: 'Premium organic produce directly from farms',
              ondc_seller_id: 'ONDC-ORG-001'
            },
            {
              id: 2,
              name: 'Tech Gadgets',
              description: 'Latest technology gadgets and accessories',
              ondc_seller_id: 'ONDC-TECH-002'
            }
          ];

          setTenants(mockTenants);

          if (!currentTenant) {
            setCurrentTenant(mockTenants[0]);
            api.setTenantId(mockTenants[0].ondc_seller_id);
          }
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadTenants();

    // Cleanup function to prevent memory leaks
    return () => {
      isMounted = false;
    };
  }, [currentTenant]);

  // Handle tenant selection
  const selectTenant = (tenantId) => {
    console.log('Selecting tenant:', tenantId);
    const tenant = tenants.find(t => t.id === parseInt(tenantId) || t.id === tenantId || t.ondc_seller_id === tenantId);

    if (tenant) {
      console.log('Found tenant:', tenant);
      setCurrentTenant(tenant);
      api.setTenantId(tenant.ondc_seller_id);

      // Refresh the current page to load tenant-specific data
      router.replace(router.asPath);
    } else {
      console.error('Tenant not found:', tenantId);
    }
  };

  // Context value
  const value = {
    currentTenant,
    tenants,
    loading,
    error,
    selectTenant
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
};

/**
 * Hook for using tenant context
 * @returns {Object} Tenant context
 */
export const useTenant = () => {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

export default TenantContext;
