/**
 * Product categories data for ONDC Seller Platform
 * 
 * This file contains the category structure and metadata used throughout
 * the application for product organization and navigation.
 */

export interface Category {
  id: string;
  name: string;
  description: string;
  handle: string;
  image?: string;
  subcategories: Subcategory[];
  metadata?: Record<string, any>;
}

export interface Subcategory {
  id: string;
  name: string;
  description: string;
  handle: string;
  category_id: string;
  image?: string;
  metadata?: Record<string, any>;
}

export const productCategories: Category[] = [
  {
    id: 'fashion',
    name: 'Fashion',
    description: 'Clothing, accessories, and fashion items for all ages and styles',
    handle: 'fashion',
    image: '/images/categories/fashion.jpg',
    subcategories: [
      {
        id: 'mens',
        name: "Men's Clothing",
        description: 'Stylish clothing and accessories for men',
        handle: 'mens',
        category_id: 'fashion',
        image: '/images/categories/mens.jpg',
        metadata: {
          popular_items: ['shirts', 'jeans', 'suits', 'casual-wear'],
          size_guide: 'mens-size-guide',
        },
      },
      {
        id: 'womens',
        name: "Women's Clothing",
        description: 'Trendy fashion and accessories for women',
        handle: 'womens',
        category_id: 'fashion',
        image: '/images/categories/womens.jpg',
        metadata: {
          popular_items: ['dresses', 'tops', 'sarees', 'ethnic-wear'],
          size_guide: 'womens-size-guide',
        },
      },
      {
        id: 'kids',
        name: "Kids' Clothing",
        description: 'Comfortable and fun clothing for children',
        handle: 'kids',
        category_id: 'fashion',
        image: '/images/categories/kids.jpg',
        metadata: {
          popular_items: ['t-shirts', 'dresses', 'school-uniforms'],
          age_groups: ['0-2', '2-5', '5-8', '8-12'],
        },
      },
      {
        id: 'footwear',
        name: 'Footwear',
        description: 'Shoes, sandals, and footwear for all occasions',
        handle: 'footwear',
        category_id: 'fashion',
        image: '/images/categories/footwear.jpg',
        metadata: {
          popular_items: ['casual-shoes', 'formal-shoes', 'sports-shoes', 'sandals'],
          size_guide: 'footwear-size-guide',
        },
      },
    ],
    metadata: {
      seasonal_collections: ['summer', 'winter', 'monsoon'],
      trending_styles: ['ethnic', 'western', 'fusion'],
    },
  },
  {
    id: 'electronics',
    name: 'Electronics',
    description: 'Latest gadgets, devices, and electronic accessories',
    handle: 'electronics',
    image: '/images/categories/electronics.jpg',
    subcategories: [
      {
        id: 'smartphones',
        name: 'Smartphones',
        description: 'Latest smartphones and mobile devices',
        handle: 'smartphones',
        category_id: 'electronics',
        image: '/images/categories/smartphones.jpg',
        metadata: {
          popular_brands: ['apple', 'samsung', 'xiaomi', 'oneplus', 'google'],
          features: ['5g', 'wireless-charging', 'fast-charging', 'waterproof'],
        },
      },
      {
        id: 'laptops',
        name: 'Laptops',
        description: 'Laptops and notebooks for work and gaming',
        handle: 'laptops',
        category_id: 'electronics',
        image: '/images/categories/laptops.jpg',
        metadata: {
          popular_brands: ['apple', 'dell', 'hp', 'lenovo', 'asus'],
          use_cases: ['gaming', 'business', 'student', 'creative'],
        },
      },
      {
        id: 'tablets',
        name: 'Tablets',
        description: 'Tablets and e-readers for entertainment and productivity',
        handle: 'tablets',
        category_id: 'electronics',
        image: '/images/categories/tablets.jpg',
        metadata: {
          popular_brands: ['apple', 'samsung', 'amazon', 'microsoft'],
          screen_sizes: ['7-inch', '8-inch', '10-inch', '12-inch'],
        },
      },
      {
        id: 'accessories',
        name: 'Electronics Accessories',
        description: 'Chargers, cases, headphones, and other accessories',
        handle: 'accessories',
        category_id: 'electronics',
        image: '/images/categories/accessories.jpg',
        metadata: {
          popular_items: ['chargers', 'cases', 'headphones', 'power-banks'],
          compatibility: ['universal', 'brand-specific'],
        },
      },
    ],
    metadata: {
      warranty_info: 'manufacturer-warranty-applies',
      tech_support: 'available',
    },
  },
  {
    id: 'home-garden',
    name: 'Home & Garden',
    description: 'Home decor, furniture, and gardening supplies',
    handle: 'home-garden',
    image: '/images/categories/home-garden.jpg',
    subcategories: [
      {
        id: 'furniture',
        name: 'Furniture',
        description: 'Indoor and outdoor furniture for your home',
        handle: 'furniture',
        category_id: 'home-garden',
        image: '/images/categories/furniture.jpg',
      },
      {
        id: 'decor',
        name: 'Home Decor',
        description: 'Decorative items and accessories for your home',
        handle: 'decor',
        category_id: 'home-garden',
        image: '/images/categories/decor.jpg',
      },
      {
        id: 'kitchen',
        name: 'Kitchen & Dining',
        description: 'Kitchen appliances, cookware, and dining essentials',
        handle: 'kitchen',
        category_id: 'home-garden',
        image: '/images/categories/kitchen.jpg',
      },
      {
        id: 'garden',
        name: 'Garden & Outdoor',
        description: 'Gardening tools, plants, and outdoor equipment',
        handle: 'garden',
        category_id: 'home-garden',
        image: '/images/categories/garden.jpg',
      },
    ],
  },
  {
    id: 'books-media',
    name: 'Books & Media',
    description: 'Books, magazines, music, and entertainment media',
    handle: 'books-media',
    image: '/images/categories/books-media.jpg',
    subcategories: [
      {
        id: 'books',
        name: 'Books',
        description: 'Fiction, non-fiction, educational, and reference books',
        handle: 'books',
        category_id: 'books-media',
        image: '/images/categories/books.jpg',
      },
      {
        id: 'magazines',
        name: 'Magazines',
        description: 'Current magazines and periodicals',
        handle: 'magazines',
        category_id: 'books-media',
        image: '/images/categories/magazines.jpg',
      },
      {
        id: 'music',
        name: 'Music',
        description: 'CDs, vinyl records, and music accessories',
        handle: 'music',
        category_id: 'books-media',
        image: '/images/categories/music.jpg',
      },
      {
        id: 'movies',
        name: 'Movies & TV',
        description: 'DVDs, Blu-rays, and entertainment media',
        handle: 'movies',
        category_id: 'books-media',
        image: '/images/categories/movies.jpg',
      },
    ],
  },
];

/**
 * Get category by ID
 */
export function getCategoryById(categoryId: string): Category | undefined {
  return productCategories.find(category => category.id === categoryId);
}

/**
 * Get subcategory by category ID and subcategory ID
 */
export function getSubcategoryById(categoryId: string, subcategoryId: string): Subcategory | undefined {
  const category = getCategoryById(categoryId);
  return category?.subcategories.find(subcategory => subcategory.id === subcategoryId);
}

/**
 * Get all subcategories for a category
 */
export function getSubcategoriesByCategoryId(categoryId: string): Subcategory[] {
  const category = getCategoryById(categoryId);
  return category?.subcategories || [];
}

/**
 * Get category by handle
 */
export function getCategoryByHandle(handle: string): Category | undefined {
  return productCategories.find(category => category.handle === handle);
}

/**
 * Get subcategory by handle
 */
export function getSubcategoryByHandle(categoryHandle: string, subcategoryHandle: string): Subcategory | undefined {
  const category = getCategoryByHandle(categoryHandle);
  return category?.subcategories.find(subcategory => subcategory.handle === subcategoryHandle);
}

/**
 * Search categories and subcategories by name
 */
export function searchCategories(query: string): (Category | Subcategory)[] {
  const results: (Category | Subcategory)[] = [];
  const lowerQuery = query.toLowerCase();

  productCategories.forEach(category => {
    // Check category name
    if (category.name.toLowerCase().includes(lowerQuery) || 
        category.description.toLowerCase().includes(lowerQuery)) {
      results.push(category);
    }

    // Check subcategories
    category.subcategories.forEach(subcategory => {
      if (subcategory.name.toLowerCase().includes(lowerQuery) || 
          subcategory.description.toLowerCase().includes(lowerQuery)) {
        results.push(subcategory);
      }
    });
  });

  return results;
}

/**
 * Get category breadcrumb path
 */
export function getCategoryBreadcrumb(categoryId: string, subcategoryId?: string): Array<{label: string, href: string}> {
  const breadcrumb = [
    { label: 'Home', href: '/' },
    { label: 'Products', href: '/products' },
  ];

  const category = getCategoryById(categoryId);
  if (category) {
    breadcrumb.push({
      label: category.name,
      href: `/products/category/${category.id}`,
    });

    if (subcategoryId) {
      const subcategory = getSubcategoryById(categoryId, subcategoryId);
      if (subcategory) {
        breadcrumb.push({
          label: subcategory.name,
          href: `/products/category/${category.id}/${subcategory.id}`,
        });
      }
    }
  }

  return breadcrumb;
}
