{"name": "@ondc-seller/prisma", "version": "0.0.1", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsc", "clean": "rimraf .turbo node_modules dist", "db:generate": "prisma generate", "db:push": "prisma db push --skip-generate", "dev": "tsc --watch", "format": "prisma format", "lint": "eslint \"src/**/*.ts\"", "test": "jest"}, "dependencies": {"@prisma/client": "^6.9.0"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.10.4", "eslint": "^8.56.0", "jest": "^29.7.0", "prisma": "^6.9.0", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}}