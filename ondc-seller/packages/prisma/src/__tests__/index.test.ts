import { prisma } from '../index';

// Mock the PrismaClient
jest.mock('@prisma/client', () => {
  const mockPrismaClient = {
    $connect: jest.fn(),
    $disconnect: jest.fn(),
  };
  
  return {
    PrismaClient: jest.fn(() => mockPrismaClient),
  };
});

describe('Prisma Client', () => {
  it('should export a PrismaClient instance', () => {
    expect(prisma).toBeDefined();
    expect(typeof prisma).toBe('object');
  });
  
  it('should export types from @prisma/client', () => {
    // This is more of a TypeScript check, but we can at least verify the exports exist
    const exports = require('../index');
    expect(exports).toHaveProperty('prisma');
  });
});
