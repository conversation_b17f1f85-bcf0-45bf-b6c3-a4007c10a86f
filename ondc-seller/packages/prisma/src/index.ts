import { PrismaClient } from './generated/prisma';

export * from './generated/prisma';

// Create a singleton PrismaClient instance
const globalForPrisma = global as unknown as { prisma: PrismaClient };

export const prisma =
  globalForPrisma.prisma ||
  new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['query', 'error', 'warn'] : ['error'],
  });

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Export types from our e-commerce schema
export type {
  // Core e-commerce models
  Category,
  Product,
  ProductVariant,
  InventoryItem,

  // Supporting models
  ProductPrice,
  ProductTag,
  ProductImage,
  ProductOption,
  ProductOptionValue,
  ProductVariantOption,

  // Collections
  Collection,
  CollectionProduct,

  // ONDC integration
  OndcCatalogSync,

  // Audit and system
  AuditLog,

  // Enums
  ProductStatus,
  InventoryStatus,
} from './generated/prisma';
