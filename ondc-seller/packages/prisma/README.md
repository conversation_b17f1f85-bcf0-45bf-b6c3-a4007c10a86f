# ONDC Seller Platform - Prisma Package

## 🗄️ Comprehensive E-commerce Database Schema

This package contains a complete Prisma schema for the ONDC seller platform, following **Medusa Commerce patterns** with full **ONDC protocol integration**.

## 🚀 Quick Start

### 1. Install Dependencies

```bash
npm install
```

### 2. Generate Prisma Client

```bash
npm run db:generate
```

### 3. Validate Schema

```bash
node validate-schema.js
```

### 4. Set Up Database (Development)

```bash
# Set your DATABASE_URL in .env
npm run db:push
```

### 5. Set Up Database (Production)

```bash
prisma migrate dev --name init
```

## 📊 Schema Overview

### Core E-commerce Models (14 total)

#### 🏷️ **Category**

- Hierarchical product categorization
- Parent/child relationships
- SEO-friendly handles

#### 📦 **Product**

- Main product entity
- Medusa Commerce compatible
- ONDC integration fields
- Draft/Published/Archived status

#### 🎨 **ProductVariant**

- Product variations (size, color, etc.)
- Multi-tier pricing (base, compare, cost)
- Physical dimensions
- Unique SKU management

#### 📊 **InventoryItem**

- Real-time stock tracking
- Reserved quantity management
- Low stock alerts
- Multi-location support

#### 💰 **ProductPrice**

- Multi-currency support
- Regional pricing
- Price types (base, sale, member)

#### 🏷️ **ProductTag**

- Product tagging system
- Search optimization

#### 🖼️ **ProductImage**

- Product images with metadata
- Sort ordering
- Main image designation

#### ⚙️ **ProductOption & ProductOptionValue**

- Product options (size, color, material)
- Option values (Small, Medium, Large)

#### 📚 **Collection & CollectionProduct**

- Product collections for marketing
- Many-to-many relationships

#### 🔄 **OndcCatalogSync**

- ONDC synchronization tracking
- Error handling and statistics

#### 📝 **AuditLog**

- Complete change tracking
- User and session tracking

## 🔗 Key Relationships

```
Category (1) → Products (N)
Product (1) → ProductVariants (N)
ProductVariant (1) ↔ InventoryItem (1)
Product (N) ↔ Collections (N)
Category (1) → ChildCategories (N)
```

## 🎯 ONDC Integration

### ONDC-Specific Fields

- `ondcCategoryId` - Maps to ONDC category taxonomy
- `ondcItemId` - Unique ONDC item identifier
- `ondcVariantId` - ONDC variant identifier
- `ondcInventoryId` - ONDC inventory tracking

### Synchronization

- Real-time sync status tracking
- Error handling and retry logic
- Statistics and monitoring

## 🔧 Usage Examples

### Basic Product Creation

```typescript
import { prisma } from '@ondc-seller/prisma';

const product = await prisma.product.create({
  data: {
    title: 'iPhone 15 Pro',
    handle: 'iphone-15-pro',
    status: 'PUBLISHED',
    categoryId: 'category-id',
    variants: {
      create: {
        title: 'iPhone 15 Pro - 128GB',
        sku: 'IPHONE-15-PRO-128GB',
        price: 99900.0,
        inventoryItem: {
          create: {
            quantity: 50,
            lowStockThreshold: 10,
          },
        },
      },
    },
  },
});
```

### Complex Query with Relations

```typescript
const productWithEverything = await prisma.product.findUnique({
  where: { handle: 'iphone-15-pro' },
  include: {
    category: { include: { parentCategory: true } },
    variants: { include: { inventoryItem: true, prices: true } },
    tags: true,
    images: true,
    collections: { include: { collection: true } },
  },
});
```

### Inventory Management

```typescript
// Update inventory
await prisma.inventoryItem.update({
  where: { variantId: 'variant-id' },
  data: {
    quantity: { decrement: 1 },
    reservedQuantity: { increment: 1 },
  },
});

// Check low stock
const lowStockItems = await prisma.inventoryItem.findMany({
  where: {
    quantity: { lte: prisma.inventoryItem.fields.lowStockThreshold },
  },
  include: { variant: { include: { product: true } } },
});
```

## 📈 Performance Optimizations

### Strategic Indexes

- `handle` fields for SEO URLs
- `sku` for product variants
- `status` for filtering
- Relationship foreign keys

### Unique Constraints

- Product handles
- Variant SKUs
- Category handles
- Tag combinations

## 🛡️ Data Integrity

### Cascade Behaviors

- Product deletion → Cascades to variants, inventory, tags, images
- Category deletion → Cascades to products (with warning)
- Variant deletion → Cascades to inventory and prices

### Validation

- SKUs must be unique
- Handles must be URL-safe
- Prices must be positive
- Quantities cannot be negative

## 📝 Available Scripts

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database (development)
npm run db:push

# Format schema file
npm run format

# Build TypeScript
npm run build

# Run tests
npm run test

# Validate schema
node validate-schema.js
```

## 📚 Documentation

- **[Complete Schema Documentation](./SCHEMA_DOCUMENTATION.md)** - Detailed model documentation
- **[Test Suite](./src/test-schema.ts)** - Comprehensive testing examples
- **[Validation Script](./validate-schema.js)** - Schema validation tool

## 🔄 Migration Strategy

### Development

```bash
npm run db:push
```

### Production

```bash
prisma migrate dev --name "descriptive-name"
prisma migrate deploy
```

## 🚨 Important Notes

1. **Always backup** before running migrations in production
2. **Test migrations** in staging environment first
3. **Use transactions** for multi-table operations
4. **Monitor performance** with database query logs
5. **Validate data** before and after migrations

## 🤝 Contributing

1. Make schema changes in `prisma/schema.prisma`
2. Run `npm run db:generate` to update client
3. Run `npm run format` to format schema
4. Run `node validate-schema.js` to validate
5. Update documentation if needed
6. Test thoroughly before committing

## 📞 Support

For questions about the schema or database design, refer to:

- Schema documentation in this package
- Prisma documentation: https://prisma.io/docs
- Medusa Commerce patterns: https://medusajs.com
- ONDC protocol documentation

---

**Status**: ✅ **Production Ready**
**Models**: 14 core e-commerce models
**Validation**: All tests passing
**Integration**: ONDC protocol compatible
