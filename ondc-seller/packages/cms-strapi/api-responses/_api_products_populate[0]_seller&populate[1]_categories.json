{"data": [{"id": 2, "documentId": "xcjgtt8scjnbeios0xh73ht9", "name": "Organic Apples", "description": [{"type": "paragraph", "children": [{"text": "Fresh organic apples from the farm", "type": "text"}]}], "short_description": "Organic apples", "price": 150, "sale_price": 120, "sku": "ORG-APL-001", "inventory_quantity": 100, "product_status": "Published", "featured": true, "tags": "Organic, Fruit, Healthy", "weight": 1, "createdAt": "2025-05-19T08:03:20.895Z", "updatedAt": "2025-05-19T08:03:20.895Z", "publishedAt": "2025-05-19T08:03:21.148Z", "seller": {"id": 5, "documentId": "ov30y6mls0mwdq9cupgpdskr", "name": "Organic Farms", "description": [{"type": "paragraph", "children": [{"text": "Premium organic produce directly from farms", "type": "text"}]}], "email": "<EMAIL>", "phone": "**********", "address": "123 Farm Road", "city": "Bangalore", "state": "Karnataka", "pincode": "560001", "ondc_seller_id": "ONDC-ORG-001", "seller_status": "Active", "createdAt": "2025-05-19T07:42:05.735Z", "updatedAt": "2025-05-19T08:09:54.057Z", "publishedAt": "2025-05-19T08:09:54.080Z"}, "categories": [{"id": 10, "documentId": "kfp2uu1h08f6u3ztcrnos76n", "name": "Organic Food", "description": [{"type": "paragraph", "children": [{"text": "Fresh organic produce", "type": "text"}]}], "slug": "organic-food", "featured": true, "createdAt": "2025-05-19T07:58:24.402Z", "updatedAt": "2025-05-19T08:05:31.832Z", "publishedAt": "2025-05-19T08:05:31.853Z"}]}, {"id": 4, "documentId": "ibxz1kyhrv2tbccxeutnos8j", "name": "Smartphone X", "description": [{"type": "paragraph", "children": [{"text": "Latest smartphone with advanced features", "type": "text"}]}], "short_description": "Premium smartphone", "price": 50000, "sale_price": 45000, "sku": "TECH-SPX-001", "inventory_quantity": 50, "product_status": "Published", "featured": true, "tags": "Smartphone, Tech, Premium", "weight": 0.2, "createdAt": "2025-05-19T08:16:00.855Z", "updatedAt": "2025-05-19T08:16:03.028Z", "publishedAt": "2025-05-19T08:16:03.054Z", "seller": {"id": 6, "documentId": "u7hp6unuof92n1lbmjnm9vef", "name": "Tech Gadgets", "description": [{"type": "paragraph", "children": [{"text": "Latest technology gadgets and accessories", "type": "text"}]}], "email": "<EMAIL>", "phone": "8765432109", "address": "456 Tech Park", "city": "Mumbai", "state": "Maharashtra", "pincode": "400001", "ondc_seller_id": "ONDC-TECH-002", "seller_status": "Active", "createdAt": "2025-05-19T07:48:29.328Z", "updatedAt": "2025-05-19T08:12:01.720Z", "publishedAt": "2025-05-19T08:12:01.739Z"}, "categories": [{"id": 11, "documentId": "sktwaug9gczis6lm7i4rdmce", "name": "Smartphones", "description": [{"type": "paragraph", "children": [{"text": "Mobile phones and accessories", "type": "text"}]}], "slug": "smartphones", "featured": true, "createdAt": "2025-05-19T07:57:24.035Z", "updatedAt": "2025-05-19T08:06:23.494Z", "publishedAt": "2025-05-19T08:06:23.509Z"}]}], "meta": {"pagination": {"page": 1, "pageSize": 25, "pageCount": 1, "total": 2}}}