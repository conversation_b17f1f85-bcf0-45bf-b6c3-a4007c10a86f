#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { CallToolRequestSchema, ListToolsRequestSchema } from '@modelcontextprotocol/sdk/types.js';

const server = new Server(
  {
    name: 'medusa-mcp',
    version: '0.1.0',
  },
  {
    capabilities: {
      tools: {},
    },
  }
);

// Medusa API base URL
const MEDUSA_API_URL = process.env.MEDUSA_API_URL || 'http://localhost:9000';

// Helper function to make API calls to Medusa
async function callMedusaAPI(endpoint: string, options: RequestInit = {}) {
  const url = `${MEDUSA_API_URL}${endpoint}`;

  const defaultHeaders = {
    'Content-Type': 'application/json',
    Accept: 'application/json',
  };

  const response = await fetch(url, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }

  return await response.json();
}

// Define available tools
const tools = [
  {
    name: 'list_products',
    description: 'List all products from the Medusa store',
    inputSchema: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Number of products to return (default: 10)',
          default: 10,
        },
        offset: {
          type: 'number',
          description: 'Number of products to skip (default: 0)',
          default: 0,
        },
      },
    },
  },
  {
    name: 'get_product',
    description: 'Get a specific product by ID',
    inputSchema: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'Product ID',
        },
      },
      required: ['id'],
    },
  },
  {
    name: 'create_product',
    description: 'Create a new product',
    inputSchema: {
      type: 'object',
      properties: {
        title: {
          type: 'string',
          description: 'Product title',
        },
        description: {
          type: 'string',
          description: 'Product description',
        },
        handle: {
          type: 'string',
          description: 'Product handle (URL slug)',
        },
        status: {
          type: 'string',
          description: 'Product status',
          enum: ['draft', 'proposed', 'published', 'rejected'],
          default: 'draft',
        },
      },
      required: ['title'],
    },
  },
  {
    name: 'list_orders',
    description: 'List all orders from the Medusa store',
    inputSchema: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Number of orders to return (default: 10)',
          default: 10,
        },
        offset: {
          type: 'number',
          description: 'Number of orders to skip (default: 0)',
          default: 0,
        },
      },
    },
  },
  {
    name: 'get_order',
    description: 'Get a specific order by ID',
    inputSchema: {
      type: 'object',
      properties: {
        id: {
          type: 'string',
          description: 'Order ID',
        },
      },
      required: ['id'],
    },
  },
  {
    name: 'list_customers',
    description: 'List all customers from the Medusa store',
    inputSchema: {
      type: 'object',
      properties: {
        limit: {
          type: 'number',
          description: 'Number of customers to return (default: 10)',
          default: 10,
        },
        offset: {
          type: 'number',
          description: 'Number of customers to skip (default: 0)',
          default: 0,
        },
      },
    },
  },
];

server.setRequestHandler(ListToolsRequestSchema, async () => {
  return {
    tools,
  };
});

server.setRequestHandler(CallToolRequestSchema, async request => {
  const { name, arguments: args } = request.params;

  try {
    let result;

    switch (name) {
      case 'list_products':
        const { limit = 10, offset = 0 } = args as any;
        result = await callMedusaAPI(`/admin/products?limit=${limit}&offset=${offset}`);
        break;

      case 'get_product':
        const { id: productId } = args as any;
        result = await callMedusaAPI(`/admin/products/${productId}`);
        break;

      case 'create_product':
        const productData = args as any;
        result = await callMedusaAPI('/admin/products', {
          method: 'POST',
          body: JSON.stringify(productData),
        });
        break;

      case 'list_orders':
        const { limit: orderLimit = 10, offset: orderOffset = 0 } = args as any;
        result = await callMedusaAPI(`/admin/orders?limit=${orderLimit}&offset=${orderOffset}`);
        break;

      case 'get_order':
        const { id: orderId } = args as any;
        result = await callMedusaAPI(`/admin/orders/${orderId}`);
        break;

      case 'list_customers':
        const { limit: customerLimit = 10, offset: customerOffset = 0 } = args as any;
        result = await callMedusaAPI(
          `/admin/customers?limit=${customerLimit}&offset=${customerOffset}`
        );
        break;

      default:
        throw new Error(`Unknown tool: ${name}`);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(result, null, 2),
        },
      ],
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error: ${error instanceof Error ? error.message : String(error)}`,
        },
      ],
      isError: true,
    };
  }
});

async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport);
  console.error('Medusa MCP server running on stdio');
}

main().catch(error => {
  console.error('Server error:', error);
  process.exit(1);
});
