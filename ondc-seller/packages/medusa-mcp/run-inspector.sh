#!/bin/bash

# <PERSON><PERSON>t to run the MCP Inspector

# Kill any existing MCP Inspector processes
echo "Killing any existing MCP Inspector processes..."
pkill -f "mcp-inspector" || true

# Kill any process using ports 6280 and 6281
echo "Freeing ports 6280 and 6281..."
fuser -k 6280/tcp || true
fuser -k 6281/tcp || true

# Wait for port to be released
sleep 2

# Run the MCP Inspector
echo "Starting MCP Inspector..."
npx @modelcontextprotocol/inspector
