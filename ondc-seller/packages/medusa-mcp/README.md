# `medusa-mcp`

## Overview

`medusa-mcp` is a **Model Context Protocol (MCP) server** designed for integration with the Medusa JavaScript SDK. It provides a scalable backend layer for managing and interacting with Medusa’s data models, enabling automation, orchestration, and intelligent service extensions.

---

## 🧩 What is an MCP Server?

An **MCP server** is a modular, extensible backend that:

- Enables **real-time service orchestration**
- Supports **standardized, high-throughput communication**
- Acts as a **bridge between AI/automation tools and real-world systems**

These servers are used in areas like AI, IoT, and enterprise software to connect various services and automate tasks using standardized protocols like JSON-RPC.

### 🔑 Key Features

- **Modular Architecture** – Composable services for flexibility
- **High Efficiency** – Optimized for speed and scale
- **Extensible Design** – Add new capabilities easily
- **Cross-Environment Deployment** – Cloud, on-prem, or hybrid
- **AI-Ready Interfaces** – Integrate LLMs and tools seamlessly

### 🧠 Role in AI Systems

MCP servers allow AI agents to:

- Access real-time data from APIs, files, or databases
- Automate business processes (e.g., order fulfillment, pricing updates)
- Interact with external services in a secure and controlled way

---

---

## 🚀 Medusa JS + MCP

Using `medusa-mcp`, Medusa JS can:

- Automate workflows (e.g., inventory or pricing adjustments)
- Connect with external tools (email, analytics, etc.)
- Use AI agents to analyze trends and trigger actions
- Enable scalable, modular architecture for commerce platforms

---

## ✨ Features

- ✅ **Model Context Protocol (MCP)** support
- 📈 **Scalable** infrastructure
- 🧱 **Extensible** plugin architecture
- 🔗 **Integrated** with Medusa JS SDK

---

## 🛠️ Installation

Clone the repository and install dependencies:

```bash
npm install
```

Build the project:

```bash
npm run build
```

---

## ▶️ Usage

### Fixed Implementation

We've created a fixed implementation that addresses TypeScript compatibility issues:

1. Fixed the `StoreProductListResponse` import issue
2. Fixed the `hooks` property on the `Client` type
3. Resolved TypeScript errors with parameter types and Zod schema integration
4. Improved error handling

#### Using the Startup Scripts (Recommended)

We've created startup scripts to ensure a clean startup without port conflicts:

```bash
# Option 1: Use the MCP Proxy Server (recommended)
# This starts both the MCP Proxy Server and the MCP Inspector in one command
./run-mcp-proxy.sh

# Option 2: Use the all-in-one test script with simple HTTP server
./test-simple-server.sh

# Option 3: Use the all-in-one test script with MCP server
./test-mcp-inspector.sh

# Option 4: Start the MCP server and inspector separately
# Start the MCP server (kills existing processes and starts the server)
./start-mcp.sh

# Run the MCP Inspector in a separate terminal
./run-inspector.sh
```

#### Manual Startup

You can also run the fixed implementation manually in development mode:

```bash
# Run simplified Medusa-only MCP server (recommended)
npm run dev:simple

# Run Medusa-only MCP server
npm run dev:fixed

# Run Strapi-only MCP server
npm run dev:strapi

# Run combined MCP server (Medusa + Strapi)
npm run dev:combined
```

For production use, build the project first and then run the server:

```bash
# Build the project
npm run build:fixed

# Run simplified Medusa-only MCP server (recommended)
npm run start:simple

# Run Medusa-only MCP server
npm run start:fixed

# Run Strapi-only MCP server
npm run start:strapi

# Run combined MCP server (Medusa + Strapi)
npm run start:combined
```

### Original Implementation

Start the original server:

```bash
npm start
```

Test using the MCP Inspector:

```bash
# Use our custom configuration to avoid port conflicts
npm run inspector

# Or use the direct command
npx @modelcontextprotocol/inspector --config mcp-inspector.config.json ./dist/index.js
```

> **Note:** If you encounter port conflicts, use the `./run-inspector.sh` script to ensure a clean startup.

---

## 🌍 Environment Variables

| Variable             | Description                         |
| -------------------- | ----------------------------------- |
| `MEDUSA_BACKEND_URL` | Your Medusa backend URL             |
| `PUBLISHABLE_KEY`    | Your Medusa publishable API key     |
| `MEDUSA_USERNAME`    | Medusa admin username (for admin)   |
| `MEDUSA_PASSWORD`    | Medusa admin password (for admin)   |
| `DEFAULT_TENANT_ID`  | Default tenant ID for multi-tenancy |
| `STRAPI_URL`         | Your Strapi CMS URL                 |
| `STRAPI_API_TOKEN`   | Your Strapi API token               |

Create a `.env` file in the root directory with these variables.

Server runs at: [http://localhost:3000](http://localhost:3000)

---

## 🧠 Architecture Diagram

Here's how the `medusa-mcp` server fits into a typical setup with Medusa JS and external systems:

```

       +-------------------------+
       |     AI Assistant /      |
       |     LLM / Automation    |
       +-----------+-------------+
                   |
                   v
    +--------------+--------------+
    |     MCP Server (medusa-mcp) |
    |-----------------------------|
    | - JSON-RPC Communication    |
    | - AI-Ready Interface        |
    | - Plugin Support            |
    +------+----------------------+
                   |
                   +
                   |
                   v
         +-------------------+
         | Medusa Backend    |
         | (Products, Orders)|
         +-------------------+
                   |
                   |
                   v
           +--------------+
           | Medusa Store |
           | Frontend     |
           +--------------+
                   |
                   |
                   v
      +-------------------------+
      | External Services / API |
      | (e.g., Payments, Email) |
      +-------------------------+
```

## 🧪 Customization

To tailor the server to your Medusa setup:

> Replace `admin.json` and `store.json` with your own OAS definitions for fine-grained control.

- Replace the OpenAPI schemas in the `oas/` folder:
  - `admin.json` – Admin endpoints
  - `store.json` – Storefront endpoints

Use the [`@medusajs/medusa-oas-cli`](https://www.npmjs.com/package/@medusajs/medusa-oas-cli) to regenerate these files.

You can also **fork this project** to build your own custom MCP-powered Medusa integration.

---

## 🤝 Contributing

We welcome contributions! Please see our [CONTRIBUTING.md](CONTRIBUTING.md) guide.

---

## 📄 License

This project is licensed under the MIT License. See the [LICENSE](LICENSE) file for details.
