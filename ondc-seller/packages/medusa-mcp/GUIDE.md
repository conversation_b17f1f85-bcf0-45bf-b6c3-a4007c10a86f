# Medusa MCP Server Guide

This guide provides step-by-step instructions on how to run and test the Medusa Model Context Protocol (MCP) server.

## Prerequisites

- Node.js 18+
- npm or yarn
- Medusa backend running (optional for testing)
- Strapi CMS (optional for testing)

## Installation

1. Clone the repository:

```bash
git clone https://github.com/SGFGOV/medusa-mcp.git
cd medusa-mcp
```

2. Install dependencies:

```bash
npm install
```

## Configuration

1. Create a `.env` file in the root directory with the following variables:

```
# Medusa Backend URL - Point to your Medusa backend
MEDUSA_BACKEND_URL=http://localhost:9001

# Publishable API key from your Medusa store
PUBLISHABLE_KEY=pk_test_123456

# Admin credentials (for admin operations)
MEDUSA_USERNAME=<EMAIL>
MEDUSA_PASSWORD=admin123

# Development mode
NODE_ENV=development

# Multi-tenancy support
ENABLE_MULTI_TENANCY=true
DEFAULT_TENANT_ID=default

# Debug mode
DEBUG=true
LOG_LEVEL=debug

# Strapi CMS configuration
STRAPI_URL=http://localhost:1337
STRAPI_API_TOKEN=your_strapi_api_token
```

## Running the MCP Server

### Development Mode

You can run the MCP server in development mode using the following commands:

```bash
# Run simplified Medusa-only MCP server (recommended)
npm run dev:simple

# Run Medusa-only MCP server
npm run dev:fixed

# Run Strapi-only MCP server
npm run dev:strapi

# Run combined MCP server (Medusa + Strapi)
npm run dev:combined
```

### Production Mode

For production use, build the project first and then run the server:

```bash
# Build the project
npm run build:fixed

# Run simplified Medusa-only MCP server (recommended)
npm run start:simple

# Run Medusa-only MCP server
npm run start:fixed

# Run Strapi-only MCP server
npm run start:strapi

# Run combined MCP server (Medusa + Strapi)
npm run start:combined
```

## Testing with MCP Inspector

You can test the MCP server using the MCP Inspector tool:

1. Install MCP Inspector globally:

```bash
npm install -g @modelcontextprotocol/inspector
```

2. Run the MCP server in one terminal:

```bash
npm run dev:simple
```

3. Run the MCP Inspector in another terminal:

```bash
mcp-inspector m
```

4. The MCP Inspector will connect to the MCP server and allow you to test the available tools.

## Available Tools

### Medusa Tools

- `listProducts`: List products from the Medusa store
  - Parameters:
    - `limit` (number, optional): Maximum number of products to return
    - `offset` (number, optional): Number of products to skip
    - `q` (string, optional): Search query
    - `category_id` (string, optional): Filter by category ID

- `getProduct`: Get a product by ID from the Medusa store
  - Parameters:
    - `id` (string, required): Product ID

- `listCategories`: List product categories from the Medusa store
  - Parameters:
    - `limit` (number, optional): Maximum number of categories to return
    - `offset` (number, optional): Number of categories to skip
    - `parent_id` (string, optional): Filter by parent category ID

### Strapi Tools

- `getStrapiContent`: Get content from Strapi CMS
  - Parameters:
    - `contentType` (string, required): Content type to fetch
    - `id` (string, optional): ID of the content to fetch
    - `populate` (string, optional): Fields to populate
    - `locale` (string, optional): Locale to fetch content in
    - `tenantId` (string, optional): Tenant ID for multi-tenant support

- `listStrapiContent`: List content from Strapi CMS
  - Parameters:
    - `contentType` (string, required): Content type to list
    - `filters` (string, optional): Filters in JSON string format
    - `sort` (string, optional): Sort fields
    - `pagination` (string, optional): Pagination parameters in JSON string format
    - `populate` (string, optional): Fields to populate
    - `locale` (string, optional): Locale to fetch content in
    - `tenantId` (string, optional): Tenant ID for multi-tenant support

## Troubleshooting

If you encounter any issues, check the following:

1. Make sure your Medusa backend is running and accessible
2. Verify that your Strapi CMS is running and the API token is valid
3. Check the console logs for detailed error messages
4. Ensure that the required environment variables are set correctly

## License

ISC
