#!/bin/bash

# Start script for Medusa MCP Server

# Kill any existing MCP processes
echo "Killing any existing MCP processes..."
pkill -f "mcp|strapi-mcp" || true

# Kill any process using port 6277 or 6278
echo "Freeing ports 6277 and 6278..."
fuser -k 6277/tcp || true
fuser -k 6278/tcp || true

# Wait for ports to be released
sleep 2

# Skip building due to TypeScript compatibility issues
# and run in development mode instead

# Start the MCP server
echo "Starting Simple MCP Server..."
npm run dev:simple-server
