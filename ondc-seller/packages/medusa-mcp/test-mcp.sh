#!/bin/bash

# Test script for Medusa MCP Server

# Start the MCP server in the background
echo "Starting Medusa MCP Server..."
npm run dev:simple > mcp-server.log 2>&1 &
SERVER_PID=$!

# Wait for the server to start
sleep 2

# Check if the server is running
if ! ps -p $SERVER_PID > /dev/null; then
  echo "Failed to start MCP server. Check mcp-server.log for details."
  exit 1
fi

echo "MCP server started with PID: $SERVER_PID"

# Test the listProducts tool
echo "Testing listProducts tool..."
echo '{"jsonrpc":"2.0","id":"1","method":"mcp.callTool","params":{"name":"listProducts","args":{"limit":10}}}' | nc -U /tmp/mcp.sock

# Test the getProduct tool
echo "Testing getProduct tool..."
echo '{"jsonrpc":"2.0","id":"2","method":"mcp.callTool","params":{"name":"getProduct","args":{"id":"prod_123"}}}' | nc -U /tmp/mcp.sock

# Test the listCategories tool
echo "Testing listCategories tool..."
echo '{"jsonrpc":"2.0","id":"3","method":"mcp.callTool","params":{"name":"listCategories","args":{"limit":10}}}' | nc -U /tmp/mcp.sock

# Kill the server
echo "Killing MCP server..."
kill $SERVER_PID

echo "Test completed."
