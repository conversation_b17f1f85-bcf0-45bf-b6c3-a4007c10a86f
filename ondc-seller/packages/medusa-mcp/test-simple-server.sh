#!/bin/bash

# Test script for Simple MCP Server with MCP Inspector

# Kill any existing processes
echo "Killing any existing processes..."
pkill -f "mcp|inspector" || true
fuser -k 6277/tcp || true
fuser -k 6278/tcp || true
fuser -k 6274/tcp || true

# Wait for ports to be released
sleep 2

# Start the simple server in the background
echo "Starting Simple MCP Server..."
npm run dev:simple-server > simple-server.log 2>&1 &
SERVER_PID=$!

# Wait for the server to start
sleep 2

# Check if the server is running
if ! ps -p $SERVER_PID > /dev/null; then
  echo "Failed to start Simple MCP Server. Check simple-server.log for details."
  exit 1
fi

echo "Simple MCP Server started with PID: $SERVER_PID"

# Test the server
echo "Testing Simple MCP Server..."
RESPONSE=$(wget -qO- http://localhost:6278/)
echo "Response from Simple MCP Server:"
echo "$RESPONSE"

# Start the MCP Inspector in the background
echo "Starting MCP Inspector..."
npx @modelcontextprotocol/inspector > mcp-inspector.log 2>&1 &
INSPECTOR_PID=$!

# Wait for the inspector to start
sleep 2

# Check if the inspector is running
if ! ps -p $INSPECTOR_PID > /dev/null; then
  echo "Failed to start MCP Inspector. Check mcp-inspector.log for details."
  kill $SERVER_PID
  exit 1
fi

echo "MCP Inspector started with PID: $INSPECTOR_PID"

# Open the MCP Inspector in the browser
echo "Opening MCP Inspector in the browser..."
xdg-open http://127.0.0.1:6274 || open http://127.0.0.1:6274 || echo "Failed to open browser. Please open http://127.0.0.1:6274 manually."

# Wait for user input
echo ""
echo "Press Enter to stop the Simple MCP Server and MCP Inspector..."
read

# Kill the processes
echo "Killing Simple MCP Server and MCP Inspector..."
kill $SERVER_PID $INSPECTOR_PID

echo "Test completed."
