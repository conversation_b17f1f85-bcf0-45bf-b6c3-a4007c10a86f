{"name": "@sgftech/medusa-mcp", "version": "1.0.0", "main": "index.js", "private": "false", "scripts": {"dev:fixed": "ts-node src/fixed/index.ts", "dev:strapi": "ts-node src/fixed/strapi.ts", "dev:combined": "ts-node src/fixed/combined.ts", "dev:simple": "ts-node src/fixed/simple.ts", "dev:minimal": "ts-node src/fixed/minimal.ts", "dev:basic": "ts-node src/fixed/basic.ts", "dev:test": "ts-node src/fixed/test.ts", "dev:simple-server": "node src/fixed/simple-server.js", "dev:mcp-proxy": "node src/fixed/mcp-proxy.js", "test": "echo \"Error: no test specified\" && exit 1", "build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "inspector": "mcp-inspector m --config mcp-inspector.config.json", "dev:inspector": "npx @modelcontextprotocol/inspector --config mcp-inspector.config.json node dist/index.js", "inspector:simple": "npx @modelcontextprotocol/inspector --config mcp-inspector.config.json node dist/fixed/simple.js", "inspector:minimal": "npx @modelcontextprotocol/inspector --config mcp-inspector.config.json node dist/fixed/minimal.js", "build:fixed": "tsc -p tsconfig.json && cp -r src/fixed dist/", "start:fixed": "node dist/fixed/index.js", "start:strapi": "node dist/fixed/strapi.js", "start:combined": "node dist/fixed/combined.js", "start:simple": "node dist/fixed/simple.js", "start:minimal": "node dist/fixed/minimal.js"}, "keywords": [], "author": {"name": "<PERSON><PERSON>d <PERSON>", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/SGFGOV/medusa-mcp.git"}, "license": "ISC", "description": "", "dependencies": {"@medusajs/js-sdk": "2.6.0", "@modelcontextprotocol/sdk": "^1.8.0", "dotenv": "^16.4.7", "node-fetch": "^2.7.0", "zod": "^3.24.2"}, "devDependencies": {"@mikro-orm/core": "^6.4.11", "@types/node": "^22.14.0", "@types/node-fetch": "^2.6.12", "@typescript-eslint/eslint-plugin": "^5.33.1", "@typescript-eslint/parser": "^5.33.1", "awilix": "^12.0.5", "eslint": "^8.22.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^8.5.0", "eslint-config-typescript": "^3.0.0", "eslint-plugin-file-progress": "^1.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-jsx-a11y": "^6.4.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "ioredis": "^5.6.0", "mcp-inspector": "^1.0.0", "prettier": "^2.7.1", "typescript": "^5.8.3", "vite": "^6.2.5"}}