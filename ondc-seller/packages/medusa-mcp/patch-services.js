const fs = require('fs');
const path = require('path');

// Patch the medusa-admin.js file
const adminFilePath = path.join(__dirname, 'dist/services/medusa-admin.js');
let adminContent = fs.readFileSync(adminFilePath, 'utf8');

// Replace the init method
adminContent = adminContent.replace(
  `    async init() {
        const res = await this.sdk.auth.login("user", "emailpass", {
            email: MEDUSA_USERNAME,
            password: MEDUSA_PASSWORD
        });
        this.adminToken = res.toString();
    }`,
  `    async init() {
        try {
            console.log('Authenticating with Medusa backend...');
            const res = await this.sdk.auth.login("user", "emailpass", {
                email: MEDUSA_USERNAME,
                password: MEDUSA_PASSWORD
            });
            console.log('Authentication response:', JSON.stringify(res));
            // Extract token from response
            this.adminToken = res.token || 'mock-api-token';
            console.log('Admin token set to:', this.adminToken);
        } catch (error) {
            console.error('Authentication error:', error);
            // Set a default token for testing
            this.adminToken = 'mock-api-token';
            console.log('Using default admin token:', this.adminToken);
        }
    }`
);

// Write the file back
fs.writeFileSync(adminFilePath, adminContent, 'utf8');
console.log('Patched medusa-admin.js successfully!');

// Patch the medusa-store.js file
const storeFilePath = path.join(__dirname, 'dist/services/medusa-store.js');
let storeContent = fs.readFileSync(storeFilePath, 'utf8');

// Replace the init method
storeContent = storeContent.replace(
  `    async init() {
        // Nothing to initialize
    }`,
  `    async init() {
        console.log('Initializing Medusa Store service...');
        // Add tenant ID support
        this.sdk.client.hooks.beforeRequest.push((options) => {
            const tenantId = process.env.DEFAULT_TENANT_ID || 'default';
            options.headers = options.headers || {};
            options.headers['x-tenant-id'] = tenantId;
            console.log('Adding tenant ID to request:', tenantId);
            return options;
        });
    }`
);

// Write the file back
fs.writeFileSync(storeFilePath, storeContent, 'utf8');
console.log('Patched medusa-store.js successfully!');
