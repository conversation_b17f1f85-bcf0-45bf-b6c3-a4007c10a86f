# ONDC Adapter Service - Developer Guide

## Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Getting Started](#getting-started)
4. [Development Setup](#development-setup)
5. [API Documentation](#api-documentation)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [Contributing](#contributing)

## Overview

The ONDC Adapter Service is a Node.js/TypeScript microservice that acts as a protocol adapter between the ONDC (Open Network for Digital Commerce) network and Medusa Commerce backend. It provides:

- **ONDC Protocol Compliance**: Full implementation of ONDC protocol messages
- **Multi-tenant Architecture**: Support for multiple sellers on a single instance
- **Authentication & Authorization**: Secure access control with JWT and API keys
- **Rate Limiting**: Protection against abuse and DoS attacks
- **Health Monitoring**: Comprehensive health checks and metrics
- **Development Mode**: Mock data and simplified authentication for development

## Architecture

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ONDC Network  │    │  ONDC Adapter   │    │ Medusa Commerce │
│                 │◄──►│    Service      │◄──►│    Backend      │
│   (BAP/BPP)     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │   Database      │
                       └─────────────────┘
```

### Service Components

- **Controllers**: Handle HTTP requests and responses
- **Services**: Business logic and external integrations
- **Middleware**: Authentication, rate limiting, error handling
- **Validators**: ONDC message validation
- **Types**: TypeScript type definitions

### Key Features

- **ONDC Protocol Support**: search, select, init, confirm, status, track, cancel, update, rating
- **Multi-tenancy**: Isolated data and configuration per tenant
- **Development Mode**: Hardcoded credentials (demo/demo) and mock data
- **Production Mode**: Full authentication and real API integration
- **Health Checks**: Kubernetes-ready health endpoints
- **Rate Limiting**: Configurable rate limits per endpoint
- **Comprehensive Logging**: Structured logging with Winston
- **Error Handling**: Graceful error handling with proper ONDC error responses

## Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- PostgreSQL 12+
- Redis 6+
- Medusa Commerce backend (optional for development)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ondc-seller/packages/ondc-adapter
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start in development mode**
   ```bash
   npm run dev
   ```

5. **Test the service**
   ```bash
   curl http://localhost:3001/health
   ```

## Development Setup

### Environment Configuration

Create a `.env` file with the following variables:

```bash
# Application
NODE_ENV=development
PORT=3001
LOG_LEVEL=debug

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/ondc_seller_dev
REDIS_URL=redis://localhost:6379

# Medusa Integration
MEDUSA_BACKEND_URL=http://localhost:9001
MEDUSA_API_KEY=your-medusa-api-key

# ONDC Network (development values)
ONDC_NETWORK_URL=https://staging.ondc.org
ONDC_SUBSCRIBER_ID=dev-seller-app
ONDC_SUBSCRIBER_URL=http://localhost:3001
ONDC_SIGNING_PRIVATE_KEY=dev-signing-key
ONDC_ENCRYPTION_PRIVATE_KEY=dev-encryption-key
ONDC_UNIQUE_KEY_ID=dev-unique-key

# Authentication
JWT_SECRET=your-jwt-secret
JWT_EXPIRES_IN=24h

# Security
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Development Features
ENABLE_MOCK_ONDC=true
ENABLE_DEBUG_LOGGING=true
```

### Development Mode Features

When `NODE_ENV=development`:

- **Mock Authentication**: Use `demo/demo` credentials
- **Mock Data**: Returns sample data instead of calling real APIs
- **Relaxed Validation**: More permissive validation rules
- **Debug Logging**: Detailed request/response logging

### Development Workflow

1. **Start the service**
   ```bash
   npm run dev
   ```

2. **Run tests**
   ```bash
   npm test
   npm run test:watch  # Watch mode
   npm run test:coverage  # With coverage
   ```

3. **Lint code**
   ```bash
   npm run lint
   npm run lint:fix
   ```

4. **Build for production**
   ```bash
   npm run build
   ```

### Testing ONDC Endpoints

#### Search Request
```bash
curl -X POST http://localhost:3001/api/v1/ondc/search \
  -H "Content-Type: application/json" \
  -H "X-Tenant-ID: demo" \
  -H "X-Dev-Username: demo" \
  -H "X-Dev-Password: demo" \
  -d '{
    "context": {
      "domain": "ONDC:RET10",
      "country": "IND",
      "city": "std:080",
      "action": "search",
      "core_version": "1.2.0",
      "bap_id": "buyer-app.ondc.org",
      "bap_uri": "https://buyer-app.ondc.org/protocol/v1",
      "bpp_id": "seller-app.ondc.org",
      "bpp_uri": "https://seller-app.ondc.org/protocol/v1",
      "transaction_id": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
      "message_id": "3fa85f64-5717-4562-b3fc-2c963f66afa7",
      "timestamp": "2025-05-22T10:15:00.000Z",
      "ttl": "PT30S"
    },
    "message": {
      "intent": {
        "item": {
          "descriptor": {
            "name": "laptop"
          }
        },
        "category": {
          "id": "Electronics"
        }
      }
    }
  }'
```

#### Health Check
```bash
curl http://localhost:3001/health
```

#### API Status
```bash
curl -H "X-Tenant-ID: demo" \
     -H "X-Dev-Username: demo" \
     -H "X-Dev-Password: demo" \
     http://localhost:3001/api/v1/status
```

## API Documentation

### OpenAPI Specification

The complete API documentation is available in OpenAPI 3.0 format:
- **File**: `docs/openapi.yaml`
- **Interactive Docs**: Available at `/api-docs` when running the service

### Key Endpoints

#### Health Endpoints
- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health with dependency checks
- `GET /health/ready` - Kubernetes readiness probe
- `GET /health/live` - Kubernetes liveness probe

#### API Endpoints
- `GET /api/v1/status` - API status and feature information

#### ONDC Protocol Endpoints
- `POST /api/v1/ondc/search` - Handle search requests
- `POST /api/v1/ondc/select` - Handle select requests
- `POST /api/v1/ondc/init` - Handle init requests
- `POST /api/v1/ondc/confirm` - Handle confirm requests
- `POST /api/v1/ondc/status` - Handle status requests
- `POST /api/v1/ondc/track` - Handle track requests
- `POST /api/v1/ondc/cancel` - Handle cancel requests
- `POST /api/v1/ondc/update` - Handle update requests
- `POST /api/v1/ondc/rating` - Handle rating requests

### Authentication

#### Development Mode
Use headers for authentication:
```bash
X-Tenant-ID: demo
X-Dev-Username: demo
X-Dev-Password: demo
```

#### Production Mode
Use JWT tokens:
```bash
Authorization: Bearer <jwt-token>
X-Tenant-ID: <tenant-id>
```

### Rate Limiting

Default rate limits:
- **Standard endpoints**: 100 requests per 15 minutes
- **ONDC endpoints**: 200 requests per minute
- **Health endpoints**: 1000 requests per minute

Rate limit headers in responses:
- `X-RateLimit-Limit`: Request limit
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time

### Error Handling

#### Standard HTTP Errors
```json
{
  "error": {
    "message": "Authentication required",
    "statusCode": 401,
    "timestamp": "2025-05-22T10:15:00.000Z",
    "requestId": "req-123",
    "path": "/api/v1/ondc/search"
  }
}
```

#### ONDC Protocol Errors
```json
{
  "message": {
    "ack": {
      "status": "NACK"
    }
  },
  "error": {
    "type": "DOMAIN-ERROR",
    "code": "20001",
    "message": "Invalid message structure",
    "path": "message.intent.item"
  }
}
```
