# ONDC Protocol Schemas
# Complete schema definitions for ONDC message structures

schemas:
  # ONDC Context Schema
  ONDCContext:
    type: object
    required:
      - domain
      - country
      - city
      - action
      - core_version
      - bap_id
      - bap_uri
      - bpp_id
      - bpp_uri
      - transaction_id
      - message_id
      - timestamp
    properties:
      domain:
        type: string
        example: "ONDC:RET10"
        description: ONDC domain identifier
      country:
        type: string
        pattern: "^[A-Z]{3}$"
        example: "IND"
        description: ISO 3166-1 alpha-3 country code
      city:
        type: string
        example: "std:080"
        description: City code
      action:
        type: string
        enum: [search, select, init, confirm, status, track, cancel, update, rating, on_search, on_select, on_init, on_confirm, on_status, on_track, on_cancel, on_update, on_rating]
        description: ONDC action type
      core_version:
        type: string
        example: "1.2.0"
        description: ONDC core version
      bap_id:
        type: string
        example: "buyer-app.ondc.org"
        description: Buyer App ID
      bap_uri:
        type: string
        format: uri
        example: "https://buyer-app.ondc.org/protocol/v1"
        description: Buyer App URI
      bpp_id:
        type: string
        example: "seller-app.ondc.org"
        description: Seller App ID
      bpp_uri:
        type: string
        format: uri
        example: "https://seller-app.ondc.org/protocol/v1"
        description: Seller App URI
      transaction_id:
        type: string
        format: uuid
        example: "3fa85f64-5717-4562-b3fc-2c963f66afa6"
        description: Unique transaction identifier
      message_id:
        type: string
        format: uuid
        example: "3fa85f64-5717-4562-b3fc-2c963f66afa7"
        description: Unique message identifier
      timestamp:
        type: string
        format: date-time
        example: "2025-05-22T10:15:00.000Z"
        description: Message timestamp
      key:
        type: string
        description: Encryption key identifier
      ttl:
        type: string
        example: "PT30S"
        description: Time to live in ISO 8601 duration format

  # ONDC Search Request
  ONDCSearchRequest:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        properties:
          intent:
            type: object
            properties:
              item:
                type: object
                properties:
                  descriptor:
                    type: object
                    properties:
                      name:
                        type: string
                        example: "laptop"
              category:
                type: object
                properties:
                  id:
                    type: string
                    example: "Electronics"
              provider:
                type: object
                properties:
                  id:
                    type: string
              fulfillment:
                type: object
                properties:
                  end:
                    type: object
                    properties:
                      location:
                        $ref: '#/schemas/Location'

  # ONDC Search Response
  ONDCSearchResponse:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - catalog
        properties:
          catalog:
            type: object
            properties:
              bpp/descriptor:
                $ref: '#/schemas/Descriptor'
              bpp/providers:
                type: array
                items:
                  $ref: '#/schemas/Provider'

  # ONDC Select Request
  ONDCSelectRequest:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order
        properties:
          order:
            type: object
            required:
              - provider
              - items
            properties:
              provider:
                type: object
                required:
                  - id
                properties:
                  id:
                    type: string
              items:
                type: array
                minItems: 1
                items:
                  type: object
                  required:
                    - id
                    - quantity
                  properties:
                    id:
                      type: string
                    quantity:
                      type: object
                      required:
                        - count
                      properties:
                        count:
                          type: integer
                          minimum: 1
              fulfillments:
                type: array
                items:
                  $ref: '#/schemas/Fulfillment'

  # ONDC Select Response
  ONDCSelectResponse:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order
        properties:
          order:
            $ref: '#/schemas/Order'

  # ONDC Init Request
  ONDCInitRequest:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order
        properties:
          order:
            type: object
            required:
              - provider
              - items
              - billing
              - fulfillments
            properties:
              provider:
                type: object
                required:
                  - id
                properties:
                  id:
                    type: string
              items:
                type: array
                minItems: 1
                items:
                  $ref: '#/schemas/Item'
              billing:
                $ref: '#/schemas/BillingInfo'
              fulfillments:
                type: array
                minItems: 1
                items:
                  $ref: '#/schemas/Fulfillment'

  # ONDC Init Response
  ONDCInitResponse:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order
        properties:
          order:
            $ref: '#/schemas/Order'

  # ONDC Confirm Request
  ONDCConfirmRequest:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order
        properties:
          order:
            type: object
            required:
              - id
              - provider
              - items
              - billing
              - fulfillments
              - payment
            properties:
              id:
                type: string
              provider:
                type: object
                required:
                  - id
                properties:
                  id:
                    type: string
              items:
                type: array
                minItems: 1
                items:
                  $ref: '#/schemas/Item'
              billing:
                $ref: '#/schemas/BillingInfo'
              fulfillments:
                type: array
                minItems: 1
                items:
                  $ref: '#/schemas/Fulfillment'
              payment:
                $ref: '#/schemas/Payment'

  # ONDC Confirm Response
  ONDCConfirmResponse:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order
        properties:
          order:
            $ref: '#/schemas/Order'

  # ONDC Status Request
  ONDCStatusRequest:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order_id
        properties:
          order_id:
            type: string

  # ONDC Status Response
  ONDCStatusResponse:
    type: object
    required:
      - context
      - message
    properties:
      context:
        $ref: '#/schemas/ONDCContext'
      message:
        type: object
        required:
          - order
        properties:
          order:
            $ref: '#/schemas/Order'
