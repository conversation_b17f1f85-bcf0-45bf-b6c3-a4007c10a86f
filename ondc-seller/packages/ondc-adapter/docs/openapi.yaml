openapi: 3.0.3
info:
  title: ONDC Adapter Service API
  description: |
    ONDC Protocol Adapter Service for translating between Medusa Commerce and ONDC network.

    This service provides:
    - ONDC protocol message handling (search, select, init, confirm, status, track, cancel, update, rating)
    - Multi-tenant support for multiple sellers
    - Authentication and authorization
    - Rate limiting and security
    - Health monitoring and metrics

    ## Authentication

    The API supports multiple authentication methods:
    - Development mode: Username/password headers (`X-Dev-Username`, `X-Dev-Password`)
    - Production mode: JWT tokens (`Authorization: Bearer <token>`)
    - API keys (`X-API-Key`)

    ## Multi-tenancy

    All requests must include a tenant identifier via the `X-Tenant-ID` header.

    ## Rate Limiting

    API endpoints are rate-limited based on tenant and IP address:
    - Standard endpoints: 100 requests per 15 minutes
    - ONDC protocol endpoints: 200 requests per minute

  version: 1.0.0
  contact:
    name: ONDC Seller Team
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3001
    description: Development server
  - url: https://api.ondc-seller.com
    description: Production server

paths:
  # Health Check Endpoints
  /health:
    get:
      tags:
        - Health
      summary: Basic health check
      description: Returns basic service health status
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthStatus'
              example:
                status: healthy
                timestamp: '2025-05-22T10:15:00.000Z'
                service: ondc-adapter
                version: 1.0.0
                environment: development
                uptime: 3600

  /health/detailed:
    get:
      tags:
        - Health
      summary: Detailed health check
      description: Returns comprehensive health status including dependency checks
      responses:
        '200':
          description: Detailed health information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DetailedHealthStatus'

  /health/ready:
    get:
      tags:
        - Health
      summary: Readiness check
      description: Returns readiness status for Kubernetes readiness probe
      responses:
        '200':
          description: Service is ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadinessStatus'
        '503':
          description: Service is not ready
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReadinessStatus'

  /health/live:
    get:
      tags:
        - Health
      summary: Liveness check
      description: Returns liveness status for Kubernetes liveness probe
      responses:
        '200':
          description: Service is alive
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LivenessStatus'

  # API Status
  /api/v1/status:
    get:
      tags:
        - API
      summary: API status
      description: Returns API status and feature information
      security:
        - TenantAuth: []
        - DevAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantId'
      responses:
        '200':
          description: API status information
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiStatus'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '400':
          $ref: '#/components/responses/BadRequest'

  # ONDC Protocol Endpoints
  /api/v1/ondc/search:
    post:
      tags:
        - ONDC Protocol
      summary: Handle ONDC search request
      description: |
        Processes ONDC search requests from BAPs and returns catalog information.

        This endpoint:
        - Validates the ONDC message structure
        - Searches products in Medusa Commerce backend
        - Transforms results to ONDC catalog format
        - Returns on_search response
      security:
        - TenantAuth: []
        - DevAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ONDCSearchRequest'
            example:
              context:
                domain: 'ONDC:RET10'
                country: 'IND'
                city: 'std:080'
                action: 'search'
                core_version: '1.2.0'
                bap_id: 'buyer-app.ondc.org'
                bap_uri: 'https://buyer-app.ondc.org/protocol/v1'
                bpp_id: 'seller-app.ondc.org'
                bpp_uri: 'https://seller-app.ondc.org/protocol/v1'
                transaction_id: '3fa85f64-5717-4562-b3fc-2c963f66afa6'
                message_id: '3fa85f64-5717-4562-b3fc-2c963f66afa7'
                timestamp: '2025-05-22T10:15:00.000Z'
                ttl: 'PT30S'
              message:
                intent:
                  item:
                    descriptor:
                      name: 'laptop'
                  category:
                    id: 'Electronics'
      responses:
        '200':
          description: Search processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ONDCSearchResponse'
        '400':
          $ref: '#/components/responses/ONDCError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '429':
          $ref: '#/components/responses/RateLimited'
        '500':
          $ref: '#/components/responses/ONDCError'

  /api/v1/ondc/select:
    post:
      tags:
        - ONDC Protocol
      summary: Handle ONDC select request
      description: |
        Processes ONDC select requests from BAPs and returns pricing/availability information.

        This endpoint:
        - Validates the selected items
        - Calculates pricing and availability
        - Returns on_select response with quote
      security:
        - TenantAuth: []
        - DevAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ONDCSelectRequest'
      responses:
        '200':
          description: Select processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ONDCSelectResponse'
        '400':
          $ref: '#/components/responses/ONDCError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ONDCError'

  /api/v1/ondc/init:
    post:
      tags:
        - ONDC Protocol
      summary: Handle ONDC init request
      description: |
        Processes ONDC init requests from BAPs and returns order draft.

        This endpoint:
        - Validates billing and fulfillment information
        - Creates order draft in Medusa
        - Returns on_init response
      security:
        - TenantAuth: []
        - DevAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ONDCInitRequest'
      responses:
        '200':
          description: Init processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ONDCInitResponse'
        '400':
          $ref: '#/components/responses/ONDCError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ONDCError'

  /api/v1/ondc/confirm:
    post:
      tags:
        - ONDC Protocol
      summary: Handle ONDC confirm request
      description: |
        Processes ONDC confirm requests from BAPs and creates the final order.

        This endpoint:
        - Validates payment information
        - Creates confirmed order in Medusa
        - Returns on_confirm response
      security:
        - TenantAuth: []
        - DevAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ONDCConfirmRequest'
      responses:
        '200':
          description: Confirm processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ONDCConfirmResponse'
        '400':
          $ref: '#/components/responses/ONDCError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ONDCError'

  /api/v1/ondc/status:
    post:
      tags:
        - ONDC Protocol
      summary: Handle ONDC status request
      description: |
        Processes ONDC status requests from BAPs and returns order status.

        This endpoint:
        - Retrieves order status from Medusa
        - Returns on_status response with current order state
      security:
        - TenantAuth: []
        - DevAuth: []
      parameters:
        - $ref: '#/components/parameters/TenantId'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ONDCStatusRequest'
      responses:
        '200':
          description: Status processed successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ONDCStatusResponse'
        '400':
          $ref: '#/components/responses/ONDCError'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '500':
          $ref: '#/components/responses/ONDCError'

components:
  securitySchemes:
    TenantAuth:
      type: apiKey
      in: header
      name: X-Tenant-ID
      description: Tenant identifier for multi-tenant access

    DevAuth:
      type: http
      scheme: basic
      description: Development mode authentication (demo/demo)

    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token authentication for production

    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key authentication

  parameters:
    TenantId:
      name: X-Tenant-ID
      in: header
      required: true
      schema:
        type: string
        example: demo
      description: Tenant identifier for multi-tenant access

  responses:
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error:
              message: Authentication required
              statusCode: 401
              timestamp: '2025-05-22T10:15:00.000Z'

    BadRequest:
      description: Invalid request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error:
              message: Invalid tenant ID
              statusCode: 400
              timestamp: '2025-05-22T10:15:00.000Z'

    RateLimited:
      description: Rate limit exceeded
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ONDCErrorResponse'
          example:
            message:
              ack:
                status: NACK
            error:
              type: CORE-ERROR
              code: '10002'
              message: Rate limit exceeded. Please try again later.

    ONDCError:
      description: ONDC protocol error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ONDCErrorResponse'
          example:
            message:
              ack:
                status: NACK
            error:
              type: DOMAIN-ERROR
              code: '20001'
              message: Invalid message structure
