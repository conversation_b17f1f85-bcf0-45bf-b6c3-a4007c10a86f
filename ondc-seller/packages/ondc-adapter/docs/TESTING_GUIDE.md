# ONDC Adapter Service - Testing Guide

## Table of Contents

1. [Overview](#overview)
2. [Test Structure](#test-structure)
3. [Running Tests](#running-tests)
4. [Test Categories](#test-categories)
5. [Writing Tests](#writing-tests)
6. [Test Coverage](#test-coverage)
7. [Continuous Integration](#continuous-integration)

## Overview

The ONDC Adapter Service uses Jest as the primary testing framework with comprehensive test coverage including:

- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end workflow testing
- **API Tests**: HTTP endpoint testing
- **Mock Services**: External dependency mocking

### Testing Philosophy

- **100% Test Coverage**: All code paths should be tested
- **Test-Driven Development**: Write tests before implementation
- **Realistic Scenarios**: Tests should mirror real-world usage
- **Fast Execution**: Tests should run quickly for rapid feedback
- **Isolated Tests**: Each test should be independent

## Test Structure

```
tests/
├── setup.ts                    # Global test configuration
├── integration/                # Integration tests
│   ├── ondcFlow.test.ts        # Complete ONDC workflows
│   └── healthCheck.test.ts     # Health endpoint tests
└── mocks/                      # Mock data and utilities

src/
├── config/
│   └── __tests__/
│       └── environment.test.ts # Configuration tests
├── controllers/
│   └── __tests__/
│       ├── healthController.test.ts
│       └── ondcController.test.ts
├── services/
│   └── __tests__/
│       ├── medusaService.test.ts
│       ├── ondcMessageHandler.test.ts
│       └── ondcValidator.test.ts
└── middleware/
    └── __tests__/
        ├── authMiddleware.test.ts
        ├── rateLimiter.test.ts
        └── tenantMiddleware.test.ts
```

## Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage report
npm run test:coverage

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run tests for CI/CD
npm run test:ci
```

### Test Environment

Tests run with the following environment:
- `NODE_ENV=test`
- Mock external services (Medusa, Redis, Database)
- In-memory data storage
- Reduced logging (error level only)

### Environment Variables for Testing

Create a `.env.test` file:

```bash
NODE_ENV=test
PORT=3002
LOG_LEVEL=error

# Test database
DATABASE_URL=postgresql://test:test@localhost:5432/test_db
REDIS_URL=redis://localhost:6379/1

# Test Medusa
MEDUSA_BACKEND_URL=http://localhost:9001
MEDUSA_API_KEY=test-medusa-api-key

# Test ONDC
ONDC_NETWORK_URL=https://test.ondc.org
ONDC_SUBSCRIBER_ID=test-seller-app
ONDC_SUBSCRIBER_URL=http://localhost:3002
ONDC_SIGNING_PRIVATE_KEY=test-signing-key
ONDC_ENCRYPTION_PRIVATE_KEY=test-encryption-key
ONDC_UNIQUE_KEY_ID=test-unique-key

# Test auth
JWT_SECRET=test-jwt-secret
JWT_EXPIRES_IN=1h

# Test security
CORS_ORIGINS=http://localhost:3000,http://localhost:3002
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000

# Test features
ENABLE_MOCK_ONDC=true
ENABLE_METRICS=false
```

## Test Categories

### 1. Unit Tests

Test individual components in isolation:

```typescript
// Example: Service unit test
describe('ONDCValidator', () => {
  let validator: ONDCValidator;

  beforeEach(() => {
    validator = new ONDCValidator();
  });

  describe('validateMessage', () => {
    it('should validate valid search message', async () => {
      const validMessage = createMockONDCMessage('search', {
        intent: { item: { descriptor: { name: 'laptop' } } }
      });

      const result = await validator.validateMessage(validMessage, 'search');

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });
  });
});
```

### 2. Integration Tests

Test complete workflows:

```typescript
// Example: ONDC flow integration test
describe('ONDC Flow Integration', () => {
  it('should complete full ONDC flow: search -> select -> init -> confirm -> status', async () => {
    // Step 1: Search
    const searchResponse = await request(app)
      .post('/api/v1/ondc/search')
      .set('X-Tenant-ID', 'demo')
      .set('X-Dev-Username', 'demo')
      .set('X-Dev-Password', 'demo')
      .send(searchMessage)
      .expect(200);

    // Step 2: Select
    const selectResponse = await request(app)
      .post('/api/v1/ondc/select')
      .set('X-Tenant-ID', 'demo')
      .set('X-Dev-Username', 'demo')
      .set('X-Dev-Password', 'demo')
      .send(selectMessage)
      .expect(200);

    // Continue with init, confirm, status...
  });
});
```

### 3. API Tests

Test HTTP endpoints:

```typescript
// Example: Health endpoint test
describe('Health Controller', () => {
  it('should return basic health status', async () => {
    const response = await request(app)
      .get('/health')
      .expect(200);

    expect(response.body).toMatchObject({
      status: 'healthy',
      timestamp: expect.any(String),
      service: 'ondc-adapter',
      version: expect.any(String),
      uptime: expect.any(Number),
    });
  });
});
```

### 4. Error Handling Tests

Test error scenarios:

```typescript
describe('Error Handling', () => {
  it('should handle validation errors gracefully', async () => {
    const invalidMessage = { /* invalid structure */ };

    const response = await request(app)
      .post('/api/v1/ondc/search')
      .set('X-Tenant-ID', 'demo')
      .set('X-Dev-Username', 'demo')
      .set('X-Dev-Password', 'demo')
      .send(invalidMessage)
      .expect(400);

    expect(response.body).toMatchObject({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'DOMAIN-ERROR',
        code: '20001',
        message: 'Invalid message structure',
      },
    });
  });
});
```

## Writing Tests

### Test Utilities

Use the provided test utilities in `tests/setup.ts`:

```typescript
import {
  createMockONDCMessage,
  createMockTenant,
  createMockUser,
  setTestEnv,
  restoreEnv,
} from '../tests/setup';

// Create mock ONDC message
const searchMessage = createMockONDCMessage('search', {
  intent: { item: { descriptor: { name: 'laptop' } } }
});

// Create mock tenant
const tenant = createMockTenant();

// Create mock user
const user = createMockUser();
```

### Mocking External Services

Mock external dependencies:

```typescript
// Mock Medusa service
jest.mock('../services/medusaService', () => ({
  MedusaService: jest.fn().mockImplementation(() => ({
    searchProducts: jest.fn().mockResolvedValue([]),
    calculateQuote: jest.fn().mockResolvedValue({}),
    createOrder: jest.fn().mockResolvedValue({}),
  })),
}));

// Mock Redis
jest.mock('redis', () => ({
  createClient: jest.fn(() => ({
    connect: jest.fn(),
    get: jest.fn(),
    set: jest.fn(),
    del: jest.fn(),
  })),
}));
```

### Test Data

Create realistic test data:

```typescript
const validONDCContext = {
  domain: "ONDC:RET10",
  country: "IND",
  city: "std:080",
  action: "search",
  core_version: "1.2.0",
  bap_id: "buyer-app.ondc.org",
  bap_uri: "https://buyer-app.ondc.org/protocol/v1",
  bpp_id: "seller-app.ondc.org",
  bpp_uri: "https://seller-app.ondc.org/protocol/v1",
  transaction_id: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
  message_id: "3fa85f64-5717-4562-b3fc-2c963f66afa7",
  timestamp: "2025-05-22T10:15:00.000Z",
  ttl: "PT30S"
};
```

### Async Testing

Handle asynchronous operations:

```typescript
describe('Async Operations', () => {
  it('should handle async service calls', async () => {
    const mockService = jest.fn().mockResolvedValue({ success: true });
    
    const result = await mockService();
    
    expect(result).toEqual({ success: true });
    expect(mockService).toHaveBeenCalledTimes(1);
  });

  it('should handle async errors', async () => {
    const mockService = jest.fn().mockRejectedValue(new Error('Service error'));
    
    await expect(mockService()).rejects.toThrow('Service error');
  });
});
```

## Test Coverage

### Coverage Requirements

- **Statements**: 100%
- **Branches**: 100%
- **Functions**: 100%
- **Lines**: 100%

### Coverage Reports

Generate coverage reports:

```bash
# Generate HTML coverage report
npm run test:coverage

# View coverage report
open coverage/lcov-report/index.html
```

### Coverage Configuration

Jest coverage configuration in `jest.config.js`:

```javascript
module.exports = {
  collectCoverage: true,
  coverageDirectory: 'coverage',
  coverageReporters: ['text', 'text-summary', 'html', 'lcov', 'json'],
  
  coverageThreshold: {
    global: {
      branches: 100,
      functions: 100,
      lines: 100,
      statements: 100
    }
  },
  
  collectCoverageFrom: [
    'src/**/*.ts',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/**/*.test.ts',
    '!src/index.ts'
  ]
};
```

## Continuous Integration

### GitHub Actions

Example CI configuration (`.github/workflows/test.yml`):

```yaml
name: Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      
      redis:
        image: redis:6
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run tests
        run: npm run test:ci
        env:
          DATABASE_URL: postgresql://postgres:test@localhost:5432/test_db
          REDIS_URL: redis://localhost:6379
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
```

### Pre-commit Hooks

Set up pre-commit hooks with Husky:

```json
{
  "husky": {
    "hooks": {
      "pre-commit": "npm run lint && npm run test:unit",
      "pre-push": "npm run test:ci"
    }
  }
}
```
