# ONDC Adapter Service - Deployment Guide

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Environment Configuration](#environment-configuration)
4. [Docker Deployment](#docker-deployment)
5. [Kubernetes Deployment](#kubernetes-deployment)
6. [Production Considerations](#production-considerations)
7. [Monitoring & Logging](#monitoring--logging)
8. [Troubleshooting](#troubleshooting)

## Overview

This guide covers deploying the ONDC Adapter Service in various environments:

- **Development**: Local development setup
- **Staging**: Pre-production testing environment
- **Production**: Live production deployment

### Deployment Options

- **Docker**: Containerized deployment
- **Kubernetes**: Orchestrated container deployment
- **Cloud Platforms**: AWS, GCP, Azure
- **Traditional Servers**: VM or bare metal deployment

## Prerequisites

### System Requirements

- **CPU**: 2+ cores (4+ recommended for production)
- **Memory**: 4GB+ RAM (8GB+ recommended for production)
- **Storage**: 20GB+ available space
- **Network**: Stable internet connection with HTTPS support

### Dependencies

- **Node.js**: 18+ (LTS recommended)
- **PostgreSQL**: 12+ (14+ recommended)
- **Redis**: 6+ (7+ recommended)
- **Docker**: 20+ (if using containers)
- **Kubernetes**: 1.20+ (if using K8s)

### External Services

- **Medusa Commerce Backend**: Running and accessible
- **ONDC Network**: Valid credentials and network access
- **SSL Certificate**: For HTTPS endpoints (production)

## Environment Configuration

### Production Environment Variables

Create a `.env.production` file:

```bash
# Application
NODE_ENV=production
PORT=3001
LOG_LEVEL=info

# Database
DATABASE_URL=*******************************************/ondc_seller_prod
REDIS_URL=redis://redis-host:6379

# Medusa Integration
MEDUSA_BACKEND_URL=https://your-medusa-backend.com
MEDUSA_API_KEY=your-production-medusa-api-key

# ONDC Network (production values)
ONDC_NETWORK_URL=https://prod.ondc.org
ONDC_SUBSCRIBER_ID=your-production-subscriber-id
ONDC_SUBSCRIBER_URL=https://your-domain.com
ONDC_SIGNING_PRIVATE_KEY=your-production-signing-key
ONDC_ENCRYPTION_PRIVATE_KEY=your-production-encryption-key
ONDC_UNIQUE_KEY_ID=your-production-unique-key

# Authentication
JWT_SECRET=your-strong-jwt-secret-256-bits
JWT_EXPIRES_IN=24h

# Security
CORS_ORIGINS=https://your-frontend.com,https://your-admin.com
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# Production Features
ENABLE_MOCK_ONDC=false
ENABLE_DEBUG_LOGGING=false
ENABLE_METRICS=true
METRICS_PORT=9090

# Services
NOTIFICATION_SERVICE_URL=https://your-notification-service.com
AUTH_SERVICE_URL=https://your-auth-service.com
```

### Security Considerations

- **Secrets Management**: Use environment variables or secret management systems
- **SSL/TLS**: Always use HTTPS in production
- **API Keys**: Rotate keys regularly
- **Database**: Use strong passwords and connection encryption
- **Network**: Restrict access to necessary ports only

## Docker Deployment

### Dockerfile

The service includes a production-ready Dockerfile:

```dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime

RUN addgroup -g 1001 -S nodejs
RUN adduser -S ondc -u 1001

WORKDIR /app

COPY --from=builder /app/node_modules ./node_modules
COPY --chown=ondc:nodejs . .

USER ondc

EXPOSE 3001

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

CMD ["npm", "start"]
```

### Docker Compose

For local development and testing:

```yaml
version: '3.8'

services:
  ondc-adapter:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************/ondc_seller
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  postgres:
    image: postgres:14-alpine
    environment:
      POSTGRES_DB: ondc_seller
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### Building and Running

```bash
# Build the Docker image
docker build -t ondc-adapter:latest .

# Run with Docker Compose
docker-compose up -d

# Check logs
docker-compose logs -f ondc-adapter

# Stop services
docker-compose down
```

## Kubernetes Deployment

### Namespace

```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: ondc-seller
```

### ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: ondc-adapter-config
  namespace: ondc-seller
data:
  NODE_ENV: "production"
  PORT: "3001"
  LOG_LEVEL: "info"
  MEDUSA_BACKEND_URL: "https://your-medusa-backend.com"
  ONDC_NETWORK_URL: "https://prod.ondc.org"
  CORS_ORIGINS: "https://your-frontend.com"
  RATE_LIMIT_WINDOW_MS: "900000"
  RATE_LIMIT_MAX_REQUESTS: "100"
  ENABLE_MOCK_ONDC: "false"
  ENABLE_METRICS: "true"
  METRICS_PORT: "9090"
```

### Secret

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: ondc-adapter-secrets
  namespace: ondc-seller
type: Opaque
stringData:
  DATABASE_URL: "********************************************/ondc_seller"
  REDIS_URL: "redis://redis:6379"
  MEDUSA_API_KEY: "your-production-medusa-api-key"
  ONDC_SUBSCRIBER_ID: "your-production-subscriber-id"
  ONDC_SUBSCRIBER_URL: "https://your-domain.com"
  ONDC_SIGNING_PRIVATE_KEY: "your-production-signing-key"
  ONDC_ENCRYPTION_PRIVATE_KEY: "your-production-encryption-key"
  ONDC_UNIQUE_KEY_ID: "your-production-unique-key"
  JWT_SECRET: "your-strong-jwt-secret-256-bits"
```

### Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ondc-adapter
  namespace: ondc-seller
  labels:
    app: ondc-adapter
spec:
  replicas: 3
  selector:
    matchLabels:
      app: ondc-adapter
  template:
    metadata:
      labels:
        app: ondc-adapter
    spec:
      containers:
      - name: ondc-adapter
        image: ondc-adapter:latest
        ports:
        - containerPort: 3001
        - containerPort: 9090
        envFrom:
        - configMapRef:
            name: ondc-adapter-config
        - secretRef:
            name: ondc-adapter-secrets
        livenessProbe:
          httpGet:
            path: /health/live
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

### Service

```yaml
apiVersion: v1
kind: Service
metadata:
  name: ondc-adapter-service
  namespace: ondc-seller
spec:
  selector:
    app: ondc-adapter
  ports:
  - name: http
    port: 80
    targetPort: 3001
  - name: metrics
    port: 9090
    targetPort: 9090
  type: ClusterIP
```

### Ingress

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ondc-adapter-ingress
  namespace: ondc-seller
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - your-domain.com
    secretName: ondc-adapter-tls
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: ondc-adapter-service
            port:
              number: 80
```

### HorizontalPodAutoscaler

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ondc-adapter-hpa
  namespace: ondc-seller
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ondc-adapter
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

### Deploying to Kubernetes

```bash
# Apply all configurations
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n ondc-seller

# Check logs
kubectl logs -f deployment/ondc-adapter -n ondc-seller

# Scale deployment
kubectl scale deployment ondc-adapter --replicas=5 -n ondc-seller
```

## Production Considerations

### Performance Optimization

- **Node.js Clustering**: Use PM2 or cluster module
- **Connection Pooling**: Configure database connection pools
- **Caching**: Implement Redis caching for frequently accessed data
- **CDN**: Use CDN for static assets
- **Load Balancing**: Distribute traffic across multiple instances

### Security Hardening

- **Network Policies**: Restrict pod-to-pod communication
- **RBAC**: Implement role-based access control
- **Pod Security Policies**: Enforce security constraints
- **Image Scanning**: Scan container images for vulnerabilities
- **Secrets Rotation**: Regularly rotate secrets and certificates

### Backup and Recovery

- **Database Backups**: Regular automated backups
- **Configuration Backups**: Version control all configurations
- **Disaster Recovery**: Document recovery procedures
- **Testing**: Regularly test backup and recovery processes

## Monitoring & Logging

### Health Checks

The service provides multiple health check endpoints:

- `/health` - Basic health status
- `/health/detailed` - Comprehensive health with dependencies
- `/health/ready` - Kubernetes readiness probe
- `/health/live` - Kubernetes liveness probe

### Metrics

Prometheus metrics available at `/metrics`:

- Request count and duration
- Error rates
- Database connection status
- Memory and CPU usage
- Custom business metrics

### Logging

Structured logging with Winston:

- **Development**: Console output with colors
- **Production**: JSON format for log aggregation
- **Log Levels**: error, warn, info, debug
- **Request Logging**: All HTTP requests logged

### Monitoring Stack

Recommended monitoring setup:

- **Prometheus**: Metrics collection
- **Grafana**: Metrics visualization
- **AlertManager**: Alert notifications
- **ELK Stack**: Log aggregation and analysis

## Troubleshooting

### Common Issues

#### Service Won't Start

```bash
# Check logs
kubectl logs deployment/ondc-adapter -n ondc-seller

# Check configuration
kubectl describe configmap ondc-adapter-config -n ondc-seller

# Check secrets
kubectl describe secret ondc-adapter-secrets -n ondc-seller
```

#### Database Connection Issues

```bash
# Test database connectivity
kubectl exec -it deployment/ondc-adapter -n ondc-seller -- \
  psql $DATABASE_URL -c "SELECT 1"

# Check database logs
kubectl logs deployment/postgres -n ondc-seller
```

#### High Memory Usage

```bash
# Check memory usage
kubectl top pods -n ondc-seller

# Increase memory limits
kubectl patch deployment ondc-adapter -n ondc-seller -p \
  '{"spec":{"template":{"spec":{"containers":[{"name":"ondc-adapter","resources":{"limits":{"memory":"2Gi"}}}]}}}}'
```

#### Rate Limiting Issues

```bash
# Check rate limit configuration
curl -I https://your-domain.com/health

# Adjust rate limits
kubectl patch configmap ondc-adapter-config -n ondc-seller -p \
  '{"data":{"RATE_LIMIT_MAX_REQUESTS":"200"}}'
```

### Debug Mode

Enable debug logging:

```bash
kubectl patch configmap ondc-adapter-config -n ondc-seller -p \
  '{"data":{"LOG_LEVEL":"debug"}}'

kubectl rollout restart deployment/ondc-adapter -n ondc-seller
```

### Performance Profiling

```bash
# Enable Node.js profiling
kubectl patch deployment ondc-adapter -n ondc-seller -p \
  '{"spec":{"template":{"spec":{"containers":[{"name":"ondc-adapter","env":[{"name":"NODE_OPTIONS","value":"--inspect=0.0.0.0:9229"}]}]}}}}'

# Port forward for profiling
kubectl port-forward deployment/ondc-adapter 9229:9229 -n ondc-seller
```
