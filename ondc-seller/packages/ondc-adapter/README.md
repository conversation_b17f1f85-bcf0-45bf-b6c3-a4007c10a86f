# ONDC Adapter Service

A production-ready Node.js/TypeScript microservice that acts as a protocol adapter between the ONDC (Open Network for Digital Commerce) network and Medusa Commerce backend.

## 🚀 Features

- **Complete ONDC Protocol Support**: All 9 ONDC endpoints (search, select, init, confirm, status, track, cancel, update, rating)
- **Multi-tenant Architecture**: Support for multiple sellers on a single instance
- **Production Ready**: Comprehensive testing, monitoring, and deployment guides
- **Development Mode**: Mock data and simplified authentication for development
- **Security**: JWT authentication, rate limiting, CORS protection
- **Health Monitoring**: Kubernetes-ready health checks and metrics
- **OpenAPI Documentation**: Complete API specifications

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Architecture](#architecture)
- [API Endpoints](#api-endpoints)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Documentation](#documentation)
- [Contributing](#contributing)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- PostgreSQL 12+
- Redis 6+
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd ondc-seller/packages/ondc-adapter

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your configuration

# Start in development mode
npm run dev
```

### Test the Service

```bash
# Health check
curl http://localhost:3001/health

# API status (with authentication)
curl -H "X-Tenant-ID: demo" \
     -H "X-Dev-Username: demo" \
     -H "X-Dev-Password: demo" \
     http://localhost:3001/api/v1/status
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ONDC Network  │    │  ONDC Adapter   │    │ Medusa Commerce │
│                 │◄──►│    Service      │◄──►│    Backend      │
│   (BAP/BPP)     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │   Database      │
                       └─────────────────┘
```

### Key Components

- **Controllers**: HTTP request/response handling
- **Services**: Business logic and external integrations
- **Middleware**: Authentication, rate limiting, error handling
- **Validators**: ONDC message validation
- **Multi-tenancy**: Isolated data per seller

## 🔌 API Endpoints

### Health Endpoints
- `GET /health` - Basic health check
- `GET /health/detailed` - Comprehensive health status
- `GET /health/ready` - Kubernetes readiness probe
- `GET /health/live` - Kubernetes liveness probe

### ONDC Protocol Endpoints
- `POST /api/v1/ondc/search` - Product search
- `POST /api/v1/ondc/select` - Item selection
- `POST /api/v1/ondc/init` - Order initialization
- `POST /api/v1/ondc/confirm` - Order confirmation
- `POST /api/v1/ondc/status` - Order status
- `POST /api/v1/ondc/track` - Order tracking
- `POST /api/v1/ondc/cancel` - Order cancellation
- `POST /api/v1/ondc/update` - Order updates
- `POST /api/v1/ondc/rating` - Rating submission

### Authentication

#### Development Mode
```bash
X-Tenant-ID: demo
X-Dev-Username: demo
X-Dev-Password: demo
```

#### Production Mode
```bash
Authorization: Bearer <jwt-token>
X-Tenant-ID: <tenant-id>
```

## 💻 Development

### Environment Configuration

```bash
# Application
NODE_ENV=development
PORT=3001
LOG_LEVEL=debug

# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/ondc_seller_dev
REDIS_URL=redis://localhost:6379

# Medusa Integration
MEDUSA_BACKEND_URL=http://localhost:9001
MEDUSA_API_KEY=your-medusa-api-key

# ONDC Network
ONDC_NETWORK_URL=https://staging.ondc.org
ONDC_SUBSCRIBER_ID=dev-seller-app
ONDC_SUBSCRIBER_URL=http://localhost:3001

# Development Features
ENABLE_MOCK_ONDC=true
ENABLE_DEBUG_LOGGING=true
```

### Development Scripts

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Lint code
npm run lint
npm run lint:fix

# Clean build artifacts
npm run clean
```

## 🧪 Testing

### Test Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run with coverage
npm run test:coverage

# Run unit tests only
npm run test:unit

# Run integration tests only
npm run test:integration

# Run tests for CI/CD
npm run test:ci
```

### Test Structure

- **Unit Tests**: Individual component testing
- **Integration Tests**: End-to-end workflow testing
- **API Tests**: HTTP endpoint testing
- **Mock Services**: External dependency mocking

### Coverage Requirements

- **Statements**: 100%
- **Branches**: 100%
- **Functions**: 100%
- **Lines**: 100%

## 🚀 Deployment

### Docker

```bash
# Build Docker image
docker build -t ondc-adapter:latest .

# Run with Docker Compose
docker-compose up -d
```

### Kubernetes

```bash
# Deploy to Kubernetes
kubectl apply -f k8s/

# Check deployment status
kubectl get pods -n ondc-seller

# View logs
kubectl logs -f deployment/ondc-adapter -n ondc-seller
```

### Production Considerations

- **Environment Variables**: Use secrets management
- **SSL/TLS**: Always use HTTPS in production
- **Database**: Use connection pooling and encryption
- **Monitoring**: Set up Prometheus and Grafana
- **Logging**: Use structured logging with log aggregation

## 📚 Documentation

### Comprehensive Guides

- **[Developer Guide](docs/DEVELOPER_GUIDE.md)**: Complete development setup and workflow
- **[Testing Guide](docs/TESTING_GUIDE.md)**: Testing strategies and best practices
- **[Deployment Guide](docs/DEPLOYMENT_GUIDE.md)**: Production deployment instructions
- **[OpenAPI Specification](docs/openapi.yaml)**: Complete API documentation

### API Documentation

- **OpenAPI 3.0**: Complete API specifications
- **Interactive Docs**: Available at `/api-docs` when running
- **ONDC Schemas**: Detailed ONDC message schemas

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NODE_ENV` | Environment mode | `development` | No |
| `PORT` | Server port | `3001` | No |
| `DATABASE_URL` | PostgreSQL connection | - | Yes |
| `REDIS_URL` | Redis connection | `redis://localhost:6379` | No |
| `MEDUSA_BACKEND_URL` | Medusa backend URL | `http://localhost:9001` | No |
| `MEDUSA_API_KEY` | Medusa API key | - | Yes |
| `ONDC_NETWORK_URL` | ONDC network URL | - | Yes |
| `ONDC_SUBSCRIBER_ID` | ONDC subscriber ID | - | Yes |
| `JWT_SECRET` | JWT signing secret | - | Yes |

### Development Mode Features

When `NODE_ENV=development`:

- **Mock Authentication**: Use `demo/demo` credentials
- **Mock Data**: Returns sample data instead of real API calls
- **Relaxed Validation**: More permissive validation rules
- **Debug Logging**: Detailed request/response logging

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Workflow

1. **Setup**: Follow the development setup guide
2. **Testing**: Write tests for new features
3. **Linting**: Ensure code passes linting
4. **Documentation**: Update documentation as needed
5. **Review**: Submit PR for code review

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check the comprehensive guides in `/docs`
- **Issues**: Report bugs and feature requests on GitHub
- **Community**: Join our developer community discussions

## 🚀 Current Status

### Phase 1: Core Infrastructure ✅
- [x] Project structure and configuration
- [x] TypeScript setup with strict typing
- [x] Express.js server with middleware
- [x] Environment configuration management
- [x] Logging system with Winston
- [x] Error handling middleware
- [x] Health check endpoints

### Phase 2: ONDC Protocol Implementation ✅
- [x] ONDC message validation
- [x] Core ONDC endpoints (search, select, init, confirm, status)
- [x] Medusa Commerce integration
- [x] Multi-tenant architecture
- [x] Authentication middleware (development mode)
- [x] Rate limiting
- [x] Request/response logging

### Phase 3: Production Readiness ✅
- [x] Comprehensive testing suite (Jest with 100% coverage target)
- [x] OpenAPI 3.0 specifications
- [x] Complete ONDC endpoints (search, select, init, confirm, status, track, cancel, update, rating)
- [x] Developer documentation and guides
- [x] Production deployment guides
- [x] Testing framework and utilities

---

**Built with ❤️ for the ONDC ecosystem**
