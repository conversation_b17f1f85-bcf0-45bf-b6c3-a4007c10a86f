/**
 * Jest Test Setup
 *
 * Global test configuration and setup for ONDC Adapter Service tests.
 * Configures test environment, mocks, and utilities.
 */

import dotenv from 'dotenv';
import path from 'path';

// Load test environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env.test') });

// Set test environment
process.env.NODE_ENV = 'test';
process.env.LOG_LEVEL = 'error'; // Reduce log noise during tests

// Mock external dependencies
jest.mock('axios');
jest.mock('redis');

// Global test timeout
jest.setTimeout(30000);

// Mock console methods to reduce noise
const originalConsole = { ...console };

beforeAll(() => {
  // Suppress console output during tests unless explicitly needed
  console.log = jest.fn();
  console.info = jest.fn();
  console.warn = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  // Restore console
  Object.assign(console, originalConsole);
});

// Global test utilities
declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidONDCMessage(): R;
      toHaveValidONDCContext(): R;
      toBeValidTenant(): R;
    }
  }
}

// Custom Jest matchers for ONDC testing
expect.extend({
  toBeValidONDCMessage(received: any) {
    const pass = received && typeof received === 'object' && received.context && received.message;

    if (pass) {
      return {
        message: () => `Expected ${JSON.stringify(received)} not to be a valid ONDC message`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `Expected ${JSON.stringify(received)} to be a valid ONDC message with context and message properties`,
        pass: false,
      };
    }
  },

  toHaveValidONDCContext(received: any) {
    const requiredFields = [
      'domain',
      'country',
      'city',
      'action',
      'core_version',
      'bap_id',
      'bap_uri',
      'bpp_id',
      'bpp_uri',
      'transaction_id',
      'message_id',
      'timestamp',
    ];

    const pass =
      received && typeof received === 'object' && requiredFields.every(field => received[field]);

    if (pass) {
      return {
        message: () => `Expected ${JSON.stringify(received)} not to have valid ONDC context`,
        pass: true,
      };
    } else {
      const missingFields = requiredFields.filter(field => !received?.[field]);
      return {
        message: () =>
          `Expected ONDC context to have all required fields. Missing: ${missingFields.join(', ')}`,
        pass: false,
      };
    }
  },

  toBeValidTenant(received: any) {
    const pass =
      received && typeof received === 'object' && received.id && received.name && received.settings;

    if (pass) {
      return {
        message: () => `Expected ${JSON.stringify(received)} not to be a valid tenant`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `Expected ${JSON.stringify(received)} to be a valid tenant with id, name, and settings`,
        pass: false,
      };
    }
  },
});

// Mock data factories
export const createMockONDCContext = (overrides: any = {}) => ({
  domain: 'ONDC:RET10',
  country: 'IND',
  city: 'std:080',
  action: 'search',
  core_version: '1.2.0',
  bap_id: 'buyer-app.ondc.org',
  bap_uri: 'https://buyer-app.ondc.org/protocol/v1',
  bpp_id: 'seller-app.ondc.org',
  bpp_uri: 'https://seller-app.ondc.org/protocol/v1',
  transaction_id: '3fa85f64-5717-4562-b3fc-2c963f66afa6',
  message_id: '3fa85f64-5717-4562-b3fc-2c963f66afa7',
  timestamp: new Date().toISOString(),
  ttl: 'PT30S',
  ...overrides,
});

export const createMockONDCMessage = (
  action: string,
  messageOverrides: any = {},
  contextOverrides: any = {}
) => ({
  context: createMockONDCContext({ action, ...contextOverrides }),
  message: {
    ...messageOverrides,
  },
});

export const createMockTenant = (overrides: any = {}) => ({
  id: 'test-tenant',
  name: 'Test Tenant',
  status: 'active',
  settings: {
    features: ['catalog', 'orders', 'fulfillment'],
    ondcConfig: {
      subscriberId: 'test-seller',
      subscriberUrl: 'https://test-seller.ondc.org',
      signingPrivateKey: 'test-signing-key',
      encryptionPrivateKey: 'test-encryption-key',
      uniqueKeyId: 'test-unique-key',
    },
    medusaConfig: {
      backendUrl: 'http://localhost:9001',
      apiKey: 'test-medusa-key',
    },
  },
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
});

export const createMockUser = (overrides: any = {}) => ({
  id: 'test-user',
  email: '<EMAIL>',
  role: 'seller',
  permissions: ['catalog:read', 'orders:write'],
  tenantId: 'test-tenant',
  ...overrides,
});

export const createMockProduct = (overrides: any = {}) => ({
  id: 'prod_test_01',
  title: 'Test Product',
  subtitle: 'A test product',
  description: 'This is a test product for unit testing',
  handle: 'test-product',
  thumbnail: 'https://example.com/test-product.jpg',
  images: ['https://example.com/test-product.jpg'],
  variants: [
    {
      id: 'variant_test_01',
      title: 'Default Variant',
      sku: 'TEST-001',
      prices: [
        {
          id: 'price_test_01',
          currency_code: 'INR',
          amount: 10000, // 100.00 INR
        },
      ],
      inventory_quantity: 100,
      manage_inventory: true,
      allow_backorder: false,
    },
  ],
  status: 'published',
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
});

// Test database cleanup utilities
export const cleanupTestData = async () => {
  // Add cleanup logic for test data if needed
  // This would be implemented when we add database integration
};

// Mock HTTP request/response utilities
export const createMockRequest = (overrides: any = {}) => ({
  body: {},
  headers: {},
  params: {},
  query: {},
  user: createMockUser(),
  tenant: createMockTenant(),
  id: 'test-request-id',
  ip: '127.0.0.1',
  path: '/test',
  method: 'GET',
  originalUrl: '/test',
  connection: { remoteAddress: '127.0.0.1' },
  ...overrides,
});

export const createMockResponse = () => {
  const res: any = {};
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.setHeader = jest.fn().mockReturnValue(res);
  res.end = jest.fn().mockReturnValue(res);
  return res;
};

export const createMockNext = () => jest.fn();

// Environment variable helpers
export const setTestEnv = (vars: Record<string, string>) => {
  Object.entries(vars).forEach(([key, value]) => {
    process.env[key] = value;
  });
};

export const restoreEnv = (vars: string[]) => {
  vars.forEach(key => {
    delete process.env[key];
  });
};
