/**
 * ONDC Flow Integration Tests
 * 
 * End-to-end integration tests for complete ONDC message flows
 * including search -> select -> init -> confirm -> status.
 */

import request from 'supertest';
import express from 'express';
import { ONDCAdapterApp } from '../../src/index';
import {
  createMockONDCMessage,
  createMockTenant,
  createMockUser,
  setTestEnv,
  restoreEnv,
} from '../setup';

describe('ONDC Flow Integration Tests', () => {
  let app: express.Application;
  let server: any;
  let ondcApp: ONDCAdapterApp;

  beforeAll(async () => {
    // Set test environment variables
    setTestEnv({
      NODE_ENV: 'test',
      PORT: '3002', // Use different port for tests
      LOG_LEVEL: 'error',
      DATABASE_URL: 'postgresql://test:test@localhost:5432/test_db',
      REDIS_URL: 'redis://localhost:6379/1',
      MEDUSA_BACKEND_URL: 'http://localhost:9001',
      MEDUSA_API_KEY: 'test-medusa-key',
      JWT_SECRET: 'test-jwt-secret',
    });

    // Create ONDC Adapter app instance
    ondcApp = new ONDCAdapterApp();
    app = ondcApp.getApp();
    
    // Start server
    server = await ondcApp.start();
  });

  afterAll(async () => {
    // Stop server
    if (server) {
      await ondcApp.stop();
    }

    // Restore environment
    restoreEnv([
      'NODE_ENV', 'PORT', 'LOG_LEVEL', 'DATABASE_URL', 'REDIS_URL',
      'MEDUSA_BACKEND_URL', 'MEDUSA_API_KEY', 'JWT_SECRET'
    ]);
  });

  describe('Complete ONDC Transaction Flow', () => {
    let transactionId: string;
    let orderId: string;

    beforeEach(() => {
      transactionId = '3fa85f64-5717-4562-b3fc-2c963f66afa6';
      orderId = '';
    });

    it('should complete full ONDC flow: search -> select -> init -> confirm -> status', async () => {
      // Step 1: Search
      const searchMessage = createMockONDCMessage('search', {
        intent: {
          item: {
            descriptor: {
              name: 'laptop',
            },
          },
          category: {
            id: 'Electronics',
          },
        },
      }, { transaction_id: transactionId });

      const searchResponse = await request(app)
        .post('/api/v1/ondc/search')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(searchMessage)
        .expect(200);

      expect(searchResponse.body).toMatchObject({
        context: {
          action: 'on_search',
          transaction_id: transactionId,
        },
        message: {
          catalog: {
            'bpp/providers': expect.arrayContaining([
              expect.objectContaining({
                items: expect.any(Array),
              }),
            ]),
          },
        },
      });

      // Extract item ID for next steps
      const items = searchResponse.body.message.catalog['bpp/providers'][0].items;
      expect(items.length).toBeGreaterThan(0);
      const itemId = items[0].id;

      // Step 2: Select
      const selectMessage = createMockONDCMessage('select', {
        order: {
          provider: {
            id: 'demo-seller',
          },
          items: [
            {
              id: itemId,
              quantity: {
                count: 1,
              },
            },
          ],
        },
      }, { transaction_id: transactionId });

      const selectResponse = await request(app)
        .post('/api/v1/ondc/select')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(selectMessage)
        .expect(200);

      expect(selectResponse.body).toMatchObject({
        context: {
          action: 'on_select',
          transaction_id: transactionId,
        },
        message: {
          order: {
            provider: expect.any(Object),
            items: expect.arrayContaining([
              expect.objectContaining({
                id: itemId,
              }),
            ]),
            quote: expect.any(Object),
          },
        },
      });

      // Step 3: Init
      const initMessage = createMockONDCMessage('init', {
        order: {
          provider: {
            id: 'demo-seller',
          },
          items: [
            {
              id: itemId,
              quantity: {
                count: 1,
              },
            },
          ],
          billing: {
            name: 'Test Customer',
            address: {
              locality: 'Test Area',
              city: 'Bangalore',
              state: 'Karnataka',
              country: 'IND',
              area_code: '560001',
            },
            phone: '+919999999999',
          },
          fulfillments: [
            {
              end: {
                location: {
                  address: {
                    locality: 'Test Area',
                    city: 'Bangalore',
                    state: 'Karnataka',
                    country: 'IND',
                    area_code: '560001',
                  },
                },
                contact: {
                  phone: '+919999999999',
                },
              },
            },
          ],
        },
      }, { transaction_id: transactionId });

      const initResponse = await request(app)
        .post('/api/v1/ondc/init')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(initMessage)
        .expect(200);

      expect(initResponse.body).toMatchObject({
        context: {
          action: 'on_init',
          transaction_id: transactionId,
        },
        message: {
          order: expect.objectContaining({
            id: expect.any(String),
            state: expect.any(String),
          }),
        },
      });

      orderId = initResponse.body.message.order.id;

      // Step 4: Confirm
      const confirmMessage = createMockONDCMessage('confirm', {
        order: {
          id: orderId,
          provider: {
            id: 'demo-seller',
          },
          items: [
            {
              id: itemId,
              quantity: {
                count: 1,
              },
            },
          ],
          billing: {
            name: 'Test Customer',
            address: {
              locality: 'Test Area',
              city: 'Bangalore',
              state: 'Karnataka',
              country: 'IND',
              area_code: '560001',
            },
            phone: '+919999999999',
          },
          fulfillments: [
            {
              end: {
                location: {
                  address: {
                    locality: 'Test Area',
                    city: 'Bangalore',
                    state: 'Karnataka',
                    country: 'IND',
                    area_code: '560001',
                  },
                },
                contact: {
                  phone: '+919999999999',
                },
              },
            },
          ],
          payment: {
            params: {
              amount: '100',
              currency: 'INR',
            },
            status: 'PAID',
            type: 'ON-ORDER',
            collected_by: 'BAP',
          },
        },
      }, { transaction_id: transactionId });

      const confirmResponse = await request(app)
        .post('/api/v1/ondc/confirm')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(confirmMessage)
        .expect(200);

      expect(confirmResponse.body).toMatchObject({
        context: {
          action: 'on_confirm',
          transaction_id: transactionId,
        },
        message: {
          order: expect.objectContaining({
            id: orderId,
            state: expect.any(String),
          }),
        },
      });

      // Step 5: Status
      const statusMessage = createMockONDCMessage('status', {
        order_id: orderId,
      }, { transaction_id: transactionId });

      const statusResponse = await request(app)
        .post('/api/v1/ondc/status')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(statusMessage)
        .expect(200);

      expect(statusResponse.body).toMatchObject({
        context: {
          action: 'on_status',
          transaction_id: transactionId,
        },
        message: {
          order: expect.objectContaining({
            id: orderId,
            state: expect.any(String),
          }),
        },
      });
    });

    it('should handle validation errors in the flow', async () => {
      // Test with invalid select message (missing provider)
      const invalidSelectMessage = createMockONDCMessage('select', {
        order: {
          items: [
            {
              id: 'item-1',
              quantity: {
                count: 1,
              },
            },
          ],
        },
      }, { transaction_id: transactionId });

      const response = await request(app)
        .post('/api/v1/ondc/select')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(invalidSelectMessage)
        .expect(400);

      expect(response.body).toMatchObject({
        message: {
          ack: {
            status: 'NACK',
          },
        },
        error: {
          type: 'DOMAIN-ERROR',
          code: '20001',
          message: 'Invalid message structure',
        },
      });
    });

    it('should handle authentication errors', async () => {
      const searchMessage = createMockONDCMessage('search', {
        intent: {},
      });

      const response = await request(app)
        .post('/api/v1/ondc/search')
        .set('X-Tenant-ID', 'demo')
        // Missing authentication headers
        .send(searchMessage)
        .expect(401);

      expect(response.body).toMatchObject({
        error: expect.objectContaining({
          message: expect.stringContaining('Authentication required'),
        }),
      });
    });

    it('should handle tenant validation errors', async () => {
      const searchMessage = createMockONDCMessage('search', {
        intent: {},
      });

      const response = await request(app)
        .post('/api/v1/ondc/search')
        .set('X-Tenant-ID', 'invalid-tenant')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(searchMessage)
        .expect(400);

      expect(response.body).toMatchObject({
        error: expect.objectContaining({
          message: expect.stringContaining('Invalid tenant'),
        }),
      });
    });
  });

  describe('Multi-tenant ONDC Flow', () => {
    it('should handle requests from different tenants', async () => {
      const searchMessage = createMockONDCMessage('search', {
        intent: {
          item: {
            descriptor: {
              name: 'test-product',
            },
          },
        },
      });

      // Test with demo tenant
      const demoResponse = await request(app)
        .post('/api/v1/ondc/search')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(searchMessage)
        .expect(200);

      expect(demoResponse.body.message.catalog['bpp/descriptor'].name).toBe('Demo Tenant');

      // Test with default tenant
      const defaultResponse = await request(app)
        .post('/api/v1/ondc/search')
        .set('X-Tenant-ID', 'default')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(searchMessage)
        .expect(200);

      expect(defaultResponse.body.message.catalog['bpp/descriptor'].name).toBe('Default Tenant');
    });
  });

  describe('Rate Limiting Integration', () => {
    it('should enforce rate limits on ONDC endpoints', async () => {
      const searchMessage = createMockONDCMessage('search', {
        intent: {},
      });

      // Make multiple requests rapidly
      const requests = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/v1/ondc/search')
          .set('X-Tenant-ID', 'demo')
          .set('X-Dev-Username', 'demo')
          .set('X-Dev-Password', 'demo')
          .send(searchMessage)
      );

      const responses = await Promise.all(requests);

      // All requests should succeed in test environment (higher limits)
      responses.forEach(response => {
        expect([200, 429]).toContain(response.status);
      });
    });
  });

  describe('Health Checks Integration', () => {
    it('should provide health status while handling ONDC requests', async () => {
      // Make an ONDC request
      const searchMessage = createMockONDCMessage('search', {
        intent: {},
      });

      await request(app)
        .post('/api/v1/ondc/search')
        .set('X-Tenant-ID', 'demo')
        .set('X-Dev-Username', 'demo')
        .set('X-Dev-Password', 'demo')
        .send(searchMessage)
        .expect(200);

      // Check health status
      const healthResponse = await request(app)
        .get('/health')
        .expect(200);

      expect(healthResponse.body.status).toBe('healthy');
    });
  });
});
