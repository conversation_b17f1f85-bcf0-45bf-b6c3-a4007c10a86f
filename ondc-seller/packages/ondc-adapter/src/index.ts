/**
 * ONDC Adapter Service
 *
 * This service acts as a bridge between Medusa Commerce and the ONDC network,
 * translating between Medusa's internal data structures and ONDC protocol messages.
 *
 * Key Features:
 * - ONDC protocol compliance and message translation
 * - Multi-tenant support with tenant-specific configurations
 * - Real-time order synchronization with Medusa
 * - Comprehensive logging and monitoring
 * - Development and production mode support
 *
 * <AUTHOR> Seller Team
 * @version 1.0.0
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import dotenv from 'dotenv';
import { v4 as uuidv4 } from 'uuid';

import { logger } from './config/logger';
import { config } from './config/environment';
import { errorHandler } from './middleware/errorHandler';
import { rateLimiter } from './middleware/rateLimiter';
import { tenantMiddleware } from './middleware/tenantMiddleware';
import { authMiddleware } from './middleware/authMiddleware';

// Import route handlers
import { healthRoutes } from './controllers/healthController';
import { ondcRoutes } from './controllers/ondcController';
// TODO: Implement these controllers in Phase 3
// import { catalogRoutes } from './controllers/catalogController';
// import { orderRoutes } from './controllers/orderController';
// import { fulfillmentRoutes } from './controllers/fulfillmentController';

// Load environment variables
dotenv.config();

/**
 * ONDC Adapter Application Class
 *
 * Manages the Express application lifecycle, middleware setup,
 * and route configuration for the ONDC adapter service.
 */
class ONDCAdapterApp {
  private app: express.Application;
  private port: number;

  constructor() {
    this.app = express();
    this.port = config.port;
    this.initializeMiddleware();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  /**
   * Initialize Express middleware stack
   *
   * Sets up security, logging, parsing, and custom middleware
   * in the correct order for optimal performance and security.
   */
  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(
      helmet({
        contentSecurityPolicy: {
          directives: {
            defaultSrc: ["'self'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            scriptSrc: ["'self'"],
            imgSrc: ["'self'", 'data:', 'https:'],
          },
        },
        hsts: {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true,
        },
      })
    );

    // CORS configuration
    this.app.use(
      cors({
        origin: config.corsOrigins,
        credentials: true,
        methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
        allowedHeaders: [
          'Content-Type',
          'Authorization',
          'X-Tenant-ID',
          'X-Request-ID',
          'X-API-Key',
        ],
      })
    );

    // Compression and parsing
    this.app.use(compression());
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Logging middleware
    this.app.use(
      morgan('combined', {
        stream: {
          write: (message: string) => logger.info(message.trim()),
        },
      })
    );

    // Rate limiting
    this.app.use(rateLimiter);

    // Custom middleware
    this.app.use(tenantMiddleware);
    this.app.use(authMiddleware);

    // Request ID middleware for tracing
    this.app.use((req, res, next) => {
      const requestId = (req.headers['x-request-id'] as string) || uuidv4();
      req.id = requestId;
      res.setHeader('X-Request-ID', requestId);
      next();
    });
  }

  /**
   * Initialize API routes
   *
   * Sets up all route handlers with proper prefixes and versioning.
   * Routes are organized by functionality for better maintainability.
   */
  private initializeRoutes(): void {
    // Health check routes (no auth required)
    this.app.use('/health', healthRoutes);

    // API v1 routes
    const apiV1 = express.Router();

    // ONDC protocol routes (Phase 2 implementation)
    apiV1.use('/ondc', ondcRoutes);

    // TODO: Add these routes in Phase 3
    // apiV1.use('/catalog', catalogRoutes);
    // apiV1.use('/orders', orderRoutes);
    // apiV1.use('/fulfillment', fulfillmentRoutes);

    // Status endpoint for testing
    apiV1.get('/status', (req, res) => {
      res.json({
        message: 'ONDC Adapter API v1 is running',
        timestamp: new Date().toISOString(),
        tenant: req.tenant?.name || 'No tenant',
        user: req.user?.email || 'No user',
        features: {
          ondcProtocol: true,
          multiTenant: true,
          authentication: true,
          rateLimiting: true,
          healthChecks: true,
        },
      });
    });

    this.app.use('/api/v1', apiV1);

    // Root route
    this.app.get('/', (req, res) => {
      res.json({
        service: 'ONDC Adapter Service',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString(),
        environment: config.nodeEnv,
        documentation: '/api/v1/docs',
      });
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.originalUrl} not found`,
        timestamp: new Date().toISOString(),
      });
    });
  }

  /**
   * Initialize error handling middleware
   *
   * Sets up global error handling for unhandled errors,
   * ensuring proper logging and response formatting.
   */
  private initializeErrorHandling(): void {
    this.app.use(errorHandler);

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
      // Don't exit the process in production
      if (config.nodeEnv !== 'production') {
        process.exit(1);
      }
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', error => {
      logger.error('Uncaught Exception:', error);
      // Graceful shutdown
      this.gracefulShutdown();
    });

    // Handle SIGTERM and SIGINT for graceful shutdown
    process.on('SIGTERM', () => this.gracefulShutdown());
    process.on('SIGINT', () => this.gracefulShutdown());
  }

  /**
   * Start the ONDC Adapter service
   *
   * Starts the Express server and logs startup information.
   * Includes environment-specific configuration logging.
   */
  public start(): void {
    this.app.listen(this.port, () => {
      logger.info(`🚀 ONDC Adapter Service started successfully`);
      logger.info(`📡 Server running on port ${this.port}`);
      logger.info(`🌍 Environment: ${config.nodeEnv}`);
      logger.info(`🔧 Mode: ${config.isDevelopment ? 'Development' : 'Production'}`);

      if (config.isDevelopment) {
        logger.info(`📚 API Documentation: http://localhost:${this.port}/api/v1/docs`);
        logger.info(`❤️  Health Check: http://localhost:${this.port}/health`);
      }
    });
  }

  /**
   * Graceful shutdown handler
   *
   * Handles cleanup operations when the service is shutting down,
   * ensuring all connections are properly closed.
   */
  private gracefulShutdown(): void {
    logger.info('🛑 Received shutdown signal, starting graceful shutdown...');

    // Close database connections, cleanup resources, etc.
    // This will be expanded as we add more services

    setTimeout(() => {
      logger.info('✅ Graceful shutdown completed');
      process.exit(0);
    }, 5000);
  }

  /**
   * Get Express application instance
   *
   * @returns Express application instance for testing
   */
  public getApp(): express.Application {
    return this.app;
  }
}

// Start the service if this file is run directly
if (require.main === module) {
  const ondcAdapter = new ONDCAdapterApp();
  ondcAdapter.start();
}

export default ONDCAdapterApp;
