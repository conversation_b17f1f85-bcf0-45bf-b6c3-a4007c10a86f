/**
 * Authentication Middleware
 *
 * Handles authentication for the ONDC Adapter service with support for
 * multiple authentication methods including JWT tokens, API keys, and
 * development mode credentials.
 *
 * <AUTHOR> Seller Team
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config/environment';
import { logger } from '../config/logger';
import { createError } from './errorHandler';

/**
 * User interface
 *
 * Defines the structure of authenticated user information.
 */
export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  tenantId: string;
  permissions: string[];
  isActive: boolean;
}

/**
 * JWT payload interface
 *
 * Defines the structure of JWT token payload.
 */
interface JWTPayload {
  userId: string;
  email: string;
  tenantId: string;
  role: string;
  iat: number;
  exp: number;
}

/**
 * API key interface
 *
 * Defines the structure of API key information.
 */
interface ApiKey {
  id: string;
  key: string;
  name: string;
  tenantId: string;
  permissions: string[];
  isActive: boolean;
  lastUsed?: Date;
}

/**
 * Mock users for development mode
 *
 * In production, this would be replaced with database queries
 * or external authentication service calls.
 */
const mockUsers: Record<string, User> = {
  'demo-user': {
    id: 'demo-user',
    email: '<EMAIL>',
    name: 'Demo User',
    role: 'admin',
    tenantId: 'demo',
    permissions: ['read', 'write', 'admin'],
    isActive: true,
  },
  'default-user': {
    id: 'default-user',
    email: '<EMAIL>',
    name: 'Default Admin',
    role: 'admin',
    tenantId: 'default',
    permissions: ['read', 'write', 'admin'],
    isActive: true,
  },
};

/**
 * Mock API keys for development mode
 */
const mockApiKeys: Record<string, ApiKey> = {
  'dev-api-key-123': {
    id: 'api-key-1',
    key: 'dev-api-key-123',
    name: 'Development API Key',
    tenantId: 'default',
    permissions: ['read', 'write'],
    isActive: true,
  },
  'demo-api-key-456': {
    id: 'api-key-2',
    key: 'demo-api-key-456',
    name: 'Demo API Key',
    tenantId: 'demo',
    permissions: ['read', 'write', 'admin'],
    isActive: true,
  },
};

/**
 * Extract token from request
 *
 * Extracts authentication token from Authorization header
 * or other sources.
 */
const extractToken = (req: Request): string | null => {
  const authHeader = req.headers.authorization;

  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // Check for token in query parameter (for WebSocket connections)
  const queryToken = req.query.token as string;
  if (queryToken) {
    return queryToken;
  }

  return null;
};

/**
 * Extract API key from request
 *
 * Extracts API key from X-API-Key header.
 */
const extractApiKey = (req: Request): string | null => {
  return (req.headers['x-api-key'] as string) || null;
};

/**
 * Verify JWT token
 *
 * Verifies and decodes JWT token, returning user information.
 */
const verifyJWT = async (token: string): Promise<User | null> => {
  try {
    const decoded = jwt.verify(token, config.auth.jwtSecret) as JWTPayload;

    // In production, fetch user from database
    // const user = await UserService.findById(decoded.userId);

    // For development, use mock users
    const user = mockUsers[decoded.userId];
    if (!user || !user.isActive) {
      return null;
    }

    return user;
  } catch (error: any) {
    logger.debug('JWT verification failed', {
      error: error.message,
      token: token.substring(0, 20) + '...',
    });
    return null;
  }
};

/**
 * Verify API key
 *
 * Verifies API key and returns associated user/service information.
 */
const verifyApiKey = async (apiKey: string): Promise<User | null> => {
  try {
    // In production, fetch API key from database
    // const keyRecord = await ApiKeyService.findByKey(apiKey);

    // For development, use mock API keys
    const keyRecord = mockApiKeys[apiKey];
    if (!keyRecord || !keyRecord.isActive) {
      return null;
    }

    // Update last used timestamp
    keyRecord.lastUsed = new Date();

    // Return a user-like object for API key authentication
    return {
      id: `api-key:${keyRecord.id}`,
      email: `api-key@${keyRecord.tenantId}`,
      name: keyRecord.name,
      role: 'api',
      tenantId: keyRecord.tenantId,
      permissions: keyRecord.permissions,
      isActive: true,
    };
  } catch (error: any) {
    logger.debug('API key verification failed', {
      error: error.message,
      apiKey: apiKey.substring(0, 10) + '...',
    });
    return null;
  }
};

/**
 * Development mode authentication
 *
 * Provides hardcoded authentication for development mode
 * using demo/demo credentials.
 */
const developmentAuth = (req: Request): User | null => {
  if (!config.isDevelopment) {
    return null;
  }

  // Check for development credentials in headers
  const username = req.headers['x-dev-username'] as string;
  const password = req.headers['x-dev-password'] as string;

  if (username === 'demo' && password === 'demo') {
    return mockUsers['demo-user'] || null;
  }

  if (username === 'admin' && password === 'admin') {
    return mockUsers['default-user'] || null;
  }

  return null;
};

/**
 * Public endpoints that don't require authentication
 */
const publicEndpoints = ['/health', '/api/v1/docs', '/'];

/**
 * Check if endpoint is public
 */
const isPublicEndpoint = (path: string): boolean => {
  return publicEndpoints.some(endpoint => path === endpoint || path.startsWith(endpoint + '/'));
};

/**
 * Main authentication middleware
 *
 * Handles authentication using multiple methods and attaches
 * user information to the request object.
 */
export const authMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Skip authentication for public endpoints
    if (isPublicEndpoint(req.path)) {
      return next();
    }

    let user: User | null = null;

    // 1. Try JWT authentication
    const token = extractToken(req);
    if (token) {
      user = await verifyJWT(token);
      if (user) {
        logger.debug('JWT authentication successful', {
          userId: user.id,
          tenantId: user.tenantId,
          path: req.path,
        });
      }
    }

    // 2. Try API key authentication
    if (!user) {
      const apiKey = extractApiKey(req);
      if (apiKey) {
        user = await verifyApiKey(apiKey);
        if (user) {
          logger.debug('API key authentication successful', {
            userId: user.id,
            tenantId: user.tenantId,
            path: req.path,
          });
        }
      }
    }

    // 3. Try development mode authentication
    if (!user && config.isDevelopment) {
      user = developmentAuth(req);
      if (user) {
        logger.debug('Development authentication successful', {
          userId: user.id,
          tenantId: user.tenantId,
          path: req.path,
        });
      }
    }

    // 4. Check if authentication is required
    if (!user) {
      throw createError.unauthorized('Authentication required');
    }

    // Attach user to request
    req.user = user;

    // Set user context in response headers
    res.setHeader('X-User-ID', user.id);
    res.setHeader('X-User-Role', user.role);

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Optional authentication middleware
 *
 * Attempts authentication but doesn't fail if no credentials provided.
 * Useful for endpoints that work with or without authentication.
 */
export const optionalAuth = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const token = extractToken(req);
    const apiKey = extractApiKey(req);

    if (token || apiKey || config.isDevelopment) {
      // Try authentication but don't fail
      await authMiddleware(req, res, error => {
        if (error && error.statusCode === 401) {
          // Ignore authentication errors
          next();
        } else {
          next(error);
        }
      });
    } else {
      next();
    }
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

/**
 * Role-based authorization middleware
 *
 * Middleware factory that creates authorization guards for specific roles.
 */
export const requireRole = (requiredRole: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createError.unauthorized('Authentication required');
    }

    if (req.user.role !== requiredRole && req.user.role !== 'admin') {
      throw createError.forbidden(`Role '${requiredRole}' required`, {
        userRole: req.user.role,
        requiredRole,
      });
    }

    next();
  };
};

/**
 * Permission-based authorization middleware
 *
 * Middleware factory that creates authorization guards for specific permissions.
 */
export const requirePermission = (permission: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw createError.unauthorized('Authentication required');
    }

    if (!req.user.permissions.includes(permission) && !req.user.permissions.includes('admin')) {
      throw createError.forbidden(`Permission '${permission}' required`, {
        userPermissions: req.user.permissions,
        requiredPermission: permission,
      });
    }

    next();
  };
};

/**
 * Generate JWT token
 *
 * Helper function to generate JWT tokens for authenticated users.
 */
export const generateToken = (user: User): string => {
  const payload: JWTPayload = {
    userId: user.id,
    email: user.email,
    tenantId: user.tenantId,
    role: user.role,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + 24 * 60 * 60, // 24 hours
  };

  return jwt.sign(payload, config.auth.jwtSecret);
};

export default authMiddleware;
