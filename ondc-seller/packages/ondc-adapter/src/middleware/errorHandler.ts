/**
 * Error Handler Middleware
 *
 * Centralized error handling middleware for the ONDC Adapter service.
 * Provides consistent error responses, logging, and error classification
 * for better debugging and monitoring.
 *
 * <AUTHOR> Seller Team
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../config/logger';
import { config } from '../config/environment';

/**
 * Custom error class for API errors
 *
 * Extends the standard Error class with additional properties
 * for HTTP status codes and error categorization.
 */
class ApiError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public errorCode?: string;
  public details?: any;

  constructor(
    message: string,
    statusCode: number = 500,
    isOperational: boolean = true,
    errorCode?: string,
    details?: any
  ) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    if (errorCode !== undefined) {
      this.errorCode = errorCode;
    }
    if (details !== undefined) {
      this.details = details;
    }

    // Capture stack trace
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * ONDC specific error class
 *
 * Specialized error class for ONDC protocol errors with
 * additional context for ONDC message handling.
 */
class ONDCError extends ApiError {
  public ondcErrorCode?: string;
  public transactionId?: string;
  public messageId?: string;

  constructor(
    message: string,
    statusCode: number = 400,
    ondcErrorCode?: string,
    transactionId?: string,
    messageId?: string,
    details?: any
  ) {
    super(message, statusCode, true, ondcErrorCode, details);
    this.name = 'ONDCError';
    if (ondcErrorCode !== undefined) {
      this.ondcErrorCode = ondcErrorCode;
    }
    if (transactionId !== undefined) {
      this.transactionId = transactionId;
    }
    if (messageId !== undefined) {
      this.messageId = messageId;
    }
  }
}

/**
 * Validation error class
 *
 * Specialized error class for input validation errors
 * with detailed field-level error information.
 */
class ValidationError extends ApiError {
  public validationErrors: any[];

  constructor(message: string, validationErrors: any[] = []) {
    super(message, 400, true, 'VALIDATION_ERROR');
    this.name = 'ValidationError';
    this.validationErrors = validationErrors;
  }
}

/**
 * Error response formatter
 *
 * Formats error responses consistently across the application
 * with appropriate detail levels for different environments.
 */
const formatErrorResponse = (error: any, req: Request) => {
  const baseResponse: any = {
    error: {
      message: error.message || 'Internal Server Error',
      statusCode: error.statusCode || 500,
      timestamp: new Date().toISOString(),
      requestId: req.id,
      path: req.originalUrl,
    },
  };

  // Add error code if available
  if (error.errorCode) {
    baseResponse.error.code = error.errorCode;
  }

  // Add ONDC specific fields
  if (error instanceof ONDCError) {
    baseResponse.error.ondcErrorCode = error.ondcErrorCode;
    baseResponse.error.transactionId = error.transactionId;
    baseResponse.error.messageId = error.messageId;
  }

  // Add validation errors
  if (error instanceof ValidationError) {
    baseResponse.error.validationErrors = error.validationErrors;
  }

  // Add details in development mode
  if (config.isDevelopment && error.details) {
    baseResponse.error.details = error.details;
  }

  // Add stack trace in development mode
  if (config.isDevelopment && error.stack) {
    baseResponse.error.stack = error.stack;
  }

  return baseResponse;
};

/**
 * Error logger
 *
 * Logs errors with appropriate detail levels and context
 * for debugging and monitoring purposes.
 */
const logError = (error: any, req: Request) => {
  const logContext = {
    requestId: req.id,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.get('User-Agent'),
    ip: req.ip,
    tenantId: req.tenantId,
    userId: req.user?.id,
  };

  if (error instanceof ONDCError) {
    logger.error('ONDC Protocol Error', {
      ...logContext,
      ondcErrorCode: error.ondcErrorCode,
      transactionId: error.transactionId,
      messageId: error.messageId,
      message: error.message,
      stack: error.stack,
    });
  } else if (error instanceof ValidationError) {
    logger.warn('Validation Error', {
      ...logContext,
      message: error.message,
      validationErrors: error.validationErrors,
    });
  } else if (error.statusCode && error.statusCode < 500) {
    // Client errors (4xx)
    logger.warn('Client Error', {
      ...logContext,
      statusCode: error.statusCode,
      message: error.message,
    });
  } else {
    // Server errors (5xx)
    logger.error('Server Error', {
      ...logContext,
      statusCode: error.statusCode || 500,
      message: error.message,
      stack: error.stack,
    });
  }
};

/**
 * Main error handler middleware
 *
 * Catches all errors in the application, logs them appropriately,
 * and sends consistent error responses to clients.
 */
export const errorHandler = (error: any, req: Request, res: Response, next: NextFunction): void => {
  // Log the error
  logError(error, req);

  // Don't send error response if headers already sent
  if (res.headersSent) {
    return next(error);
  }

  // Format and send error response
  const errorResponse = formatErrorResponse(error, req);
  const statusCode = error.statusCode || 500;

  res.status(statusCode).json(errorResponse);
};

/**
 * Async error wrapper
 *
 * Wraps async route handlers to automatically catch and forward
 * any thrown errors to the error handling middleware.
 */
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

/**
 * Not found handler
 *
 * Handles requests to non-existent routes with a consistent
 * 404 error response.
 */
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new ApiError(`Route ${req.originalUrl} not found`, 404, true, 'ROUTE_NOT_FOUND');
  next(error);
};

/**
 * Error factory functions
 *
 * Convenience functions for creating common error types
 * with consistent messaging and status codes.
 */
export const createError = {
  badRequest: (message: string, details?: any) =>
    new ApiError(message, 400, true, 'BAD_REQUEST', details),

  unauthorized: (message: string = 'Unauthorized') =>
    new ApiError(message, 401, true, 'UNAUTHORIZED'),

  forbidden: (message: string = 'Forbidden', details?: any) =>
    new ApiError(message, 403, true, 'FORBIDDEN', details),

  notFound: (message: string = 'Not Found') => new ApiError(message, 404, true, 'NOT_FOUND'),

  conflict: (message: string, details?: any) =>
    new ApiError(message, 409, true, 'CONFLICT', details),

  validation: (message: string, validationErrors: any[] = []) =>
    new ValidationError(message, validationErrors),

  internal: (message: string = 'Internal Server Error', details?: any) =>
    new ApiError(message, 500, false, 'INTERNAL_ERROR', details),

  ondc: (
    message: string,
    ondcErrorCode?: string,
    transactionId?: string,
    messageId?: string,
    details?: any
  ) => new ONDCError(message, 400, ondcErrorCode, transactionId, messageId, details),
};

export { ApiError, ONDCError, ValidationError };
