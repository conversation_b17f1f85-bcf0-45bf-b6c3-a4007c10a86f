/**
 * Rate Limiter Middleware
 *
 * Implements rate limiting for the ONDC Adapter service to prevent
 * abuse and ensure fair usage. Supports different rate limits for
 * different endpoints and user types.
 *
 * <AUTHOR> Seller Team
 */

import rateLimit from 'express-rate-limit';
import { Request, Response } from 'express';
import { config } from '../config/environment';
import { logger } from '../config/logger';
import { createError } from './errorHandler';

/**
 * Rate limit store interface
 *
 * Defines the interface for rate limit storage backends
 * (memory, Redis, etc.)
 */
interface RateLimitStore {
  incr(key: string): Promise<{ totalHits: number; timeToExpire: number }>;
  decrement(key: string): Promise<void>;
  resetKey(key: string): Promise<void>;
}

/**
 * Memory-based rate limit store
 *
 * Simple in-memory storage for rate limiting.
 * Note: This should be replaced with Redis in production
 * for multi-instance deployments.
 */
class MemoryStore implements RateLimitStore {
  private store = new Map<string, { count: number; resetTime: number }>();

  async incr(key: string): Promise<{ totalHits: number; timeToExpire: number }> {
    const now = Date.now();
    const windowMs = config.security.rateLimit.windowMs;

    let record = this.store.get(key);

    if (!record || now > record.resetTime) {
      record = {
        count: 1,
        resetTime: now + windowMs,
      };
    } else {
      record.count++;
    }

    this.store.set(key, record);

    return {
      totalHits: record.count,
      timeToExpire: Math.max(0, record.resetTime - now),
    };
  }

  async decrement(key: string): Promise<void> {
    const record = this.store.get(key);
    if (record && record.count > 0) {
      record.count--;
      this.store.set(key, record);
    }
  }

  async resetKey(key: string): Promise<void> {
    this.store.delete(key);
  }
}

/**
 * Rate limit key generator
 *
 * Generates unique keys for rate limiting based on
 * IP address, user ID, tenant ID, and endpoint.
 */
const generateRateLimitKey = (req: Request, prefix: string = 'rl'): string => {
  const parts = [prefix];

  // Add IP address
  parts.push(req.ip || 'unknown');

  // Add user ID if authenticated
  if (req.user?.id) {
    parts.push(`user:${req.user.id}`);
  }

  // Add tenant ID if available
  if (req.tenantId) {
    parts.push(`tenant:${req.tenantId}`);
  }

  // Add endpoint path for endpoint-specific limits
  const path = req.route?.path || req.path;
  if (path) {
    parts.push(`path:${path.replace(/[^a-zA-Z0-9]/g, '_')}`);
  }

  return parts.join(':');
};

/**
 * Rate limit exceeded handler
 *
 * Handles rate limit exceeded responses with proper
 * logging and error formatting.
 */
const rateLimitHandler = (req: Request, res: Response) => {
  const error = createError.badRequest('Too many requests, please try again later', {
    retryAfter: res.get('Retry-After'),
    limit: res.get('X-RateLimit-Limit'),
    remaining: res.get('X-RateLimit-Remaining'),
  });

  logger.warn('Rate limit exceeded', {
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    path: req.path,
    method: req.method,
    userId: req.user?.id,
    tenantId: req.tenantId,
  });

  res.status(429).json({
    error: {
      message: error.message,
      statusCode: 429,
      code: 'RATE_LIMIT_EXCEEDED',
      details: error.details,
      timestamp: new Date().toISOString(),
      requestId: req.id,
    },
  });
};

/**
 * Skip rate limiting function
 *
 * Determines whether to skip rate limiting for certain
 * requests (health checks, internal services, etc.)
 */
const skipRateLimit = (req: Request): boolean => {
  // Skip for health checks
  if (req.path === '/health' || req.path.startsWith('/health/')) {
    return true;
  }

  // Skip for internal service calls (if API key is present)
  if (req.headers['x-api-key']) {
    return true;
  }

  // Skip in test environment
  if (config.isTest) {
    return true;
  }

  return false;
};

/**
 * Default rate limiter configuration
 *
 * General rate limiting for all endpoints with
 * configurable limits and window.
 */
export const rateLimiter = rateLimit({
  windowMs: config.security.rateLimit.windowMs,
  max: config.security.rateLimit.maxRequests,
  message: 'Too many requests from this IP, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: req => generateRateLimitKey(req, 'general'),
});

/**
 * Strict rate limiter for sensitive endpoints
 *
 * More restrictive rate limiting for authentication,
 * ONDC protocol endpoints, and other sensitive operations.
 */
export const strictRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 requests per window
  message: 'Too many requests to sensitive endpoint, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: req => generateRateLimitKey(req, 'strict'),
});

/**
 * ONDC protocol rate limiter
 *
 * Specialized rate limiting for ONDC protocol endpoints
 * with higher limits but per-transaction tracking.
 */
export const ondcRateLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 100, // 100 ONDC messages per window
  message: 'Too many ONDC protocol requests, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  skip: skipRateLimit,
  handler: rateLimitHandler,
  keyGenerator: req => {
    // Use transaction ID for ONDC-specific rate limiting
    const transactionId = req.body?.context?.transaction_id;
    if (transactionId) {
      return `ondc:transaction:${transactionId}`;
    }
    return generateRateLimitKey(req, 'ondc');
  },
});

/**
 * API rate limiter for external integrations
 *
 * Rate limiting for API endpoints used by external
 * services with API key authentication.
 */
export const apiRateLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 1000, // 1000 requests per minute for API users
  message: 'API rate limit exceeded, please try again later',
  standardHeaders: true,
  legacyHeaders: false,
  skip: req => {
    // Skip if no API key (will be handled by auth middleware)
    if (!req.headers['x-api-key']) {
      return true;
    }
    return skipRateLimit(req);
  },
  handler: rateLimitHandler,
  keyGenerator: req => {
    const apiKey = req.headers['x-api-key'] as string;
    return `api:${apiKey}`;
  },
});

/**
 * Create custom rate limiter
 *
 * Factory function for creating custom rate limiters
 * with specific configurations.
 */
export const createRateLimiter = (options: {
  windowMs: number;
  max: number;
  message?: string;
  keyPrefix?: string;
  skipFunction?: (req: Request) => boolean;
}) => {
  return rateLimit({
    windowMs: options.windowMs,
    max: options.max,
    message: options.message || 'Rate limit exceeded',
    standardHeaders: true,
    legacyHeaders: false,
    skip: options.skipFunction || skipRateLimit,
    handler: rateLimitHandler,
    keyGenerator: req => generateRateLimitKey(req, options.keyPrefix || 'custom'),
  });
};

export default rateLimiter;
