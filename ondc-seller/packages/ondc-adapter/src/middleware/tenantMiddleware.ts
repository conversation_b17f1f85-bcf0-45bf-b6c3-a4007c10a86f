/**
 * Tenant Middleware
 *
 * Handles multi-tenant functionality for the ONDC Adapter service.
 * Extracts tenant information from requests and provides tenant-specific
 * configuration and data isolation.
 *
 * <AUTHOR> Seller Team
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../config/logger';
import { createError } from './errorHandler';

/**
 * Tenant interface
 *
 * Defines the structure of tenant information
 * used throughout the application.
 */
export interface Tenant {
  id: string;
  name: string;
  domain?: string;
  subdomain?: string;
  isActive: boolean;
  settings: {
    ondcConfig?: {
      subscriberId: string;
      subscriberUrl: string;
      signingKey: string;
      encryptionKey: string;
    };
    medusaConfig?: {
      storeId: string;
      apiKey: string;
    };
    features: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Extend Express Request interface to include tenant information
 */
declare global {
  namespace Express {
    interface Request {
      tenant?: Tenant;
      tenantId?: string;
      id?: string;
      user?: any;
    }
  }
}

/**
 * Mock tenant data for development
 *
 * In production, this would be replaced with database queries
 * or external tenant management service calls.
 */
const mockTenants: Record<string, Tenant> = {
  default: {
    id: 'default',
    name: 'Default Tenant',
    domain: 'localhost',
    subdomain: 'default',
    isActive: true,
    settings: {
      ondcConfig: {
        subscriberId: 'default-seller',
        subscriberUrl: 'http://localhost:3001',
        signingKey: 'default-signing-key',
        encryptionKey: 'default-encryption-key',
      },
      medusaConfig: {
        storeId: 'default-store',
        apiKey: 'default-api-key',
      },
      features: ['catalog', 'orders', 'fulfillment'],
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
  demo: {
    id: 'demo',
    name: 'Demo Tenant',
    domain: 'demo.localhost',
    subdomain: 'demo',
    isActive: true,
    settings: {
      ondcConfig: {
        subscriberId: 'demo-seller',
        subscriberUrl: 'http://demo.localhost:3001',
        signingKey: 'demo-signing-key',
        encryptionKey: 'demo-encryption-key',
      },
      medusaConfig: {
        storeId: 'demo-store',
        apiKey: 'demo-api-key',
      },
      features: ['catalog', 'orders', 'fulfillment', 'analytics'],
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  },
};

/**
 * Extract tenant ID from request
 *
 * Attempts to extract tenant ID from various sources:
 * 1. X-Tenant-ID header
 * 2. Subdomain
 * 3. Query parameter
 * 4. JWT token
 * 5. Default tenant
 */
const extractTenantId = (req: Request): string => {
  // 1. Check X-Tenant-ID header (highest priority)
  const headerTenantId = req.headers['x-tenant-id'] as string;
  if (headerTenantId) {
    return headerTenantId;
  }

  // 2. Extract from subdomain
  const host = req.get('host');
  if (host) {
    const subdomain = host.split('.')[0];
    if (subdomain && subdomain !== 'localhost' && subdomain !== 'www') {
      return subdomain;
    }
  }

  // 3. Check query parameter
  const queryTenantId = req.query.tenantId as string;
  if (queryTenantId) {
    return queryTenantId;
  }

  // 4. Extract from JWT token (if available)
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    try {
      const token = authHeader.substring(7);
      // In a real implementation, decode JWT and extract tenant ID
      // const decoded = jwt.verify(token, config.auth.jwtSecret);
      // return decoded.tenantId;
    } catch (error) {
      // Invalid token, continue to default
    }
  }

  // 5. Default tenant
  return 'default';
};

/**
 * Fetch tenant information
 *
 * Retrieves tenant information from the database or cache.
 * In development mode, uses mock data.
 */
const fetchTenant = async (tenantId: string): Promise<Tenant | null> => {
  try {
    // In development mode, use mock data
    if (process.env.NODE_ENV === 'development') {
      return mockTenants[tenantId] || null;
    }

    // In production, this would query the database
    // const tenant = await TenantService.findById(tenantId);
    // return tenant;

    // For now, return mock data
    return mockTenants[tenantId] || null;
  } catch (error: any) {
    logger.error('Error fetching tenant', {
      tenantId,
      error: error.message,
    });
    return null;
  }
};

/**
 * Validate tenant
 *
 * Checks if the tenant is valid and active.
 * Performs additional validation based on request context.
 */
const validateTenant = (tenant: Tenant, req: Request): boolean => {
  // Check if tenant is active
  if (!tenant.isActive) {
    return false;
  }

  // Check if tenant has required features for the endpoint
  const path = req.path;
  if (path.includes('/catalog') && !tenant.settings.features.includes('catalog')) {
    return false;
  }
  if (path.includes('/orders') && !tenant.settings.features.includes('orders')) {
    return false;
  }
  if (path.includes('/fulfillment') && !tenant.settings.features.includes('fulfillment')) {
    return false;
  }

  return true;
};

/**
 * Tenant middleware
 *
 * Main middleware function that extracts, validates, and attaches
 * tenant information to the request object.
 */
export const tenantMiddleware = async (
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    // Skip tenant validation for health checks and public endpoints
    if (req.path === '/health' || req.path.startsWith('/health/')) {
      return next();
    }

    // Extract tenant ID
    const tenantId = extractTenantId(req);
    req.tenantId = tenantId;

    // Fetch tenant information
    const tenant = await fetchTenant(tenantId);
    if (!tenant) {
      throw createError.badRequest(`Tenant '${tenantId}' not found`, { tenantId });
    }

    // Validate tenant
    if (!validateTenant(tenant, req)) {
      throw createError.forbidden(`Tenant '${tenantId}' is not authorized for this operation`, {
        tenantId,
        path: req.path,
      });
    }

    // Attach tenant to request
    req.tenant = tenant;

    // Add tenant context to response headers
    res.setHeader('X-Tenant-ID', tenant.id);
    res.setHeader('X-Tenant-Name', tenant.name);

    // Log tenant context
    logger.debug('Tenant context established', {
      tenantId: tenant.id,
      tenantName: tenant.name,
      path: req.path,
      method: req.method,
      requestId: req.id,
    });

    next();
  } catch (error) {
    next(error);
  }
};

/**
 * Require tenant middleware
 *
 * Stricter middleware that requires a valid tenant to be present.
 * Used for endpoints that must have tenant context.
 */
export const requireTenant = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.tenant) {
    throw createError.badRequest('Tenant context is required for this operation');
  }
  next();
};

/**
 * Tenant feature guard
 *
 * Middleware factory that creates guards for specific tenant features.
 * Ensures the tenant has the required feature enabled.
 */
export const requireTenantFeature = (feature: string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.tenant) {
      throw createError.badRequest('Tenant context is required');
    }

    if (!req.tenant.settings.features.includes(feature)) {
      throw createError.forbidden(`Tenant does not have '${feature}' feature enabled`, {
        tenantId: req.tenant.id,
        feature,
      });
    }

    next();
  };
};

/**
 * Get tenant configuration
 *
 * Helper function to get tenant-specific configuration
 * for ONDC and Medusa integrations.
 */
export const getTenantConfig = (tenant: Tenant) => {
  return {
    ondc: tenant.settings.ondcConfig,
    medusa: tenant.settings.medusaConfig,
    features: tenant.settings.features,
  };
};

/**
 * Tenant service class
 *
 * Service class for tenant-related operations.
 * In production, this would interact with a database.
 */
export class TenantService {
  /**
   * Find tenant by ID
   */
  static async findById(tenantId: string): Promise<Tenant | null> {
    return fetchTenant(tenantId);
  }

  /**
   * Find tenant by domain
   */
  static async findByDomain(domain: string): Promise<Tenant | null> {
    // In production, query database by domain
    const tenant = Object.values(mockTenants).find(
      t => t.domain === domain || t.subdomain === domain
    );
    return tenant || null;
  }

  /**
   * Update tenant configuration
   */
  static async updateConfig(
    tenantId: string,
    config: Partial<Tenant['settings']>
  ): Promise<Tenant | null> {
    const tenant = await this.findById(tenantId);
    if (!tenant) {
      return null;
    }

    // In production, update database
    tenant.settings = { ...tenant.settings, ...config };
    tenant.updatedAt = new Date();

    return tenant;
  }

  /**
   * List all tenants
   */
  static async list(): Promise<Tenant[]> {
    return Object.values(mockTenants);
  }
}

export default tenantMiddleware;
