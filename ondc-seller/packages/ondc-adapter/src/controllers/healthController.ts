/**
 * Health Controller
 *
 * Provides health check endpoints for the ONDC Adapter service.
 * Includes basic health status, detailed system checks, and dependency
 * health monitoring for comprehensive service monitoring.
 *
 * <AUTHOR> Seller Team
 */

import { Router, Request, Response } from 'express';
import { config } from '../config/environment';
import { logger } from '../config/logger';
import { asyncHandler } from '../middleware/errorHandler';

/**
 * Health status interface
 */
interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks?: Record<string, HealthCheck>;
}

/**
 * Individual health check interface
 */
interface HealthCheck {
  status: 'pass' | 'fail' | 'warn';
  message?: string;
  duration?: number;
  timestamp: string;
  details?: any;
}

/**
 * Service dependencies for health checks
 */
const dependencies = {
  database: {
    name: 'Database',
    url: config.database.url,
    timeout: 5000,
  },
  redis: {
    name: 'Redis Cache',
    url: config.database.redis.url,
    timeout: 3000,
  },
  medusa: {
    name: 'Medusa Backend',
    url: config.medusa.backendUrl,
    timeout: 5000,
  },
  ondc: {
    name: 'ONDC Network',
    url: config.ondc.networkUrl,
    timeout: 10000,
  },
  authService: {
    name: 'Auth Service',
    url: config.services.auth.url,
    timeout: 5000,
  },
  notificationService: {
    name: 'Notification Service',
    url: config.services.notification.url,
    timeout: 5000,
  },
};

/**
 * Check database connectivity
 */
const checkDatabase = async (): Promise<HealthCheck> => {
  const start = Date.now();

  try {
    // In production, this would use actual database connection
    // const result = await db.query('SELECT 1');

    // Mock database check for development
    await new Promise(resolve => setTimeout(resolve, 100));

    return {
      status: 'pass',
      message: 'Database connection successful',
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    return {
      status: 'fail',
      message: `Database connection failed: ${error.message}`,
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
      details: { error: error.message },
    };
  }
};

/**
 * Check Redis connectivity
 */
const checkRedis = async (): Promise<HealthCheck> => {
  const start = Date.now();

  try {
    // In production, this would use actual Redis connection
    // const result = await redis.ping();

    // Mock Redis check for development
    await new Promise(resolve => setTimeout(resolve, 50));

    return {
      status: 'pass',
      message: 'Redis connection successful',
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    return {
      status: 'fail',
      message: `Redis connection failed: ${error.message}`,
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
      details: { error: error.message },
    };
  }
};

/**
 * Check external service connectivity
 */
const checkExternalService = async (
  name: string,
  url: string,
  timeout: number = 5000
): Promise<HealthCheck> => {
  const start = Date.now();

  try {
    // In production, this would make actual HTTP requests
    // const response = await axios.get(`${url}/health`, { timeout });

    // Mock external service check for development
    await new Promise(resolve => setTimeout(resolve, Math.random() * 200));

    // Simulate occasional failures for testing
    if (Math.random() < 0.1) {
      throw new Error('Service temporarily unavailable');
    }

    return {
      status: 'pass',
      message: `${name} is accessible`,
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
    };
  } catch (error: any) {
    return {
      status: 'fail',
      message: `${name} is not accessible: ${error.message}`,
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
      details: { url, error: error.message },
    };
  }
};

/**
 * Check system resources
 */
const checkSystemResources = (): HealthCheck => {
  const start = Date.now();

  try {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    // Convert bytes to MB
    const memoryMB = {
      rss: Math.round(memoryUsage.rss / 1024 / 1024),
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
      external: Math.round(memoryUsage.external / 1024 / 1024),
    };

    // Check if memory usage is concerning
    const heapUsagePercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    const status = heapUsagePercent > 90 ? 'warn' : 'pass';

    return {
      status,
      message: status === 'warn' ? 'High memory usage detected' : 'System resources normal',
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
      details: {
        memory: memoryMB,
        heapUsagePercent: Math.round(heapUsagePercent),
        uptime: process.uptime(),
      },
    };
  } catch (error: any) {
    return {
      status: 'fail',
      message: `System resource check failed: ${error.message}`,
      duration: Date.now() - start,
      timestamp: new Date().toISOString(),
      details: { error: error.message },
    };
  }
};

/**
 * Basic health check endpoint
 *
 * GET /health
 * Returns basic health status without detailed checks
 */
const getBasicHealth = asyncHandler(async (req: Request, res: Response) => {
  const healthStatus: HealthStatus = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    environment: config.nodeEnv,
  };

  res.status(200).json(healthStatus);
});

/**
 * Detailed health check endpoint
 *
 * GET /health/detailed
 * Returns comprehensive health status with dependency checks
 */
const getDetailedHealth = asyncHandler(async (req: Request, res: Response) => {
  const checks: Record<string, HealthCheck> = {};

  // Run all health checks in parallel
  const [
    databaseCheck,
    redisCheck,
    systemCheck,
    medusaCheck,
    ondcCheck,
    authServiceCheck,
    notificationServiceCheck,
  ] = await Promise.all([
    checkDatabase(),
    checkRedis(),
    checkSystemResources(),
    checkExternalService('Medusa Backend', dependencies.medusa.url, dependencies.medusa.timeout),
    checkExternalService('ONDC Network', dependencies.ondc.url, dependencies.ondc.timeout),
    checkExternalService(
      'Auth Service',
      dependencies.authService.url,
      dependencies.authService.timeout
    ),
    checkExternalService(
      'Notification Service',
      dependencies.notificationService.url,
      dependencies.notificationService.timeout
    ),
  ]);

  checks.database = databaseCheck;
  checks.redis = redisCheck;
  checks.system = systemCheck;
  checks.medusa = medusaCheck;
  checks.ondc = ondcCheck;
  checks.authService = authServiceCheck;
  checks.notificationService = notificationServiceCheck;

  // Determine overall health status
  const failedChecks = Object.values(checks).filter(check => check.status === 'fail');
  const warnChecks = Object.values(checks).filter(check => check.status === 'warn');

  let overallStatus: 'healthy' | 'unhealthy' | 'degraded';
  if (failedChecks.length > 0) {
    overallStatus = 'unhealthy';
  } else if (warnChecks.length > 0) {
    overallStatus = 'degraded';
  } else {
    overallStatus = 'healthy';
  }

  const healthStatus: HealthStatus = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    version: '1.0.0',
    environment: config.nodeEnv,
    checks,
  };

  // Set appropriate HTTP status code
  const statusCode = overallStatus === 'healthy' ? 200 : overallStatus === 'degraded' ? 200 : 503;

  res.status(statusCode).json(healthStatus);
});

/**
 * Readiness check endpoint
 *
 * GET /health/ready
 * Returns 200 if service is ready to accept requests
 */
const getReadiness = asyncHandler(async (req: Request, res: Response) => {
  // Check critical dependencies
  const [databaseCheck, redisCheck] = await Promise.all([checkDatabase(), checkRedis()]);

  const isReady = databaseCheck.status === 'pass' && redisCheck.status === 'pass';

  if (isReady) {
    res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString(),
      checks: {
        database: databaseCheck,
        redis: redisCheck,
      },
    });
  } else {
    res.status(503).json({
      status: 'not ready',
      timestamp: new Date().toISOString(),
      checks: {
        database: databaseCheck,
        redis: redisCheck,
      },
    });
  }
});

/**
 * Liveness check endpoint
 *
 * GET /health/live
 * Returns 200 if service is alive (basic functionality)
 */
const getLiveness = asyncHandler(async (req: Request, res: Response) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    pid: process.pid,
  });
});

/**
 * Health routes
 */
export const healthRoutes = Router();

healthRoutes.get('/', getBasicHealth);
healthRoutes.get('/detailed', getDetailedHealth);
healthRoutes.get('/ready', getReadiness);
healthRoutes.get('/live', getLiveness);

export default healthRoutes;
