/**
 * ONDC Controller Unit Tests
 * 
 * Comprehensive tests for ONDC protocol message handlers including
 * search, select, init, confirm, and status endpoints.
 */

import request from 'supertest';
import express from 'express';
import { ondcRoutes } from '../ondcController';
import { ONDCMessageHandler } from '../../services/ondcMessageHandler';
import { ONDCValidator } from '../../services/ondcValidator';
import {
  createMockONDCMessage,
  createMockTenant,
  createMockUser,
  createMockRequest,
  createMockResponse,
  createMockNext,
} from '../../../tests/setup';

// Mock dependencies
jest.mock('../../services/ondcMessageHandler');
jest.mock('../../services/ondcValidator');
jest.mock('../../middleware/errorHandler', () => ({
  asyncHandler: (fn: any) => fn,
}));
jest.mock('../../middleware/tenantMiddleware', () => ({
  requireTenantFeature: () => (req: any, res: any, next: any) => next(),
}));
jest.mock('../../middleware/rateLimiter', () => ({
  ondcRateLimiter: (req: any, res: any, next: any) => next(),
}));

const MockedONDCMessageHandler = ONDCMessageHandler as jest.MockedClass<typeof ONDCMessageHandler>;
const MockedONDCValidator = ONDCValidator as jest.MockedClass<typeof ONDCValidator>;

describe('ONDC Controller', () => {
  let app: express.Application;
  let mockMessageHandler: jest.Mocked<ONDCMessageHandler>;
  let mockValidator: jest.Mocked<ONDCValidator>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock instances
    mockMessageHandler = {
      handleSearch: jest.fn(),
      handleSelect: jest.fn(),
      handleInit: jest.fn(),
      handleConfirm: jest.fn(),
      handleStatus: jest.fn(),
    } as any;

    mockValidator = {
      validateMessage: jest.fn(),
    } as any;

    // Mock constructors
    MockedONDCMessageHandler.mockImplementation(() => mockMessageHandler);
    MockedONDCValidator.mockImplementation(() => mockValidator);

    // Create test app
    app = express();
    app.use(express.json());
    
    // Add mock middleware to attach tenant and user
    app.use((req: any, res, next) => {
      req.tenant = createMockTenant();
      req.user = createMockUser();
      next();
    });
    
    app.use('/ondc', ondcRoutes);
  });

  describe('POST /ondc/search', () => {
    const searchMessage = createMockONDCMessage('search', {
      intent: {
        item: {
          descriptor: {
            name: 'laptop',
          },
        },
        category: {
          id: 'Electronics',
        },
      },
    });

    it('should handle valid search request successfully', async () => {
      // Arrange
      const mockSearchResponse = {
        context: { ...searchMessage.context, action: 'on_search' },
        message: {
          catalog: {
            'bpp/providers': [
              {
                id: 'test-provider',
                items: [
                  {
                    id: 'item-1',
                    descriptor: { name: 'Test Item' },
                  },
                ],
              },
            ],
          },
        },
      };

      mockValidator.validateMessage.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: [],
      });

      mockMessageHandler.handleSearch.mockResolvedValue(mockSearchResponse);

      // Act
      const response = await request(app)
        .post('/ondc/search')
        .send(searchMessage)
        .expect(200);

      // Assert
      expect(mockValidator.validateMessage).toHaveBeenCalledWith(searchMessage, 'search');
      expect(mockMessageHandler.handleSearch).toHaveBeenCalledWith(
        searchMessage,
        expect.objectContaining({ id: 'test-tenant' })
      );
      expect(response.body).toEqual(mockSearchResponse);
    });

    it('should return NACK for invalid search message', async () => {
      // Arrange
      mockValidator.validateMessage.mockResolvedValue({
        isValid: false,
        errors: ['Invalid message structure'],
        warnings: [],
      });

      // Act
      const response = await request(app)
        .post('/ondc/search')
        .send(searchMessage)
        .expect(400);

      // Assert
      expect(response.body).toEqual({
        message: {
          ack: {
            status: 'NACK',
          },
        },
        error: {
          type: 'DOMAIN-ERROR',
          code: '20001',
          message: 'Invalid message structure',
        },
      });
      expect(mockMessageHandler.handleSearch).not.toHaveBeenCalled();
    });

    it('should handle search processing errors', async () => {
      // Arrange
      mockValidator.validateMessage.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: [],
      });

      mockMessageHandler.handleSearch.mockRejectedValue(new Error('Processing failed'));

      // Act
      const response = await request(app)
        .post('/ondc/search')
        .send(searchMessage)
        .expect(500);

      // Assert
      expect(response.body).toEqual({
        message: {
          ack: {
            status: 'NACK',
          },
        },
        error: {
          type: 'CORE-ERROR',
          code: '10001',
          message: 'Internal server error',
        },
      });
    });
  });

  describe('POST /ondc/select', () => {
    const selectMessage = createMockONDCMessage('select', {
      order: {
        provider: {
          id: 'test-provider',
        },
        items: [
          {
            id: 'item-1',
            quantity: {
              count: 2,
            },
          },
        ],
      },
    });

    it('should handle valid select request successfully', async () => {
      // Arrange
      const mockSelectResponse = {
        context: { ...selectMessage.context, action: 'on_select' },
        message: {
          order: {
            provider: { id: 'test-provider' },
            items: [{ id: 'item-1', quantity: { count: 2 } }],
            quote: {
              price: { currency: 'INR', value: '200' },
            },
          },
        },
      };

      mockValidator.validateMessage.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: [],
      });

      mockMessageHandler.handleSelect.mockResolvedValue(mockSelectResponse);

      // Act
      const response = await request(app)
        .post('/ondc/select')
        .send(selectMessage)
        .expect(200);

      // Assert
      expect(mockValidator.validateMessage).toHaveBeenCalledWith(selectMessage, 'select');
      expect(mockMessageHandler.handleSelect).toHaveBeenCalledWith(
        selectMessage,
        expect.objectContaining({ id: 'test-tenant' })
      );
      expect(response.body).toEqual(mockSelectResponse);
    });

    it('should return NACK for invalid select message', async () => {
      // Arrange
      mockValidator.validateMessage.mockResolvedValue({
        isValid: false,
        errors: ['Missing required fields'],
        warnings: [],
      });

      // Act
      const response = await request(app)
        .post('/ondc/select')
        .send(selectMessage)
        .expect(400);

      // Assert
      expect(response.body.message.ack.status).toBe('NACK');
      expect(response.body.error.type).toBe('DOMAIN-ERROR');
    });
  });

  describe('POST /ondc/init', () => {
    const initMessage = createMockONDCMessage('init', {
      order: {
        provider: { id: 'test-provider' },
        items: [{ id: 'item-1', quantity: { count: 1 } }],
        billing: {
          name: 'Test Customer',
          address: {
            locality: 'Test Area',
            city: 'Test City',
            state: 'Test State',
            country: 'IND',
            area_code: '560001',
          },
          phone: '+919999999999',
        },
        fulfillments: [
          {
            end: {
              location: {
                address: {
                  locality: 'Test Area',
                  city: 'Test City',
                  state: 'Test State',
                  country: 'IND',
                  area_code: '560001',
                },
              },
              contact: {
                phone: '+919999999999',
              },
            },
          },
        ],
      },
    });

    it('should handle valid init request successfully', async () => {
      // Arrange
      const mockInitResponse = {
        context: { ...initMessage.context, action: 'on_init' },
        message: {
          order: {
            id: 'order-draft-123',
            state: 'Created',
            provider: { id: 'test-provider' },
            items: [{ id: 'item-1', quantity: { count: 1 } }],
          },
        },
      };

      mockValidator.validateMessage.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: [],
      });

      mockMessageHandler.handleInit.mockResolvedValue(mockInitResponse);

      // Act
      const response = await request(app)
        .post('/ondc/init')
        .send(initMessage)
        .expect(200);

      // Assert
      expect(mockValidator.validateMessage).toHaveBeenCalledWith(initMessage, 'init');
      expect(mockMessageHandler.handleInit).toHaveBeenCalledWith(
        initMessage,
        expect.objectContaining({ id: 'test-tenant' })
      );
      expect(response.body).toEqual(mockInitResponse);
    });
  });

  describe('POST /ondc/confirm', () => {
    const confirmMessage = createMockONDCMessage('confirm', {
      order: {
        id: 'order-123',
        provider: { id: 'test-provider' },
        items: [{ id: 'item-1', quantity: { count: 1 } }],
        billing: {
          name: 'Test Customer',
          address: {
            locality: 'Test Area',
            city: 'Test City',
            state: 'Test State',
            country: 'IND',
            area_code: '560001',
          },
          phone: '+919999999999',
        },
        fulfillments: [
          {
            end: {
              location: {
                address: {
                  locality: 'Test Area',
                  city: 'Test City',
                  state: 'Test State',
                  country: 'IND',
                  area_code: '560001',
                },
              },
              contact: {
                phone: '+919999999999',
              },
            },
          },
        ],
        payment: {
          params: {
            amount: '100',
            currency: 'INR',
          },
          status: 'PAID',
          type: 'ON-ORDER',
          collected_by: 'BAP',
        },
      },
    });

    it('should handle valid confirm request successfully', async () => {
      // Arrange
      const mockConfirmResponse = {
        context: { ...confirmMessage.context, action: 'on_confirm' },
        message: {
          order: {
            id: 'order-123',
            state: 'Accepted',
            provider: { id: 'test-provider' },
            items: [{ id: 'item-1', quantity: { count: 1 } }],
          },
        },
      };

      mockValidator.validateMessage.mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: [],
      });

      mockMessageHandler.handleConfirm.mockResolvedValue(mockConfirmResponse);

      // Act
      const response = await request(app)
        .post('/ondc/confirm')
        .send(confirmMessage)
        .expect(200);

      // Assert
      expect(mockValidator.validateMessage).toHaveBeenCalledWith(confirmMessage, 'confirm');
      expect(mockMessageHandler.handleConfirm).toHaveBeenCalledWith(
        confirmMessage,
        expect.objectContaining({ id: 'test-tenant' })
      );
      expect(response.body).toEqual(mockConfirmResponse);
    });
  });

  describe('POST /ondc/status', () => {
    const statusMessage = createMockONDCMessage('status', {
      order_id: 'order-123',
    });

    it('should handle valid status request successfully', async () => {
      // Arrange
      const mockStatusResponse = {
        context: { ...statusMessage.context, action: 'on_status' },
        message: {
          order: {
            id: 'order-123',
            state: 'In-progress',
            provider: { id: 'test-provider' },
            items: [{ id: 'item-1', quantity: { count: 1 } }],
            fulfillments: [
              {
                id: 'fulfillment-1',
                state: 'Pending',
              },
            ],
          },
        },
      };

      mockMessageHandler.handleStatus.mockResolvedValue(mockStatusResponse);

      // Act
      const response = await request(app)
        .post('/ondc/status')
        .send(statusMessage)
        .expect(200);

      // Assert
      expect(mockMessageHandler.handleStatus).toHaveBeenCalledWith(
        statusMessage,
        expect.objectContaining({ id: 'test-tenant' })
      );
      expect(response.body).toEqual(mockStatusResponse);
    });

    it('should handle status processing errors', async () => {
      // Arrange
      mockMessageHandler.handleStatus.mockRejectedValue(new Error('Order not found'));

      // Act
      const response = await request(app)
        .post('/ondc/status')
        .send(statusMessage)
        .expect(500);

      // Assert
      expect(response.body.message.ack.status).toBe('NACK');
      expect(response.body.error.type).toBe('CORE-ERROR');
    });
  });
});
