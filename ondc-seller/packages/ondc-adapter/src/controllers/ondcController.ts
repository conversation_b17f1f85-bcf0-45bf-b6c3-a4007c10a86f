/**
 * ONDC Protocol Controller
 *
 * Handles ONDC protocol-specific endpoints for network communication.
 * Implements the core ONDC message handling including search, select,
 * init, confirm, status, track, cancel, update, and rating flows.
 *
 * <AUTHOR> Seller Team
 */

import { Router, Request, Response } from 'express';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { requireTenantFeature } from '../middleware/tenantMiddleware';
import { ondcRateLimiter } from '../middleware/rateLimiter';
import { ONDCMessageHandler } from '../services/ondcMessageHandler';
import { ONDCValidator } from '../services/ondcValidator';
import { logger, ondcLogger } from '../config/logger';

/**
 * ONDC Context interface
 * Standard ONDC message context structure
 */
interface ONDCContext {
  domain: string;
  country: string;
  city: string;
  action: string;
  core_version: string;
  bap_id: string;
  bap_uri: string;
  bpp_id: string;
  bpp_uri: string;
  transaction_id: string;
  message_id: string;
  timestamp: string;
  key?: string;
  ttl?: string;
}

/**
 * ONDC Message interface
 * Standard ONDC message structure
 */
interface ONDCMessage {
  context: ONDCContext;
  message: any;
  error?: {
    type: string;
    code: string;
    path?: string;
    message: string;
  };
}

/**
 * Initialize ONDC services
 */
const messageHandler = new ONDCMessageHandler();
const validator = new ONDCValidator();

/**
 * Search endpoint - Handle search requests from BAPs
 *
 * POST /ondc/search
 * Processes search requests and returns catalog information
 */
const handleSearch = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('search', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  // Validate ONDC message structure
  const validationResult = await validator.validateMessage(ondcMessage, 'search');
  if (!validationResult.isValid) {
    ondcLogger.error('search', new Error('Invalid message structure'), {
      validationErrors: validationResult.errors,
      tenantId: req.tenant?.id,
    });

    res.status(400).json({
      message: {
        ack: {
          status: 'NACK',
        },
      },
      error: {
        type: 'DOMAIN-ERROR',
        code: '20001',
        message: 'Invalid message structure',
      },
    });
    return;
  }

  try {
    // Process search request
    const searchResponse = await messageHandler.handleSearch(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_search', searchResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(searchResponse);
  } catch (error: any) {
    ondcLogger.error('search', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: {
        ack: {
          status: 'NACK',
        },
      },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Select endpoint - Handle select requests from BAPs
 *
 * POST /ondc/select
 * Processes item selection and returns pricing/availability
 */
const handleSelect = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('select', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  // Validate message
  const validationResult = await validator.validateMessage(ondcMessage, 'select');
  if (!validationResult.isValid) {
    res.status(400).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'DOMAIN-ERROR',
        code: '20001',
        message: 'Invalid message structure',
      },
    });
    return;
  }

  try {
    const selectResponse = await messageHandler.handleSelect(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_select', selectResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(selectResponse);
  } catch (error: any) {
    ondcLogger.error('select', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Init endpoint - Handle order initialization from BAPs
 *
 * POST /ondc/init
 * Processes order initialization and returns order draft
 */
const handleInit = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('init', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  const validationResult = await validator.validateMessage(ondcMessage, 'init');
  if (!validationResult.isValid) {
    res.status(400).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'DOMAIN-ERROR',
        code: '20001',
        message: 'Invalid message structure',
      },
    });
    return;
  }

  try {
    const initResponse = await messageHandler.handleInit(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_init', initResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(initResponse);
  } catch (error: any) {
    ondcLogger.error('init', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Confirm endpoint - Handle order confirmation from BAPs
 *
 * POST /ondc/confirm
 * Processes order confirmation and creates the order
 */
const handleConfirm = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('confirm', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  const validationResult = await validator.validateMessage(ondcMessage, 'confirm');
  if (!validationResult.isValid) {
    res.status(400).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'DOMAIN-ERROR',
        code: '20001',
        message: 'Invalid message structure',
      },
    });
    return;
  }

  try {
    const confirmResponse = await messageHandler.handleConfirm(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_confirm', confirmResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(confirmResponse);
  } catch (error: any) {
    ondcLogger.error('confirm', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Status endpoint - Handle order status requests from BAPs
 *
 * POST /ondc/status
 * Returns current order status and tracking information
 */
const handleStatus = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('status', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  try {
    const statusResponse = await messageHandler.handleStatus(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_status', statusResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(statusResponse);
  } catch (error: any) {
    ondcLogger.error('status', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Track endpoint - Handle tracking requests from BAPs
 *
 * POST /ondc/track
 * Returns tracking information for orders
 */
const handleTrack = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('track', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  try {
    const trackResponse = await messageHandler.handleTrack(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_track', trackResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(trackResponse);
  } catch (error: any) {
    ondcLogger.error('track', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Cancel endpoint - Handle cancellation requests from BAPs
 *
 * POST /ondc/cancel
 * Processes order cancellation requests
 */
const handleCancel = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('cancel', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  try {
    const cancelResponse = await messageHandler.handleCancel(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_cancel', cancelResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(cancelResponse);
  } catch (error: any) {
    ondcLogger.error('cancel', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Update endpoint - Handle order update requests from BAPs
 *
 * POST /ondc/update
 * Processes order modification requests
 */
const handleUpdate = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('update', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  try {
    const updateResponse = await messageHandler.handleUpdate(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_update', updateResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(updateResponse);
  } catch (error: any) {
    ondcLogger.error('update', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * Rating endpoint - Handle rating requests from BAPs
 *
 * POST /ondc/rating
 * Processes rating and feedback requests
 */
const handleRating = asyncHandler(async (req: Request, res: Response): Promise<void> => {
  const ondcMessage: ONDCMessage = req.body;

  ondcLogger.incoming('rating', ondcMessage, {
    tenantId: req.tenant?.id,
    userId: req.user?.id,
  });

  try {
    const ratingResponse = await messageHandler.handleRating(ondcMessage, req.tenant!);

    ondcLogger.outgoing('on_rating', ratingResponse, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.json(ratingResponse);
  } catch (error: any) {
    ondcLogger.error('rating', error, {
      tenantId: req.tenant?.id,
      transactionId: ondcMessage.context.transaction_id,
    });

    res.status(500).json({
      message: { ack: { status: 'NACK' } },
      error: {
        type: 'CORE-ERROR',
        code: '10001',
        message: 'Internal server error',
      },
    });
  }
});

/**
 * ONDC routes configuration
 */
export const ondcRoutes = Router();

// Apply ONDC-specific rate limiting
ondcRoutes.use(ondcRateLimiter);

// Require catalog feature for all ONDC endpoints
ondcRoutes.use(requireTenantFeature('catalog'));

// ONDC protocol endpoints
ondcRoutes.post('/search', handleSearch);
ondcRoutes.post('/select', handleSelect);
ondcRoutes.post('/init', handleInit);
ondcRoutes.post('/confirm', handleConfirm);
ondcRoutes.post('/status', handleStatus);

// Additional ONDC endpoints (Phase 3 implementation)
ondcRoutes.post('/track', handleTrack);
ondcRoutes.post('/cancel', handleCancel);
ondcRoutes.post('/update', handleUpdate);
ondcRoutes.post('/rating', handleRating);

export default ondcRoutes;
