/**
 * Medusa Service Tests
 * 
 * Unit tests for Medusa Commerce integration including
 * product search, order management, and API communication.
 */

import axios from 'axios';
import { MedusaService } from '../medusaService';
import { createMockTenant, createMockProduct } from '../../../tests/setup';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock logger
jest.mock('../../config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  },
}));

// Mock config
jest.mock('../../config/environment', () => ({
  config: {
    medusa: {
      backendUrl: 'http://localhost:9001',
      apiKey: 'test-api-key',
    },
    isDevelopment: true,
  },
}));

describe('MedusaService', () => {
  let medusaService: MedusaService;
  let mockTenant: any;
  let mockAxiosInstance: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock axios.create
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      interceptors: {
        request: {
          use: jest.fn(),
        },
        response: {
          use: jest.fn(),
        },
      },
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    medusaService = new MedusaService();
    mockTenant = createMockTenant();
  });

  describe('constructor', () => {
    it('should create axios instance with correct configuration', () => {
      // Assert
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:9001',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-api-key',
        },
        timeout: 10000,
      });
    });

    it('should setup request and response interceptors', () => {
      // Assert
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });
  });

  describe('searchProducts', () => {
    it('should return mock products in development mode', async () => {
      // Arrange
      const searchCriteria = {
        query: 'laptop',
        category: 'Electronics',
        location: null,
        provider: null,
      };

      // Act
      const result = await medusaService.searchProducts(searchCriteria, mockTenant);

      // Assert
      expect(result).toHaveLength(2);
      expect(result[0]).toMatchObject({
        id: 'prod_01',
        title: 'Sample Product 1',
        handle: 'sample-product-1',
      });
      expect(result[1]).toMatchObject({
        id: 'prod_02',
        title: 'Sample Product 2',
        handle: 'sample-product-2',
      });
    });

    it('should make API call with correct parameters in production mode', async () => {
      // Arrange
      const searchCriteria = {
        query: 'laptop',
        category: 'Electronics',
        location: null,
        provider: null,
      };

      const mockProducts = [createMockProduct()];
      mockAxiosInstance.get.mockResolvedValue({
        data: { products: mockProducts },
      });

      // Temporarily override isDevelopment
      const originalConfig = require('../../config/environment').config;
      originalConfig.isDevelopment = false;

      // Act
      const result = await medusaService.searchProducts(searchCriteria, mockTenant);

      // Assert
      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/store/products', {
        params: {
          limit: 50,
          offset: 0,
          q: 'laptop',
          category_id: 'Electronics',
        },
        headers: {
          'x-tenant-id': mockTenant.id,
        },
      });
      expect(result).toEqual(mockProducts);

      // Restore original config
      originalConfig.isDevelopment = true;
    });

    it('should handle API errors gracefully', async () => {
      // Arrange
      const searchCriteria = {
        query: 'laptop',
        category: 'Electronics',
        location: null,
        provider: null,
      };

      mockAxiosInstance.get.mockRejectedValue(new Error('API Error'));

      // Temporarily override isDevelopment
      const originalConfig = require('../../config/environment').config;
      originalConfig.isDevelopment = false;

      // Act
      const result = await medusaService.searchProducts(searchCriteria, mockTenant);

      // Assert
      expect(result).toEqual([]);

      // Restore original config
      originalConfig.isDevelopment = true;
    });
  });

  describe('calculateQuote', () => {
    it('should return mock quote in development mode', async () => {
      // Arrange
      const items = [
        {
          id: 'prod_01',
          quantity: 2,
          price: { value: 100 },
        },
        {
          id: 'prod_02',
          quantity: 1,
          price: { value: 150 },
        },
      ];

      // Act
      const result = await medusaService.calculateQuote(items, mockTenant);

      // Assert
      expect(result).toMatchObject({
        items,
        total: { currency: 'INR', value: '350' },
        breakup: [
          {
            title: 'Item Total',
            price: { currency: 'INR', value: '350' },
          },
          {
            title: 'Delivery Charges',
            price: { currency: 'INR', value: '50' },
          },
        ],
      });
    });

    it('should create cart and calculate quote in production mode', async () => {
      // Arrange
      const items = [
        {
          id: 'variant_01',
          quantity: 2,
        },
      ];

      const mockCart = {
        id: 'cart_123',
        total: 200,
        subtotal: 180,
        tax_total: 20,
        shipping_total: 0,
        items: items,
      };

      mockAxiosInstance.post
        .mockResolvedValueOnce({
          data: { cart: { id: 'cart_123' } },
        })
        .mockResolvedValue({
          data: { cart: mockCart },
        });

      mockAxiosInstance.get.mockResolvedValue({
        data: { cart: mockCart },
      });

      // Temporarily override isDevelopment
      const originalConfig = require('../../config/environment').config;
      originalConfig.isDevelopment = false;

      // Act
      const result = await medusaService.calculateQuote(items, mockTenant);

      // Assert
      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/store/carts', {
        region_id: 'default-region',
      }, {
        headers: {
          'x-tenant-id': mockTenant.id,
        },
      });

      expect(result).toMatchObject({
        items: mockCart.items,
        total: { currency: 'INR', value: '200' },
        breakup: [
          {
            title: 'Item Total',
            price: { currency: 'INR', value: '180' },
          },
          {
            title: 'Tax',
            price: { currency: 'INR', value: '20' },
          },
          {
            title: 'Shipping',
            price: { currency: 'INR', value: '0' },
          },
        ],
      });

      // Restore original config
      originalConfig.isDevelopment = true;
    });

    it('should handle quote calculation errors', async () => {
      // Arrange
      const items = [{ id: 'variant_01', quantity: 1 }];

      mockAxiosInstance.post.mockRejectedValue(new Error('Cart creation failed'));

      // Temporarily override isDevelopment
      const originalConfig = require('../../config/environment').config;
      originalConfig.isDevelopment = false;

      // Act
      const result = await medusaService.calculateQuote(items, mockTenant);

      // Assert
      expect(result).toMatchObject({
        items,
        total: { currency: 'INR', value: '100' },
      });

      // Restore original config
      originalConfig.isDevelopment = true;
    });
  });

  describe('createOrderDraft', () => {
    it('should return mock order draft in development mode', async () => {
      // Arrange
      const orderInfo = {
        items: [{ id: 'prod_01', quantity: 1 }],
        billing: { name: 'Test Customer' },
        fulfillments: [{ id: 'fulfillment_01' }],
      };

      // Act
      const result = await medusaService.createOrderDraft(orderInfo, mockTenant);

      // Assert
      expect(result).toMatchObject({
        id: expect.stringContaining('draft_'),
        state: 'Draft',
        items: orderInfo.items,
        billing: orderInfo.billing,
        fulfillments: orderInfo.fulfillments,
        quote: { currency: 'INR', value: '1000' },
      });
    });
  });

  describe('createOrder', () => {
    it('should return mock order in development mode', async () => {
      // Arrange
      const orderInfo = {
        id: 'order_123',
        items: [{ id: 'prod_01', quantity: 1 }],
        billing: { name: 'Test Customer' },
        fulfillments: [{ id: 'fulfillment_01' }],
        payment: { status: 'PAID' },
      };

      // Act
      const result = await medusaService.createOrder(orderInfo, mockTenant);

      // Assert
      expect(result).toMatchObject({
        id: 'order_123',
        status: 'pending',
        fulfillment_status: 'not_fulfilled',
        payment_status: 'awaiting',
        items: orderInfo.items,
        billing_address: orderInfo.billing,
        total: 1000,
      });
    });
  });

  describe('getOrderStatus', () => {
    it('should return mock order status in development mode', async () => {
      // Arrange
      const orderId = 'order_123';

      // Act
      const result = await medusaService.getOrderStatus(orderId, mockTenant);

      // Assert
      expect(result).toMatchObject({
        id: orderId,
        state: 'Accepted',
        fulfillments: [
          {
            id: 'fulfillment_01',
            state: 'Pending',
            tracking: false,
          },
        ],
        payment: {
          status: 'PAID',
          type: 'ON-ORDER',
        },
      });
    });

    it('should make API call in production mode', async () => {
      // Arrange
      const orderId = 'order_123';
      const mockOrder = {
        id: orderId,
        status: 'completed',
        fulfillment_status: 'fulfilled',
      };

      mockAxiosInstance.get.mockResolvedValue({
        data: { order: mockOrder },
      });

      // Temporarily override isDevelopment
      const originalConfig = require('../../config/environment').config;
      originalConfig.isDevelopment = false;

      // Act
      const result = await medusaService.getOrderStatus(orderId, mockTenant);

      // Assert
      expect(mockAxiosInstance.get).toHaveBeenCalledWith(`/store/orders/${orderId}`, {
        headers: {
          'x-tenant-id': mockTenant.id,
        },
      });
      expect(result).toEqual(mockOrder);

      // Restore original config
      originalConfig.isDevelopment = true;
    });

    it('should handle order status errors', async () => {
      // Arrange
      const orderId = 'order_123';

      mockAxiosInstance.get.mockRejectedValue(new Error('Order not found'));

      // Temporarily override isDevelopment
      const originalConfig = require('../../config/environment').config;
      originalConfig.isDevelopment = false;

      // Act
      const result = await medusaService.getOrderStatus(orderId, mockTenant);

      // Assert
      expect(result).toMatchObject({
        id: orderId,
        state: 'Accepted',
      });

      // Restore original config
      originalConfig.isDevelopment = true;
    });
  });

  describe('private helper methods', () => {
    it('should transform cart to quote correctly', () => {
      // This tests the private transformCartToQuote method indirectly
      // through the calculateQuote method in production mode
      const cart = {
        items: [{ id: 'item_01' }],
        total: 100,
        subtotal: 90,
        tax_total: 10,
        shipping_total: 0,
      };

      // The method is private, so we test it through public interface
      // This is covered in the calculateQuote tests above
      expect(true).toBe(true); // Placeholder assertion
    });
  });
});
