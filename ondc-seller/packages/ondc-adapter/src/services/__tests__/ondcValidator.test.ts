/**
 * ONDC Validator Service Tests
 * 
 * Unit tests for ONDC message validation including schema validation,
 * business logic validation, and error handling.
 */

import { ONDCValidator } from '../ondcValidator';
import { createMockONDCMessage, createMockONDCContext } from '../../../tests/setup';

// Mock logger
jest.mock('../../config/logger', () => ({
  logger: {
    debug: jest.fn(),
    error: jest.fn(),
  },
}));

describe('ONDCValidator', () => {
  let validator: ONDCValidator;

  beforeEach(() => {
    validator = new ONDCValidator();
  });

  describe('validateMessage', () => {
    describe('search validation', () => {
      it('should validate valid search message', async () => {
        // Arrange
        const validSearchMessage = createMockONDCMessage('search', {
          intent: {
            item: {
              descriptor: {
                name: 'laptop',
              },
            },
            category: {
              id: 'Electronics',
            },
          },
        });

        // Act
        const result = await validator.validateMessage(validSearchMessage, 'search');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should accept search message with minimal intent', async () => {
        // Arrange
        const minimalSearchMessage = createMockONDCMessage('search', {
          intent: {},
        });

        // Act
        const result = await validator.validateMessage(minimalSearchMessage, 'search');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    describe('select validation', () => {
      it('should validate valid select message', async () => {
        // Arrange
        const validSelectMessage = createMockONDCMessage('select', {
          order: {
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 2,
                },
              },
            ],
          },
        });

        // Act
        const result = await validator.validateMessage(validSelectMessage, 'select');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should reject select message without provider', async () => {
        // Arrange
        const invalidSelectMessage = createMockONDCMessage('select', {
          order: {
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 2,
                },
              },
            ],
          },
        });

        // Act
        const result = await validator.validateMessage(invalidSelectMessage, 'select');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('provider'));
      });

      it('should reject select message without items', async () => {
        // Arrange
        const invalidSelectMessage = createMockONDCMessage('select', {
          order: {
            provider: {
              id: 'test-provider',
            },
            items: [],
          },
        });

        // Act
        const result = await validator.validateMessage(invalidSelectMessage, 'select');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('items'));
      });

      it('should reject select message with invalid quantity', async () => {
        // Arrange
        const invalidSelectMessage = createMockONDCMessage('select', {
          order: {
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 0, // Invalid: must be at least 1
                },
              },
            ],
          },
        });

        // Act
        const result = await validator.validateMessage(invalidSelectMessage, 'select');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('count'));
      });
    });

    describe('init validation', () => {
      it('should validate valid init message', async () => {
        // Arrange
        const validInitMessage = createMockONDCMessage('init', {
          order: {
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 1,
                },
              },
            ],
            billing: {
              name: 'Test Customer',
              address: {
                locality: 'Test Area',
                city: 'Test City',
                state: 'Test State',
                country: 'IND',
                area_code: '560001',
              },
              phone: '+919999999999',
            },
            fulfillments: [
              {
                end: {
                  location: {
                    address: {
                      locality: 'Test Area',
                      city: 'Test City',
                      state: 'Test State',
                      country: 'IND',
                      area_code: '560001',
                    },
                  },
                  contact: {
                    phone: '+919999999999',
                  },
                },
              },
            ],
          },
        });

        // Act
        const result = await validator.validateMessage(validInitMessage, 'init');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should reject init message without billing information', async () => {
        // Arrange
        const invalidInitMessage = createMockONDCMessage('init', {
          order: {
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 1,
                },
              },
            ],
            fulfillments: [
              {
                end: {
                  location: {
                    address: {
                      locality: 'Test Area',
                      city: 'Test City',
                      state: 'Test State',
                      country: 'IND',
                      area_code: '560001',
                    },
                  },
                },
              },
            ],
          },
        });

        // Act
        const result = await validator.validateMessage(invalidInitMessage, 'init');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('billing'));
      });

      it('should reject init message without fulfillments', async () => {
        // Arrange
        const invalidInitMessage = createMockONDCMessage('init', {
          order: {
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 1,
                },
              },
            ],
            billing: {
              name: 'Test Customer',
              address: {
                locality: 'Test Area',
                city: 'Test City',
                state: 'Test State',
                country: 'IND',
                area_code: '560001',
              },
              phone: '+919999999999',
            },
            fulfillments: [],
          },
        });

        // Act
        const result = await validator.validateMessage(invalidInitMessage, 'init');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('fulfillments'));
      });
    });

    describe('confirm validation', () => {
      it('should validate valid confirm message', async () => {
        // Arrange
        const validConfirmMessage = createMockONDCMessage('confirm', {
          order: {
            id: 'order-123',
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 1,
                },
              },
            ],
            billing: {
              name: 'Test Customer',
              address: {
                locality: 'Test Area',
                city: 'Test City',
                state: 'Test State',
                country: 'IND',
                area_code: '560001',
              },
              phone: '+919999999999',
            },
            fulfillments: [
              {
                end: {
                  location: {
                    address: {
                      locality: 'Test Area',
                      city: 'Test City',
                      state: 'Test State',
                      country: 'IND',
                      area_code: '560001',
                    },
                  },
                  contact: {
                    phone: '+919999999999',
                  },
                },
              },
            ],
            payment: {
              params: {
                amount: '100',
                currency: 'INR',
              },
              status: 'PAID',
              type: 'ON-ORDER',
              collected_by: 'BAP',
            },
          },
        });

        // Act
        const result = await validator.validateMessage(validConfirmMessage, 'confirm');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should reject confirm message without order ID', async () => {
        // Arrange
        const invalidConfirmMessage = createMockONDCMessage('confirm', {
          order: {
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 1,
                },
              },
            ],
            billing: {
              name: 'Test Customer',
              address: {
                locality: 'Test Area',
                city: 'Test City',
                state: 'Test State',
                country: 'IND',
                area_code: '560001',
              },
              phone: '+919999999999',
            },
            fulfillments: [
              {
                end: {
                  location: {
                    address: {
                      locality: 'Test Area',
                      city: 'Test City',
                      state: 'Test State',
                      country: 'IND',
                      area_code: '560001',
                    },
                  },
                },
              },
            ],
            payment: {
              params: {
                amount: '100',
                currency: 'INR',
              },
              status: 'PAID',
              type: 'ON-ORDER',
              collected_by: 'BAP',
            },
          },
        });

        // Act
        const result = await validator.validateMessage(invalidConfirmMessage, 'confirm');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('id'));
      });

      it('should reject confirm message without payment information', async () => {
        // Arrange
        const invalidConfirmMessage = createMockONDCMessage('confirm', {
          order: {
            id: 'order-123',
            provider: {
              id: 'test-provider',
            },
            items: [
              {
                id: 'item-1',
                quantity: {
                  count: 1,
                },
              },
            ],
            billing: {
              name: 'Test Customer',
              address: {
                locality: 'Test Area',
                city: 'Test City',
                state: 'Test State',
                country: 'IND',
                area_code: '560001',
              },
              phone: '+919999999999',
            },
            fulfillments: [
              {
                end: {
                  location: {
                    address: {
                      locality: 'Test Area',
                      city: 'Test City',
                      state: 'Test State',
                      country: 'IND',
                      area_code: '560001',
                    },
                  },
                },
              },
            ],
          },
        });

        // Act
        const result = await validator.validateMessage(invalidConfirmMessage, 'confirm');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('payment'));
      });
    });

    describe('status validation', () => {
      it('should validate valid status message', async () => {
        // Arrange
        const validStatusMessage = createMockONDCMessage('status', {
          order_id: 'order-123',
        });

        // Act
        const result = await validator.validateMessage(validStatusMessage, 'status');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should reject status message without order_id', async () => {
        // Arrange
        const invalidStatusMessage = createMockONDCMessage('status', {});

        // Act
        const result = await validator.validateMessage(invalidStatusMessage, 'status');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('order_id'));
      });
    });

    describe('context validation', () => {
      it('should validate valid ONDC context', async () => {
        // Arrange
        const validMessage = createMockONDCMessage('search', {});

        // Act
        const result = await validator.validateMessage(validMessage, 'search');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });

      it('should reject message with invalid transaction_id format', async () => {
        // Arrange
        const invalidMessage = createMockONDCMessage('search', {}, {
          transaction_id: 'invalid-uuid',
        });

        // Act
        const result = await validator.validateMessage(invalidMessage, 'search');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('transaction_id'));
      });

      it('should reject message with invalid message_id format', async () => {
        // Arrange
        const invalidMessage = createMockONDCMessage('search', {}, {
          message_id: 'invalid-uuid',
        });

        // Act
        const result = await validator.validateMessage(invalidMessage, 'search');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('message_id'));
      });

      it('should reject message with action mismatch', async () => {
        // Arrange
        const invalidMessage = createMockONDCMessage('search', {}, {
          action: 'select', // Mismatch with expected 'search'
        });

        // Act
        const result = await validator.validateMessage(invalidMessage, 'search');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors).toContain(expect.stringContaining('Action mismatch'));
      });

      it('should warn about old timestamp', async () => {
        // Arrange
        const oldTimestamp = new Date(Date.now() - 10 * 60 * 1000).toISOString(); // 10 minutes ago
        const messageWithOldTimestamp = createMockONDCMessage('search', {}, {
          timestamp: oldTimestamp,
        });

        // Act
        const result = await validator.validateMessage(messageWithOldTimestamp, 'search');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.warnings).toContain(expect.stringContaining('timestamp is more than 5 minutes old'));
      });

      it('should warn about unsupported core version', async () => {
        // Arrange
        const messageWithUnsupportedVersion = createMockONDCMessage('search', {}, {
          core_version: '2.0.0', // Unsupported version
        });

        // Act
        const result = await validator.validateMessage(messageWithUnsupportedVersion, 'search');

        // Assert
        expect(result.isValid).toBe(true);
        expect(result.warnings).toContain(expect.stringContaining('Unsupported core version'));
      });
    });

    describe('error handling', () => {
      it('should handle validation errors gracefully', async () => {
        // Arrange
        const invalidMessage = {
          context: null, // Invalid context
          message: {},
        };

        // Act
        const result = await validator.validateMessage(invalidMessage, 'search');

        // Assert
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });

      it('should handle unknown action types', async () => {
        // Arrange
        const messageWithUnknownAction = createMockONDCMessage('unknown_action', {});

        // Act
        const result = await validator.validateMessage(messageWithUnknownAction, 'unknown_action');

        // Assert
        expect(result.isValid).toBe(true); // Should pass context validation
        expect(result.warnings).toContain(expect.stringContaining('No validation schema found'));
      });
    });
  });
});
