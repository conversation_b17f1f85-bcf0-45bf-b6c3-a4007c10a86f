/**
 * ONDC Message Handler Service Tests
 * 
 * Unit tests for ONDC message processing, transformation,
 * and Medusa integration logic.
 */

import { ONDCMessageHandler } from '../ondcMessageHandler';
import { MedusaService } from '../medusaService';
import {
  createMockONDCMessage,
  createMockTenant,
  createMockProduct,
} from '../../../tests/setup';

// Mock dependencies
jest.mock('../medusaService');
jest.mock('../../config/logger', () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
  },
  ondcLogger: {
    incoming: jest.fn(),
    outgoing: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    debug: jest.fn(),
  },
}));

const MockedMedusaService = MedusaService as jest.MockedClass<typeof MedusaService>;

describe('ONDCMessageHandler', () => {
  let messageHandler: ONDCMessageHandler;
  let mockMedusaService: jest.Mocked<MedusaService>;
  let mockTenant: any;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock Medusa service
    mockMedusaService = {
      searchProducts: jest.fn(),
      calculateQuote: jest.fn(),
      createOrderDraft: jest.fn(),
      createOrder: jest.fn(),
      getOrderStatus: jest.fn(),
    } as any;

    MockedMedusaService.mockImplementation(() => mockMedusaService);

    // Create handler instance
    messageHandler = new ONDCMessageHandler();
    mockTenant = createMockTenant();
  });

  describe('handleSearch', () => {
    it('should process search request and return ONDC catalog', async () => {
      // Arrange
      const searchMessage = createMockONDCMessage('search', {
        intent: {
          item: {
            descriptor: {
              name: 'laptop',
            },
          },
          category: {
            id: 'Electronics',
          },
        },
      });

      const mockProducts = [
        createMockProduct({
          id: 'prod_01',
          title: 'Test Laptop',
          price: 50000,
        }),
        createMockProduct({
          id: 'prod_02',
          title: 'Another Laptop',
          price: 75000,
        }),
      ];

      mockMedusaService.searchProducts.mockResolvedValue(mockProducts);

      // Act
      const result = await messageHandler.handleSearch(searchMessage, mockTenant);

      // Assert
      expect(mockMedusaService.searchProducts).toHaveBeenCalledWith(
        {
          query: 'laptop',
          category: 'Electronics',
          location: null,
          provider: null,
        },
        mockTenant
      );

      expect(result).toMatchObject({
        context: {
          action: 'on_search',
          transaction_id: searchMessage.context.transaction_id,
        },
        message: {
          catalog: {
            'bpp/descriptor': {
              name: mockTenant.name,
            },
            'bpp/providers': expect.arrayContaining([
              expect.objectContaining({
                id: mockTenant.settings.ondcConfig.subscriberId,
                items: expect.arrayContaining([
                  expect.objectContaining({
                    id: 'prod_01',
                    descriptor: {
                      name: 'Test Laptop',
                    },
                  }),
                ]),
              }),
            ]),
          },
        },
      });
    });

    it('should handle search with empty results', async () => {
      // Arrange
      const searchMessage = createMockONDCMessage('search', {
        intent: {
          item: {
            descriptor: {
              name: 'nonexistent',
            },
          },
        },
      });

      mockMedusaService.searchProducts.mockResolvedValue([]);

      // Act
      const result = await messageHandler.handleSearch(searchMessage, mockTenant);

      // Assert
      expect(result.message.catalog['bpp/providers'][0].items).toEqual([]);
    });

    it('should handle search errors gracefully', async () => {
      // Arrange
      const searchMessage = createMockONDCMessage('search', {
        intent: {
          item: {
            descriptor: {
              name: 'laptop',
            },
          },
        },
      });

      mockMedusaService.searchProducts.mockRejectedValue(new Error('Database error'));

      // Act & Assert
      await expect(messageHandler.handleSearch(searchMessage, mockTenant)).rejects.toThrow('Database error');
    });
  });

  describe('handleSelect', () => {
    it('should process select request and return quote', async () => {
      // Arrange
      const selectMessage = createMockONDCMessage('select', {
        order: {
          provider: {
            id: 'test-provider',
          },
          items: [
            {
              id: 'prod_01',
              quantity: {
                count: 2,
              },
            },
          ],
        },
      });

      const mockQuote = {
        items: [
          {
            id: 'prod_01',
            quantity: { count: 2 },
            price: { currency: 'INR', value: '100' },
          },
        ],
        total: { currency: 'INR', value: '200' },
        breakup: [
          {
            title: 'Item Total',
            price: { currency: 'INR', value: '200' },
          },
        ],
      };

      mockMedusaService.calculateQuote.mockResolvedValue(mockQuote);

      // Act
      const result = await messageHandler.handleSelect(selectMessage, mockTenant);

      // Assert
      expect(mockMedusaService.calculateQuote).toHaveBeenCalledWith(
        selectMessage.message.order.items,
        mockTenant
      );

      expect(result).toMatchObject({
        context: {
          action: 'on_select',
          transaction_id: selectMessage.context.transaction_id,
        },
        message: {
          order: {
            provider: expect.objectContaining({
              id: mockTenant.settings.ondcConfig.subscriberId,
            }),
            items: mockQuote.items,
            quote: mockQuote.quote,
          },
        },
      });
    });
  });

  describe('handleInit', () => {
    it('should process init request and return order draft', async () => {
      // Arrange
      const initMessage = createMockONDCMessage('init', {
        order: {
          provider: { id: 'test-provider' },
          items: [{ id: 'prod_01', quantity: { count: 1 } }],
          billing: {
            name: 'Test Customer',
            address: {
              locality: 'Test Area',
              city: 'Test City',
              state: 'Test State',
              country: 'IND',
              area_code: '560001',
            },
            phone: '+919999999999',
          },
          fulfillments: [
            {
              end: {
                location: {
                  address: {
                    locality: 'Test Area',
                    city: 'Test City',
                    state: 'Test State',
                    country: 'IND',
                    area_code: '560001',
                  },
                },
                contact: {
                  phone: '+919999999999',
                },
              },
            },
          ],
        },
      });

      const mockOrderDraft = {
        id: 'draft_123',
        state: 'Draft',
        items: initMessage.message.order.items,
        billing: initMessage.message.order.billing,
        fulfillments: initMessage.message.order.fulfillments,
        quote: { currency: 'INR', value: '100' },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockMedusaService.createOrderDraft.mockResolvedValue(mockOrderDraft);

      // Act
      const result = await messageHandler.handleInit(initMessage, mockTenant);

      // Assert
      expect(mockMedusaService.createOrderDraft).toHaveBeenCalledWith(
        {
          items: initMessage.message.order.items,
          billing: initMessage.message.order.billing,
          fulfillments: initMessage.message.order.fulfillments,
        },
        mockTenant
      );

      expect(result).toMatchObject({
        context: {
          action: 'on_init',
          transaction_id: initMessage.context.transaction_id,
        },
        message: {
          order: expect.objectContaining({
            id: 'draft_123',
            state: 'Draft',
          }),
        },
      });
    });
  });

  describe('handleConfirm', () => {
    it('should process confirm request and create order', async () => {
      // Arrange
      const confirmMessage = createMockONDCMessage('confirm', {
        order: {
          id: 'order_123',
          provider: { id: 'test-provider' },
          items: [{ id: 'prod_01', quantity: { count: 1 } }],
          billing: {
            name: 'Test Customer',
            address: {
              locality: 'Test Area',
              city: 'Test City',
              state: 'Test State',
              country: 'IND',
              area_code: '560001',
            },
            phone: '+919999999999',
          },
          fulfillments: [
            {
              end: {
                location: {
                  address: {
                    locality: 'Test Area',
                    city: 'Test City',
                    state: 'Test State',
                    country: 'IND',
                    area_code: '560001',
                  },
                },
                contact: {
                  phone: '+919999999999',
                },
              },
            },
          ],
          payment: {
            params: {
              amount: '100',
              currency: 'INR',
            },
            status: 'PAID',
            type: 'ON-ORDER',
            collected_by: 'BAP',
          },
        },
      });

      const mockCreatedOrder = {
        id: 'order_123',
        status: 'pending',
        fulfillment_status: 'not_fulfilled',
        payment_status: 'awaiting',
        display_id: 1001,
        cart_id: 'cart_123',
        customer_id: 'customer_01',
        email: '<EMAIL>',
        billing_address: confirmMessage.message.order.billing,
        shipping_address: confirmMessage.message.order.billing,
        items: confirmMessage.message.order.items,
        total: 100,
        subtotal: 95,
        tax_total: 5,
        shipping_total: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockMedusaService.createOrder.mockResolvedValue(mockCreatedOrder);

      // Act
      const result = await messageHandler.handleConfirm(confirmMessage, mockTenant);

      // Assert
      expect(mockMedusaService.createOrder).toHaveBeenCalledWith(
        {
          id: 'order_123',
          items: confirmMessage.message.order.items,
          billing: confirmMessage.message.order.billing,
          fulfillments: confirmMessage.message.order.fulfillments,
          payment: confirmMessage.message.order.payment,
        },
        mockTenant
      );

      expect(result).toMatchObject({
        context: {
          action: 'on_confirm',
          transaction_id: confirmMessage.context.transaction_id,
        },
        message: {
          order: expect.objectContaining({
            id: 'order_123',
            state: 'Created',
          }),
        },
      });
    });
  });

  describe('handleStatus', () => {
    it('should process status request and return order status', async () => {
      // Arrange
      const statusMessage = createMockONDCMessage('status', {
        order_id: 'order_123',
      });

      const mockOrderStatus = {
        id: 'order_123',
        state: 'Accepted',
        items: [{ id: 'prod_01', quantity: { count: 1 } }],
        fulfillments: [
          {
            id: 'fulfillment_01',
            state: 'Pending',
            tracking: false,
          },
        ],
        quote: { currency: 'INR', value: '100' },
        payment: {
          status: 'PAID',
          type: 'ON-ORDER',
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      mockMedusaService.getOrderStatus.mockResolvedValue(mockOrderStatus);

      // Act
      const result = await messageHandler.handleStatus(statusMessage, mockTenant);

      // Assert
      expect(mockMedusaService.getOrderStatus).toHaveBeenCalledWith(
        'order_123',
        mockTenant
      );

      expect(result).toMatchObject({
        context: {
          action: 'on_status',
          transaction_id: statusMessage.context.transaction_id,
        },
        message: {
          order: expect.objectContaining({
            id: 'order_123',
            state: 'Accepted',
          }),
        },
      });
    });
  });
});
