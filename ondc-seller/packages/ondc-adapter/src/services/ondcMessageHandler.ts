/**
 * ONDC Message Handler Service
 *
 * Core service for processing ONDC protocol messages and coordinating
 * with Medusa Commerce backend. Handles message transformation,
 * business logic, and response generation.
 *
 * <AUTHOR> Seller Team
 */

import { Tenant } from '../middleware/tenantMiddleware';
import { MedusaService } from './medusaService';
import { logger, ondcLogger } from '../config/logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * ONDC Message interfaces
 */
interface ONDCContext {
  domain: string;
  country: string;
  city: string;
  action: string;
  core_version: string;
  bap_id: string;
  bap_uri: string;
  bpp_id: string;
  bpp_uri: string;
  transaction_id: string;
  message_id: string;
  timestamp: string;
  key?: string;
  ttl?: string;
}

interface ONDCMessage {
  context: ONDCContext;
  message: any;
  error?: any;
}

/**
 * ONDC Message Handler Class
 *
 * Processes ONDC protocol messages and coordinates with backend services
 */
export class ONDCMessageHandler {
  private medusaService: MedusaService;

  constructor() {
    this.medusaService = new MedusaService();
  }

  /**
   * Handle search requests
   *
   * Processes search requests from BAPs and returns catalog information
   * from Medusa Commerce backend.
   */
  async handleSearch(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: searchMessage } = message;

    logger.info('Processing ONDC search request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract search criteria
      const searchCriteria = this.extractSearchCriteria(searchMessage);

      // Get products from Medusa
      const products = await this.medusaService.searchProducts(searchCriteria, tenant);

      // Transform to ONDC catalog format
      const ondcCatalog = this.transformToONDCCatalog(products, tenant);

      // Build response context
      const responseContext = this.buildResponseContext(context, 'on_search');

      return {
        context: responseContext,
        message: {
          catalog: ondcCatalog,
        },
      };
    } catch (error: any) {
      logger.error('Error processing search request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Handle select requests
   *
   * Processes item selection and returns pricing and availability information
   */
  async handleSelect(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: selectMessage } = message;

    logger.info('Processing ONDC select request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract selected items
      const selectedItems = this.extractSelectedItems(selectMessage);

      // Get pricing and availability from Medusa
      const quote = await this.medusaService.calculateQuote(selectedItems, tenant);

      // Transform to ONDC quote format
      const ondcQuote = this.transformToONDCQuote(quote, tenant);

      const responseContext = this.buildResponseContext(context, 'on_select');

      return {
        context: responseContext,
        message: {
          order: {
            provider: this.buildProviderInfo(tenant),
            items: ondcQuote.items,
            billing: ondcQuote.billing,
            fulfillments: ondcQuote.fulfillments,
            quote: ondcQuote.quote,
          },
        },
      };
    } catch (error: any) {
      logger.error('Error processing select request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Handle init requests
   *
   * Processes order initialization and returns order draft
   */
  async handleInit(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: initMessage } = message;

    logger.info('Processing ONDC init request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract order information
      const orderInfo = this.extractOrderInfo(initMessage);

      // Create order draft in Medusa
      const orderDraft = await this.medusaService.createOrderDraft(orderInfo, tenant);

      // Transform to ONDC order format
      const ondcOrder = this.transformToONDCOrder(orderDraft, tenant);

      const responseContext = this.buildResponseContext(context, 'on_init');

      return {
        context: responseContext,
        message: {
          order: ondcOrder,
        },
      };
    } catch (error: any) {
      logger.error('Error processing init request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Handle confirm requests
   *
   * Processes order confirmation and creates the final order
   */
  async handleConfirm(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: confirmMessage } = message;

    logger.info('Processing ONDC confirm request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract confirmed order
      const confirmedOrder = this.extractConfirmedOrder(confirmMessage);

      // Create order in Medusa
      const createdOrder = await this.medusaService.createOrder(confirmedOrder, tenant);

      // Transform to ONDC order format
      const ondcOrder = this.transformToONDCOrder(createdOrder, tenant);

      const responseContext = this.buildResponseContext(context, 'on_confirm');

      return {
        context: responseContext,
        message: {
          order: ondcOrder,
        },
      };
    } catch (error: any) {
      logger.error('Error processing confirm request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Handle status requests
   *
   * Returns current order status and tracking information
   */
  async handleStatus(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: statusMessage } = message;

    logger.info('Processing ONDC status request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract order ID
      const orderId = statusMessage.order_id;

      // Get order status from Medusa
      const orderStatus = await this.medusaService.getOrderStatus(orderId, tenant);

      // Transform to ONDC status format
      const ondcStatus = this.transformToONDCStatus(orderStatus, tenant);

      const responseContext = this.buildResponseContext(context, 'on_status');

      return {
        context: responseContext,
        message: {
          order: ondcStatus,
        },
      };
    } catch (error: any) {
      logger.error('Error processing status request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Extract search criteria from ONDC message
   */
  private extractSearchCriteria(message: any): any {
    return {
      query: message.intent?.item?.descriptor?.name || '',
      category: message.intent?.category?.id || '',
      location: message.intent?.fulfillment?.end?.location || null,
      provider: message.intent?.provider?.id || null,
    };
  }

  /**
   * Extract selected items from ONDC message
   */
  private extractSelectedItems(message: any): any[] {
    return message.order?.items || [];
  }

  /**
   * Extract order information from ONDC message
   */
  private extractOrderInfo(message: any): any {
    return {
      items: message.order?.items || [],
      billing: message.order?.billing || {},
      fulfillments: message.order?.fulfillments || [],
    };
  }

  /**
   * Extract confirmed order from ONDC message
   */
  private extractConfirmedOrder(message: any): any {
    return {
      id: message.order?.id,
      items: message.order?.items || [],
      billing: message.order?.billing || {},
      fulfillments: message.order?.fulfillments || [],
      payment: message.order?.payment || {},
    };
  }

  /**
   * Transform Medusa products to ONDC catalog format
   */
  private transformToONDCCatalog(products: any[], tenant: Tenant): any {
    return {
      'bpp/descriptor': {
        name: tenant.name,
        symbol: tenant.settings.ondcConfig?.subscriberId || 'default',
        short_desc: `${tenant.name} - ONDC Seller`,
        long_desc: `Complete catalog from ${tenant.name}`,
        images: [],
      },
      'bpp/providers': [
        {
          id: tenant.settings.ondcConfig?.subscriberId || 'default',
          descriptor: {
            name: tenant.name,
            symbol: tenant.settings.ondcConfig?.subscriberId || 'default',
            short_desc: `${tenant.name} - ONDC Seller`,
            long_desc: `Products and services from ${tenant.name}`,
            images: [],
          },
          categories: this.buildCategories(products),
          items: this.buildItems(products),
          fulfillments: this.buildFulfillments(tenant),
        },
      ],
    };
  }

  /**
   * Transform to ONDC quote format
   */
  private transformToONDCQuote(quote: any, tenant: Tenant): any {
    // Mock implementation - replace with actual transformation logic
    return {
      items: quote.items || [],
      billing: quote.billing || {},
      fulfillments: quote.fulfillments || [],
      quote: {
        price: quote.total || { currency: 'INR', value: '0' },
        breakup: quote.breakup || [],
      },
    };
  }

  /**
   * Transform to ONDC order format
   */
  private transformToONDCOrder(order: any, tenant: Tenant): any {
    // Mock implementation - replace with actual transformation logic
    return {
      id: order.id || uuidv4(),
      state: order.state || 'Created',
      provider: this.buildProviderInfo(tenant),
      items: order.items || [],
      billing: order.billing || {},
      fulfillments: order.fulfillments || [],
      quote: order.quote || {},
      payment: order.payment || {},
      created_at: order.created_at || new Date().toISOString(),
      updated_at: order.updated_at || new Date().toISOString(),
    };
  }

  /**
   * Transform to ONDC status format
   */
  private transformToONDCStatus(status: any, tenant: Tenant): any {
    return {
      id: status.id,
      state: status.state,
      provider: this.buildProviderInfo(tenant),
      items: status.items || [],
      fulfillments: status.fulfillments || [],
      quote: status.quote || {},
      payment: status.payment || {},
      created_at: status.created_at,
      updated_at: status.updated_at,
    };
  }

  /**
   * Build response context
   */
  private buildResponseContext(requestContext: ONDCContext, action: string): ONDCContext {
    return {
      ...requestContext,
      action,
      message_id: uuidv4(),
      timestamp: new Date().toISOString(),
      bap_id: requestContext.bpp_id,
      bap_uri: requestContext.bpp_uri,
      bpp_id: requestContext.bap_id,
      bpp_uri: requestContext.bap_uri,
    };
  }

  /**
   * Build provider information
   */
  private buildProviderInfo(tenant: Tenant): any {
    return {
      id: tenant.settings.ondcConfig?.subscriberId || tenant.id,
      descriptor: {
        name: tenant.name,
        symbol: tenant.settings.ondcConfig?.subscriberId || tenant.id,
        short_desc: `${tenant.name} - ONDC Seller`,
        long_desc: `Products and services from ${tenant.name}`,
        images: [],
      },
    };
  }

  /**
   * Build categories from products
   */
  private buildCategories(products: any[]): any[] {
    // Mock implementation - extract unique categories
    return [
      {
        id: 'default-category',
        descriptor: {
          name: 'General',
        },
      },
    ];
  }

  /**
   * Build items from products
   */
  private buildItems(products: any[]): any[] {
    return products.map(product => ({
      id: product.id,
      descriptor: {
        name: product.title || product.name,
        code: product.handle || product.id,
        symbol: product.thumbnail || '',
        short_desc: product.subtitle || '',
        long_desc: product.description || '',
        images: product.images || [],
      },
      price: {
        currency: 'INR',
        value: product.price?.toString() || '0',
      },
      category_id: 'default-category',
      fulfillment_id: 'default-fulfillment',
      location_id: 'default-location',
    }));
  }

  /**
   * Handle track requests
   *
   * Returns tracking information for orders
   */
  async handleTrack(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: trackMessage } = message;

    logger.info('Processing ONDC track request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract order ID
      const orderId = trackMessage.order_id;

      // Get tracking information from Medusa
      const trackingInfo = await this.medusaService.getTrackingInfo(orderId, tenant);

      // Transform to ONDC tracking format
      const ondcTracking = this.transformToONDCTracking(trackingInfo, tenant);

      const responseContext = this.buildResponseContext(context, 'on_track');

      return {
        context: responseContext,
        message: {
          tracking: ondcTracking,
        },
      };
    } catch (error: any) {
      logger.error('Error processing track request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Handle cancel requests
   *
   * Processes order cancellation requests
   */
  async handleCancel(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: cancelMessage } = message;

    logger.info('Processing ONDC cancel request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract cancellation information
      const cancellationInfo = this.extractCancellationInfo(cancelMessage);

      // Process cancellation in Medusa
      const cancelResult = await this.medusaService.cancelOrder(cancellationInfo, tenant);

      // Transform to ONDC cancellation format
      const ondcCancel = this.transformToONDCCancel(cancelResult, tenant);

      const responseContext = this.buildResponseContext(context, 'on_cancel');

      return {
        context: responseContext,
        message: {
          order: ondcCancel,
        },
      };
    } catch (error: any) {
      logger.error('Error processing cancel request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Handle update requests
   *
   * Processes order modification requests
   */
  async handleUpdate(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: updateMessage } = message;

    logger.info('Processing ONDC update request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract update information
      const updateInfo = this.extractUpdateInfo(updateMessage);

      // Process update in Medusa
      const updateResult = await this.medusaService.updateOrder(updateInfo, tenant);

      // Transform to ONDC update format
      const ondcUpdate = this.transformToONDCUpdate(updateResult, tenant);

      const responseContext = this.buildResponseContext(context, 'on_update');

      return {
        context: responseContext,
        message: {
          order: ondcUpdate,
        },
      };
    } catch (error: any) {
      logger.error('Error processing update request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Handle rating requests
   *
   * Processes rating and feedback requests
   */
  async handleRating(message: ONDCMessage, tenant: Tenant): Promise<any> {
    const { context, message: ratingMessage } = message;

    logger.info('Processing ONDC rating request', {
      transactionId: context.transaction_id,
      messageId: context.message_id,
      tenantId: tenant.id,
    });

    try {
      // Extract rating information
      const ratingInfo = this.extractRatingInfo(ratingMessage);

      // Process rating in Medusa
      const ratingResult = await this.medusaService.submitRating(ratingInfo, tenant);

      // Transform to ONDC rating format
      const ondcRating = this.transformToONDCRating(ratingResult, tenant);

      const responseContext = this.buildResponseContext(context, 'on_rating');

      return {
        context: responseContext,
        message: {
          rating: ondcRating,
        },
      };
    } catch (error: any) {
      logger.error('Error processing rating request', {
        error: error.message,
        transactionId: context.transaction_id,
        tenantId: tenant.id,
      });
      throw error;
    }
  }

  /**
   * Extract cancellation information from ONDC message
   */
  private extractCancellationInfo(message: any): any {
    return {
      orderId: message.order_id,
      cancellationReasonId: message.cancellation_reason_id,
      descriptor: message.descriptor,
    };
  }

  /**
   * Extract update information from ONDC message
   */
  private extractUpdateInfo(message: any): any {
    return {
      orderId: message.order?.id,
      updateTarget: message.update_target,
      order: message.order,
    };
  }

  /**
   * Extract rating information from ONDC message
   */
  private extractRatingInfo(message: any): any {
    return {
      ratingCategory: message.rating_category,
      id: message.id,
      value: message.value,
      feedback: message.feedback_form,
    };
  }

  /**
   * Transform to ONDC tracking format
   */
  private transformToONDCTracking(tracking: any, tenant: Tenant): any {
    return {
      id: tracking.id || 'tracking-001',
      url: tracking.url || `https://${tenant.name.toLowerCase()}.tracking.com`,
      location: {
        gps: tracking.gps || '12.9716,77.5946',
        address: tracking.address || 'In transit',
      },
      status: tracking.status || 'Order-picked-up',
      time: {
        timestamp: tracking.timestamp || new Date().toISOString(),
      },
    };
  }

  /**
   * Transform to ONDC cancellation format
   */
  private transformToONDCCancel(cancel: any, tenant: Tenant): any {
    return {
      id: cancel.id,
      state: 'Cancelled',
      provider: this.buildProviderInfo(tenant),
      items: cancel.items || [],
      cancellation: {
        cancelled_by: cancel.cancelled_by || 'seller',
        reason: {
          id: cancel.reason_id || '001',
          descriptor: {
            code: cancel.reason_code || 'BUYER_CANCELLED',
          },
        },
      },
      created_at: cancel.created_at,
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Transform to ONDC update format
   */
  private transformToONDCUpdate(update: any, tenant: Tenant): any {
    return {
      id: update.id,
      state: update.state || 'Updated',
      provider: this.buildProviderInfo(tenant),
      items: update.items || [],
      quote: update.quote || {},
      updated_at: new Date().toISOString(),
    };
  }

  /**
   * Transform to ONDC rating format
   */
  private transformToONDCRating(rating: any, tenant: Tenant): any {
    return {
      rating_category: rating.category || 'Order',
      id: rating.id || uuidv4(),
      value: rating.value || 5,
      feedback_form: rating.feedback || [],
      feedback_id: rating.feedback_id || uuidv4(),
    };
  }

  /**
   * Build fulfillments
   */
  private buildFulfillments(tenant: Tenant): any[] {
    return [
      {
        id: 'default-fulfillment',
        type: 'Delivery',
        provider_id: tenant.settings.ondcConfig?.subscriberId || tenant.id,
        rating: 4.5,
        state: {
          descriptor: {
            code: 'Serviceable',
          },
        },
        tracking: false,
        start: {
          location: {
            id: 'default-location',
            descriptor: {
              name: `${tenant.name} Store`,
            },
            gps: '12.9716,77.5946', // Default to Bangalore
            address: {
              locality: 'Default Area',
              street: 'Default Street',
              city: 'Bangalore',
              area_code: '560001',
              state: 'Karnataka',
            },
          },
          time: {
            range: {
              start: '09:00:00.000Z',
              end: '18:00:00.000Z',
            },
          },
          contact: {
            phone: '+919999999999',
            email: '<EMAIL>',
          },
        },
      },
    ];
  }
}
