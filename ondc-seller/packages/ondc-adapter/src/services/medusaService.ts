/**
 * Medusa Service
 *
 * Service for integrating with Medusa Commerce backend.
 * Handles product search, order management, and data synchronization
 * between ONDC protocol and Medusa Commerce.
 *
 * <AUTHOR> Seller Team
 */

import axios, { AxiosInstance } from 'axios';
import { Tenant } from '../middleware/tenantMiddleware';
import { config } from '../config/environment';
import { logger } from '../config/logger';

/**
 * Medusa API interfaces
 */
interface MedusaProduct {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  handle: string;
  thumbnail?: string;
  images?: string[];
  variants: MedusaVariant[];
  categories?: MedusaCategory[];
  collection?: MedusaCollection;
  status: string;
  created_at: string;
  updated_at: string;
}

interface MedusaVariant {
  id: string;
  title: string;
  sku?: string;
  prices: MedusaPrice[];
  inventory_quantity: number;
  manage_inventory: boolean;
  allow_backorder: boolean;
}

interface MedusaPrice {
  id: string;
  currency_code: string;
  amount: number;
  region_id?: string;
}

interface MedusaCategory {
  id: string;
  name: string;
  handle: string;
  description?: string;
}

interface MedusaCollection {
  id: string;
  title: string;
  handle: string;
}

interface MedusaOrder {
  id: string;
  status: string;
  fulfillment_status: string;
  payment_status: string;
  display_id: number;
  cart_id: string;
  customer_id: string;
  email: string;
  billing_address: any;
  shipping_address: any;
  items: any[];
  total: number;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  created_at: string;
  updated_at: string;
}

/**
 * Medusa Service Class
 *
 * Handles all interactions with Medusa Commerce backend
 */
export class MedusaService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.medusa.backendUrl,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${config.medusa.apiKey}`,
      },
      timeout: 10000,
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      config => {
        logger.debug('Medusa API Request', {
          method: config.method?.toUpperCase(),
          url: config.url,
          baseURL: config.baseURL,
        });
        return config;
      },
      error => {
        logger.error('Medusa API Request Error', { error: error.message });
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      response => {
        logger.debug('Medusa API Response', {
          status: response.status,
          url: response.config.url,
          dataSize: JSON.stringify(response.data).length,
        });
        return response;
      },
      error => {
        logger.error('Medusa API Response Error', {
          status: error.response?.status,
          url: error.config?.url,
          message: error.message,
          data: error.response?.data,
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Search products based on criteria
   *
   * @param criteria Search criteria from ONDC message
   * @param tenant Tenant context
   * @returns Array of products matching criteria
   */
  async searchProducts(criteria: any, tenant: Tenant): Promise<MedusaProduct[]> {
    try {
      logger.info('Searching products in Medusa', {
        criteria,
        tenantId: tenant.id,
      });

      // In development mode, return mock data
      if (config.isDevelopment) {
        return this.getMockProducts(criteria, tenant);
      }

      // Build query parameters
      const params: any = {
        limit: 50,
        offset: 0,
      };

      if (criteria.query) {
        params.q = criteria.query;
      }

      if (criteria.category) {
        params.category_id = criteria.category;
      }

      // Make request to Medusa
      const response = await this.client.get('/store/products', {
        params,
        headers: {
          'x-tenant-id': tenant.id,
        },
      });

      return response.data.products || [];
    } catch (error: any) {
      logger.error('Error searching products in Medusa', {
        error: error.message,
        criteria,
        tenantId: tenant.id,
      });

      // Return empty array on error
      return [];
    }
  }

  /**
   * Calculate quote for selected items
   *
   * @param items Selected items from ONDC message
   * @param tenant Tenant context
   * @returns Quote information
   */
  async calculateQuote(items: any[], tenant: Tenant): Promise<any> {
    try {
      logger.info('Calculating quote in Medusa', {
        itemCount: items.length,
        tenantId: tenant.id,
      });

      // In development mode, return mock quote
      if (config.isDevelopment) {
        return this.getMockQuote(items, tenant);
      }

      // Create cart and add items
      const cartResponse = await this.client.post(
        '/store/carts',
        {
          region_id: 'default-region',
        },
        {
          headers: {
            'x-tenant-id': tenant.id,
          },
        }
      );

      const cartId = cartResponse.data.cart.id;

      // Add items to cart
      for (const item of items) {
        await this.client.post(
          `/store/carts/${cartId}/line-items`,
          {
            variant_id: item.id,
            quantity: item.quantity || 1,
          },
          {
            headers: {
              'x-tenant-id': tenant.id,
            },
          }
        );
      }

      // Get updated cart with totals
      const updatedCartResponse = await this.client.get(`/store/carts/${cartId}`, {
        headers: {
          'x-tenant-id': tenant.id,
        },
      });

      return this.transformCartToQuote(updatedCartResponse.data.cart);
    } catch (error: any) {
      logger.error('Error calculating quote in Medusa', {
        error: error.message,
        itemCount: items.length,
        tenantId: tenant.id,
      });

      // Return mock quote on error
      return this.getMockQuote(items, tenant);
    }
  }

  /**
   * Create order draft
   *
   * @param orderInfo Order information from ONDC message
   * @param tenant Tenant context
   * @returns Order draft
   */
  async createOrderDraft(orderInfo: any, tenant: Tenant): Promise<any> {
    try {
      logger.info('Creating order draft in Medusa', {
        tenantId: tenant.id,
      });

      // In development mode, return mock order draft
      if (config.isDevelopment) {
        return this.getMockOrderDraft(orderInfo, tenant);
      }

      // Implementation for actual Medusa order draft creation
      // This would involve creating a cart, adding items, setting addresses, etc.

      return this.getMockOrderDraft(orderInfo, tenant);
    } catch (error: any) {
      logger.error('Error creating order draft in Medusa', {
        error: error.message,
        tenantId: tenant.id,
      });

      return this.getMockOrderDraft(orderInfo, tenant);
    }
  }

  /**
   * Create confirmed order
   *
   * @param orderInfo Confirmed order information
   * @param tenant Tenant context
   * @returns Created order
   */
  async createOrder(orderInfo: any, tenant: Tenant): Promise<MedusaOrder> {
    try {
      logger.info('Creating order in Medusa', {
        orderId: orderInfo.id,
        tenantId: tenant.id,
      });

      // In development mode, return mock order
      if (config.isDevelopment) {
        return this.getMockOrder(orderInfo, tenant);
      }

      // Implementation for actual Medusa order creation
      // This would involve completing the cart and creating the order

      return this.getMockOrder(orderInfo, tenant);
    } catch (error: any) {
      logger.error('Error creating order in Medusa', {
        error: error.message,
        orderId: orderInfo.id,
        tenantId: tenant.id,
      });

      return this.getMockOrder(orderInfo, tenant);
    }
  }

  /**
   * Get order status
   *
   * @param orderId Order ID
   * @param tenant Tenant context
   * @returns Order status information
   */
  async getOrderStatus(orderId: string, tenant: Tenant): Promise<any> {
    try {
      logger.info('Getting order status from Medusa', {
        orderId,
        tenantId: tenant.id,
      });

      // In development mode, return mock status
      if (config.isDevelopment) {
        return this.getMockOrderStatus(orderId, tenant);
      }

      const response = await this.client.get(`/store/orders/${orderId}`, {
        headers: {
          'x-tenant-id': tenant.id,
        },
      });

      return response.data.order;
    } catch (error: any) {
      logger.error('Error getting order status from Medusa', {
        error: error.message,
        orderId,
        tenantId: tenant.id,
      });

      return this.getMockOrderStatus(orderId, tenant);
    }
  }

  /**
   * Get tracking information
   *
   * @param orderId Order ID
   * @param tenant Tenant context
   * @returns Tracking information
   */
  async getTrackingInfo(orderId: string, tenant: Tenant): Promise<any> {
    try {
      logger.info('Getting tracking info from Medusa', {
        orderId,
        tenantId: tenant.id,
      });

      // In development mode, return mock tracking
      if (config.isDevelopment) {
        return this.getMockTrackingInfo(orderId, tenant);
      }

      const response = await this.client.get(`/store/orders/${orderId}/tracking`, {
        headers: {
          'x-tenant-id': tenant.id,
        },
      });

      return response.data.tracking;
    } catch (error: any) {
      logger.error('Error getting tracking info from Medusa', {
        error: error.message,
        orderId,
        tenantId: tenant.id,
      });

      return this.getMockTrackingInfo(orderId, tenant);
    }
  }

  /**
   * Cancel order
   *
   * @param cancellationInfo Cancellation information
   * @param tenant Tenant context
   * @returns Cancellation result
   */
  async cancelOrder(cancellationInfo: any, tenant: Tenant): Promise<any> {
    try {
      logger.info('Cancelling order in Medusa', {
        orderId: cancellationInfo.orderId,
        tenantId: tenant.id,
      });

      // In development mode, return mock cancellation
      if (config.isDevelopment) {
        return this.getMockCancellation(cancellationInfo, tenant);
      }

      const response = await this.client.post(
        `/store/orders/${cancellationInfo.orderId}/cancel`,
        {
          reason: cancellationInfo.cancellationReasonId,
          descriptor: cancellationInfo.descriptor,
        },
        {
          headers: {
            'x-tenant-id': tenant.id,
          },
        }
      );

      return response.data.order;
    } catch (error: any) {
      logger.error('Error cancelling order in Medusa', {
        error: error.message,
        orderId: cancellationInfo.orderId,
        tenantId: tenant.id,
      });

      return this.getMockCancellation(cancellationInfo, tenant);
    }
  }

  /**
   * Update order
   *
   * @param updateInfo Update information
   * @param tenant Tenant context
   * @returns Update result
   */
  async updateOrder(updateInfo: any, tenant: Tenant): Promise<any> {
    try {
      logger.info('Updating order in Medusa', {
        orderId: updateInfo.orderId,
        tenantId: tenant.id,
      });

      // In development mode, return mock update
      if (config.isDevelopment) {
        return this.getMockUpdate(updateInfo, tenant);
      }

      const response = await this.client.put(
        `/store/orders/${updateInfo.orderId}`,
        {
          update_target: updateInfo.updateTarget,
          order: updateInfo.order,
        },
        {
          headers: {
            'x-tenant-id': tenant.id,
          },
        }
      );

      return response.data.order;
    } catch (error: any) {
      logger.error('Error updating order in Medusa', {
        error: error.message,
        orderId: updateInfo.orderId,
        tenantId: tenant.id,
      });

      return this.getMockUpdate(updateInfo, tenant);
    }
  }

  /**
   * Submit rating
   *
   * @param ratingInfo Rating information
   * @param tenant Tenant context
   * @returns Rating result
   */
  async submitRating(ratingInfo: any, tenant: Tenant): Promise<any> {
    try {
      logger.info('Submitting rating in Medusa', {
        ratingCategory: ratingInfo.ratingCategory,
        tenantId: tenant.id,
      });

      // In development mode, return mock rating
      if (config.isDevelopment) {
        return this.getMockRating(ratingInfo, tenant);
      }

      const response = await this.client.post(
        '/store/ratings',
        {
          rating_category: ratingInfo.ratingCategory,
          id: ratingInfo.id,
          value: ratingInfo.value,
          feedback_form: ratingInfo.feedback,
        },
        {
          headers: {
            'x-tenant-id': tenant.id,
          },
        }
      );

      return response.data.rating;
    } catch (error: any) {
      logger.error('Error submitting rating in Medusa', {
        error: error.message,
        ratingCategory: ratingInfo.ratingCategory,
        tenantId: tenant.id,
      });

      return this.getMockRating(ratingInfo, tenant);
    }
  }

  /**
   * Mock data methods for development
   */
  private getMockProducts(criteria: any, tenant: Tenant): MedusaProduct[] {
    return [
      {
        id: 'prod_01',
        title: 'Sample Product 1',
        subtitle: 'High quality sample product',
        description: 'This is a sample product for ONDC testing',
        handle: 'sample-product-1',
        thumbnail: 'https://picsum.photos/300/300?random=1',
        images: ['https://picsum.photos/300/300?random=1'],
        variants: [
          {
            id: 'variant_01',
            title: 'Default Variant',
            sku: 'SAMPLE-001',
            prices: [
              {
                id: 'price_01',
                currency_code: 'INR',
                amount: 10000, // 100.00 INR
              },
            ],
            inventory_quantity: 100,
            manage_inventory: true,
            allow_backorder: false,
          },
        ],
        status: 'published',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
      {
        id: 'prod_02',
        title: 'Sample Product 2',
        subtitle: 'Another quality product',
        description: 'This is another sample product for ONDC testing',
        handle: 'sample-product-2',
        thumbnail: 'https://picsum.photos/300/300?random=2',
        images: ['https://picsum.photos/300/300?random=2'],
        variants: [
          {
            id: 'variant_02',
            title: 'Default Variant',
            sku: 'SAMPLE-002',
            prices: [
              {
                id: 'price_02',
                currency_code: 'INR',
                amount: 15000, // 150.00 INR
              },
            ],
            inventory_quantity: 50,
            manage_inventory: true,
            allow_backorder: false,
          },
        ],
        status: 'published',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      },
    ];
  }

  private getMockQuote(items: any[], tenant: Tenant): any {
    const total = items.reduce(
      (sum, item) => sum + (item.price?.value || 100) * (item.quantity || 1),
      0
    );

    return {
      items,
      total: { currency: 'INR', value: total.toString() },
      breakup: [
        {
          title: 'Item Total',
          price: { currency: 'INR', value: total.toString() },
        },
        {
          title: 'Delivery Charges',
          price: { currency: 'INR', value: '50' },
        },
      ],
    };
  }

  private getMockOrderDraft(orderInfo: any, tenant: Tenant): any {
    return {
      id: `draft_${Date.now()}`,
      state: 'Draft',
      items: orderInfo.items || [],
      billing: orderInfo.billing || {},
      fulfillments: orderInfo.fulfillments || [],
      quote: { currency: 'INR', value: '1000' },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  private getMockOrder(orderInfo: any, tenant: Tenant): MedusaOrder {
    return {
      id: orderInfo.id || `order_${Date.now()}`,
      status: 'pending',
      fulfillment_status: 'not_fulfilled',
      payment_status: 'awaiting',
      display_id: Math.floor(Math.random() * 10000),
      cart_id: `cart_${Date.now()}`,
      customer_id: 'customer_01',
      email: '<EMAIL>',
      billing_address: orderInfo.billing || {},
      shipping_address: orderInfo.billing || {},
      items: orderInfo.items || [],
      total: 1000,
      subtotal: 950,
      tax_total: 50,
      shipping_total: 0,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  private getMockOrderStatus(orderId: string, tenant: Tenant): any {
    return {
      id: orderId,
      state: 'Accepted',
      items: [],
      fulfillments: [
        {
          id: 'fulfillment_01',
          state: 'Pending',
          tracking: false,
        },
      ],
      quote: { currency: 'INR', value: '1000' },
      payment: {
        status: 'PAID',
        type: 'ON-ORDER',
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  private getMockTrackingInfo(orderId: string, tenant: Tenant): any {
    return {
      id: `tracking_${orderId}`,
      url: `https://${tenant.name.toLowerCase()}.tracking.com/track/${orderId}`,
      gps: '12.9716,77.5946',
      address: 'Package is in transit to delivery location',
      status: 'Order-picked-up',
      timestamp: new Date().toISOString(),
    };
  }

  private getMockCancellation(cancellationInfo: any, tenant: Tenant): any {
    return {
      id: cancellationInfo.orderId,
      state: 'Cancelled',
      items: [],
      cancelled_by: 'seller',
      reason_id: cancellationInfo.cancellationReasonId || '001',
      reason_code: 'BUYER_CANCELLED',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
  }

  private getMockUpdate(updateInfo: any, tenant: Tenant): any {
    return {
      id: updateInfo.orderId,
      state: 'Updated',
      items: updateInfo.order?.items || [],
      quote: { currency: 'INR', value: '1000' },
      updated_at: new Date().toISOString(),
    };
  }

  private getMockRating(ratingInfo: any, tenant: Tenant): any {
    return {
      category: ratingInfo.ratingCategory || 'Order',
      id: ratingInfo.id || `rating_${Date.now()}`,
      value: ratingInfo.value || 5,
      feedback: ratingInfo.feedback || [],
      feedback_id: `feedback_${Date.now()}`,
      created_at: new Date().toISOString(),
    };
  }

  private transformCartToQuote(cart: any): any {
    return {
      items: cart.items || [],
      total: { currency: 'INR', value: cart.total?.toString() || '0' },
      breakup: [
        {
          title: 'Item Total',
          price: { currency: 'INR', value: cart.subtotal?.toString() || '0' },
        },
        {
          title: 'Tax',
          price: { currency: 'INR', value: cart.tax_total?.toString() || '0' },
        },
        {
          title: 'Shipping',
          price: { currency: 'INR', value: cart.shipping_total?.toString() || '0' },
        },
      ],
    };
  }
}
