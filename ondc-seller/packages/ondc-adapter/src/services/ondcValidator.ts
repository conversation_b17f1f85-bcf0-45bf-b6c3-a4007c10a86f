/**
 * ONDC Validator Service
 * 
 * Validates ONDC protocol messages for compliance with ONDC specifications.
 * Ensures message structure, required fields, and business logic validation.
 * 
 * <AUTHOR> Seller Team
 */

import <PERSON><PERSON> from 'joi';
import { logger } from '../config/logger';

/**
 * Validation result interface
 */
interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * ONDC Context schema
 */
const contextSchema = Joi.object({
  domain: Joi.string().required(),
  country: Joi.string().length(3).required(), // ISO 3166-1 alpha-3
  city: Joi.string().required(),
  action: Joi.string().required(),
  core_version: Joi.string().required(),
  bap_id: Joi.string().required(),
  bap_uri: Joi.string().uri().required(),
  bpp_id: Joi.string().required(),
  bpp_uri: Joi.string().uri().required(),
  transaction_id: Joi.string().uuid().required(),
  message_id: Joi.string().uuid().required(),
  timestamp: Joi.string().isoDate().required(),
  key: Joi.string().optional(),
  ttl: Joi.string().optional(),
});

/**
 * Search message schema
 */
const searchMessageSchema = Joi.object({
  intent: Joi.object({
    item: Joi.object({
      descriptor: Joi.object({
        name: Joi.string().optional(),
      }).optional(),
    }).optional(),
    category: Joi.object({
      id: Joi.string().optional(),
    }).optional(),
    provider: Joi.object({
      id: Joi.string().optional(),
    }).optional(),
    fulfillment: Joi.object({
      end: Joi.object({
        location: Joi.object({
          gps: Joi.string().optional(),
          area_code: Joi.string().optional(),
        }).optional(),
      }).optional(),
    }).optional(),
  }).optional(),
});

/**
 * Select message schema
 */
const selectMessageSchema = Joi.object({
  order: Joi.object({
    provider: Joi.object({
      id: Joi.string().required(),
    }).required(),
    items: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        quantity: Joi.object({
          count: Joi.number().integer().min(1).required(),
        }).required(),
      })
    ).min(1).required(),
    fulfillments: Joi.array().items(
      Joi.object({
        end: Joi.object({
          location: Joi.object({
            gps: Joi.string().optional(),
            area_code: Joi.string().optional(),
          }).optional(),
        }).optional(),
      })
    ).optional(),
  }).required(),
});

/**
 * Init message schema
 */
const initMessageSchema = Joi.object({
  order: Joi.object({
    provider: Joi.object({
      id: Joi.string().required(),
    }).required(),
    items: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        quantity: Joi.object({
          count: Joi.number().integer().min(1).required(),
        }).required(),
      })
    ).min(1).required(),
    billing: Joi.object({
      name: Joi.string().required(),
      address: Joi.object({
        name: Joi.string().optional(),
        building: Joi.string().optional(),
        locality: Joi.string().required(),
        city: Joi.string().required(),
        state: Joi.string().required(),
        country: Joi.string().required(),
        area_code: Joi.string().required(),
      }).required(),
      email: Joi.string().email().optional(),
      phone: Joi.string().required(),
    }).required(),
    fulfillments: Joi.array().items(
      Joi.object({
        end: Joi.object({
          location: Joi.object({
            gps: Joi.string().optional(),
            address: Joi.object({
              name: Joi.string().optional(),
              building: Joi.string().optional(),
              locality: Joi.string().required(),
              city: Joi.string().required(),
              state: Joi.string().required(),
              country: Joi.string().required(),
              area_code: Joi.string().required(),
            }).required(),
          }).required(),
          contact: Joi.object({
            phone: Joi.string().required(),
            email: Joi.string().email().optional(),
          }).optional(),
        }).required(),
      })
    ).min(1).required(),
  }).required(),
});

/**
 * Confirm message schema
 */
const confirmMessageSchema = Joi.object({
  order: Joi.object({
    id: Joi.string().required(),
    provider: Joi.object({
      id: Joi.string().required(),
    }).required(),
    items: Joi.array().items(
      Joi.object({
        id: Joi.string().required(),
        quantity: Joi.object({
          count: Joi.number().integer().min(1).required(),
        }).required(),
      })
    ).min(1).required(),
    billing: Joi.object({
      name: Joi.string().required(),
      address: Joi.object({
        name: Joi.string().optional(),
        building: Joi.string().optional(),
        locality: Joi.string().required(),
        city: Joi.string().required(),
        state: Joi.string().required(),
        country: Joi.string().required(),
        area_code: Joi.string().required(),
      }).required(),
      email: Joi.string().email().optional(),
      phone: Joi.string().required(),
    }).required(),
    fulfillments: Joi.array().items(
      Joi.object({
        end: Joi.object({
          location: Joi.object({
            gps: Joi.string().optional(),
            address: Joi.object({
              name: Joi.string().optional(),
              building: Joi.string().optional(),
              locality: Joi.string().required(),
              city: Joi.string().required(),
              state: Joi.string().required(),
              country: Joi.string().required(),
              area_code: Joi.string().required(),
            }).required(),
          }).required(),
          contact: Joi.object({
            phone: Joi.string().required(),
            email: Joi.string().email().optional(),
          }).optional(),
        }).required(),
      })
    ).min(1).required(),
    payment: Joi.object({
      uri: Joi.string().uri().optional(),
      tl_method: Joi.string().optional(),
      params: Joi.object({
        amount: Joi.string().required(),
        currency: Joi.string().length(3).required(),
        transaction_id: Joi.string().optional(),
      }).required(),
      status: Joi.string().valid('PAID', 'NOT-PAID').required(),
      type: Joi.string().valid('ON-ORDER', 'PRE-FULFILLMENT', 'ON-FULFILLMENT', 'POST-FULFILLMENT').required(),
      collected_by: Joi.string().valid('BAP', 'BPP').required(),
    }).required(),
  }).required(),
});

/**
 * Status message schema
 */
const statusMessageSchema = Joi.object({
  order_id: Joi.string().required(),
});

/**
 * ONDC Validator Class
 */
export class ONDCValidator {
  private messageSchemas: Map<string, Joi.ObjectSchema>;

  constructor() {
    this.messageSchemas = new Map([
      ['search', searchMessageSchema],
      ['select', selectMessageSchema],
      ['init', initMessageSchema],
      ['confirm', confirmMessageSchema],
      ['status', statusMessageSchema],
    ]);
  }

  /**
   * Validate ONDC message
   * 
   * @param message ONDC message to validate
   * @param action ONDC action type
   * @returns Validation result
   */
  async validateMessage(message: any, action: string): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Validate context
      const contextValidation = this.validateContext(message.context, action);
      if (!contextValidation.isValid) {
        result.isValid = false;
        result.errors.push(...contextValidation.errors);
        result.warnings.push(...contextValidation.warnings);
      }

      // Validate message body
      const messageValidation = this.validateMessageBody(message.message, action);
      if (!messageValidation.isValid) {
        result.isValid = false;
        result.errors.push(...messageValidation.errors);
        result.warnings.push(...messageValidation.warnings);
      }

      // Business logic validation
      const businessValidation = await this.validateBusinessLogic(message, action);
      if (!businessValidation.isValid) {
        result.isValid = false;
        result.errors.push(...businessValidation.errors);
        result.warnings.push(...businessValidation.warnings);
      }

      logger.debug('ONDC message validation completed', {
        action,
        isValid: result.isValid,
        errorCount: result.errors.length,
        warningCount: result.warnings.length,
      });

      return result;
    } catch (error: any) {
      logger.error('Error during ONDC message validation', {
        error: error.message,
        action,
      });

      return {
        isValid: false,
        errors: [`Validation error: ${error.message}`],
        warnings: [],
      };
    }
  }

  /**
   * Validate ONDC context
   */
  private validateContext(context: any, action: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      // Validate context structure
      const { error } = contextSchema.validate(context);
      if (error) {
        result.isValid = false;
        result.errors.push(`Context validation error: ${error.message}`);
        return result;
      }

      // Validate action matches context
      if (context.action !== action) {
        result.isValid = false;
        result.errors.push(`Action mismatch: expected '${action}', got '${context.action}'`);
      }

      // Validate timestamp is recent (within 5 minutes)
      const messageTime = new Date(context.timestamp);
      const now = new Date();
      const timeDiff = Math.abs(now.getTime() - messageTime.getTime());
      const fiveMinutes = 5 * 60 * 1000;

      if (timeDiff > fiveMinutes) {
        result.warnings.push('Message timestamp is more than 5 minutes old');
      }

      // Validate core version
      const supportedVersions = ['1.2.0', '1.1.0'];
      if (!supportedVersions.includes(context.core_version)) {
        result.warnings.push(`Unsupported core version: ${context.core_version}`);
      }

      return result;
    } catch (error: any) {
      return {
        isValid: false,
        errors: [`Context validation error: ${error.message}`],
        warnings: [],
      };
    }
  }

  /**
   * Validate message body
   */
  private validateMessageBody(messageBody: any, action: string): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      const schema = this.messageSchemas.get(action);
      if (!schema) {
        result.warnings.push(`No validation schema found for action: ${action}`);
        return result;
      }

      const { error } = schema.validate(messageBody);
      if (error) {
        result.isValid = false;
        result.errors.push(`Message body validation error: ${error.message}`);
      }

      return result;
    } catch (error: any) {
      return {
        isValid: false,
        errors: [`Message body validation error: ${error.message}`],
        warnings: [],
      };
    }
  }

  /**
   * Validate business logic
   */
  private async validateBusinessLogic(message: any, action: string): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    try {
      switch (action) {
        case 'search':
          return this.validateSearchLogic(message);
        case 'select':
          return this.validateSelectLogic(message);
        case 'init':
          return this.validateInitLogic(message);
        case 'confirm':
          return this.validateConfirmLogic(message);
        case 'status':
          return this.validateStatusLogic(message);
        default:
          result.warnings.push(`No business logic validation for action: ${action}`);
      }

      return result;
    } catch (error: any) {
      return {
        isValid: false,
        errors: [`Business logic validation error: ${error.message}`],
        warnings: [],
      };
    }
  }

  /**
   * Validate search business logic
   */
  private validateSearchLogic(message: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    // Add search-specific business logic validation
    // For example: validate location constraints, category restrictions, etc.

    return result;
  }

  /**
   * Validate select business logic
   */
  private validateSelectLogic(message: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    // Add select-specific business logic validation
    // For example: validate item availability, quantity limits, etc.

    return result;
  }

  /**
   * Validate init business logic
   */
  private validateInitLogic(message: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    // Add init-specific business logic validation
    // For example: validate delivery address, billing information, etc.

    return result;
  }

  /**
   * Validate confirm business logic
   */
  private validateConfirmLogic(message: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    // Add confirm-specific business logic validation
    // For example: validate payment information, final order details, etc.

    return result;
  }

  /**
   * Validate status business logic
   */
  private validateStatusLogic(message: any): ValidationResult {
    const result: ValidationResult = {
      isValid: true,
      errors: [],
      warnings: [],
    };

    // Add status-specific business logic validation
    // For example: validate order ID format, access permissions, etc.

    return result;
  }
}
