/**
 * Logger Configuration
 *
 * Centralized logging configuration using <PERSON> for the ONDC Adapter service.
 * Provides structured logging with different levels, formats, and transports
 * for development and production environments.
 *
 * <AUTHOR> Seller Team
 */

import winston from 'winston';
import { config } from './environment';

/**
 * Custom log format for development
 *
 * Provides colorized, human-readable log output for development
 * with timestamp, level, and message formatting.
 */
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;

    // Add stack trace for errors
    if (stack) {
      log += `\n${stack}`;
    }

    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }

    return log;
  })
);

/**
 * Production log format
 *
 * Provides structured JSON logging for production environments
 * with all necessary fields for log aggregation and analysis.
 */
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf(info => {
    const { timestamp, level, message, ...meta } = info;
    return JSON.stringify({
      timestamp,
      level,
      message,
      service: 'ondc-adapter',
      environment: config.nodeEnv,
      ...meta,
    });
  })
);

/**
 * Create logger transports based on environment
 *
 * Development: Console output with colors
 * Production: Console + File outputs with JSON format
 */
const createTransports = (): winston.transport[] => {
  const transports: winston.transport[] = [
    new winston.transports.Console({
      level: config.logging.level,
      format: config.isDevelopment ? developmentFormat : productionFormat,
    }),
  ];

  // Add file transports in production
  if (config.isProduction) {
    transports.push(
      // Error log file
      new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        format: productionFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      }),

      // Combined log file
      new winston.transports.File({
        filename: 'logs/combined.log',
        format: productionFormat,
        maxsize: 5242880, // 5MB
        maxFiles: 5,
      })
    );
  }

  return transports;
};

/**
 * Winston logger instance
 *
 * Configured with appropriate transports, formats, and levels
 * based on the current environment.
 */
export const logger = winston.createLogger({
  level: config.logging.level,
  format: config.isDevelopment ? developmentFormat : productionFormat,
  defaultMeta: {
    service: 'ondc-adapter',
    environment: config.nodeEnv,
  },
  transports: createTransports(),

  // Don't exit on handled exceptions
  exitOnError: false,

  // Handle uncaught exceptions and rejections
  exceptionHandlers: [
    new winston.transports.Console({
      format: config.isDevelopment ? developmentFormat : productionFormat,
    }),
  ],

  rejectionHandlers: [
    new winston.transports.Console({
      format: config.isDevelopment ? developmentFormat : productionFormat,
    }),
  ],
});

/**
 * Request logger middleware helper
 *
 * Creates structured log entries for HTTP requests with
 * request ID, method, URL, and response time.
 */
export const createRequestLogger = () => {
  return (req: any, res: any, next: any) => {
    const start = Date.now();

    res.on('finish', () => {
      const duration = Date.now() - start;
      const logData = {
        requestId: req.id,
        method: req.method,
        url: req.originalUrl,
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        tenantId: req.tenantId,
      };

      if (res.statusCode >= 400) {
        logger.warn('HTTP Request Error', logData);
      } else {
        logger.info('HTTP Request', logData);
      }
    });

    next();
  };
};

/**
 * ONDC protocol logger
 *
 * Specialized logger for ONDC protocol messages with
 * structured formatting for better debugging and monitoring.
 */
export const ondcLogger = {
  /**
   * Log incoming ONDC messages
   */
  incoming: (action: string, message: any, context?: any) => {
    logger.info('ONDC Incoming Message', {
      direction: 'incoming',
      action,
      messageId: message.context?.message_id,
      transactionId: message.context?.transaction_id,
      timestamp: message.context?.timestamp,
      ...context,
    });

    if (config.logging.enableDebug) {
      logger.debug('ONDC Incoming Message Details', {
        direction: 'incoming',
        action,
        message,
      });
    }
  },

  /**
   * Log outgoing ONDC messages
   */
  outgoing: (action: string, message: any, context?: any) => {
    logger.info('ONDC Outgoing Message', {
      direction: 'outgoing',
      action,
      messageId: message.context?.message_id,
      transactionId: message.context?.transaction_id,
      timestamp: message.context?.timestamp,
      ...context,
    });

    if (config.logging.enableDebug) {
      logger.debug('ONDC Outgoing Message Details', {
        direction: 'outgoing',
        action,
        message,
      });
    }
  },

  /**
   * Log ONDC errors
   */
  error: (action: string, error: any, context?: any) => {
    logger.error('ONDC Protocol Error', {
      action,
      error: error.message || error,
      stack: error.stack,
      ...context,
    });
  },
};

/**
 * Database logger
 *
 * Specialized logger for database operations with
 * query timing and error tracking.
 */
export const dbLogger = {
  /**
   * Log database queries
   */
  query: (query: string, params?: any[], duration?: number) => {
    const logData: any = {
      type: 'database_query',
      query: query.substring(0, 200), // Truncate long queries
    };

    if (params && config.logging.enableDebug) {
      logData.params = params;
    }

    if (duration) {
      logData.duration = `${duration}ms`;
    }

    logger.debug('Database Query', logData);
  },

  /**
   * Log database errors
   */
  error: (error: any, query?: string) => {
    logger.error('Database Error', {
      type: 'database_error',
      error: error.message || error,
      query: query?.substring(0, 200),
      stack: error.stack,
    });
  },
};

/**
 * Performance logger
 *
 * Helper for tracking performance metrics and timing
 * of critical operations.
 */
export const perfLogger = {
  /**
   * Start timing an operation
   */
  start: (operation: string) => {
    const startTime = process.hrtime.bigint();

    return {
      end: (context?: any) => {
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds

        logger.info('Performance Metric', {
          type: 'performance',
          operation,
          duration: `${duration.toFixed(2)}ms`,
          ...context,
        });

        return duration;
      },
    };
  },
};

// Create logs directory in production
if (config.isProduction) {
  const fs = require('fs');
  const path = require('path');

  const logsDir = path.join(process.cwd(), 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
}

export default logger;
