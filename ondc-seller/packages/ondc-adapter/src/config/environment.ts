/**
 * Environment Configuration
 * 
 * Centralized configuration management for the ONDC Adapter service.
 * Handles environment variables, validation, and provides type-safe
 * configuration objects for different environments.
 * 
 * <AUTHOR> Seller Team
 */

import dotenv from 'dotenv';
import <PERSON><PERSON> from 'joi';

// Load environment variables
dotenv.config();

/**
 * Environment variable validation schema
 * 
 * Defines required and optional environment variables with their
 * types, default values, and validation rules.
 */
const envSchema = Joi.object({
  // Application settings
  NODE_ENV: Joi.string()
    .valid('development', 'production', 'test')
    .default('development'),
  PORT: Joi.number().default(3001),
  
  // Database configuration
  DATABASE_URL: Joi.string().required(),
  REDIS_URL: Joi.string().default('redis://localhost:6379'),
  
  // Medusa integration
  MEDUSA_BACKEND_URL: Joi.string().default('http://localhost:9001'),
  MEDUSA_API_KEY: Joi.string().required(),
  
  // ONDC network configuration
  ONDC_NETWORK_URL: Joi.string().required(),
  ONDC_SUBSCRIBER_ID: Joi.string().required(),
  ONDC_SUBSCRIBER_URL: Joi.string().required(),
  ONDC_SIGNING_PRIVATE_KEY: Joi.string().required(),
  ONDC_ENCRYPTION_PRIVATE_KEY: Joi.string().required(),
  ONDC_UNIQUE_KEY_ID: Joi.string().required(),
  
  // Authentication
  JWT_SECRET: Joi.string().default('supersecret'),
  JWT_EXPIRES_IN: Joi.string().default('24h'),
  
  // Security
  CORS_ORIGINS: Joi.string().default('http://localhost:3000,http://localhost:3001'),
  RATE_LIMIT_WINDOW_MS: Joi.number().default(15 * 60 * 1000), // 15 minutes
  RATE_LIMIT_MAX_REQUESTS: Joi.number().default(100),
  
  // Logging
  LOG_LEVEL: Joi.string()
    .valid('error', 'warn', 'info', 'debug')
    .default('info'),
  LOG_FORMAT: Joi.string()
    .valid('json', 'simple')
    .default('json'),
  
  // Development mode settings
  ENABLE_MOCK_ONDC: Joi.boolean().default(false),
  ENABLE_DEBUG_LOGGING: Joi.boolean().default(false),
  
  // Notification service
  NOTIFICATION_SERVICE_URL: Joi.string().default('http://localhost:3003'),
  
  // Auth service
  AUTH_SERVICE_URL: Joi.string().default('http://localhost:3002'),
  
  // Monitoring
  ENABLE_METRICS: Joi.boolean().default(true),
  METRICS_PORT: Joi.number().default(9090),
}).unknown();

/**
 * Validate environment variables
 */
const { error, value: envVars } = envSchema.validate(process.env);

if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

/**
 * Configuration object with validated environment variables
 */
export const config = {
  // Application settings
  nodeEnv: envVars.NODE_ENV as string,
  port: envVars.PORT as number,
  isDevelopment: envVars.NODE_ENV === 'development',
  isProduction: envVars.NODE_ENV === 'production',
  isTest: envVars.NODE_ENV === 'test',
  
  // Database configuration
  database: {
    url: envVars.DATABASE_URL as string,
    redis: {
      url: envVars.REDIS_URL as string,
    },
  },
  
  // Medusa integration
  medusa: {
    backendUrl: envVars.MEDUSA_BACKEND_URL as string,
    apiKey: envVars.MEDUSA_API_KEY as string,
  },
  
  // ONDC network configuration
  ondc: {
    networkUrl: envVars.ONDC_NETWORK_URL as string,
    subscriberId: envVars.ONDC_SUBSCRIBER_ID as string,
    subscriberUrl: envVars.ONDC_SUBSCRIBER_URL as string,
    signingPrivateKey: envVars.ONDC_SIGNING_PRIVATE_KEY as string,
    encryptionPrivateKey: envVars.ONDC_ENCRYPTION_PRIVATE_KEY as string,
    uniqueKeyId: envVars.ONDC_UNIQUE_KEY_ID as string,
    enableMock: envVars.ENABLE_MOCK_ONDC as boolean,
  },
  
  // Authentication
  auth: {
    jwtSecret: envVars.JWT_SECRET as string,
    jwtExpiresIn: envVars.JWT_EXPIRES_IN as string,
    serviceUrl: envVars.AUTH_SERVICE_URL as string,
  },
  
  // Security
  security: {
    corsOrigins: (envVars.CORS_ORIGINS as string).split(','),
    rateLimit: {
      windowMs: envVars.RATE_LIMIT_WINDOW_MS as number,
      maxRequests: envVars.RATE_LIMIT_MAX_REQUESTS as number,
    },
  },
  
  // Logging
  logging: {
    level: envVars.LOG_LEVEL as string,
    format: envVars.LOG_FORMAT as string,
    enableDebug: envVars.ENABLE_DEBUG_LOGGING as boolean,
  },
  
  // Services
  services: {
    notification: {
      url: envVars.NOTIFICATION_SERVICE_URL as string,
    },
    auth: {
      url: envVars.AUTH_SERVICE_URL as string,
    },
  },
  
  // Monitoring
  monitoring: {
    enableMetrics: envVars.ENABLE_METRICS as boolean,
    metricsPort: envVars.METRICS_PORT as number,
  },
  
  // Convenience getters
  get corsOrigins() {
    return this.security.corsOrigins;
  },
  
  get isDev() {
    return this.isDevelopment;
  },
  
  get isProd() {
    return this.isProduction;
  },
} as const;

/**
 * Development mode configuration overrides
 * 
 * Provides sensible defaults for development environment
 * when certain environment variables are not set.
 */
export const developmentDefaults = {
  // Mock ONDC credentials for development
  ondc: {
    networkUrl: 'https://staging.ondc.org',
    subscriberId: 'dev-seller-app',
    subscriberUrl: 'http://localhost:3001',
    signingPrivateKey: 'dev-signing-key',
    encryptionPrivateKey: 'dev-encryption-key',
    uniqueKeyId: 'dev-unique-key',
  },
  
  // Development database
  database: {
    url: 'postgresql://postgres:password@localhost:5432/ondc_seller_dev',
  },
  
  // Development auth
  auth: {
    jwtSecret: 'dev-jwt-secret',
    credentials: {
      username: 'demo',
      password: 'demo',
    },
  },
};

/**
 * Apply development defaults if in development mode
 */
if (config.isDevelopment) {
  // Apply defaults only if values are not already set
  Object.keys(developmentDefaults).forEach((key) => {
    const configKey = key as keyof typeof developmentDefaults;
    if (!process.env[key.toUpperCase()]) {
      Object.assign(config[configKey], developmentDefaults[configKey]);
    }
  });
}

/**
 * Configuration validation
 * 
 * Ensures all required configuration is present and valid
 * before the application starts.
 */
export function validateConfig(): void {
  const requiredInProduction = [
    'ONDC_NETWORK_URL',
    'ONDC_SUBSCRIBER_ID',
    'ONDC_SIGNING_PRIVATE_KEY',
    'MEDUSA_API_KEY',
  ];
  
  if (config.isProduction) {
    const missing = requiredInProduction.filter(
      (key) => !process.env[key]
    );
    
    if (missing.length > 0) {
      throw new Error(
        `Missing required production environment variables: ${missing.join(', ')}`
      );
    }
  }
}

// Validate configuration on import
validateConfig();
