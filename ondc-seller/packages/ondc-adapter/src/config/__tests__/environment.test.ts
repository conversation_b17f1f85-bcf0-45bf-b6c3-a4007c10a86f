/**
 * Environment Configuration Tests
 *
 * Basic tests for environment configuration loading and validation.
 */

import { config } from '../environment';

describe('Environment Configuration', () => {
  it('should load configuration successfully', () => {
    expect(config).toBeDefined();
    expect(config.nodeEnv).toBeDefined();
    expect(config.port).toBeDefined();
  });

  it('should have required configuration properties', () => {
    expect(config).toHaveProperty('nodeEnv');
    expect(config).toHaveProperty('port');
    expect(config).toHaveProperty('isDevelopment');
    expect(config).toHaveProperty('isProduction');
    expect(config).toHaveProperty('isTest');
  });

  it('should have medusa configuration', () => {
    expect(config).toHaveProperty('medusa');
    expect(config.medusa).toHaveProperty('backendUrl');
    expect(config.medusa).toHaveProperty('apiKey');
  });

  it('should have database configuration', () => {
    expect(config).toHaveProperty('database');
    expect(config.database).toHaveProperty('url');
  });

  it('should have redis configuration', () => {
    expect(config).toHaveProperty('database');
    expect(config.database).toHaveProperty('redis');
    expect(config.database.redis).toHaveProperty('url');
  });

  it('should have auth configuration', () => {
    expect(config).toHaveProperty('auth');
    expect(config.auth).toHaveProperty('jwtSecret');
    expect(config.auth).toHaveProperty('jwtExpiresIn');
  });

  it('should have security configuration', () => {
    expect(config).toHaveProperty('security');
    expect(config.security).toHaveProperty('corsOrigins');
    expect(Array.isArray(config.security.corsOrigins)).toBe(true);
  });

  it('should have rate limiting configuration', () => {
    expect(config).toHaveProperty('security');
    expect(config.security).toHaveProperty('rateLimit');
    expect(config.security.rateLimit).toHaveProperty('windowMs');
    expect(config.security.rateLimit).toHaveProperty('maxRequests');
  });

  it('should have logging configuration', () => {
    expect(config).toHaveProperty('logging');
    expect(config.logging).toHaveProperty('level');
    expect(config.logging).toHaveProperty('format');
  });

  it('should correctly identify environment', () => {
    if (process.env.NODE_ENV === 'test') {
      expect(config.isTest).toBe(true);
      expect(config.isDevelopment).toBe(false);
      expect(config.isProduction).toBe(false);
    } else if (process.env.NODE_ENV === 'development') {
      expect(config.isDevelopment).toBe(true);
      expect(config.isTest).toBe(false);
      expect(config.isProduction).toBe(false);
    } else if (process.env.NODE_ENV === 'production') {
      expect(config.isProduction).toBe(true);
      expect(config.isDevelopment).toBe(false);
      expect(config.isTest).toBe(false);
    }
  });

  it('should have valid port number', () => {
    expect(typeof config.port).toBe('number');
    expect(config.port).toBeGreaterThan(0);
    expect(config.port).toBeLessThan(65536);
  });

  it('should have valid URLs', () => {
    expect(config.medusa.backendUrl).toMatch(/^https?:\/\/.+/);
    expect(config.database.url).toMatch(/^postgresql:\/\/.+/);
    expect(config.database.redis.url).toMatch(/^redis:\/\/.+/);
  });
});
