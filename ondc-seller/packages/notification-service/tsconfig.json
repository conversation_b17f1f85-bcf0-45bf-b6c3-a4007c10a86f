{"compilerOptions": {"target": "ES2021", "module": "commonjs", "lib": ["ES2021"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "exactOptionalPropertyTypes": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/controllers/*": ["src/controllers/*"], "@/services/*": ["src/services/*"], "@/middleware/*": ["src/middleware/*"], "@/models/*": ["src/models/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"], "@/config/*": ["src/config/*"], "@/tests/*": ["src/tests/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}