{"name": "@ondc-seller/notification-service", "version": "0.0.1", "private": true, "description": "Real-time Notification Service with Supabase and RabbitMQ support for ONDC Seller Platform", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "start": "node dist/index.js", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rimraf dist coverage", "api:docs": "swagger-jsdoc -d swaggerDef.js src/**/*.ts -o docs/openapi.yaml", "docker:build": "docker build -t notification-service .", "docker:run": "docker run -p 3003:3003 notification-service"}, "dependencies": {"@ondc-seller/prisma": "*", "@supabase/supabase-js": "^2.38.5", "@supabase/realtime-js": "^2.8.4", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "joi": "^17.11.0", "socket.io": "^4.7.4", "amqplib": "^0.10.3", "bull": "^4.12.2", "redis": "^4.6.10", "firebase-admin": "^11.11.1", "web-push": "^3.6.6", "nodemailer": "^6.9.7", "twilio": "^4.19.3", "dotenv": "^16.3.1", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "cron": "^3.1.6", "node-cron": "^3.0.3"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/joi": "^17.2.3", "@types/amqplib": "^0.10.4", "@types/web-push": "^3.6.3", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/cron": "^2.0.1", "@types/node-cron": "^3.0.11", "@types/node": "^20.10.5", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "ts-node-dev": "^2.0.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "rimraf": "^5.0.5", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "keywords": ["notifications", "real-time", "supabase", "rabbitmq", "websockets", "push-notifications", "typescript"], "author": "ONDC Seller Team", "license": "MIT"}