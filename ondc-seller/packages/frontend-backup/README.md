# ONDC Seller Frontend

This is the frontend application for the ONDC Seller Platform, built with Next.js and Tailwind CSS.

## Features

- Product management
- Order management
- Customer management
- Analytics dashboard
- Responsive design
- Strapi CMS integration
- Multi-tenant support
- Robust error handling with generic error pages
- API connectivity monitoring

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm (v9 or later)

### Installation

1. Install dependencies:

```bash
npm install
```

2. Set up environment variables:

Copy the example environment file and update it with your configuration:

```bash
cp .env.example .env
```

3. Start the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Development

### Directory Structure

- `src/app`: Next.js app router pages
- `src/components`: Reusable UI components
- `src/lib`: Utility functions and API clients

### Building

```bash
npm run build
```

### Testing

```bash
npm run test
```

## Customization

### Styling

This project uses Tailwind CSS for styling. You can customize the theme in `tailwind.config.js`.

### API Integration

The API client is configured in `src/lib/api.ts`. You can modify it to connect to your backend API.

### Strapi CMS Integration

This project integrates with Strapi CMS for content management. The Strapi client is configured in `src/lib/strapi.ts`.

#### Configuration

Set the following environment variables to configure the Strapi integration:

- `NEXT_PUBLIC_STRAPI_API_URL`: The URL of the Strapi API (default: http://localhost:1339)
- `STRAPI_API_TOKEN`: The API token for authenticating with Strapi

#### Testing

You can test the Strapi integration by visiting the admin dashboard at [http://localhost:3000/admin/strapi-test](http://localhost:3000/admin/strapi-test).

### Multi-Tenant Support

This project supports multi-tenancy through the `src/lib/tenant.ts` module. You can set the tenant ID in the admin dashboard at [http://localhost:3000/admin](http://localhost:3000/admin).

### Error Handling

The application implements robust error handling with generic error pages instead of mock data fallbacks:

#### Error Page Component

The `ErrorPage` component (`src/components/ErrorPage.tsx`) provides a consistent error experience with:

- Dynamic status codes (400, 401, 403, 404, 500, 502, 503, 504)
- Contextual error messages and icons
- Retry functionality for recoverable errors
- Navigation options (back button, home link)
- Contact support links

#### Error Handling Logic

The error handling system (`src/lib/error.ts`) includes:

- `handleApiError()`: Standardizes error objects from various sources
- `shouldShowErrorPage()`: Determines when to show error pages vs. warnings
- `getErrorPageStatusCode()`: Maps errors to appropriate HTTP status codes

#### Error Page Behavior

- **Network Errors (503)**: Shows error page with retry option
- **Server Errors (5xx)**: Shows error page with retry option
- **Authentication Errors (401/403)**: Shows error page with login redirect
- **Client Errors (4xx)**: Shows warning messages, not full error pages
- **Non-critical Components**: Banners and carousels use fallback data

#### Testing Error Handling

Visit [http://localhost:3000/test-error](http://localhost:3000/test-error) to test different error scenarios and see how the application responds.

## Mock Service Worker (MSW) Integration

The application includes MSW for realistic API mocking during development and testing, providing network-level request interception.

### MSW Features

- **Network-Level Mocking**: Intercepts actual HTTP requests at the service worker level
- **Realistic Testing**: Provides authentic network behavior with delays and responses
- **Development Control**: Easy enable/disable functionality via UI or environment variables
- **Multi-Tenant Support**: Handles tenant-specific data with `x-tenant-id` headers
- **Runtime Toggle**: Switch between real API and mock data without restart

### MSW Configuration

#### Environment Variables

```bash
# Enable/disable MSW globally
NEXT_PUBLIC_MSW_ENABLED=false  # Set to 'true' to enable by default
```

#### Runtime Control

- **MSW Control Component**: Bottom-right corner widget (development only)
- **Local Storage**: Persists MSW preference across browser sessions
- **Visual Indicators**: Shows current MSW status (Active/Inactive)

### MSW API Endpoints

The mock server provides the following endpoints:

- `GET /health` - Health check endpoint
- `GET /store/products` - Products listing with pagination and tenant filtering
- `GET /store/products/:id` - Single product details with 404 handling
- `GET /store/collections` - Product collections by tenant
- `POST /store/auth` - Authentication endpoint with mock tokens

### Mock Data

MSW provides realistic product data with:

- **Product Images**: Using Picsum Photos for variety
- **Tenant Isolation**: Products filtered by `x-tenant-id` header
- **Pagination Support**: Limit and offset parameters
- **Error Simulation**: 404 responses for non-existent products

### Testing MSW

#### MSW Test Dashboard

Visit [http://localhost:3000/test-msw](http://localhost:3000/test-msw) to:

- View current MSW status
- Test different API endpoints
- Monitor response times and status codes
- View detailed response data

#### Manual Testing

1. **Enable MSW**: Use the control widget or set environment variable
2. **Test Products**: Visit `/products` to see mock data
3. **Test Errors**: Try non-existent product IDs
4. **Compare Modes**: Toggle MSW on/off to compare real vs. mock data

### MSW vs Real API

| Feature           | MSW Enabled             | MSW Disabled             |
| ----------------- | ----------------------- | ------------------------ |
| Data Source       | Mock data               | Real backend API         |
| Network Behavior  | Simulated delays        | Actual network calls     |
| Error Handling    | Mock error responses    | Real error responses     |
| Development Speed | Fast, no backend needed | Requires running backend |
| Testing           | Predictable responses   | Real-world scenarios     |

### Integration with Error Handling

MSW works seamlessly with the application's error handling system:

- **Network Errors**: When MSW is disabled and backend unavailable
- **Mock Errors**: When MSW simulates error responses
- **Graceful Fallback**: Error pages work in both modes

## Deployment

The frontend can be deployed to Vercel or any other Next.js-compatible hosting service.

```bash
npm run build
npm run start
```
