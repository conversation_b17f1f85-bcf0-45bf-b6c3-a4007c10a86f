/* Global styles for the ONDC Seller Platform */

:root {
  --primary-color: #3B82F6;
  --secondary-color: #1E40AF;
  --text-color: #333333;
  --light-gray: #F3F4F6;
  --medium-gray: #E5E7EB;
  --dark-gray: #9CA3AF;
  --success-color: #10B981;
  --error-color: #EF4444;
  --warning-color: #F59E0B;
  --info-color: #3B82F6;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html,
body {
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  color: var(--text-color);
  line-height: 1.6;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

button {
  cursor: pointer;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.error-container {
  padding: 20px;
  margin: 20px 0;
  background-color: #FEE2E2;
  border: 1px solid #EF4444;
  border-radius: 4px;
  color: #B91C1C;
}

/* Header styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid var(--medium-gray);
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: var(--primary-color);
}

.nav {
  display: flex;
  gap: 20px;
}

.nav a {
  color: var(--text-color);
  text-decoration: none;
}

.nav a:hover {
  color: var(--primary-color);
}

/* Footer styles */
.footer {
  margin-top: 40px;
  padding: 20px 0;
  border-top: 1px solid var(--medium-gray);
  text-align: center;
  color: var(--dark-gray);
}

/* Button styles */
.button {
  display: inline-block;
  padding: 8px 16px;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.button:hover {
  background-color: var(--secondary-color);
}

.button-secondary {
  background-color: white;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.button-secondary:hover {
  background-color: var(--light-gray);
}

/* Form styles */
.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--medium-gray);
  border-radius: 4px;
  font-size: 1rem;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

/* Card styles */
.card {
  border: 1px solid var(--medium-gray);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background-color: white;
}

.card-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 10px;
}

/* Alert styles */
.alert {
  padding: 12px 16px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.alert-success {
  background-color: #D1FAE5;
  border: 1px solid #10B981;
  color: #065F46;
}

.alert-error {
  background-color: #FEE2E2;
  border: 1px solid #EF4444;
  color: #B91C1C;
}

.alert-warning {
  background-color: #FEF3C7;
  border: 1px solid #F59E0B;
  color: #92400E;
}

.alert-info {
  background-color: #DBEAFE;
  border: 1px solid #3B82F6;
  color: #1E40AF;
}

/* Grid styles */
.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .nav {
    margin-top: 10px;
  }
  
  .grid {
    grid-template-columns: 1fr;
  }
}
