// API Response Types

// Common Types
export interface PaginationParams {
  limit?: number;
  offset?: number;
}

export interface PaginatedResponse {
  limit: number;
  offset: number;
  count: number;
}

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

// Product Types
export interface ProductVariant {
  id: string;
  title: string;
  sku?: string;
  ean?: string;
  upc?: string;
  barcode?: string;
  inventory_quantity: number;
  allow_backorder: boolean;
  manage_inventory: boolean;
  hs_code?: string;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  options: ProductOption[];
  prices: ProductPrice[];
  created_at: string;
  updated_at: string;
}

export interface ProductOption {
  id: string;
  value: string;
  option_id: string;
  option: {
    id: string;
    title: string;
  };
}

export interface ProductPrice {
  id: string;
  currency_code: string;
  amount: number;
  min_quantity?: number;
  max_quantity?: number;
  price_list_id?: string;
  region_id?: string;
}

export interface ProductImage {
  id: string;
  url: string;
}

export interface Product {
  id: string;
  title: string;
  subtitle?: string;
  description?: string;
  handle?: string;
  is_giftcard: boolean;
  status: string;
  thumbnail?: string;
  profile_id?: string;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  hs_code?: string;
  origin_country?: string;
  mid_code?: string;
  material?: string;
  collection_id?: string;
  collection?: any;
  type_id?: string;
  type?: any;
  tags?: any[];
  discountable: boolean;
  external_id?: string;
  metadata?: Record<string, any>;
  variants: ProductVariant[];
  options: any[];
  images: ProductImage[];
  categories?: any[];
  created_at: string;
  updated_at: string;
}

export interface ProductQueryParams extends PaginationParams {
  q?: string;
  collection_id?: string;
  tags?: string[];
  title?: string;
  description?: string;
  handle?: string;
  is_giftcard?: boolean;
  type_id?: string;
  category_id?: string;
  include_category_children?: boolean;
  created_at?: {
    lt?: string;
    gt?: string;
    lte?: string;
    gte?: string;
  };
  updated_at?: {
    lt?: string;
    gt?: string;
    lte?: string;
    gte?: string;
  };
}

export interface ProductResponse extends PaginatedResponse {
  products: Product[];
}

// Inventory Types
export interface InventoryItem {
  id: string;
  sku?: string;
  origin_country?: string;
  hs_code?: string;
  mid_code?: string;
  material?: string;
  weight?: number;
  length?: number;
  height?: number;
  width?: number;
  requires_shipping: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface InventoryLevel {
  id: string;
  inventory_item_id: string;
  location_id: string;
  stocked_quantity: number;
  reserved_quantity: number;
  incoming_quantity: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface InventoryUpdateData {
  stocked_quantity?: number;
  incoming_quantity?: number;
  metadata?: Record<string, any>;
}

export interface InventoryResponse {
  inventory_item: InventoryItem;
}

export interface InventoryLevelResponse {
  inventory_level: InventoryLevel;
}

// Order Types
export interface Address {
  first_name: string;
  last_name: string;
  phone?: string;
  company?: string;
  address_1: string;
  address_2?: string;
  city: string;
  province?: string;
  postal_code: string;
  country_code: string;
  metadata?: Record<string, any>;
}

export interface LineItem {
  variant_id: string;
  quantity: number;
  metadata?: Record<string, any>;
}

export interface OrderCreateData {
  email: string;
  billing_address: Address;
  shipping_address: Address;
  items: LineItem[];
  region_id: string;
  customer_id?: string;
  payment_method?: {
    provider_id: string;
    data?: Record<string, any>;
  };
  shipping_method?: {
    provider_id: string;
    profile_id: string;
    price: number;
    data?: Record<string, any>;
  };
  metadata?: Record<string, any>;
}

export interface OrderItem {
  id: string;
  title: string;
  description?: string;
  thumbnail?: string;
  quantity: number;
  variant: {
    id: string;
    title: string;
    sku?: string;
    product: {
      id: string;
      title: string;
    };
  };
  unit_price: number;
  subtotal: number;
  total: number;
  tax_total: number;
  discount_total: number;
  adjustments: any[];
  tax_lines: any[];
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  status: string;
  fulfillment_status: string;
  payment_status: string;
  display_id: string;
  cart_id?: string;
  customer_id?: string;
  customer?: any;
  email: string;
  billing_address_id?: string;
  billing_address?: Address;
  shipping_address_id?: string;
  shipping_address?: Address;
  region_id: string;
  region?: any;
  currency_code: string;
  tax_rate?: number;
  items: OrderItem[];
  payments: any[];
  fulfillments: any[];
  returns: any[];
  claims: any[];
  refunds: any[];
  swaps: any[];
  draft_order_id?: string;
  draft_order?: any;
  subtotal: number;
  tax_total: number;
  shipping_total: number;
  discount_total: number;
  gift_card_total: number;
  total: number;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface OrderResponse {
  order: Order;
}

export interface OrdersResponse extends PaginatedResponse {
  orders: Order[];
}

// Auth Types
export interface LoginData {
  email: string;
  password: string;
}

export interface User {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  phone?: string;
  has_account: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  user: User;
  token?: string;
}

// Tenant Types
export interface Tenant {
  id: string;
  name: string;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface TenantResponse {
  tenant: Tenant;
}

export interface TenantsResponse extends PaginatedResponse {
  tenants: Tenant[];
}
