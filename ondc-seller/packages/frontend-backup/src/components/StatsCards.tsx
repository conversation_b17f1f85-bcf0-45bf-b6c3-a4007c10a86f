'use client';

import { useState, useEffect } from 'react';

interface StatItem {
  id: string;
  title: string;
  value: string;
  change: string;
  changeType: 'increase' | 'decrease' | 'neutral';
  icon: string;
  color: string;
}

const StatsCards = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [animatedValues, setAnimatedValues] = useState<Record<string, number>>({});

  const stats: StatItem[] = [
    {
      id: 'products',
      title: 'Total Products',
      value: '1,247',
      change: '+12%',
      changeType: 'increase',
      icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
      color: 'blue'
    },
    {
      id: 'orders',
      title: 'Monthly Orders',
      value: '3,892',
      change: '+23%',
      changeType: 'increase',
      icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
      color: 'green'
    },
    {
      id: 'revenue',
      title: 'Monthly Revenue',
      value: '₹2,45,678',
      change: '+18%',
      changeType: 'increase',
      icon: 'M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1',
      color: 'purple'
    },
    {
      id: 'customers',
      title: 'Active Customers',
      value: '8,456',
      change: '+7%',
      changeType: 'increase',
      icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z',
      color: 'orange'
    }
  ];

  useEffect(() => {
    setIsVisible(true);
    
    // Animate numbers
    const targetValues: Record<string, number> = {
      products: 1247,
      orders: 3892,
      revenue: 245678,
      customers: 8456
    };

    const animateValue = (id: string, target: number) => {
      let current = 0;
      const increment = target / 60; // 60 frames for 1 second animation
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          current = target;
          clearInterval(timer);
        }
        setAnimatedValues(prev => ({ ...prev, [id]: Math.floor(current) }));
      }, 16); // ~60fps
    };

    // Start animations with delays
    setTimeout(() => animateValue('products', targetValues.products), 500);
    setTimeout(() => animateValue('orders', targetValues.orders), 700);
    setTimeout(() => animateValue('revenue', targetValues.revenue), 900);
    setTimeout(() => animateValue('customers', targetValues.customers), 1100);
  }, []);

  const formatValue = (id: string, originalValue: string) => {
    const animatedValue = animatedValues[id];
    if (animatedValue === undefined) return '0';
    
    if (id === 'revenue') {
      return `₹${animatedValue.toLocaleString()}`;
    }
    return animatedValue.toLocaleString();
  };

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: 'from-blue-500 to-blue-600 text-blue-600',
      green: 'from-green-500 to-green-600 text-green-600',
      purple: 'from-purple-500 to-purple-600 text-purple-600',
      orange: 'from-orange-500 to-orange-600 text-orange-600'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <div className="py-8">
      <div className="text-center mb-8">
        <h2 className={`text-3xl font-bold text-gray-900 mb-2 transition-all duration-600 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}>
          Business Overview
        </h2>
        <p className={`text-gray-600 transition-all duration-600 delay-200 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}>
          Track your performance with real-time metrics
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div
            key={stat.id}
            className={`stats-card bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 group ${
              isVisible ? 'animate-fade-in-up' : 'opacity-0'
            }`}
            style={{ animationDelay: `${index * 150}ms` }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gradient-to-br ${getColorClasses(stat.color).split(' ').slice(0, 2).join(' ')} bg-opacity-10`}>
                <svg 
                  className={`h-6 w-6 ${getColorClasses(stat.color).split(' ')[2]}`} 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={stat.icon}
                  />
                </svg>
              </div>
              <div className={`flex items-center text-sm font-medium ${
                stat.changeType === 'increase' ? 'text-green-600' : 
                stat.changeType === 'decrease' ? 'text-red-600' : 'text-gray-600'
              }`}>
                {stat.changeType === 'increase' && (
                  <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 17l9.2-9.2M17 17V7H7" />
                  </svg>
                )}
                {stat.changeType === 'decrease' && (
                  <svg className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 7l-9.2 9.2M7 7v10h10" />
                  </svg>
                )}
                {stat.change}
              </div>
            </div>
            
            <div className="mb-2">
              <div className="stats-number text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">
                {formatValue(stat.id, stat.value)}
              </div>
            </div>
            
            <div className="text-sm font-medium text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
              {stat.title}
            </div>

            {/* Progress bar for visual appeal */}
            <div className="mt-4 progress-bar h-1">
              <div 
                className={`progress-fill bg-gradient-to-r ${getColorClasses(stat.color).split(' ').slice(0, 2).join(' ')}`}
                style={{ 
                  width: isVisible ? '100%' : '0%',
                  transitionDelay: `${index * 150 + 500}ms`
                }}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default StatsCards;
