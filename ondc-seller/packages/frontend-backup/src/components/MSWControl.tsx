'use client';

import { useState, useEffect } from 'react';
import { enableMSW, disableMSW, isMSWEnabledInStorage } from '../lib/msw';

/**
 * MSW Control Component
 * Provides a UI to enable/disable Mock Service Worker in development
 */
export default function MSWControl() {
  const [isMSWActive, setIsMSWActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // Check if MSW is enabled in localStorage
    setIsMSWActive(isMSWEnabledInStorage());
  }, []);

  const handleToggleMSW = async () => {
    setIsLoading(true);
    try {
      if (isMSWActive) {
        const success = await disableMSW();
        if (success) {
          setIsMSWActive(false);
          // Reload page to apply changes
          window.location.reload();
        }
      } else {
        const success = await enableMSW();
        if (success) {
          setIsMSWActive(true);
          // Reload page to apply changes
          window.location.reload();
        }
      }
    } catch (error) {
      console.error('Failed to toggle MSW:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white border border-gray-300 rounded-lg shadow-lg p-4 max-w-sm">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-900">Mock Service Worker</h3>
          <div className={`w-2 h-2 rounded-full ${isMSWActive ? 'bg-green-500' : 'bg-red-500'}`}></div>
        </div>
        
        <p className="text-xs text-gray-600 mb-3">
          {isMSWActive 
            ? 'MSW is intercepting API calls with mock data' 
            : 'MSW is disabled, using real API endpoints'
          }
        </p>
        
        <button
          onClick={handleToggleMSW}
          disabled={isLoading}
          className={`w-full px-3 py-2 text-xs font-medium rounded transition-colors ${
            isMSWActive
              ? 'bg-red-100 text-red-700 hover:bg-red-200 disabled:bg-red-50'
              : 'bg-green-100 text-green-700 hover:bg-green-200 disabled:bg-green-50'
          } disabled:cursor-not-allowed`}
        >
          {isLoading ? (
            <span className="flex items-center justify-center">
              <svg className="animate-spin -ml-1 mr-2 h-3 w-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Loading...
            </span>
          ) : (
            `${isMSWActive ? 'Disable' : 'Enable'} MSW`
          )}
        </button>
        
        <div className="mt-2 text-xs text-gray-500">
          Status: <span className={`font-medium ${isMSWActive ? 'text-green-600' : 'text-red-600'}`}>
            {isMSWActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>
    </div>
  );
}
