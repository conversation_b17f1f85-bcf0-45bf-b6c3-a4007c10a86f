'use client';

import React from 'react';
import Link from 'next/link';

interface ErrorPageProps {
  statusCode?: number;
  title?: string;
  message?: string;
  showRetry?: boolean;
  onRetry?: () => void;
  showBackButton?: boolean;
  backUrl?: string;
}

const getErrorDetails = (statusCode: number) => {
  switch (statusCode) {
    case 400:
      return {
        title: 'Bad Request',
        message: 'The request could not be understood by the server.',
        icon: '⚠️'
      };
    case 401:
      return {
        title: 'Unauthorized',
        message: 'You need to be authenticated to access this resource.',
        icon: '🔒'
      };
    case 403:
      return {
        title: 'Forbidden',
        message: 'You do not have permission to access this resource.',
        icon: '🚫'
      };
    case 404:
      return {
        title: 'Not Found',
        message: 'The requested resource could not be found.',
        icon: '🔍'
      };
    case 500:
      return {
        title: 'Internal Server Error',
        message: 'Something went wrong on our end. Please try again later.',
        icon: '🔧'
      };
    case 502:
      return {
        title: 'Bad Gateway',
        message: 'The server received an invalid response from the upstream server.',
        icon: '🌐'
      };
    case 503:
      return {
        title: 'Service Unavailable',
        message: 'The service is temporarily unavailable. Please try again later.',
        icon: '⏰'
      };
    case 504:
      return {
        title: 'Gateway Timeout',
        message: 'The server did not receive a timely response from the upstream server.',
        icon: '⏱️'
      };
    default:
      return {
        title: 'Connection Error',
        message: 'Unable to connect to the server. Please check your connection and try again.',
        icon: '📡'
      };
  }
};

export function ErrorPage({
  statusCode = 500,
  title,
  message,
  showRetry = true,
  onRetry,
  showBackButton = true,
  backUrl = '/'
}: ErrorPageProps) {
  const errorDetails = getErrorDetails(statusCode);
  const displayTitle = title || errorDetails.title;
  const displayMessage = message || errorDetails.message;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="text-center">
            {/* Error Icon */}
            <div className="text-6xl mb-4">
              {errorDetails.icon}
            </div>
            
            {/* Status Code */}
            <div className="text-4xl font-bold text-gray-900 mb-2">
              {statusCode}
            </div>
            
            {/* Error Title */}
            <h1 className="text-2xl font-bold text-gray-900 mb-4">
              {displayTitle}
            </h1>
            
            {/* Error Message */}
            <p className="text-gray-600 mb-8">
              {displayMessage}
            </p>
            
            {/* Action Buttons */}
            <div className="space-y-4">
              {showRetry && onRetry && (
                <button
                  onClick={onRetry}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Try Again
                </button>
              )}
              
              {showBackButton && (
                <Link
                  href={backUrl}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                  </svg>
                  Go Back
                </Link>
              )}
            </div>
            
            {/* Additional Help */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500">
                If this problem persists, please{' '}
                <Link href="/contact" className="text-blue-600 hover:text-blue-500">
                  contact support
                </Link>
                .
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ErrorPage;
