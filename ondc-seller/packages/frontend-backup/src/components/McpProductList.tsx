'use client';

import { useEffect, useState } from 'react';
// Removed useGetProducts import - using direct fetch instead
import ClientTenantDisplay from './ClientTenantDisplay';
import AddToCartButton from './cart/AddToCartButton';

interface Variant {
  id: string;
  title: string;
  prices: Array<{
    amount: number;
    currency_code: string;
  }>;
  inventory_quantity: number;
}

interface Image {
  id: string;
  url: string;
}

interface Product {
  id: string;
  title: string;
  description: string | null;
  thumbnail: string | null;
  handle: string;
  status: string;
  collection_id: string;
  collection: {
    id: string;
    title: string;
    handle: string;
  };
  variants: Variant[];
  tenant_id?: string;
  images?: Image[];
}

interface McpProductListProps {
  categoryFilter?: string;
}

export default function McpProductList({ categoryFilter = 'all' }: McpProductListProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true); // Start with loading true
  const [error, setError] = useState<Error | null>(null);
  const [mounted, setMounted] = useState(false);

  // Mount effect
  useEffect(() => {
    console.log('McpProductList component mounted');
    setMounted(true);
  }, []);

  useEffect(() => {
    // Only fetch products after component is mounted to avoid hydration issues
    if (!mounted) {
      console.log('Component not mounted yet, skipping fetch');
      return;
    }

    console.log('McpProductList useEffect triggered, categoryFilter:', categoryFilter);

    const fetchProducts = async () => {
      setIsLoading(true);
      setError(null);

      try {
        console.log('Fetching products from MCP API...');

        const response = await fetch('/api/mcp', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-tenant-id': 'default',
          },
          body: JSON.stringify({
            tool: 'getProducts',
            params: {
              limit: 20,
              offset: 0,
              category: categoryFilter !== 'all' ? categoryFilter : undefined,
            },
          }),
        });

        console.log('MCP API response status:', response.status);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('MCP API response data:', data);

        if (data.products && Array.isArray(data.products)) {
          console.log('Setting products:', data.products.length, 'products');
          setProducts(data.products);
        } else {
          console.log('No products in response or invalid format:', data);
          setProducts([]);
        }
      } catch (err) {
        console.error('Error fetching products:', err);
        setError(err instanceof Error ? err : new Error('Unknown error'));
        setProducts([]); // Set empty array on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchProducts();
  }, [mounted, categoryFilter]);

  const formatPrice = (variant: Variant) => {
    if (!variant.prices || variant.prices.length === 0) {
      return 'N/A';
    }

    const price = variant.prices[0];
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: price.currency_code.toUpperCase(),
    }).format(price.amount / 100);
  };

  // Function to get discount percentage (for demo purposes)
  const getDiscountPercentage = (variant: Variant) => {
    // This is just for UI demonstration - in a real app, you'd have actual discount data
    if (!variant.prices || variant.prices.length === 0) return null;

    // Generate a random discount between 5% and 30% for some products
    const productId = parseInt(variant.id.replace(/\D/g, ''), 10);
    if (productId % 3 === 0) {
      // Apply discount to every third product
      return Math.floor(Math.random() * 26) + 5; // Random discount between 5% and 30%
    }
    return null;
  };

  // Function to render tenant badge with appropriate color
  const getTenantBadgeColor = (tenantId: string) => {
    switch (tenantId) {
      case 'default':
        return 'bg-gray-100 text-gray-800';
      case 'tenant1':
        return 'bg-blue-100 text-blue-800';
      case 'tenant2':
        return 'bg-amber-100 text-amber-800';
      default:
        return 'bg-purple-100 text-purple-800';
    }
  };

  // Debug logging for render
  console.log(
    'McpProductList render - mounted:',
    mounted,
    'isLoading:',
    isLoading,
    'products.length:',
    products.length,
    'error:',
    error
  );

  return (
    <div>
      <div className="mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center">
          <div className="flex-shrink-0 bg-blue-100 rounded-full p-2">
            <svg
              className="h-6 w-6 text-blue-600"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900">Multi-Tenant Product Catalog</h3>
            <p className="text-sm text-gray-600">
              Current Tenant: <ClientTenantDisplay />
            </p>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="flex flex-col justify-center items-center h-64 bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          <span className="mt-4 text-gray-700">Loading products...</span>
        </div>
      ) : error ? (
        <div
          className="bg-white border-l-4 border-red-500 text-red-700 p-6 rounded-lg shadow-sm"
          role="alert"
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-red-500"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-lg font-medium">Error loading products</p>
              <p className="text-sm">{error.message}</p>
            </div>
          </div>
          <div className="mt-4">
            <button
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              onClick={() => window.location.reload()}
            >
              <svg
                className="mr-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                />
              </svg>
              Retry
            </button>
          </div>
        </div>
      ) : products.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <svg
            className="h-16 w-16 text-gray-400"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"
            />
          </svg>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No products found</h3>
          <p className="mt-1 text-sm text-gray-500">
            Try switching to a different tenant or category.
          </p>
        </div>
      ) : (
        <>
          <p className="text-sm text-gray-500 mb-4">Showing {products.length} products</p>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {products.map(product => {
              const hasDiscount =
                product.variants &&
                product.variants.length > 0 &&
                getDiscountPercentage(product.variants[0]);
              const discountPercentage = hasDiscount
                ? getDiscountPercentage(product.variants[0])
                : null;

              return (
                <div
                  key={product.id}
                  className="group relative bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-md"
                >
                  {/* Product image */}
                  <div className="relative h-60 bg-gray-100 overflow-hidden">
                    {product.thumbnail ? (
                      <img
                        src={product.thumbnail}
                        alt={product.title}
                        className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-300"
                        onError={e => {
                          e.currentTarget.src = '/images/placeholder.png';
                        }}
                      />
                    ) : (
                      <div className="h-full flex items-center justify-center">
                        <svg
                          className="h-16 w-16 text-gray-300"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={1}
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                    )}

                    {/* Discount badge */}
                    {discountPercentage && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                        {discountPercentage}% OFF
                      </div>
                    )}

                    {/* Quick actions */}
                    <div className="absolute inset-0 bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <div className="flex space-x-2">
                        <button className="bg-white p-2 rounded-full shadow hover:bg-blue-50 transition-colors duration-200">
                          <svg
                            className="h-5 w-5 text-blue-600"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                            />
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                            />
                          </svg>
                        </button>
                        <button className="bg-white p-2 rounded-full shadow hover:bg-blue-50 transition-colors duration-200">
                          <svg
                            className="h-5 w-5 text-blue-600"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Product details */}
                  <div className="p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <h3 className="text-lg font-medium text-gray-900 line-clamp-1">
                        {product.title}
                      </h3>
                      {product.tenant_id && (
                        <span
                          className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getTenantBadgeColor(product.tenant_id)}`}
                        >
                          {product.tenant_id}
                        </span>
                      )}
                    </div>

                    <p className="text-sm text-gray-500 mb-3 line-clamp-2">{product.description}</p>

                    {/* Price and stock */}
                    <div className="flex justify-between items-center mb-3">
                      <div>
                        {discountPercentage ? (
                          <div className="flex items-center">
                            <span className="text-lg font-bold text-gray-900">
                              {product.variants && product.variants.length > 0
                                ? formatPrice(product.variants[0])
                                : 'N/A'}
                            </span>
                            <span className="ml-2 text-sm text-gray-500 line-through">
                              {product.variants && product.variants.length > 0
                                ? new Intl.NumberFormat('en-IN', {
                                    style: 'currency',
                                    currency:
                                      product.variants[0].prices[0].currency_code.toUpperCase(),
                                  }).format(
                                    (product.variants[0].prices[0].amount *
                                      (100 + discountPercentage)) /
                                      100 /
                                      100
                                  )
                                : ''}
                            </span>
                          </div>
                        ) : (
                          <span className="text-lg font-bold text-gray-900">
                            {product.variants && product.variants.length > 0
                              ? formatPrice(product.variants[0])
                              : 'N/A'}
                          </span>
                        )}
                      </div>

                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                          product.variants &&
                          product.variants.length > 0 &&
                          product.variants[0].inventory_quantity > 0
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {product.variants &&
                        product.variants.length > 0 &&
                        product.variants[0].inventory_quantity > 0
                          ? `In Stock (${product.variants[0].inventory_quantity})`
                          : 'Out of Stock'}
                      </span>
                    </div>

                    {/* Add to cart button */}
                    {product.variants && product.variants.length > 0 && (
                      <AddToCartButton
                        variantId={product.variants[0].id}
                        className="w-full"
                        showQuantity={false}
                        maxQuantity={Math.min(product.variants[0].inventory_quantity, 10)}
                      />
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </>
      )}
    </div>
  );
}
