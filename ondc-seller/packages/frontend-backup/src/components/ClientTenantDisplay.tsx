'use client';

import { useEffect, useState } from 'react';
import { getCurrentTenantId } from '../lib/tenant';

export default function ClientTenantDisplay() {
  const [tenantId, setTenantId] = useState<string>('');

  useEffect(() => {
    // Only set the tenant ID after the component has mounted
    setTenantId(getCurrentTenantId());
  }, []);

  return (
    <span className="font-mono font-bold">{tenantId}</span>
  );
}
