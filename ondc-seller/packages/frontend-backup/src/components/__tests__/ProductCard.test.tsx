import { render, screen } from '@testing-library/react';
import ProductCard from '../ProductCard';

// Mock Next.js Link component
jest.mock('next/link', () => {
  return ({ children, href }: { children: React.ReactNode; href: string }) => {
    return <a href={href}>{children}</a>;
  };
});

describe('ProductCard', () => {
  const mockProduct = {
    id: '1',
    title: 'Test Product',
    description: 'Test Description',
    thumbnail: 'https://example.com/image.jpg',
    price: { amount: 1000, currency_code: 'inr' },
    inventory_quantity: 10,
  };

  it('renders product information correctly', () => {
    render(
      <ProductCard
        id={mockProduct.id}
        title={mockProduct.title}
        description={mockProduct.description}
        thumbnail={mockProduct.thumbnail}
        price={mockProduct.price}
        inventory_quantity={mockProduct.inventory_quantity}
      />
    );
    
    // Check if product title is rendered
    expect(screen.getByText('Test Product')).toBeInTheDocument();
    
    // Check if product description is rendered
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    
    // Check if inventory status is rendered
    expect(screen.getByText('In Stock (10)')).toBeInTheDocument();
    
    // Check if links are rendered
    expect(screen.getByText('View')).toHaveAttribute('href', '/products/1');
    expect(screen.getByText('Edit')).toHaveAttribute('href', '/products/1/edit');
  });

  it('shows out of stock when inventory is 0', () => {
    render(
      <ProductCard
        id={mockProduct.id}
        title={mockProduct.title}
        description={mockProduct.description}
        thumbnail={mockProduct.thumbnail}
        price={mockProduct.price}
        inventory_quantity={0}
      />
    );
    
    expect(screen.getByText('Out of Stock')).toBeInTheDocument();
  });

  it('renders without thumbnail', () => {
    render(
      <ProductCard
        id={mockProduct.id}
        title={mockProduct.title}
        description={mockProduct.description}
        thumbnail={null}
        price={mockProduct.price}
        inventory_quantity={mockProduct.inventory_quantity}
      />
    );
    
    // Should still render product information
    expect(screen.getByText('Test Product')).toBeInTheDocument();
  });
});
