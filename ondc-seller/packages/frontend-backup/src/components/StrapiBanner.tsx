import React from 'react';
import Image from 'next/image';
import Link from 'next/link';

interface Banner {
  id: number;
  attributes: {
    title: string;
    subtitle: string;
    link: string;
    image: {
      data: {
        attributes: {
          url: string;
          width: number;
          height: number;
          alternativeText: string;
        };
      };
    };
  };
}

interface StrapiBannerProps {
  banner: Banner;
}

const StrapiBanner: React.FC<StrapiBannerProps> = ({ banner }) => {
  if (!banner) return null;

  const { title, subtitle, link, image } = banner.attributes;
  const imageUrl = image?.data?.attributes?.url
    ? `${process.env.NEXT_PUBLIC_STRAPI_URL}${image.data.attributes.url}`
    : '/placeholder-banner.jpg';
  const imageAlt = image?.data?.attributes?.alternativeText || title;

  return (
    <div className="relative w-full h-[400px] overflow-hidden rounded-lg">
      <Image
        src={imageUrl}
        alt={imageAlt}
        fill
        className="object-cover"
        priority
      />
      <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex flex-col justify-center px-8 md:px-12">
        <h2 className="text-2xl md:text-4xl font-bold text-white mb-2">{title}</h2>
        <p className="text-lg md:text-xl text-white/90 mb-6 max-w-md">{subtitle}</p>
        {link && (
          <Link
            href={link}
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-6 rounded-md transition-colors duration-300 max-w-fit"
          >
            Shop Now
          </Link>
        )}
      </div>
    </div>
  );
};

export default StrapiBanner;
