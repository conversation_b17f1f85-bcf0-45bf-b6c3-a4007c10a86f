'use client';

import Link from 'next/link';
import Image from 'next/image';
import { productCategories } from '../data/categories';

interface NoProductsFoundProps {
  message?: string;
  showHomeLink?: boolean;
  showClearFiltersButton?: boolean;
  onClearFilters?: () => void;
  customAction?: React.ReactNode;
  categoryId?: string;
  subcategoryId?: string;
}

export default function NoProductsFound({
  message = 'No products found',
  showHomeLink = true,
  showClearFiltersButton = false,
  onClearFilters,
  customAction,
  categoryId,
  subcategoryId,
}: NoProductsFoundProps) {
  // Get popular categories to suggest as alternatives
  const popularCategories = productCategories.slice(0, 3);

  // Get current category and subcategory names if IDs are provided
  const currentCategory = categoryId ? productCategories.find(cat => cat.id === categoryId) : null;

  const currentSubcategory =
    currentCategory && subcategoryId
      ? currentCategory.subcategories.find(sub => sub.id === subcategoryId)
      : null;

  // Customize message based on context
  const contextMessage = currentSubcategory
    ? `No products available in ${currentSubcategory.name}`
    : currentCategory
      ? `No products available in ${currentCategory.name}`
      : message;

  return (
    <div className="text-center py-10 bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Empty state illustration */}
      <div className="relative w-40 h-40 mx-auto mb-4">
        <Image
          src="/images/empty-box.png"
          alt="No products found"
          fill
          className="object-contain"
          onError={e => {
            // Fallback to SVG if image fails to load
            e.currentTarget.style.display = 'none';
            document.getElementById('fallback-svg')?.classList.remove('hidden');
          }}
        />
        {/* Fallback SVG */}
        <div id="fallback-svg" className="hidden w-full h-full flex items-center justify-center">
          <svg
            className="h-24 w-24 text-gray-300"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
            />
          </svg>
        </div>
      </div>

      {/* Message */}
      <h3 className="text-xl font-medium text-gray-900 mb-2">No Products Available</h3>
      <p className="text-gray-500 max-w-md mx-auto">{contextMessage}</p>

      {/* Action buttons */}
      <div className="mt-6 flex flex-col sm:flex-row items-center justify-center gap-4">
        {showClearFiltersButton && onClearFilters && (
          <button
            onClick={onClearFilters}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg
              className="mr-2 -ml-1 h-5 w-5"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
            Clear Filters
          </button>
        )}

        {showHomeLink && (
          <Link
            href="/"
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg
              className="mr-2 -ml-1 h-5 w-5 text-gray-500"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
              />
            </svg>
            Back to Home
          </Link>
        )}

        {customAction && customAction}
      </div>

      {/* Suggested categories */}
      {popularCategories.length > 0 && (
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-4">Browse Popular Categories</h4>
          <div className="flex flex-wrap justify-center gap-3">
            {popularCategories.map(category => (
              <Link
                key={category.id}
                href={category.href}
                className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-800 rounded-full text-sm font-medium transition-colors duration-200"
              >
                {category.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
