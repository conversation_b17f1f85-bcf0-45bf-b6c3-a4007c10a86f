'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { mockProducts } from '../data/mockProducts';
import { useCartContext } from '../providers/CartProvider';
import { useToast } from '../providers/ToastProvider';
import { MOCK_PRODUCTS } from '../lib/msw-data';

const ProductShowcase = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const { addItem } = useCartContext();
  const { showSuccess, showError } = useToast();

  // Get featured products (first 6)
  const featuredProducts = mockProducts.slice(0, 6);
  const productsPerSlide = 3;
  const totalSlides = Math.ceil(featuredProducts.length / productsPerSlide);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % totalSlides);
    }, 4000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, totalSlides]);

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % totalSlides);
    setIsAutoPlaying(false);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + totalSlides) % totalSlides);
    setIsAutoPlaying(false);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
  };

  const getCurrentProducts = () => {
    const startIndex = currentSlide * productsPerSlide;
    return featuredProducts.slice(startIndex, startIndex + productsPerSlide);
  };

  // Helper to get variantId for a mock product
  const getVariantIdForProduct = (productId: string) => {
    const mswProduct = MOCK_PRODUCTS.find(p => p.id === productId);
    return mswProduct?.variants?.[0]?.id || productId; // fallback to productId if not found
  };

  // Add to cart handler for featured products
  const handleAddToCart = async (product: any) => {
    try {
      // Use the correct variantId from MSW data
      const variantId = getVariantIdForProduct(product.id);
      await addItem(variantId, 1);
      showSuccess('Added to cart!');
    } catch (err) {
      showError('Failed to add to cart.');
    }
  };

  return (
    <div className="py-8">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h2
            className={`text-3xl font-bold text-gray-900 mb-2 transition-all duration-600 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
          >
            Featured Products
          </h2>
          <p
            className={`text-gray-600 transition-all duration-600 delay-200 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
          >
            Discover our top-selling and trending products
          </p>
        </div>

        <div
          className={`flex items-center space-x-4 transition-all duration-600 delay-400 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
        >
          {/* Removed Auto-play toggle button */}

          {/* Removed Navigation controls (arrow icons) */}

          <Link href="/products" className="btn btn-outline group flex items-center">
            <span>View All</span>
            <svg
              className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>

      {/* Product carousel */}
      <div className="relative overflow-hidden rounded-xl">
        <div
          className="flex transition-transform duration-500 ease-in-out"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {Array.from({ length: totalSlides }).map((_, slideIndex) => (
            <div key={slideIndex} className="w-full flex-shrink-0">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 px-2">
                {featuredProducts
                  .slice(slideIndex * productsPerSlide, (slideIndex + 1) * productsPerSlide)
                  .map((product, productIndex) => (
                    <div
                      key={product.id}
                      className={`card-glow group transition-all duration-500 ${
                        isVisible ? 'animate-fade-in-up' : 'opacity-0'
                      }`}
                      style={{ animationDelay: `${productIndex * 150}ms` }}
                    >
                      <div className="relative h-64 bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
                        <Image
                          src={product.imageSrc}
                          alt={product.name}
                          fill
                          className="object-cover transition-all duration-500 group-hover:scale-110"
                        />
                        <div className="absolute top-4 left-4">
                          <span className="badge badge-success">Featured</span>
                        </div>
                        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg">
                            <svg
                              className="h-5 w-5 text-gray-700"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                              />
                            </svg>
                          </div>
                        </div>
                      </div>

                      <div className="p-6">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600 transition-colors duration-300 line-clamp-2">
                          <Link
                            href={{
                              pathname: `/products/${product.id}`,
                              query: { from: 'featured' },
                            }}
                          >
                            {product.name}
                          </Link>
                        </h3>
                        <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                          {product.description}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <span className="text-2xl font-bold text-blue-600">
                              ₹{product.price.toLocaleString()}
                            </span>
                            {product.originalPrice && (
                              <span className="text-sm text-gray-500 line-through">
                                ₹{product.originalPrice.toLocaleString()}
                              </span>
                            )}
                          </div>

                          <div className="flex items-center text-sm text-gray-500">
                            <svg
                              className="h-4 w-4 text-yellow-400 mr-1"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                            4.5 (128)
                          </div>
                        </div>

                        <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                          <button
                            className="w-full btn btn-primary"
                            onClick={() => handleAddToCart(product)}
                          >
                            Add to Cart
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Slide indicators */}
      <div className="flex justify-center mt-6 space-x-2">
        {Array.from({ length: totalSlides }).map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-3 h-3 rounded-full transition-all duration-300 ${
              index === currentSlide ? 'bg-blue-600 scale-125' : 'bg-gray-300 hover:bg-gray-400'
            }`}
          />
        ))}
      </div>
    </div>
  );
};

export default ProductShowcase;
