'use client';

import Link from 'next/link';
import AddToCartButton from './cart/AddToCartButton';
import { useToast } from '../providers/ToastProvider';

interface ProductPrice {
  amount: number;
  currency_code: string;
}

interface ProductVariant {
  id: string;
  title: string;
  prices: Array<{
    amount: number;
    currency_code: string;
  }>;
  inventory_quantity: number;
}

interface ProductProps {
  id: string;
  title: string;
  description: string | null;
  thumbnail: string | null;
  price: ProductPrice;
  inventory_quantity: number;
  variants?: ProductVariant[];
}

export default function ProductCard({
  id,
  title,
  description,
  thumbnail,
  price,
  inventory_quantity,
  variants,
}: ProductProps) {
  const { showSuccess, showError } = useToast();

  const formatPrice = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  // Get the first variant ID for the AddToCartButton
  // In a real scenario, you might want to handle multiple variants differently
  const defaultVariantId =
    variants && variants.length > 0 ? variants[0].id : `variant_${id}_default`;

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      {thumbnail && (
        <div className="h-48 bg-gray-200">
          <img src={thumbnail} alt={title} className="w-full h-full object-cover" />
        </div>
      )}
      <div className="p-4">
        <h2 className="text-xl font-semibold mb-2">
          <Link
            href={{ pathname: `/products/${id}`, query: { from: 'category' } }}
            className="hover:underline text-blue-700"
          >
            {title}
          </Link>
        </h2>
        <p className="text-gray-600 mb-2 line-clamp-2">{description}</p>
        <div className="flex justify-between items-center">
          <span className="text-lg font-bold">
            {formatPrice(price.amount, price.currency_code)}
          </span>
          <span className={`${inventory_quantity > 0 ? 'text-green-600' : 'text-red-600'}`}>
            {inventory_quantity > 0 ? `In Stock (${inventory_quantity})` : 'Out of Stock'}
          </span>
        </div>
        <div className="mt-4 space-y-2">
          {/* Add to Cart Button */}
          {inventory_quantity > 0 && (
            <AddToCartButton
              variantId={defaultVariantId}
              className="w-full"
              showQuantity={false}
              onAddToCart={() => {
                showSuccess(`Added "${title}" to cart!`);
              }}
            />
          )}

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Link
              href={`/products/${id}`}
              className="flex-1 bg-blue-600 text-white px-3 py-2 rounded text-sm hover:bg-blue-700 text-center"
            >
              View Details
            </Link>
            <Link
              href={`/products/${id}/edit`}
              className="flex-1 bg-gray-600 text-white px-3 py-2 rounded text-sm hover:bg-gray-700 text-center"
            >
              Edit
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
