'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { getBanners } from '../lib/strapi';
import { handleApiError, shouldShowErrorPage, getErrorPageStatusCode } from '../lib/error';
import { ApiError } from '../types/api';

// Banner item type
interface BannerItem {
  id: number;
  title: string;
  subtitle?: string;
  description?: string;
  cta_text?: string;
  cta_link?: string;
  image?: {
    url: string;
  };
  bgColor?: string;
}

// Default carousel items as fallback (using local images)
const DEFAULT_CAROUSEL_ITEMS = [
  {
    id: 1,
    title: 'Welcome to ONDC Seller Platform',
    subtitle: 'Connect with millions of buyers across India',
    description:
      'Connect with millions of buyers across India through the Open Network for Digital Commerce.',
    cta_text: 'Browse Products',
    cta_link: '/products',
    image: {
      url: '/images/placeholder.png',
    },
    bgColor: 'bg-blue-600',
  },
  {
    id: 2,
    title: 'Discover Quality Products',
    subtitle: 'From trusted sellers across the country',
    description: 'From trusted sellers across the country with quality assurance.',
    cta_text: 'Shop Now',
    cta_link: '/products',
    image: {
      url: '/images/placeholder.png',
    },
    bgColor: 'bg-purple-600',
  },
];

export default function HeroCarousel() {
  const [banners, setBanners] = useState<BannerItem[]>(DEFAULT_CAROUSEL_ITEMS);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ApiError | null>(null);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isPaused, setIsPaused] = useState(false);
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);
  const autoPlayRef = useRef<NodeJS.Timeout | null>(null);
  const totalSlides = banners.length;

  // Fetch banners from Strapi
  useEffect(() => {
    const fetchBanners = async () => {
      try {
        setLoading(true);
        console.log('Fetching banners from Strapi...');
        const data = await getBanners();

        if (data && data.items && data.items.length > 0) {
          console.log('Fetched banners from Strapi:', data.items);

          // Map Strapi banner data to carousel items
          const carouselItems = data.items.map((banner: any, index: number) => {
            // Assign a background color based on index
            const bgColors = ['bg-blue-600', 'bg-purple-600', 'bg-green-600', 'bg-orange-600'];
            const bgColor = bgColors[index % bgColors.length];

            return {
              ...banner,
              bgColor,
            };
          });

          setBanners(carouselItems);
          console.log('Successfully set banners from Strapi');
        } else {
          console.log('No banners found from Strapi, using default carousel items');
          // Keep the default items that are already set
        }
      } catch (err) {
        console.error('Error fetching banners from Strapi:', err);
        const apiError = handleApiError(err);
        setError(apiError);
        console.log('Using default carousel items due to Strapi error');
        // For banners, we keep the default items even on error since they're not critical
      } finally {
        setLoading(false);
      }
    };

    fetchBanners();
  }, []);

  // Minimum swipe distance (in px)
  const minSwipeDistance = 50;

  // Handle next slide
  const nextSlide = useCallback(() => {
    setCurrentSlide(prev => (prev === totalSlides - 1 ? 0 : prev + 1));
  }, [totalSlides]);

  // Handle previous slide
  const prevSlide = useCallback(() => {
    setCurrentSlide(prev => (prev === 0 ? totalSlides - 1 : prev - 1));
  }, [totalSlides]);

  // Go to a specific slide
  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  // Auto-play functionality
  useEffect(() => {
    if (!isPaused) {
      autoPlayRef.current = setInterval(() => {
        nextSlide();
      }, 5000); // 5 seconds
    }

    return () => {
      if (autoPlayRef.current) {
        clearInterval(autoPlayRef.current);
      }
    };
  }, [isPaused, nextSlide]);

  // Touch event handlers for swipe
  const onTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const onTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;

    if (isLeftSwipe) {
      nextSlide();
    } else if (isRightSwipe) {
      prevSlide();
    }
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') {
        prevSlide();
      } else if (e.key === 'ArrowRight') {
        nextSlide();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [nextSlide, prevSlide]);

  return (
    <div
      className="relative overflow-hidden rounded-lg shadow-lg"
      onMouseEnter={() => setIsPaused(true)}
      onMouseLeave={() => setIsPaused(false)}
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
      role="region"
      aria-roledescription="carousel"
      aria-label="Featured promotions"
    >
      {/* Loading state */}
      {loading && (
        <div className="flex items-center justify-center h-[400px] md:h-[500px] w-full bg-gray-100">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Error state */}
      {!loading && error && (
        <div className="flex items-center justify-center h-[400px] md:h-[500px] w-full bg-gray-100">
          <div className="text-center">
            <svg
              className="mx-auto h-12 w-12 text-red-500"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
              />
            </svg>
            <p className="mt-4 text-lg text-gray-700">{error.message}</p>
            <p className="mt-2 text-sm text-gray-500">Using default banners</p>
          </div>
        </div>
      )}

      {/* Carousel slides */}
      {!loading && (!error || !shouldShowErrorPage(error)) && (
        <div
          className="flex transition-transform duration-500 ease-in-out h-[400px] md:h-[500px]"
          style={{ transform: `translateX(-${currentSlide * 100}%)` }}
        >
          {banners.map((item, index) => (
            <div
              key={item.id}
              className={`flex-shrink-0 w-full relative ${item.bgColor}`}
              role="group"
              aria-roledescription="slide"
              aria-label={`Slide ${index + 1} of ${totalSlides}`}
            >
              {/* Background image with overlay */}
              <div className="absolute inset-0 z-0">
                {item.image &&
                typeof item.image === 'object' &&
                item.image.url &&
                typeof item.image.url === 'string' ? (
                  <Image
                    src={item.image.url}
                    alt=""
                    fill
                    className="object-cover"
                    priority={index === 0}
                    sizes="(max-width: 768px) 100vw, 1200px"
                    onError={e => {
                      // Fallback to a local placeholder if image fails to load
                      const target = e.target as HTMLImageElement;
                      target.src = '/images/placeholder.png';
                    }}
                  />
                ) : (
                  <div className="w-full h-full bg-gray-300 flex items-center justify-center">
                    <svg
                      className="h-16 w-16 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={1}
                        d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                )}
                <div className="absolute inset-0 bg-black bg-opacity-40"></div>
              </div>

              {/* Content */}
              <div className="relative z-10 flex flex-col justify-center h-full px-6 md:px-16 text-white">
                <div className="max-w-xl">
                  <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4">{item.title}</h2>
                  {item.subtitle && (
                    <p className="text-xl md:text-2xl mb-2 text-white/80">{item.subtitle}</p>
                  )}
                  {item.description && (
                    <p className="text-lg md:text-xl mb-6">{item.description}</p>
                  )}
                  {item.cta_link && (
                    <Link
                      href={item.cta_link}
                      className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-300"
                    >
                      {item.cta_text || 'Learn More'}
                      <svg
                        className="ml-2 -mr-1 h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M14 5l7 7m0 0l-7 7m7-7H3"
                        />
                      </svg>
                    </Link>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Navigation arrows */}
      {/* Removed navigation arrows as per UI update */}

      {/* Pagination indicators */}
      {!loading && (!error || !shouldShowErrorPage(error)) && banners.length > 0 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
          {banners.map((_, index) => (
            <button
              key={index}
              className={`h-2 rounded-full transition-all duration-300 ${
                currentSlide === index
                  ? 'w-8 bg-white'
                  : 'w-2 bg-white bg-opacity-50 hover:bg-opacity-75'
              }`}
              onClick={() => goToSlide(index)}
              aria-label={`Go to slide ${index + 1}`}
              aria-current={currentSlide === index ? 'true' : 'false'}
            ></button>
          ))}
        </div>
      )}
    </div>
  );
}
