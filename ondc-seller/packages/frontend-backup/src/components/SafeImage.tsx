'use client';

import React, { useState } from 'react';
import Image from 'next/image';

interface SafeImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  fallbackSrc?: string;
  priority?: boolean;
  fill?: boolean;
  sizes?: string;
}

const SafeImage: React.FC<SafeImageProps> = ({
  src,
  alt,
  width = 400,
  height = 300,
  className = '',
  fallbackSrc,
  priority = false,
  fill = false,
  sizes,
}) => {
  const [imgSrc, setImgSrc] = useState(src);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Default fallback image (SVG data URL)
  const defaultFallback = `data:image/svg+xml;base64,${btoa(`
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f3f4f6"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="14" fill="#9ca3af" text-anchor="middle" dy=".3em">
        Image not available
      </text>
    </svg>
  `)}`;

  const handleError = () => {
    setHasError(true);
    setIsLoading(false);

    if (fallbackSrc && imgSrc !== fallbackSrc) {
      setImgSrc(fallbackSrc);
      setHasError(false);
    } else if (imgSrc !== defaultFallback) {
      setImgSrc(defaultFallback);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  const imageProps = {
    src: imgSrc,
    alt,
    onError: handleError,
    onLoad: handleLoad,
    className: `${className} ${isLoading ? 'opacity-50' : 'opacity-100'} transition-opacity duration-300`,
    priority,
    ...(fill ? { fill: true } : { width, height }),
    ...(sizes && { sizes }),
  };

  return (
    <div className={`relative ${fill ? '' : `w-[${width}px] h-[${height}px]`}`}>
      {isLoading && !hasError && (
        <div
          className={`absolute inset-0 bg-gray-200 animate-pulse rounded ${fill ? '' : `w-[${width}px] h-[${height}px]`}`}
        />
      )}

      <Image
        {...imageProps}
        className={`${imageProps.className} object-cover ${hasError ? 'grayscale' : ''}`}
      />

      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500 text-xs">
          <div className="text-center">
            <svg
              className="w-8 h-8 mx-auto mb-2 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <span>Image unavailable</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default SafeImage;
