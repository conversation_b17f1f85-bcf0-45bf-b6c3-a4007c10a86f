'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useCartContext } from '../../providers/CartProvider';
import { formatPrice } from '../../lib/utils';

/**
 * Mini cart component for the header
 * @returns Mini cart component
 */
export function MiniCart() {
  const { cart, loading, error, totalItems, totalUniqueItems, subtotal, isEmpty, removeItem } =
    useCartContext();

  const [isOpen, setIsOpen] = useState(false);
  const cartRef = useRef<HTMLDivElement>(null);

  // Close the cart when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (cartRef.current && !cartRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Toggle the cart
  const toggleCart = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative" ref={cartRef}>
      <button
        className="flex items-center text-gray-700 hover:text-blue-500 focus:outline-none"
        onClick={toggleCart}
        aria-label="Shopping cart"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
          />
        </svg>
        {totalItems > 0 && (
          <span className="absolute -top-2 -right-2 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {totalItems}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-72 bg-white rounded-lg shadow-lg z-50 overflow-hidden">
          <div className="p-4 border-b">
            <h3 className="font-semibold">
              {isEmpty ? 'Your cart is empty' : `Your Cart (${totalItems})`}
            </h3>
          </div>

          {loading ? (
            <div className="p-4 text-center text-gray-500">Loading...</div>
          ) : error ? (
            <div className="p-4 text-center">
              <p className="text-red-500 text-sm mb-2">
                {error.message === 'Backend service is currently unavailable'
                  ? 'Demo Mode Active'
                  : error.message}
              </p>
              {error.message === 'Backend service is currently unavailable' && (
                <p className="text-gray-500 text-xs">
                  The cart is in demo mode. Some features may be limited.
                </p>
              )}
            </div>
          ) : isEmpty ? (
            <div className="p-4 text-center text-gray-500">
              <p>Your cart is empty</p>
              <Link href="/products" className="text-blue-500 hover:underline mt-2 inline-block">
                Start shopping
              </Link>
            </div>
          ) : (
            <>
              <div className="max-h-60 overflow-y-auto">
                {cart?.items.map(item => (
                  <div key={item.id} className="p-3 border-b flex items-center">
                    {item.thumbnail && (
                      <img
                        src={item.thumbnail}
                        alt={item.title}
                        className="w-10 h-10 object-cover rounded mr-3"
                      />
                    )}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{item.title}</p>
                      <p className="text-xs text-gray-500">
                        {formatPrice(item.unit_price)} × {item.quantity}
                      </p>
                    </div>
                    <button
                      onClick={() => removeItem(item.id)}
                      className="text-gray-400 hover:text-red-500 ml-2"
                      aria-label="Remove item"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>

              <div className="p-4 border-t">
                <div className="flex justify-between mb-4">
                  <span className="font-medium">Subtotal</span>
                  <span>{formatPrice(subtotal)}</span>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <Link
                    href="/cart"
                    className="py-2 px-4 bg-gray-200 text-gray-800 rounded text-center text-sm hover:bg-gray-300 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    View Cart
                  </Link>
                  <Link
                    href="/checkout"
                    className="py-2 px-4 bg-blue-500 text-white rounded text-center text-sm hover:bg-blue-600 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    Checkout
                  </Link>
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
}

export default MiniCart;
