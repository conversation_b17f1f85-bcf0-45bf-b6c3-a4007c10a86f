'use client';

import React, { useState } from 'react';
import { useCartContext } from '../../providers/CartProvider';
import { useToast } from '../../providers/ToastProvider';

interface AddToCartButtonProps {
  variantId: string;
  className?: string;
  showQuantity?: boolean;
  maxQuantity?: number;
  onAddToCart?: () => void;
}

/**
 * Add to cart button component
 * @param props Component props
 * @returns Add to cart button component
 */
export function AddToCartButton({
  variantId,
  className = '',
  showQuantity = true,
  maxQuantity = 10,
  onAddToCart,
}: AddToCartButtonProps) {
  const { addItem, isItemInCart, getItemQuantity, loading } = useCartContext();
  const { showError } = useToast();
  const [quantity, setQuantity] = useState(1);
  const [isAdding, setIsAdding] = useState(false);

  // Check if the item is already in the cart
  const itemInCart = isItemInCart(variantId);
  const itemQuantity = getItemQuantity(variantId);

  // Handle quantity change
  const handleQuantityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setQuantity(parseInt(e.target.value, 10));
  };

  // Handle add to cart
  const handleAddToCart = async () => {
    try {
      setIsAdding(true);
      await addItem(variantId, quantity);

      // Call the onAddToCart callback if provided
      if (onAddToCart) {
        onAddToCart();
      }
    } catch (error) {
      console.error('Error adding item to cart:', error);
      showError('Failed to add item to cart. Please try again.');
    } finally {
      setIsAdding(false);
    }
  };

  // Generate quantity options
  const quantityOptions = [];
  for (let i = 1; i <= maxQuantity; i++) {
    quantityOptions.push(
      <option key={i} value={i}>
        {i}
      </option>
    );
  }

  return (
    <div className={`flex ${showQuantity ? 'space-x-2' : ''} ${className}`}>
      {showQuantity && (
        <select
          value={quantity}
          onChange={handleQuantityChange}
          className="border border-gray-300 rounded px-2 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={isAdding || loading}
        >
          {quantityOptions}
        </select>
      )}

      <button
        onClick={handleAddToCart}
        disabled={isAdding || loading}
        className={`flex-1 bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded transition-colors ${
          isAdding || loading ? 'opacity-70 cursor-not-allowed' : ''
        }`}
      >
        {isAdding ? 'Adding...' : itemInCart ? `Add More (${itemQuantity} in cart)` : 'Add to Cart'}
      </button>
    </div>
  );
}

export default AddToCartButton;
