'use client';

import React from 'react';
import Link from 'next/link';
import { useCartContext } from '../../providers/CartProvider';
import { formatPrice } from '../../lib/utils';

/**
 * Cart summary component
 * @returns Cart summary component
 */
export function CartSummary() {
  const {
    cart,
    loading,
    error,
    totalItems,
    totalUniqueItems,
    subtotal,
    total,
    isEmpty,
    removeItem,
    updateItem,
  } = useCartContext();

  if (loading) {
    return (
      <div className="p-4 border rounded-lg shadow-sm">
        <p className="text-center text-gray-500">Loading cart...</p>
      </div>
    );
  }

  if (error) {
    const isBackendUnavailable = error.message === 'Backend service is currently unavailable';

    return (
      <div
        className={`p-4 border rounded-lg shadow-sm ${isBackendUnavailable ? 'bg-yellow-50' : 'bg-red-50'}`}
      >
        <div className="flex items-start">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className={`h-5 w-5 ${isBackendUnavailable ? 'text-yellow-500' : 'text-red-500'} mt-0.5 mr-2`}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            {isBackendUnavailable ? (
              <path
                fillRule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            ) : (
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            )}
          </svg>
          <div>
            <h3
              className={`text-sm font-medium ${isBackendUnavailable ? 'text-yellow-800' : 'text-red-800'}`}
            >
              {isBackendUnavailable ? 'Demo Mode Active' : `Error: ${error.message}`}
            </h3>
            <p
              className={`text-sm mt-1 ${isBackendUnavailable ? 'text-yellow-700' : 'text-red-700'}`}
            >
              {isBackendUnavailable
                ? "The backend service is currently unavailable. You're viewing the cart in demo mode with mock data."
                : "We're having trouble connecting to our servers. Please try again later."}
            </p>
          </div>
        </div>

        <div className="mt-4 flex space-x-2">
          <button
            onClick={() => window.location.reload()}
            className={`flex-1 py-2 px-4 rounded text-white ${
              isBackendUnavailable
                ? 'bg-yellow-500 hover:bg-yellow-600'
                : 'bg-red-500 hover:bg-red-600'
            } transition-colors`}
          >
            Retry
          </button>

          <Link
            href="/products"
            className={`flex-1 py-2 px-4 rounded text-center ${
              isBackendUnavailable
                ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200'
                : 'bg-red-100 text-red-800 hover:bg-red-200'
            } transition-colors`}
          >
            Continue Shopping
          </Link>
        </div>
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className="p-4 border rounded-lg shadow-sm">
        <p className="text-center text-gray-500">Your cart is empty</p>
      </div>
    );
  }

  return (
    <div className="p-4 border rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold mb-4">Cart Summary</h2>

      <div className="space-y-4">
        {cart?.items.map(item => (
          <div key={item.id} className="flex items-center justify-between border-b pb-2">
            <div className="flex items-center">
              {item.thumbnail && (
                <img
                  src={item.thumbnail}
                  alt={item.title}
                  className="w-12 h-12 object-cover rounded mr-2"
                />
              )}
              <div>
                <p className="font-medium">{item.title}</p>
                <p className="text-sm text-gray-500">
                  {formatPrice(item.unit_price)} × {item.quantity}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                onClick={() => updateItem(item.id, Math.max(1, item.quantity - 1))}
                className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded"
                aria-label="Decrease quantity"
              >
                -
              </button>

              <span className="w-8 text-center">{item.quantity}</span>

              <button
                onClick={() => updateItem(item.id, item.quantity + 1)}
                className="w-6 h-6 flex items-center justify-center bg-gray-200 rounded"
                aria-label="Increase quantity"
              >
                +
              </button>

              <button
                onClick={() => removeItem(item.id)}
                className="ml-2 text-red-500"
                aria-label="Remove item"
              >
                ×
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 space-y-2">
        <div className="flex justify-between">
          <span>Subtotal</span>
          <span>{formatPrice(subtotal)}</span>
        </div>

        {cart?.shipping_total > 0 && (
          <div className="flex justify-between">
            <span>Shipping</span>
            <span>{formatPrice(cart.shipping_total)}</span>
          </div>
        )}

        {cart?.tax_total > 0 && (
          <div className="flex justify-between">
            <span>Tax</span>
            <span>{formatPrice(cart.tax_total)}</span>
          </div>
        )}

        {cart?.discount_total > 0 && (
          <div className="flex justify-between">
            <span>Discount</span>
            <span>-{formatPrice(cart.discount_total)}</span>
          </div>
        )}

        <div className="flex justify-between font-semibold pt-2 border-t">
          <span>Total</span>
          <span>{formatPrice(total)}</span>
        </div>
      </div>

      <div className="mt-4">
        <button className="w-full py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
          Checkout ({totalItems} {totalItems === 1 ? 'item' : 'items'})
        </button>
      </div>
    </div>
  );
}

export default CartSummary;
