'use client';

import { useState, useRef, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { Category, productCategories } from '../data/categories';
import { debounce } from 'lodash';

export default function MegaMenu() {
  const [openCategory, setOpenCategory] = useState<string | null>(null);
  const megaMenuRef = useRef<HTMLDivElement>(null);
  const pathname = usePathname();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Track hover state for buttons and dropdowns
  const [hoveredElements, setHoveredElements] = useState<{
    button: string | null;
    dropdown: string | null;
  }>({
    button: null,
    dropdown: null,
  });

  // Handle window resize for responsive behavior
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        // Close mobile menu when switching to desktop
        setMobileMenuOpen(false);
      }
    };

    // Initial check
    handleResize();

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Close mega menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (megaMenuRef.current && !megaMenuRef.current.contains(event.target as Node)) {
        setOpenCategory(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [openCategory]);

  // Handle window resize to close dropdown on resize
  useEffect(() => {
    if (openCategory) {
      const handleResize = debounce(() => {
        setOpenCategory(null);
      }, 100);

      window.addEventListener('resize', handleResize);

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }
  }, [openCategory]);

  // Mobile menu toggle
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  // Handle mouse enter for category button
  const handleButtonMouseEnter = (categoryId: string) => {
    setHoveredElements(prev => ({ ...prev, button: categoryId }));
    setOpenCategory(categoryId);
  };

  // Handle mouse leave for category button
  const handleButtonMouseLeave = (categoryId: string) => {
    setHoveredElements(prev => ({ ...prev, button: null }));
  };

  // Handle mouse enter for dropdown
  const handleDropdownMouseEnter = (categoryId: string) => {
    setHoveredElements(prev => ({ ...prev, dropdown: categoryId }));
  };

  // Handle mouse leave for dropdown
  const handleDropdownMouseLeave = (categoryId: string) => {
    setHoveredElements(prev => ({ ...prev, dropdown: null }));
  };

  // Effect to handle menu closing based on hover state
  useEffect(() => {
    const { button, dropdown } = hoveredElements;

    // If neither the button nor dropdown is hovered, close the menu after a small delay
    if (button === null && dropdown === null && openCategory !== null) {
      const timer = setTimeout(() => {
        setOpenCategory(null);
      }, 150);

      return () => clearTimeout(timer);
    }
  }, [hoveredElements, openCategory]);

  return (
    <div className="bg-white shadow-sm" ref={megaMenuRef}>
      <div className="max-w-7xl">
        <div className="flex justify-start h-16">
          {/* Desktop navigation */}
          <div className="hidden lg:flex lg:items-center lg:space-x-8">
            {productCategories.map((category, index) => (
              <div key={category.id} className="relative">
                <button
                  className={`px-3 py-2 rounded-md text-sm font-medium ${
                    pathname.startsWith(category.href)
                      ? 'text-blue-600'
                      : 'text-gray-700 hover:text-blue-600'
                  }`}
                  onMouseEnter={() => handleButtonMouseEnter(category.id)}
                  onMouseLeave={() => handleButtonMouseLeave(category.id)}
                  onClick={() => setOpenCategory(openCategory === category.id ? null : category.id)}
                  aria-expanded={openCategory === category.id}
                >
                  {category.name}
                </button>

                {/* Mega menu dropdown */}
                {openCategory === category.id && (
                  <div
                    className="absolute z-10 mt-2 w-screen max-w-4xl left-0"
                    style={{
                      right: 'auto',
                      transform: 'none',
                    }}
                    onMouseEnter={() => handleDropdownMouseEnter(category.id)}
                    onMouseLeave={() => handleDropdownMouseLeave(category.id)}
                  >
                    <div className="w-full bg-white shadow-lg rounded-lg">
                      <div className="grid grid-cols-1 gap-x-4 gap-y-4 p-4">
                        {/* Subcategories */}
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-x-4 gap-y-2">
                          <h3 className="text-lg font-medium text-gray-900 col-span-1 sm:col-span-2 md:col-span-3 lg:col-span-4 xl:col-span-5 mb-1">
                            {category.name} Categories
                          </h3>
                          {category.subcategories.map(subcategory => (
                            <a
                              key={subcategory.id}
                              href={subcategory.href}
                              className="text-sm text-gray-700 hover:text-blue-600 transition-colors duration-300 flex items-center py-1"
                              onClick={e => {
                                e.preventDefault();
                                console.log(`Navigating to: ${subcategory.href}`);

                                // Close the mega menu first
                                setOpenCategory(null);

                                // Navigate directly
                                window.location.href = subcategory.href;
                              }}
                            >
                              <svg
                                className="w-3 h-3 mr-1 text-gray-400 flex-shrink-0"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth={2}
                                  d="M9 5l7 7-7 7"
                                />
                              </svg>
                              <span className="truncate max-w-[180px]" title={subcategory.name}>
                                {subcategory.name}
                              </span>
                            </a>
                          ))}
                        </div>
                      </div>

                      {/* View all link */}
                      <div className="bg-gray-50 px-4 py-2 flex items-center justify-end">
                        <a
                          href={category.href}
                          className="text-sm font-medium text-blue-600 hover:text-blue-500 flex items-center"
                          onClick={e => {
                            e.preventDefault();
                            console.log(`Navigating to: ${category.href}`);

                            // Close the mega menu
                            setOpenCategory(null);

                            // Navigate directly
                            window.location.href = category.href;
                          }}
                        >
                          View all {category.name}
                          <svg
                            className="ml-1 h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 5l7 7-7 7"
                            />
                          </svg>
                        </a>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden flex items-center ml-auto">
            <button
              type="button"
              className="text-gray-700 hover:text-blue-600 focus:outline-none"
              onClick={toggleMobileMenu}
              aria-expanded={mobileMenuOpen}
            >
              <span className="sr-only">{mobileMenuOpen ? 'Close menu' : 'Open menu'}</span>
              {mobileMenuOpen ? (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              ) : (
                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {mobileMenuOpen && (
        <div className="lg:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1">
            <a
              href="/"
              className="block px-3 py-2 rounded-md text-base font-medium text-blue-600"
              onClick={e => {
                e.preventDefault();
                setMobileMenuOpen(false);
                window.location.href = '/';
              }}
            >
              Home
            </a>

            {productCategories.map(category => (
              <div key={category.id} className="py-2">
                <button
                  className="flex items-center justify-between w-full px-3 py-2 text-base font-medium text-gray-900 hover:text-blue-600"
                  onClick={() => setOpenCategory(openCategory === category.id ? null : category.id)}
                  aria-expanded={openCategory === category.id}
                >
                  {category.name}
                  <svg
                    className={`h-5 w-5 transition-transform duration-300 ${openCategory === category.id ? 'transform rotate-180' : ''}`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 9l-7 7-7-7"
                    />
                  </svg>
                </button>

                {openCategory === category.id && (
                  <div className="mt-2 pl-4 space-y-1">
                    {category.subcategories.map(subcategory => (
                      <a
                        key={subcategory.id}
                        href={subcategory.href}
                        className="block px-3 py-2 text-sm text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-md"
                        onClick={e => {
                          e.preventDefault();
                          setOpenCategory(null);
                          setMobileMenuOpen(false);
                          window.location.href = subcategory.href;
                        }}
                      >
                        <span title={subcategory.name}>{subcategory.name}</span>
                      </a>
                    ))}

                    <a
                      href={category.href}
                      className="block px-3 py-2 text-sm font-medium text-blue-600 hover:bg-blue-50 rounded-md"
                      onClick={e => {
                        e.preventDefault();
                        setOpenCategory(null);
                        setMobileMenuOpen(false);
                        window.location.href = category.href;
                      }}
                    >
                      View all {category.name}
                    </a>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
