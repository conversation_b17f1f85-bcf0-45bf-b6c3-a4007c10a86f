'use client';

import { useEffect, useState, useRef } from 'react';
import { getCurrentTenantId, setCurrentTenantId } from '../lib/tenant';

// Sample tenants for testing with icons
const SAMPLE_TENANTS = [
  {
    id: 'default',
    name: 'Default Tenant',
    icon: '🏪',
    description: 'System default tenant with standard features'
  },
  {
    id: 'tenant1',
    name: 'Fashion Store',
    icon: '👕',
    description: 'Clothing and accessories retailer'
  },
  {
    id: 'tenant2',
    name: 'Electronics Hub',
    icon: '📱',
    description: 'Consumer electronics and gadgets'
  },
];

export default function TenantSelector() {
  // Use a consistent default value for server and initial client render
  const [currentTenantId, setCurrentTenant] = useState<string>('default');
  const [isClient, setIsClient] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get current tenant object - always use default tenant for initial render
  // This ensures server and client render the same content initially
  const currentTenant = SAMPLE_TENANTS.find(tenant =>
    isClient ? tenant.id === currentTenantId : tenant.id === 'default'
  ) || SAMPLE_TENANTS[0];

  // Split the effects to avoid hydration mismatches
  // First effect only sets isClient to true
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Second effect runs after component is hydrated and isClient is true
  useEffect(() => {
    if (!isClient) return;

    try {
      // Now it's safe to access localStorage
      const tenantId = getCurrentTenantId();
      setCurrentTenant(tenantId);
    } catch (error) {
      console.error('Error accessing tenant ID:', error);
    }
  }, [isClient]);

  // Third effect for click outside listener
  useEffect(() => {
    if (!isClient) return;

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isClient]);

  const handleTenantChange = (tenantId: string) => {
    setCurrentTenant(tenantId);
    setCurrentTenantId(tenantId);
    setIsOpen(false);

    // Reload the page to apply the new tenant ID
    window.location.reload();
  };

  // Only render the selector when we're on the client side
  if (!isClient) {
    return (
      <div className="flex items-center space-x-2">
        <div className="px-3 py-2 bg-gray-100 rounded-md animate-pulse">
          <span className="text-sm font-medium text-gray-500">Loading tenants...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <span className="text-xl mr-1">{currentTenant.icon}</span>
        <span>{currentTenant.name}</span>
        <svg className="w-5 h-5 ml-1 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
          <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 z-10 mt-2 w-64 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1 divide-y divide-gray-100">
            <div className="px-4 py-3">
              <p className="text-sm font-medium text-gray-900">Select Tenant</p>
              <p className="text-xs text-gray-500">Choose the tenant to manage products</p>
            </div>
            <div className="py-1">
              {SAMPLE_TENANTS.map((tenant) => (
                <button
                  key={tenant.id}
                  className={`flex items-center w-full px-4 py-2 text-sm ${tenant.id === currentTenantId ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-50'}`}
                  onClick={() => handleTenantChange(tenant.id)}
                >
                  <span className="text-xl mr-3">{tenant.icon}</span>
                  <div className="flex flex-col items-start">
                    <span className="font-medium">{tenant.name}</span>
                    <span className="text-xs text-gray-500">{tenant.description}</span>
                  </div>
                  {tenant.id === currentTenantId && (
                    <svg className="w-5 h-5 ml-auto text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
