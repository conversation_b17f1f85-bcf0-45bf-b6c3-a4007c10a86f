'use client';

import React from 'react';
import { useBackendConnectivity } from '../hooks/useBackendConnectivity';

interface BackendStatusIndicatorProps {
  showLabel?: boolean;
  showDetails?: boolean;
  className?: string;
}

/**
 * Backend status indicator component
 * @param props Component props
 * @returns Backend status indicator component
 */
export function BackendStatusIndicator({
  showLabel = true,
  showDetails = false,
  className = '',
}: BackendStatusIndicatorProps) {
  const { status, isUsingMockData, lastChecked, checkCount, checkNow } = useBackendConnectivity();
  
  // Determine status color and label
  let statusColor = 'bg-gray-500';
  let statusLabel = 'Unknown';
  let statusIcon = null;
  
  if (status === 'available') {
    statusColor = 'bg-green-500';
    statusLabel = 'Online';
    statusIcon = (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-4 w-4 text-white"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
          clipRule="evenodd"
        />
      </svg>
    );
  } else if (status === 'unavailable') {
    statusColor = 'bg-red-500';
    statusLabel = 'Offline';
    statusIcon = (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-4 w-4 text-white"
        viewBox="0 0 20 20"
        fill="currentColor"
      >
        <path
          fillRule="evenodd"
          d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
          clipRule="evenodd"
        />
      </svg>
    );
  }
  
  // Format last checked time
  const formattedLastChecked = lastChecked
    ? new Intl.DateTimeFormat('en-US', {
        hour: 'numeric',
        minute: 'numeric',
        second: 'numeric',
        hour12: true,
      }).format(lastChecked)
    : 'Never';
  
  return (
    <div className={`flex items-center ${className}`}>
      <div className="relative">
        <div
          className={`${statusColor} h-3 w-3 rounded-full ${
            status === 'unknown' ? 'animate-pulse' : ''
          }`}
        />
        {status === 'unavailable' && isUsingMockData && (
          <div className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-yellow-400" />
        )}
      </div>
      
      {showLabel && (
        <span className="ml-2 text-xs font-medium text-gray-700">
          {statusLabel}
          {isUsingMockData && status === 'unavailable' && ' (Demo Mode)'}
        </span>
      )}
      
      {showDetails && (
        <div className="ml-2">
          <button
            onClick={() => checkNow()}
            className="text-xs text-blue-500 hover:text-blue-700 underline"
            title="Check backend status now"
          >
            Check Now
          </button>
          <div className="text-xs text-gray-500 mt-1">
            Last checked: {formattedLastChecked}
            <br />
            Check count: {checkCount}
          </div>
        </div>
      )}
    </div>
  );
}

export default BackendStatusIndicator;
