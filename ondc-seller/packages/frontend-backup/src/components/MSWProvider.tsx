'use client';

import { useEffect, useState } from 'react';
import { startMSW, isMSWEnabled, isMSWEnabledInStorage } from '../lib/msw';
import MSWControl from './MSWControl';

/**
 * MSW Provider Component
 * Initializes Mock Service Worker based on environment variables and localStorage
 */
export default function MSWProvider({ children }: { children: React.ReactNode }) {
  const [isMSWReady, setIsMSWReady] = useState(true); // Force ready for now

  useEffect(() => {
    const initializeMSW = async () => {
      try {
        // Only proceed if we're in browser environment
        if (typeof window === 'undefined') {
          console.log('🔶 MSW: Skipping - not in browser environment');
          setIsMSWReady(true);
          return;
        }

        // Check if MSW should be enabled
        const envEnabled = isMSWEnabled();
        const storageEnabled = isMSWEnabledInStorage();

        console.log('🔶 MSW: Environment enabled:', envEnabled);
        console.log('🔶 MSW: Storage enabled:', storageEnabled);

        // Enable MSW if either environment variable is true or localStorage preference is true
        if (envEnabled || storageEnabled) {
          console.log('🔶 MSW: Initializing Mock Service Worker...');

          // Add timeout to prevent infinite loading
          const timeoutPromise = new Promise<boolean>((_, reject) =>
            setTimeout(() => reject(new Error('MSW initialization timeout')), 10000)
          );

          const mswPromise = startMSW();

          try {
            const success = await Promise.allSettled([mswPromise, timeoutPromise]);
            console.log('success:::::::::::', success);

            if (success) {
              console.log('✅ MSW: Mock Service Worker is ready');
            } else {
              console.log('❌ MSW: Failed to initialize Mock Service Worker');
            }
          } catch (error) {
            console.warn('⚠️ MSW: Initialization failed, continuing without MSW:', error);
          }
        } else {
          console.log('🔶 MSW: Mock Service Worker is disabled (env and storage both false)');
        }
      } catch (error) {
        console.warn('⚠️ MSW: Error during initialization, continuing without MSW:', error);
      } finally {
        setIsMSWReady(true);
      }
    };

    // Initialize MSW
    initializeMSW();
  }, []);

  // Show loading state while MSW is initializing (with timeout fallback)
  if (!isMSWReady) {
    // Force ready after 10 seconds to prevent infinite loading
    setTimeout(() => {
      if (!isMSWReady) {
        console.warn('⚠️ MSW: Forcing ready state after timeout');
        setIsMSWReady(true);
      }
    }, 10000);

    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Initializing...</p>
          <p className="mt-1 text-xs text-gray-500">
            If this takes too long, please refresh the page
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {children}
      <MSWControl />
    </>
  );
}
