'use client';

import { useState } from 'react';

export interface FilterOption {
  id: string;
  name: string;
}

export interface PriceRange {
  min: number;
  max: number;
}

export interface FilterGroup {
  id: string;
  name: string;
  options: FilterOption[];
}

export interface CategoryFiltersProps {
  filterGroups: FilterGroup[];
  priceRanges: PriceRange[];
  onFilterChange: (filters: Record<string, string[]>) => void;
  onPriceRangeChange: (priceRange: PriceRange | null) => void;
  onAvailabilityChange: (inStockOnly: boolean) => void;
  className?: string;
}

export default function CategoryFilters({
  filterGroups,
  priceRanges,
  onFilterChange,
  onPriceRangeChange,
  onAvailabilityChange,
  className = '',
}: CategoryFiltersProps) {
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});
  const [selectedPriceRange, setSelectedPriceRange] = useState<string | null>(null);
  const [inStockOnly, setInStockOnly] = useState(false);
  const [isMobileFiltersOpen, setIsMobileFiltersOpen] = useState(false);

  const handleFilterChange = (groupId: string, optionId: string) => {
    const newSelectedFilters = { ...selectedFilters };
    
    if (!newSelectedFilters[groupId]) {
      newSelectedFilters[groupId] = [];
    }
    
    const index = newSelectedFilters[groupId].indexOf(optionId);
    
    if (index === -1) {
      newSelectedFilters[groupId].push(optionId);
    } else {
      newSelectedFilters[groupId].splice(index, 1);
    }
    
    if (newSelectedFilters[groupId].length === 0) {
      delete newSelectedFilters[groupId];
    }
    
    setSelectedFilters(newSelectedFilters);
    onFilterChange(newSelectedFilters);
  };

  const handlePriceRangeChange = (rangeId: string) => {
    const newSelectedRange = selectedPriceRange === rangeId ? null : rangeId;
    setSelectedPriceRange(newSelectedRange);
    
    if (newSelectedRange === null) {
      onPriceRangeChange(null);
    } else {
      const index = parseInt(rangeId.split('-')[1]);
      onPriceRangeChange(priceRanges[index]);
    }
  };

  const handleAvailabilityChange = (checked: boolean) => {
    setInStockOnly(checked);
    onAvailabilityChange(checked);
  };

  const clearAllFilters = () => {
    setSelectedFilters({});
    setSelectedPriceRange(null);
    setInStockOnly(false);
    onFilterChange({});
    onPriceRangeChange(null);
    onAvailabilityChange(false);
  };

  const hasActiveFilters = Object.keys(selectedFilters).length > 0 || selectedPriceRange !== null || inStockOnly;

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Mobile filter dialog */}
      <div className="lg:hidden p-4 border-b border-gray-200">
        <button
          type="button"
          className="w-full flex items-center justify-between px-4 py-2 bg-blue-50 text-blue-600 rounded-md"
          onClick={() => setIsMobileFiltersOpen(!isMobileFiltersOpen)}
        >
          <span className="font-medium">Filters</span>
          <svg
            className={`h-5 w-5 transition-transform ${isMobileFiltersOpen ? 'transform rotate-180' : ''}`}
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </button>
      </div>

      {/* Filter content - always visible on desktop, toggleable on mobile */}
      <div className={`${isMobileFiltersOpen ? 'block' : 'hidden'} lg:block p-4`}>
        {/* Clear filters button */}
        {hasActiveFilters && (
          <div className="mb-4 flex justify-between items-center">
            <h2 className="text-lg font-medium text-gray-900">Filters</h2>
            <button
              type="button"
              className="text-sm text-blue-600 hover:text-blue-800"
              onClick={clearAllFilters}
            >
              Clear all
            </button>
          </div>
        )}

        {/* Filter groups */}
        {filterGroups.map((group) => (
          <div key={group.id} className="border-t border-gray-200 pt-4 mb-4">
            <h3 className="text-md font-medium text-gray-900 mb-3">{group.name}</h3>
            <div className="space-y-2">
              {group.options.map((option) => (
                <div key={option.id} className="flex items-center">
                  <input
                    id={`filter-${group.id}-${option.id}`}
                    name={`filter-${group.id}`}
                    type="checkbox"
                    checked={(selectedFilters[group.id] || []).includes(option.id)}
                    onChange={() => handleFilterChange(group.id, option.id)}
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label
                    htmlFor={`filter-${group.id}-${option.id}`}
                    className="ml-3 text-sm text-gray-700"
                  >
                    {option.name}
                  </label>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Price ranges */}
        <div className="border-t border-gray-200 pt-4 mb-4">
          <h3 className="text-md font-medium text-gray-900 mb-3">Price</h3>
          <div className="space-y-2">
            {priceRanges.map((range, index) => (
              <div key={`price-${index}`} className="flex items-center">
                <input
                  id={`price-${index}`}
                  name="price-range"
                  type="radio"
                  checked={selectedPriceRange === `price-${index}`}
                  onChange={() => handlePriceRangeChange(`price-${index}`)}
                  className="h-4 w-4 border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label
                  htmlFor={`price-${index}`}
                  className="ml-3 text-sm text-gray-700"
                >
                  {range.min === 0
                    ? `Under ₹${range.max.toLocaleString()}`
                    : range.max === Infinity
                    ? `₹${range.min.toLocaleString()}+`
                    : `₹${range.min.toLocaleString()} - ₹${range.max.toLocaleString()}`}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Availability */}
        <div className="border-t border-gray-200 pt-4">
          <h3 className="text-md font-medium text-gray-900 mb-3">Availability</h3>
          <div className="flex items-center">
            <input
              id="in-stock"
              name="in-stock"
              type="checkbox"
              checked={inStockOnly}
              onChange={(e) => handleAvailabilityChange(e.target.checked)}
              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label htmlFor="in-stock" className="ml-3 text-sm text-gray-700">
              In Stock Only
            </label>
          </div>
        </div>

        {/* Apply filters button (mobile only) */}
        <div className="mt-6 lg:hidden">
          <button
            type="button"
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
            onClick={() => setIsMobileFiltersOpen(false)}
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}
