'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import Breadcrumbs from './Breadcrumbs';
import { useCart } from '../hooks/useCart';

interface ProductDetailProps {
  product: {
    id: string;
    title: string;
    description: string | null;
    thumbnail: string | null;
    images: string[];
    price: { amount: number; currency_code: string };
    inventory_quantity: number;
    categories?: string[];
    variants?: any[];
    metadata?: Record<string, any>;
  };
  showBreadcrumbs?: boolean;
  showAdminActions?: boolean;
}

const ProductDetail: React.FC<ProductDetailProps> = ({
  product,
  showBreadcrumbs = false,
  showAdminActions = false,
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(
    product.images?.[0] || product.thumbnail || null
  );
  const [quantity, setQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);
  const { addItem, isItemInCart, getItemQuantity } = useCart();

  const formatPrice = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount / 100);
  };

  const handleAddToCart = async () => {
    setAddingToCart(true);
    try {
      const variantId = product.variants?.[0]?.id || `variant_${product.id}`;
      await addItem(variantId, quantity);
    } catch (error) {
      // Optionally, show a toast error here
    } finally {
      setAddingToCart(false);
    }
  };

  const handleQuantityChange = (newQuantity: number) => {
    if (newQuantity >= 1 && newQuantity <= (product?.inventory_quantity || 1)) {
      setQuantity(newQuantity);
    }
  };

  const productInCart = isItemInCart(product.variants?.[0]?.id || `variant_${product.id}`);
  const cartQuantity = getItemQuantity(product.variants?.[0]?.id || `variant_${product.id}`);

  return (
    <div className="flex flex-col space-y-6">
      {showBreadcrumbs && (
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'Featured Products', href: '/products' },
            { label: product.title, href: `/products/${product.id}`, active: true },
          ]}
        />
      )}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="relative h-80 bg-gray-100 rounded-lg overflow-hidden">
              {selectedImage ? (
                <Image
                  src={selectedImage}
                  alt={product.title}
                  fill
                  className="object-contain"
                  sizes="(max-width: 768px) 100vw, 50vw"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <svg
                    className="w-16 h-16 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              )}
            </div>
            {/* Thumbnail Gallery */}
            <div className="flex space-x-2 overflow-x-auto pb-2">
              {product.images?.map((image, index) => (
                <button
                  key={index}
                  className={`relative w-20 h-20 rounded-md overflow-hidden border-2 ${selectedImage === image ? 'border-blue-600' : 'border-gray-200'}`}
                  onClick={() => setSelectedImage(image)}
                >
                  <Image
                    src={image}
                    alt={`${product.title} - Image ${index + 1}`}
                    fill
                    className="object-cover"
                    sizes="80px"
                  />
                </button>
              ))}
            </div>
          </div>
          {/* Product Details */}
          <div className="space-y-4">
            <h1 className="text-3xl font-bold text-gray-900">{product.title}</h1>
            <div className="flex items-center justify-between">
              <span className="text-2xl font-bold text-blue-600">
                {formatPrice(product.price.amount, product.price.currency_code)}
              </span>
              <span
                className={`px-3 py-1 rounded-full text-sm font-medium ${
                  product.inventory_quantity > 0
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }`}
              >
                {product.inventory_quantity > 0
                  ? `In Stock (${product.inventory_quantity})`
                  : 'Out of Stock'}
              </span>
            </div>
            {product.categories && (
              <div className="flex flex-wrap gap-2">
                {product.categories.map((category, index) => (
                  <span
                    key={index}
                    className="bg-gray-100 text-gray-800 text-xs font-medium px-2.5 py-0.5 rounded"
                  >
                    {category}
                  </span>
                ))}
              </div>
            )}
            <div className="border-t border-gray-200 pt-4">
              <h2 className="text-lg font-medium text-gray-900 mb-2">Description</h2>
              <p className="text-gray-700">{product.description}</p>
            </div>
            {product.metadata && (
              <div className="border-t border-gray-200 pt-4">
                <h2 className="text-lg font-medium text-gray-900 mb-2">Product Details</h2>
                <dl className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2">
                  {Object.entries(product.metadata).map(([key, value]) => (
                    <div key={key} className="flex flex-col">
                      <dt className="text-sm font-medium text-gray-500">
                        {key.charAt(0).toUpperCase() + key.slice(1)}
                      </dt>
                      <dd className="text-sm text-gray-900">{value}</dd>
                    </div>
                  ))}
                </dl>
              </div>
            )}
            {/* Quantity Selector and Add to Cart */}
            <div className="border-t border-gray-200 pt-6">
              <div className="flex items-center space-x-4 mb-4">
                <label htmlFor="quantity" className="text-sm font-medium text-gray-700">
                  Quantity:
                </label>
                <div className="flex items-center border border-gray-300 rounded-md">
                  <button
                    type="button"
                    className="px-3 py-1 text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-colors duration-200"
                    onClick={() => handleQuantityChange(quantity - 1)}
                    disabled={quantity <= 1}
                  >
                    -
                  </button>
                  <input
                    type="number"
                    id="quantity"
                    min="1"
                    max={product.inventory_quantity}
                    value={quantity}
                    onChange={e => handleQuantityChange(parseInt(e.target.value) || 1)}
                    className="w-16 px-2 py-1 text-center border-0 focus:ring-0 focus:outline-none"
                  />
                  <button
                    type="button"
                    className="px-3 py-1 text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-colors duration-200"
                    onClick={() => handleQuantityChange(quantity + 1)}
                    disabled={quantity >= product.inventory_quantity}
                  >
                    +
                  </button>
                </div>
                {cartQuantity > 0 && (
                  <span className="text-sm text-green-600 font-medium">{cartQuantity} in cart</span>
                )}
              </div>
              <button
                onClick={handleAddToCart}
                disabled={addingToCart || product.inventory_quantity === 0}
                className={`w-full py-3 px-6 rounded-md font-medium transition-all duration-300 flex items-center justify-center ${
                  product.inventory_quantity === 0
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : addingToCart
                      ? 'bg-blue-400 text-white cursor-not-allowed'
                      : productInCart
                        ? 'bg-green-600 text-white hover:bg-green-700'
                        : 'bg-blue-600 text-white hover:bg-blue-700'
                }`}
              >
                {addingToCart ? (
                  <>
                    <svg
                      className="animate-spin -ml-1 mr-2 h-5 w-5 text-white"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Adding to Cart...
                  </>
                ) : product.inventory_quantity === 0 ? (
                  'Out of Stock'
                ) : productInCart ? (
                  <>
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                    Add More to Cart
                  </>
                ) : (
                  <>
                    <svg
                      className="w-5 h-5 mr-2"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                    Add to Cart
                  </>
                )}
              </button>
            </div>
            {/* Admin Actions */}
            {showAdminActions && (
              <div className="border-t border-gray-200 pt-4 flex space-x-4">
                <Link
                  href={`/products/${product.id}/edit`}
                  className="flex-1 bg-yellow-600 text-white py-2 px-4 rounded-md hover:bg-yellow-700 transition-colors duration-300 flex items-center justify-center"
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                    />
                  </svg>
                  Edit Product
                </Link>
                <button
                  className="flex-1 bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 transition-colors duration-300 flex items-center justify-center"
                  onClick={() => alert('Delete functionality would be implemented here')}
                >
                  <svg
                    className="w-5 h-5 mr-2"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                    />
                  </svg>
                  Delete Product
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetail;
