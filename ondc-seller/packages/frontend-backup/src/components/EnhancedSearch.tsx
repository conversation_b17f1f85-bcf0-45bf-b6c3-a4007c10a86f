'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { popularSearchTerms } from '../data/categories';

// Sample recent searches (in a real app, this would be stored in localStorage or a database)
const RECENT_SEARCHES = ['smartphone', 'laptop', 'headphones'];

// Sample filter tags
const FILTER_TAGS = [
  { id: 'category', name: 'Category', options: ['Electronics', 'Fashion', 'Home', 'Beauty', 'Grocery'] },
  { id: 'price', name: 'Price', options: ['Under ₹1,000', '₹1,000-₹5,000', '₹5,000-₹20,000', 'Over ₹20,000'] },
  { id: 'availability', name: 'Availability', options: ['In Stock', 'Fast Delivery'] },
];

export default function EnhancedSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({});
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const searchRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  
  // Filter suggestions based on search query
  useEffect(() => {
    if (searchQuery.length >= 3) {
      const filtered = popularSearchTerms.filter(term => 
        term.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredSuggestions(filtered.slice(0, 5)); // Limit to 5 suggestions
      setShowSuggestions(true);
    } else {
      setShowSuggestions(false);
    }
  }, [searchQuery]);
  
  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (searchQuery.trim()) {
      // In a real app, we would save this to recent searches
      console.log('Searching for:', searchQuery);
      console.log('With filters:', selectedFilters);
      
      // Navigate to search results page
      const filterParams = Object.entries(selectedFilters)
        .filter(([_, values]) => values.length > 0)
        .map(([key, values]) => `${key}=${values.join(',')}`)
        .join('&');
        
      const searchPath = `/search?q=${encodeURIComponent(searchQuery)}${filterParams ? `&${filterParams}` : ''}`;
      router.push(searchPath);
      
      // Close suggestions
      setShowSuggestions(false);
    }
  };
  
  // Clear search
  const clearSearch = () => {
    setSearchQuery('');
    setSelectedFilters({});
    setShowSuggestions(false);
  };
  
  // Toggle filter selection
  const toggleFilter = (category: string, value: string) => {
    setSelectedFilters(prev => {
      const current = prev[category] || [];
      const updated = current.includes(value)
        ? current.filter(v => v !== value)
        : [...current, value];
        
      return {
        ...prev,
        [category]: updated
      };
    });
  };
  
  // Highlight matching text in suggestions
  const highlightMatch = (text: string) => {
    if (!searchQuery || searchQuery.length < 3) return text;
    
    const regex = new RegExp(`(${searchQuery})`, 'gi');
    const parts = text.split(regex);
    
    return (
      <>
        {parts.map((part, i) => 
          regex.test(part) ? (
            <span key={i} className="bg-yellow-200 font-medium">{part}</span>
          ) : (
            <span key={i}>{part}</span>
          )
        )}
      </>
    );
  };
  
  return (
    <div className="relative w-full" ref={searchRef}>
      <form onSubmit={handleSearch} className="w-full">
        <div className="relative">
          {/* Search icon */}
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg 
              className="w-5 h-5 text-gray-500" 
              aria-hidden="true" 
              xmlns="http://www.w3.org/2000/svg" 
              fill="none" 
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth="2" 
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
          
          {/* Search input */}
          <input
            type="search"
            id="enhanced-search"
            className="block w-full p-3 pl-10 pr-20 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-blue-500 focus:border-blue-500 transition-all duration-300"
            placeholder="Search products, categories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={() => {
              if (searchQuery.length >= 3) {
                setShowSuggestions(true);
              }
            }}
            aria-expanded={showSuggestions}
            aria-controls="search-suggestions"
          />
          
          {/* Clear button */}
          {searchQuery && (
            <button
              type="button"
              className="absolute right-16 inset-y-0 flex items-center pr-3 text-gray-500 hover:text-gray-700"
              onClick={clearSearch}
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span className="sr-only">Clear search</span>
            </button>
          )}
          
          {/* Search button */}
          <button
            type="submit"
            className="absolute right-0 bottom-0 top-0 px-4 bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-r-lg text-sm text-white transition-colors duration-300"
          >
            Search
          </button>
        </div>
        
        {/* Selected filters */}
        {Object.entries(selectedFilters).some(([_, values]) => values.length > 0) && (
          <div className="flex flex-wrap gap-2 mt-2">
            {Object.entries(selectedFilters).map(([category, values]) => 
              values.map(value => (
                <div 
                  key={`${category}-${value}`}
                  className="flex items-center bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-1 rounded-full"
                >
                  {value}
                  <button 
                    type="button"
                    className="ml-1.5 text-blue-600 hover:text-blue-800"
                    onClick={() => toggleFilter(category, value)}
                  >
                    <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    <span className="sr-only">Remove filter</span>
                  </button>
                </div>
              ))
            )}
            <button
              type="button"
              className="text-xs text-gray-600 hover:text-gray-900 underline"
              onClick={() => setSelectedFilters({})}
            >
              Clear all
            </button>
          </div>
        )}
      </form>
      
      {/* Search suggestions dropdown */}
      {showSuggestions && (
        <div 
          id="search-suggestions"
          className="absolute z-10 w-full mt-1 bg-white rounded-md shadow-lg border border-gray-200 overflow-hidden"
        >
          {/* Recent searches */}
          {searchQuery.length < 3 && RECENT_SEARCHES.length > 0 && (
            <div className="p-3 border-b border-gray-200">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Recent Searches</h3>
              <ul className="space-y-1">
                {RECENT_SEARCHES.map((term, index) => (
                  <li key={index}>
                    <button
                      type="button"
                      className="flex items-center w-full px-2 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                      onClick={() => {
                        setSearchQuery(term);
                        setShowSuggestions(false);
                      }}
                    >
                      <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      {term}
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          )}
          
          {/* Live suggestions */}
          {searchQuery.length >= 3 && (
            <div className="p-3 border-b border-gray-200">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Suggestions</h3>
              {filteredSuggestions.length > 0 ? (
                <ul className="space-y-1">
                  {filteredSuggestions.map((term, index) => (
                    <li key={index}>
                      <button
                        type="button"
                        className="flex items-center w-full px-2 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded-md"
                        onClick={() => {
                          setSearchQuery(term);
                          setShowSuggestions(false);
                        }}
                      >
                        <svg className="w-4 h-4 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        {highlightMatch(term)}
                      </button>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-gray-500">No suggestions found</p>
              )}
            </div>
          )}
          
          {/* Filter tags */}
          <div className="p-3">
            <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2">Filter By</h3>
            <div className="space-y-3">
              {FILTER_TAGS.map((filter) => (
                <div key={filter.id} className="space-y-1">
                  <h4 className="text-sm font-medium text-gray-700">{filter.name}</h4>
                  <div className="flex flex-wrap gap-2">
                    {filter.options.map((option) => {
                      const isSelected = (selectedFilters[filter.id] || []).includes(option);
                      return (
                        <button
                          key={option}
                          type="button"
                          className={`px-2 py-1 text-xs font-medium rounded-full transition-colors duration-300 ${
                            isSelected
                              ? 'bg-blue-100 text-blue-800'
                              : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                          }`}
                          onClick={() => toggleFilter(filter.id, option)}
                        >
                          {option}
                          {isSelected && (
                            <span className="ml-1">✓</span>
                          )}
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
