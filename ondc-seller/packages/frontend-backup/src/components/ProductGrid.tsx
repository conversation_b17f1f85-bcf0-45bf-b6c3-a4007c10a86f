'use client';

import { useState } from 'react';
import Image from 'next/image';
import { formatPrice } from '../data/categories';
import AddToCartButton from './cart/AddToCartButton';
import Link from 'next/link';

export interface ProductVariant {
  id: string;
  title: string;
  prices: Array<{
    amount: number;
    currency_code: string;
  }>;
  inventory_quantity: number;
}

export interface Product {
  id: string;
  title: string;
  description: string | null;
  thumbnail: string | null;
  handle: string;
  status: string;
  variants: ProductVariant[];
  tenant_id?: string;
  images?: Array<{
    id: string;
    url: string;
  }>;
}

interface ProductGridProps {
  products: Product[];
  isLoading?: boolean;
  error?: Error | null;
  onSortChange?: (sortOption: string) => void;
  sortOptions?: Array<{ value: string; label: string }>;
  emptyStateMessage?: string;
  emptyStateAction?: React.ReactNode;
}

export default function ProductGrid({
  products,
  isLoading = false,
  error = null,
  onSortChange,
  sortOptions = [
    { value: 'newest', label: 'Newest' },
    { value: 'price-asc', label: 'Price: Low to High' },
    { value: 'price-desc', label: 'Price: High to Low' },
    { value: 'name-asc', label: 'Name: A to Z' },
    { value: 'name-desc', label: 'Name: Z to A' },
  ],
  emptyStateMessage = 'No products found',
  emptyStateAction,
}: ProductGridProps) {
  const [sortBy, setSortBy] = useState('newest');

  // Handle sort change
  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSortOption = e.target.value;
    setSortBy(newSortOption);
    if (onSortChange) {
      onSortChange(newSortOption);
    }
  };

  // Get product price
  const getProductPrice = (product: Product) => {
    if (
      product.variants &&
      product.variants.length > 0 &&
      product.variants[0].prices &&
      product.variants[0].prices.length > 0
    ) {
      return product.variants[0].prices[0];
    }
    return { amount: 0, currency_code: 'INR' };
  };

  // Get product inventory
  const getProductInventory = (product: Product) => {
    if (product.variants && product.variants.length > 0) {
      return product.variants[0].inventory_quantity;
    }
    return 0;
  };

  // Get discount percentage (if applicable)
  const getDiscountPercentage = (variant: ProductVariant) => {
    if (variant.prices && variant.prices.length > 1) {
      const originalPrice = variant.prices[0].amount;
      const discountedPrice = variant.prices[1].amount;
      if (originalPrice > discountedPrice) {
        return Math.round(((originalPrice - discountedPrice) / originalPrice) * 100);
      }
    }
    return null;
  };

  // Get tenant badge color
  const getTenantBadgeColor = (tenantId: string) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-green-100 text-green-800',
      'bg-yellow-100 text-yellow-800',
      'bg-red-100 text-red-800',
      'bg-purple-100 text-purple-800',
    ];

    // Simple hash function to determine color based on tenant ID
    const hash = tenantId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[hash % colors.length];
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-6">
          <p className="text-sm text-gray-500">Loading products...</p>
          <div className="w-48 h-10 bg-gray-200 animate-pulse rounded"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200"
            >
              <div className="h-60 bg-gray-200 animate-pulse"></div>
              <div className="p-4 space-y-3">
                <div className="h-6 bg-gray-200 animate-pulse rounded"></div>
                <div className="h-4 bg-gray-200 animate-pulse rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 animate-pulse rounded w-1/2"></div>
                <div className="h-10 bg-gray-200 animate-pulse rounded mt-4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-lg font-medium text-red-800">Error loading products</p>
              <p className="text-sm text-red-700 mt-1">{error.message}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="w-full">
        <div className="flex justify-between items-center mb-6">
          <p className="text-sm text-gray-500">No products found</p>
          {onSortChange && (
            <div className="flex items-center">
              <label
                htmlFor="sort"
                className="text-sm font-medium text-gray-700 mr-2 whitespace-nowrap"
              >
                Sort by:
              </label>
              <select
                id="sort"
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                value={sortBy}
                onChange={handleSortChange}
              >
                {sortOptions.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          )}
        </div>

        {/* Import and use NoProductsFound component */}
        <div className="w-full">
          {/* We're using a div here instead of directly importing NoProductsFound
              because the component is used by the parent components with proper context */}
          <div className="text-center py-10 bg-white rounded-lg border border-gray-200 shadow-sm">
            {/* Empty state illustration */}
            <div className="relative w-40 h-40 mx-auto mb-4">
              <div className="w-full h-full flex items-center justify-center">
                <svg
                  className="h-24 w-24 text-gray-300"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
                  />
                </svg>
              </div>
            </div>

            {/* Message */}
            <h3 className="text-xl font-medium text-gray-900 mb-2">No Products Available</h3>
            <p className="text-gray-500 max-w-md mx-auto">{emptyStateMessage}</p>

            {/* Action */}
            {emptyStateAction && <div className="mt-6">{emptyStateAction}</div>}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-6">
        <p className="text-sm text-gray-500">Showing {products.length} products</p>
        {onSortChange && (
          <div className="flex items-center">
            <label
              htmlFor="sort"
              className="text-sm font-medium text-gray-700 mr-2 whitespace-nowrap"
            >
              Sort by:
            </label>
            <select
              id="sort"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              value={sortBy}
              onChange={handleSortChange}
            >
              {sortOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {products
          .filter(product => product && product.id) // Filter out invalid products
          .map(product => {
            const price = getProductPrice(product);
            const inventoryQuantity = getProductInventory(product);
            const hasDiscount =
              product.variants &&
              product.variants.length > 0 &&
              getDiscountPercentage(product.variants[0]);
            const discountPercentage = hasDiscount
              ? getDiscountPercentage(product.variants[0])
              : null;

            return (
              <div
                key={product.id}
                className="group relative bg-white rounded-lg shadow-sm overflow-hidden border border-gray-200 transition-all duration-300 hover:shadow-md"
              >
                {/* Product image */}
                <div className="relative h-60 bg-gray-100 overflow-hidden">
                  {product.thumbnail ? (
                    <Image
                      src={product.thumbnail}
                      alt={product.title}
                      fill
                      className="object-cover object-center group-hover:scale-105 transition-transform duration-300"
                      sizes="(max-width: 768px) 100vw, 300px"
                      onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
                        e.currentTarget.src = '/images/placeholder.png';
                      }}
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full bg-gray-200">
                      <svg
                        className="h-12 w-12 text-gray-400"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={1}
                          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                    </div>
                  )}

                  {/* Discount badge */}
                  {discountPercentage && (
                    <div className="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                      {discountPercentage}% OFF
                    </div>
                  )}
                </div>

                {/* Product details */}
                <div className="p-4">
                  <div className="mb-2 flex items-center justify-between">
                    <h3 className="text-lg font-medium text-gray-900 line-clamp-1">
                      <Link href={{ pathname: `/products/${product.id}`, query: { from: 'category', category: product.category || '' } }} className="hover:underline text-blue-700">{product.title}</Link>
                    </h3>
                    {product.tenant_id && (
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${getTenantBadgeColor(product.tenant_id)}`}
                      >
                        {product.tenant_id}
                      </span>
                    )}
                  </div>

                  <p className="text-sm text-gray-500 mb-3 line-clamp-2">{product.description}</p>

                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <span className="text-lg font-bold text-gray-900">
                        {formatPrice(price.amount, price.currency_code)}
                      </span>
                      {hasDiscount && (
                        <span className="ml-2 text-sm text-gray-500 line-through">
                          {formatPrice(
                            product.variants[0].prices[0].amount,
                            product.variants[0].prices[0].currency_code
                          )}
                        </span>
                      )}
                    </div>
                    <span
                      className={`text-sm ${inventoryQuantity > 0 ? 'text-green-600' : 'text-red-600'}`}
                    >
                      {inventoryQuantity > 0 ? `In Stock (${inventoryQuantity})` : 'Out of Stock'}
                    </span>
                  </div>

                  {/* Add to cart button */}
                  {product.variants && product.variants.length > 0 && (
                    <AddToCartButton
                      variantId={product.variants[0].id}
                      className="w-full"
                      showQuantity={false}
                      maxQuantity={Math.min(inventoryQuantity, 10)}
                    />
                  )}
                </div>
              </div>
            );
          })}
      </div>
    </div>
  );
}
