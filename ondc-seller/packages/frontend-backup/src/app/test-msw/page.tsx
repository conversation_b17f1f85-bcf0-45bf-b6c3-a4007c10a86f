'use client';

import { useState } from 'react';
import { isMSWEnabledInStorage } from '../../lib/msw';

/**
 * MSW Testing Page
 * Demonstrates MSW functionality and allows testing of different scenarios
 */
export default function TestMSWPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const runTest = async (testName: string, url: string, options?: RequestInit) => {
    setIsLoading(true);
    const startTime = Date.now();
    
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          'x-tenant-id': 'default',
          ...options?.headers,
        },
        ...options,
      });
      
      const endTime = Date.now();
      const data = await response.json();
      
      const result = {
        testName,
        url,
        status: response.status,
        statusText: response.statusText,
        responseTime: endTime - startTime,
        data,
        success: response.ok,
        timestamp: new Date().toISOString(),
      };
      
      setTestResults(prev => [result, ...prev]);
    } catch (error) {
      const endTime = Date.now();
      const result = {
        testName,
        url,
        status: 0,
        statusText: 'Network Error',
        responseTime: endTime - startTime,
        data: { error: error instanceof Error ? error.message : 'Unknown error' },
        success: false,
        timestamp: new Date().toISOString(),
      };
      
      setTestResults(prev => [result, ...prev]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const tests = [
    {
      name: 'Health Check',
      url: 'http://localhost:9000/health',
      method: 'GET',
    },
    {
      name: 'Get Products',
      url: 'http://localhost:9000/store/products',
      method: 'GET',
    },
    {
      name: 'Get Single Product',
      url: 'http://localhost:9000/store/products/prod_01',
      method: 'GET',
    },
    {
      name: 'Get Collections',
      url: 'http://localhost:9000/store/collections',
      method: 'GET',
    },
    {
      name: 'Non-existent Product (404)',
      url: 'http://localhost:9000/store/products/non-existent',
      method: 'GET',
    },
  ];

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-white rounded-lg shadow-md p-6 mb-6">
        <h1 className="text-3xl font-bold mb-4">MSW Testing Dashboard</h1>
        
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h2 className="text-lg font-semibold mb-2">Current Status</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white p-3 rounded border">
              <div className="text-sm text-gray-600">MSW Status</div>
              <div className={`font-medium ${isMSWEnabledInStorage() ? 'text-green-600' : 'text-red-600'}`}>
                {isMSWEnabledInStorage() ? 'Active' : 'Inactive'}
              </div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="text-sm text-gray-600">Environment</div>
              <div className="font-medium">{process.env.NODE_ENV}</div>
            </div>
            <div className="bg-white p-3 rounded border">
              <div className="text-sm text-gray-600">Tests Run</div>
              <div className="font-medium">{testResults.length}</div>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-semibold mb-4">API Tests</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
            {tests.map((test, index) => (
              <button
                key={index}
                onClick={() => runTest(test.name, test.url, { method: test.method })}
                disabled={isLoading}
                className="p-3 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300 disabled:cursor-not-allowed transition-colors"
              >
                {test.name}
              </button>
            ))}
          </div>
          
          <div className="flex gap-2">
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
            >
              Clear Results
            </button>
          </div>
        </div>

        {isLoading && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              Running test...
            </div>
          </div>
        )}

        <div>
          <h2 className="text-lg font-semibold mb-4">Test Results</h2>
          {testResults.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              No tests run yet. Click a test button above to start.
            </div>
          ) : (
            <div className="space-y-4">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-medium">{result.testName}</h3>
                    <div className="text-sm text-gray-500">{new Date(result.timestamp).toLocaleTimeString()}</div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-2 text-sm mb-2">
                    <div>
                      <span className="text-gray-600">Status:</span>{' '}
                      <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                        {result.status} {result.statusText}
                      </span>
                    </div>
                    <div>
                      <span className="text-gray-600">Response Time:</span> {result.responseTime}ms
                    </div>
                    <div className="md:col-span-2">
                      <span className="text-gray-600">URL:</span> {result.url}
                    </div>
                  </div>
                  
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm text-gray-600 hover:text-gray-800">
                      View Response Data
                    </summary>
                    <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
