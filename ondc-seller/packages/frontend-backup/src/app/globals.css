@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary brand colors */
  --color-primary: 59, 130, 246; /* blue-500 */
  --color-primary-dark: 37, 99, 235; /* blue-600 */
  --color-primary-light: 96, 165, 250; /* blue-400 */

  /* Secondary colors */
  --color-secondary: 79, 70, 229; /* indigo-600 */
  --color-secondary-light: 129, 140, 248; /* indigo-400 */

  /* Accent colors */
  --color-accent: 245, 158, 11; /* amber-500 */

  /* Neutral colors */
  --color-background: 249, 250, 251; /* gray-50 */
  --color-surface: 255, 255, 255; /* white */
  --color-text: 31, 41, 55; /* gray-800 */
  --color-text-light: 107, 114, 128; /* gray-500 */

  /* Status colors */
  --color-success: 16, 185, 129; /* green-500 */
  --color-error: 239, 68, 68; /* red-500 */
  --color-warning: 245, 158, 11; /* amber-500 */
  --color-info: 59, 130, 246; /* blue-500 */
}

/* Custom component styles */
@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-300 transform hover:scale-105 active:scale-95;
  }

  .btn-primary {
    @apply bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg;
  }

  .btn-secondary {
    @apply bg-indigo-600 hover:bg-indigo-700 text-white shadow-md hover:shadow-lg;
  }

  .btn-outline {
    @apply border border-blue-500 text-blue-500 hover:bg-blue-50 hover:border-blue-600;
  }

  .btn-gradient {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white shadow-lg hover:shadow-xl;
  }

  .btn-animated {
    @apply relative overflow-hidden before:absolute before:inset-0 before:bg-white before:opacity-0 before:transition-opacity before:duration-300 hover:before:opacity-20;
  }

  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1;
  }

  .card-interactive {
    @apply card cursor-pointer transform hover:scale-105;
  }

  .card-glow {
    @apply card hover:shadow-blue-500/25 hover:shadow-2xl;
  }

  .badge {
    @apply px-2 py-1 text-xs font-semibold rounded-full transition-all duration-200;
  }

  .badge-success {
    @apply bg-green-100 text-green-800 hover:bg-green-200;
  }

  .badge-error {
    @apply bg-red-100 text-red-800 hover:bg-red-200;
  }

  .badge-warning {
    @apply bg-yellow-100 text-yellow-800 hover:bg-yellow-200;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800 hover:bg-blue-200;
  }

  .dropdown {
    @apply relative inline-block;
  }

  .dropdown-content {
    @apply absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-10 opacity-0 invisible transform scale-95 transition-all duration-200;
  }

  .dropdown:hover .dropdown-content {
    @apply opacity-100 visible scale-100;
  }

  .input-animated {
    @apply transition-all duration-300 border-2 border-gray-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:scale-105;
  }

  .skeleton {
    @apply bg-gray-200 animate-pulse rounded;
  }

  .skeleton-text {
    @apply skeleton h-4 w-full mb-2;
  }

  .skeleton-avatar {
    @apply skeleton h-12 w-12 rounded-full;
  }

  .loading-shimmer {
    @apply relative overflow-hidden bg-gray-200;
  }

  .loading-shimmer::after {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-shimmer;
    content: '';
  }

  .scroll-reveal {
    @apply opacity-0 translate-y-8 transition-all duration-600;
  }

  .scroll-reveal.revealed {
    @apply opacity-100 translate-y-0;
  }

  .floating-element {
    @apply animate-float;
  }

  .glow-effect {
    @apply animate-glow;
  }

  .progress-bar {
    @apply bg-gray-200 rounded-full overflow-hidden;
  }

  .progress-fill {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 h-full transition-all duration-500 ease-out;
  }

  .stats-card {
    @apply card p-6 text-center transform hover:scale-105 transition-all duration-300;
  }

  .stats-number {
    @apply text-3xl font-bold text-blue-600 animate-counter;
  }

  .nav-link {
    @apply relative px-3 py-2 text-gray-700 hover:text-blue-600 transition-all duration-300;
  }

  .nav-link::after {
    @apply absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300;
    content: '';
  }

  .nav-link:hover::after {
    @apply w-full;
  }

  .nav-link.active::after {
    @apply w-full;
  }
}

body {
  color: rgb(var(--color-text));
  background: rgb(var(--color-background));
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
