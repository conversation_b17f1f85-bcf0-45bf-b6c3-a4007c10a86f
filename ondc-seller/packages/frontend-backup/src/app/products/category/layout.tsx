import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Product Categories | ONDC Seller Platform',
  description: 'Browse our product categories including Electronics, Fashion, Home & Kitchen, Beauty & Personal Care, and Grocery & Gourmet.',
  openGraph: {
    title: 'Product Categories | ONDC Seller Platform',
    description: 'Browse our product categories including Electronics, Fashion, Home & Kitchen, Beauty & Personal Care, and Grocery & Gourmet.',
    type: 'website',
    locale: 'en_IN',
    siteName: 'ONDC Seller Platform',
  },
};

export default function CategoryLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return <>{children}</>;
}
