'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Breadcrumbs from '../../components/Breadcrumbs';
import Image from 'next/image';
import ErrorPage from '../../components/ErrorPage';
import { handleApiError, shouldShowErrorPage, getErrorPageStatusCode } from '../../lib/error';
import { ApiError } from '../../types/api';
import { productCategories } from '../../data/categories';

interface Product {
  id: string;
  title: string;
  description: string | null;
  thumbnail: string | null;
  price: {
    amount: number;
    currency_code: string;
  };
  inventory_quantity: number;
}

export default function ProductsPage() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<ApiError | null>(null);
  const [filter, setFilter] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(12);
  const [showFilters, setShowFilters] = useState(false);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        // Set a timeout for the fetch request to handle connection issues more gracefully
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        // Get the current tenant ID (or use default)
        const tenantId = localStorage.getItem('current_tenant_id') || 'default';
        console.log(`Fetching products for tenant: ${tenantId}`);

        // Use MSW-intercepted URL when MSW is enabled, otherwise use local API routes
        const isMSWActive = process.env.NEXT_PUBLIC_MSW_ENABLED === 'true';
        const apiUrl = isMSWActive ? 'http://localhost:9000/store/products' : '/api/store/products';

        console.log(
          `[Products] Using API: ${apiUrl} (MSW: ${isMSWActive ? 'enabled' : 'disabled'})`
        );

        const response = await fetch(apiUrl, {
          signal: controller.signal,
          method: 'GET',
          mode: 'cors',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
            'x-tenant-id': tenantId,
            Accept: 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Failed to fetch products: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Products fetched successfully:', data);

        // Transform the API response to match the expected format
        const transformedProducts = (data.products || []).map(product => {
          // Get the first variant's price or use a default
          const firstVariant = product.variants && product.variants[0];
          const firstPrice = firstVariant && firstVariant.prices && firstVariant.prices[0];

          return {
            ...product,
            // Add a price object that matches the expected format
            price: {
              amount: firstPrice ? firstPrice.amount : 0,
              currency_code: firstPrice ? firstPrice.currency_code : 'inr',
            },
            // Use the first variant's inventory if product doesn't have it directly
            inventory_quantity:
              product.inventory_quantity !== undefined
                ? product.inventory_quantity
                : firstVariant
                  ? firstVariant.inventory_quantity
                  : 0,
          };
        });

        console.log('Transformed products:', transformedProducts);
        setProducts(transformedProducts || []);
        setLoading(false);
        setError(null); // Clear any previous errors
      } catch (err) {
        console.error('Error fetching products:', err);

        // Handle the error using our standardized error handler
        const apiError = handleApiError(err);

        // For fetch errors that don't have status codes, check if it's a network error
        if (!apiError.status && err instanceof Error) {
          if (err.name === 'AbortError' || err.message.includes('aborted')) {
            apiError.status = 0;
            apiError.code = 'ECONNABORTED';
            apiError.message = 'Connection timeout - Backend server may not be running';
          } else if (
            err.message.includes('fetch') ||
            err.message.includes('Failed to fetch') ||
            err.message.includes('NetworkError') ||
            err.message.includes('ERR_NETWORK') ||
            err.message.includes('ECONNREFUSED') ||
            err.message.includes('connection')
          ) {
            apiError.status = 0;
            apiError.code = 'NETWORK_ERROR';
            apiError.message = 'Cannot connect to the backend server';
          } else {
            // For any other error without status, treat as network error
            apiError.status = 0;
            apiError.code = 'NETWORK_ERROR';
            apiError.message = 'Backend service is currently unavailable';
          }
        }

        setError(apiError);
        setLoading(false);

        // Don't use mock data - let the error page handle it
        setProducts([]);
      }
    };

    fetchProducts();
  }, []);

  const formatPrice = (amount?: number, currency?: string) => {
    if (amount === undefined || currency === undefined) {
      return 'Price not available';
    }
    try {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: (currency || 'INR').toUpperCase(),
      }).format(amount / 100);
    } catch (error) {
      console.error('Error formatting price:', error);
      return `₹${(amount / 100).toFixed(2)}`;
    }
  };

  // Filter products with safety checks for missing inventory data
  const filteredProducts = products.filter(product => {
    if (filter === 'all') return true;
    if (filter === 'in-stock')
      return product.inventory_quantity !== undefined && product.inventory_quantity > 0;
    if (filter === 'out-of-stock')
      return product.inventory_quantity === undefined || product.inventory_quantity === 0;
    return true;
  });

  // Sort products with safety checks for missing data
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    if (sortBy === 'name') {
      return (a.title || '').localeCompare(b.title || '');
    }
    if (sortBy === 'price-low') {
      const aAmount = a.price?.amount || 0;
      const bAmount = b.price?.amount || 0;
      return aAmount - bAmount;
    }
    if (sortBy === 'price-high') {
      const aAmount = a.price?.amount || 0;
      const bAmount = b.price?.amount || 0;
      return bAmount - aAmount;
    }
    if (sortBy === 'stock') {
      const aStock = a.inventory_quantity || 0;
      const bStock = b.inventory_quantity || 0;
      return bStock - aStock;
    }
    return 0;
  });

  if (loading) {
    return (
      <div>
        <Breadcrumbs
          items={[
            { label: 'Home', href: '/' },
            { label: 'Featured Products', href: '/products', active: true },
          ]}
        />
        <div className="text-center py-10">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-600"></div>
          <p className="mt-2 text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  // Show error page for critical errors
  if (error && shouldShowErrorPage(error)) {
    const handleRetry = () => {
      setLoading(true);
      setError(null);
      // Trigger a re-fetch by calling the effect again
      window.location.reload();
    };

    return (
      <ErrorPage
        statusCode={getErrorPageStatusCode(error)}
        title="Products Unavailable"
        message={error.message}
        showRetry={true}
        onRetry={handleRetry}
        showBackButton={true}
        backUrl="/"
      />
    );
  }

  // Breadcrumb items
  const breadcrumbItems = [
    { label: 'Home', href: '/' },
    { label: 'Featured Products', href: '/products', active: true },
  ];

  return (
    <div className="container mx-auto px-4 py-8">
      <Breadcrumbs items={breadcrumbItems} className="mb-6" />

      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4 mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Featured Products</h1>

        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center space-x-2">
            <label htmlFor="filter" className="text-sm font-medium text-gray-700 whitespace-nowrap">
              Filter:
            </label>
            <select
              id="filter"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              value={filter}
              onChange={e => setFilter(e.target.value)}
            >
              <option value="all">All Products</option>
              <option value="in-stock">In Stock</option>
              <option value="out-of-stock">Out of Stock</option>
            </select>
          </div>

          <div className="flex items-center space-x-2">
            <label htmlFor="sort" className="text-sm font-medium text-gray-700 whitespace-nowrap">
              Sort by:
            </label>
            <select
              id="sort"
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
            >
              <option value="name">Name</option>
              <option value="price-low">Price: Low to High</option>
              <option value="price-high">Price: High to Low</option>
              <option value="stock">Stock Level</option>
            </select>
          </div>

          <Link
            href="/products/new"
            className="inline-flex items-center justify-center bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 transition-colors duration-300"
          >
            <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Add New Product
          </Link>
        </div>
      </div>

      {error && !shouldShowErrorPage(error) && (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4 flex items-center">
          <svg
            className="w-5 h-5 mr-2 flex-shrink-0"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
            />
          </svg>
          <div>
            <p className="font-medium">Warning</p>
            <p className="text-sm">{error.message}</p>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {sortedProducts.map(product => (
          <div
            key={product.id}
            className="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:-translate-y-1 hover:shadow-lg"
          >
            <div className="h-48 bg-gray-200 relative">
              {product.thumbnail ? (
                <Image
                  src={product.thumbnail}
                  alt={product.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, 300px"
                />
              ) : (
                <div className="flex items-center justify-center h-full bg-gray-100">
                  <svg
                    className="w-12 h-12 text-gray-400"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={1}
                      d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
              )}
              {product.inventory_quantity === 0 && (
                <div className="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                  Out of Stock
                </div>
              )}
            </div>
            <div className="p-4">
              <h2 className="text-xl font-semibold mb-2">{product.title}</h2>
              <p className="text-gray-600 mb-2 line-clamp-2">{product.description}</p>
              <div className="flex justify-between items-center">
                <span className="text-lg font-bold">
                  {product.price
                    ? formatPrice(product.price.amount, product.price.currency_code)
                    : 'Price not available'}
                </span>
                <span
                  className={`text-sm font-medium ${
                    product.inventory_quantity && product.inventory_quantity > 0
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}
                >
                  {product.inventory_quantity !== undefined && product.inventory_quantity > 0
                    ? `In Stock (${product.inventory_quantity})`
                    : 'Out of Stock'}
                </span>
              </div>
              <div className="mt-4 flex space-x-2">
                <Link
                  href={`/products/${product.id}`}
                  className="flex-1 bg-blue-600 text-white px-3 py-1.5 rounded text-sm hover:bg-blue-700 transition-colors duration-300 flex items-center justify-center"
                >
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                    />
                  </svg>
                  View
                </Link>
                <Link
                  href={`/products/${product.id}/edit`}
                  className="flex-1 bg-yellow-600 text-white px-3 py-1.5 rounded text-sm hover:bg-yellow-700 transition-colors duration-300 flex items-center justify-center"
                >
                  <svg
                    className="w-4 h-4 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                    />
                  </svg>
                  Edit
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>

      {sortedProducts.length === 0 && (
        <div className="text-center py-10 bg-gray-50 rounded-lg">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1}
              d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"
            />
          </svg>
          <p className="mt-2 text-gray-500">No products found.</p>
          <Link
            href="/products/new"
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors duration-300"
          >
            <svg className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              />
            </svg>
            Add Your First Product
          </Link>
        </div>
      )}
    </div>
  );
}
