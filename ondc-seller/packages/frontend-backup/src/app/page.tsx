'use client';

import Link from 'next/link';
import HeroCarousel from '../components/HeroCarousel';
import Image from 'next/image';
import { productCategories } from '../data/categories';
import { useState, useEffect } from 'react';
import StatsCards from '../components/StatsCards';
import ProductShowcase from '../components/ProductShowcase';

// User role detection hook
const useUserRole = () => {
  const [userRole, setUserRole] = useState<'admin' | 'seller'>('seller');

  useEffect(() => {
    // In development mode, use hardcoded credentials
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // Check for demo admin credentials
      const username = localStorage.getItem('demo_username') || 'demo';
      const password = localStorage.getItem('demo_password') || 'demo';

      // Admin users have access to business overview
      if (username === 'admin' || username === 'demo') {
        setUserRole('admin');
      } else {
        setUserRole('seller');
      }
    } else {
      // In production, check actual user role from authentication
      const userRole = localStorage.getItem('user_role') || 'seller';
      setUserRole(userRole as 'admin' | 'seller');
    }
  }, []);

  return userRole;
};

export default function Home() {
  const [isVisible, setIsVisible] = useState(false);
  const userRole = useUserRole();

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleCategoryClick = (href: string) => {
    window.location.href = href;
  };

  return (
    <div className="flex flex-col space-y-12 pb-12 relative">
      {/* Hero Carousel */}
      <div className="-mx-4 animate-fade-in">
        <HeroCarousel />
      </div>

      {/* Stats Cards - Only visible for admin users */}
      {userRole === 'admin' && (
        <section
          className={`transition-all duration-800 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
        >
          <StatsCards />
        </section>
      )}

      {/* Featured Categories */}
      <section
        className={`py-8 transition-all duration-800 delay-200 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
      >
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Featured Categories</h2>
            <p className="text-gray-600">Discover products across various categories</p>
          </div>
          <div className="flex justify-center md:justify-end">
            <Link
              href="/products"
              className="btn btn-gradient inline-flex items-center whitespace-nowrap"
            >
              <span>View All Categories</span>
              <svg
                className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Link>
          </div>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
          {productCategories.map((category, index) => (
            <div
              key={category.id}
              className={`group relative overflow-hidden rounded-xl shadow-lg transition-all duration-500 hover:-translate-y-2 hover:shadow-2xl hover:shadow-blue-500/25 cursor-pointer ${
                isVisible ? 'animate-fade-in-up' : 'opacity-0'
              }`}
              style={{ animationDelay: `${index * 100}ms` }}
              onClick={() => handleCategoryClick(category.href)}
            >
              <div className="aspect-w-1 aspect-h-1 bg-gradient-to-br from-blue-50 to-blue-100 relative h-48">
                {category.featured && category.featured[0] && (
                  <Image
                    src={category.featured[0].imageSrc}
                    alt={category.name}
                    fill
                    className="object-cover transition-all duration-500 group-hover:scale-110"
                  />
                )}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-80 group-hover:opacity-60 transition-opacity duration-300"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 transform transition-transform duration-300 group-hover:translate-y-0">
                  <h3 className="text-lg font-bold text-white mb-1 group-hover:text-blue-200 transition-colors duration-300">
                    {category.name}
                  </h3>
                  <p className="text-sm text-white/90 group-hover:text-blue-100 transition-colors duration-300">
                    {category.subcategories.length} subcategories
                  </p>
                  <div className="mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <span className="inline-flex items-center text-xs text-blue-200 font-medium">
                      Explore now
                      <svg
                        className="ml-1 h-3 w-3"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </span>
                  </div>
                </div>
                <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                    <svg
                      className="h-4 w-4 text-white"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 5l7 7-7 7"
                      />
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Product Showcase */}
      <section
        className={`transition-all duration-800 delay-400 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
      >
        <ProductShowcase />
      </section>

      {/* Platform Features */}
      <section
        className={`py-12 transition-all duration-800 delay-600 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
      >
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Why Choose ONDC Seller Platform</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Empower your business with cutting-edge tools and seamless integrations
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {[
            {
              icon: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4',
              title: 'Manage Products',
              description:
                'Easily add, edit, and organize your product catalog with our intuitive tools and bulk upload features.',
              link: '/products',
              linkText: 'View Products',
              color: 'blue',
              delay: '0ms',
            },
            {
              icon: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
              title: 'Process Orders',
              description:
                'Track and manage customer orders, process shipments, and handle returns efficiently with real-time updates.',
              link: '/orders',
              linkText: 'View Orders',
              color: 'green',
              delay: '200ms',
            },
            {
              icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z',
              title: 'Track Analytics',
              description:
                'Make data-driven decisions with real-time insights, performance metrics, and comprehensive business analytics.',
              link: '/analytics',
              linkText: 'View Analytics',
              color: 'purple',
              delay: '400ms',
            },
          ].map((feature, index) => (
            <div
              key={index}
              className={`card-glow p-8 group cursor-pointer transition-all duration-500 hover:scale-105 ${
                isVisible ? 'animate-fade-in-up' : 'opacity-0'
              }`}
              style={{ animationDelay: feature.delay }}
            >
              <div
                className={`bg-gradient-to-br from-${feature.color}-100 to-${feature.color}-200 text-${feature.color}-600 p-4 rounded-2xl w-16 h-16 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 floating-element`}
              >
                <svg className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d={feature.icon}
                  />
                </svg>
              </div>
              <h3 className="text-2xl font-bold mb-4 group-hover:text-blue-600 transition-colors duration-300">
                {feature.title}
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">{feature.description}</p>
              <Link
                href={feature.link}
                className="inline-flex items-center text-blue-600 hover:text-blue-800 font-semibold group-hover:translate-x-2 transition-all duration-300"
              >
                {feature.linkText}
                <svg
                  className="ml-2 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                  />
                </svg>
              </Link>
            </div>
          ))}
        </div>
      </section>

      {/* Call to Action */}
      <section
        className={`bg-gradient-to-br from-blue-600 via-blue-700 to-blue-800 text-white rounded-2xl shadow-2xl overflow-hidden transition-all duration-800 delay-800 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
      >
        <div className="md:flex relative">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/90 to-transparent z-10"></div>
          <div className="p-12 md:w-2/3 relative z-20">
            <h2 className="text-4xl font-bold mb-6 animate-fade-in-up">
              Ready to grow your business?
            </h2>
            <p
              className="text-xl mb-8 text-blue-100 leading-relaxed animate-fade-in-up"
              style={{ animationDelay: '200ms' }}
            >
              Join thousands of sellers on the Open Network for Digital Commerce and reach millions
              of customers across India with our comprehensive seller platform.
            </p>
            <div
              className="flex flex-col sm:flex-row gap-4 animate-fade-in-up"
              style={{ animationDelay: '400ms' }}
            >
              <Link
                href="/account"
                className="btn btn-animated inline-flex items-center px-8 py-4 bg-white text-blue-600 hover:bg-blue-50 font-bold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                Get Started Today
                <svg
                  className="ml-3 h-5 w-5 transition-transform duration-300 group-hover:translate-x-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M14 5l7 7m0 0l-7 7m7-7H3"
                  />
                </svg>
              </Link>
              <Link
                href="/products"
                className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white hover:bg-white/10 font-semibold rounded-xl transition-all duration-300 group backdrop-blur-sm"
              >
                Explore Features
                <svg
                  className="ml-3 h-5 w-5 transition-transform duration-300 group-hover:scale-110"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </Link>
            </div>
          </div>
          <div className="md:w-1/3 relative min-h-[300px] md:min-h-[400px]">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-400/20 to-blue-800/40"></div>
            <div className="h-full w-full relative">
              <Image
                src="/images/categories/electronics.svg"
                alt="Business growth"
                fill
                className="object-cover opacity-80 floating-element"
              />
            </div>
            {/* Decorative elements */}
            <div className="absolute top-8 right-8 w-16 h-16 bg-white/10 rounded-full animate-pulse"></div>
            <div className="absolute bottom-12 right-16 w-8 h-8 bg-white/20 rounded-full animate-bounce-gentle"></div>
            <div className="absolute top-1/2 right-4 w-4 h-4 bg-white/30 rounded-full animate-float"></div>
          </div>
        </div>
      </section>
    </div>
  );
}
