import type { <PERSON>ada<PERSON> } from 'next';
import { Inter, Poppins } from 'next/font/google';
import './globals.css';
import TenantSelectorWrapper from '../components/TenantSelectorWrapper';
import EnhancedSearch from '../components/EnhancedSearch';
import MegaMenu from '../components/MegaMenu';
import Footer from '../components/Footer';
import { CartProvider } from '../providers/CartProvider';
import { ToastProvider } from '../providers/ToastProvider';
import MiniCart from '../components/cart/MiniCart';
import MSWProvider from '../components/MSWProvider';
import ErrorBoundary from '../components/ErrorBoundary';

const inter = Inter({ subsets: ['latin'], variable: '--font-inter' });
const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  subsets: ['latin'],
  variable: '--font-poppins',
});

export const metadata: Metadata = {
  title: 'ONDC Seller Platform',
  description: 'A platform for sellers on the Open Network for Digital Commerce',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={`${inter.variable} ${poppins.variable} font-sans`}>
        <ErrorBoundary>
          <MSWProvider>
            <ToastProvider>
              <CartProvider>
                {/* Top notification bar */}
                <div className="bg-blue-600 text-white text-center py-2 text-sm">
                  <div className="container mx-auto">
                    Welcome to the ONDC Seller Platform - Connect with millions of buyers across
                    India
                  </div>
                </div>

                {/* Main header */}
                <header className="bg-white shadow-sm">
                  <div className="container mx-auto px-4 py-3">
                    <div className="flex justify-between items-center">
                      {/* Logo */}
                      <div className="flex items-center">
                        <h1 className="text-2xl font-bold text-blue-600 font-poppins">
                          ONDC Seller
                        </h1>
                      </div>

                      {/* Search bar */}
                      <div className="hidden md:block flex-grow mx-10">
                        <EnhancedSearch />
                      </div>

                      {/* Tenant selector and navigation */}
                      <div className="flex items-center space-x-6">
                        <TenantSelectorWrapper />

                        {/* Cart */}
                        <MiniCart />

                        {/* User menu */}
                        <div className="relative">
                          <a
                            href="/account"
                            className="flex items-center text-gray-700 hover:text-blue-600 transition-colors duration-300"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-6 w-6"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                              />
                            </svg>
                            <span className="ml-2 hidden lg:inline">Account</span>
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Mega Menu Navigation */}
                  <div className="border-t border-gray-200 bg-white relative">
                    <div className="container mx-auto overflow-visible">
                      <MegaMenu />
                    </div>
                  </div>

                  {/* Mobile search - only visible on small screens */}
                  <div className="md:hidden container mx-auto px-4 py-2">
                    <EnhancedSearch />
                  </div>
                </header>

                <main className="container mx-auto p-4 bg-gray-50 min-h-screen">{children}</main>

                <Footer />
              </CartProvider>
            </ToastProvider>
          </MSWProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}
