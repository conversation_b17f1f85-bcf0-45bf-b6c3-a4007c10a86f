'use client';

import { useState } from 'react';
import Breadcrumbs from '../../components/Breadcrumbs';
import ClientTenantDisplay from '../../components/ClientTenantDisplay';

// Settings tabs
const SETTINGS_TABS = [
  { id: 'general', name: 'General' },
  { id: 'store', name: 'Store Profile' },
  { id: 'ondc', name: 'ONDC Settings' },
  { id: 'shipping', name: 'Shipping' },
  { id: 'tax', name: 'Tax' },
  { id: 'payment', name: 'Payment Methods' },
  { id: 'notifications', name: 'Notifications' },
  { id: 'users', name: 'Users & Permissions' },
  { id: 'integrations', name: 'Integrations' },
];

// Sample store settings
const SAMPLE_STORE_SETTINGS = {
  name: 'Electronics Hub',
  email: '<EMAIL>',
  phone: '+91 9876543210',
  address: {
    line1: '123 Tech Park',
    line2: 'Sector 5',
    city: 'Bangalore',
    state: 'Karnataka',
    postalCode: '560001',
    country: 'India',
  },
  currency: 'INR',
  timezone: 'Asia/Kolkata',
  logo: '/images/store-logo.png',
  ondcSettings: {
    sellerId: 'ONDC-SELLER-123456',
    apiEndpoint: 'https://api.ondc.org/v1',
    apiKey: '********-****-****-****-************',
    subscriberId: 'ONDC-SUB-789012',
    subscriberUrl: 'https://electronicshub.com/ondc-api',
    categories: ['electronics', 'mobile', 'computer'],
    fulfillmentTypes: ['delivery', 'pickup'],
  },
};

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('general');
  const [storeSettings, setStoreSettings] = useState(SAMPLE_STORE_SETTINGS);
  const [isEditing, setIsEditing] = useState(false);

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;

    // Handle nested properties (e.g., address.city)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setStoreSettings({
        ...storeSettings,
        [parent]: {
          ...storeSettings[parent as keyof typeof storeSettings],
          [child]: value,
        },
      });
    } else {
      setStoreSettings({
        ...storeSettings,
        [name]: value,
      });
    }
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you would save the settings to the backend here
    setIsEditing(false);
    alert('Settings saved successfully!');
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Breadcrumbs */}
      <Breadcrumbs
        items={[
          { label: 'Home', href: '/' },
          { label: 'Settings', href: '/settings', active: true },
        ]}
      />

      <div className="flex flex-col md:flex-row md:items-center md:justify-between mt-6 mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Settings</h1>

        <div className="mt-4 md:mt-0">
          {isEditing ? (
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => setIsEditing(false)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSubmit}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Save Changes
              </button>
            </div>
          ) : (
            <button
              type="button"
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg
                className="mr-2 h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"
                />
              </svg>
              Edit Settings
            </button>
          )}
        </div>
      </div>

      <div className="mb-6 p-4 bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="flex items-center">
          <div className="flex-shrink-0 bg-blue-100 rounded-full p-2">
            <svg
              className="h-6 w-6 text-blue-600"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div className="ml-4">
            <h3 className="text-lg font-medium text-gray-900">Multi-Tenant Settings</h3>
            <p className="text-sm text-gray-600">
              Current Tenant: <ClientTenantDisplay />
            </p>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {/* Settings tabs */}
        <div className="border-b border-gray-200">
          <div className="px-6 overflow-x-auto overflow-y-hidden">
            <nav className="-mb-px flex space-x-6">
              {SETTINGS_TABS.map(tab => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm
                    ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }
                  `}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Settings content */}
        <div className="p-6">
          {activeTab === 'general' && (
            <form onSubmit={handleSubmit}>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900">General Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">Basic information about your store.</p>
                </div>

                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-3">
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                      Store Name
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="name"
                        id="name"
                        value={storeSettings.name}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Email Address
                    </label>
                    <div className="mt-1">
                      <input
                        type="email"
                        name="email"
                        id="email"
                        value={storeSettings.email}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                      Phone Number
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="phone"
                        id="phone"
                        value={storeSettings.phone}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="currency" className="block text-sm font-medium text-gray-700">
                      Currency
                    </label>
                    <div className="mt-1">
                      <select
                        id="currency"
                        name="currency"
                        value={storeSettings.currency}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option value="INR">Indian Rupee (₹)</option>
                        <option value="USD">US Dollar ($)</option>
                        <option value="EUR">Euro (€)</option>
                        <option value="GBP">British Pound (£)</option>
                      </select>
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label htmlFor="timezone" className="block text-sm font-medium text-gray-700">
                      Timezone
                    </label>
                    <div className="mt-1">
                      <select
                        id="timezone"
                        name="timezone"
                        value={storeSettings.timezone}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option value="Asia/Kolkata">India (GMT+5:30)</option>
                        <option value="America/New_York">Eastern Time (GMT-5:00)</option>
                        <option value="America/Los_Angeles">Pacific Time (GMT-8:00)</option>
                        <option value="Europe/London">London (GMT+0:00)</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900 mt-8">
                    Store Address
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">Your store's physical address.</p>
                </div>

                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-6">
                    <label
                      htmlFor="address.line1"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Address Line 1
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="address.line1"
                        id="address.line1"
                        value={storeSettings.address.line1}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <label
                      htmlFor="address.line2"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Address Line 2
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="address.line2"
                        id="address.line2"
                        value={storeSettings.address.line2}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label
                      htmlFor="address.city"
                      className="block text-sm font-medium text-gray-700"
                    >
                      City
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="address.city"
                        id="address.city"
                        value={storeSettings.address.city}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label
                      htmlFor="address.state"
                      className="block text-sm font-medium text-gray-700"
                    >
                      State / Province
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="address.state"
                        id="address.state"
                        value={storeSettings.address.state}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label
                      htmlFor="address.postalCode"
                      className="block text-sm font-medium text-gray-700"
                    >
                      ZIP / Postal Code
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="address.postalCode"
                        id="address.postalCode"
                        value={storeSettings.address.postalCode}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label
                      htmlFor="address.country"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Country
                    </label>
                    <div className="mt-1">
                      <select
                        id="address.country"
                        name="address.country"
                        value={storeSettings.address.country}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      >
                        <option value="India">India</option>
                        <option value="United States">United States</option>
                        <option value="United Kingdom">United Kingdom</option>
                        <option value="Canada">Canada</option>
                        <option value="Australia">Australia</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </form>
          )}

          {activeTab === 'ondc' && (
            <form onSubmit={handleSubmit}>
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-medium leading-6 text-gray-900">ONDC Settings</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Configure your Open Network for Digital Commerce (ONDC) integration.
                  </p>
                </div>

                <div className="grid grid-cols-1 gap-y-6 gap-x-4 sm:grid-cols-6">
                  <div className="sm:col-span-3">
                    <label
                      htmlFor="ondcSettings.sellerId"
                      className="block text-sm font-medium text-gray-700"
                    >
                      ONDC Seller ID
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="ondcSettings.sellerId"
                        id="ondcSettings.sellerId"
                        value={storeSettings.ondcSettings.sellerId}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label
                      htmlFor="ondcSettings.subscriberId"
                      className="block text-sm font-medium text-gray-700"
                    >
                      ONDC Subscriber ID
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="ondcSettings.subscriberId"
                        id="ondcSettings.subscriberId"
                        value={storeSettings.ondcSettings.subscriberId}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <label
                      htmlFor="ondcSettings.apiEndpoint"
                      className="block text-sm font-medium text-gray-700"
                    >
                      ONDC API Endpoint
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="ondcSettings.apiEndpoint"
                        id="ondcSettings.apiEndpoint"
                        value={storeSettings.ondcSettings.apiEndpoint}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-6">
                    <label
                      htmlFor="ondcSettings.subscriberUrl"
                      className="block text-sm font-medium text-gray-700"
                    >
                      Subscriber URL
                    </label>
                    <div className="mt-1">
                      <input
                        type="text"
                        name="ondcSettings.subscriberUrl"
                        id="ondcSettings.subscriberUrl"
                        value={storeSettings.ondcSettings.subscriberUrl}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                    </div>
                  </div>

                  <div className="sm:col-span-3">
                    <label
                      htmlFor="ondcSettings.apiKey"
                      className="block text-sm font-medium text-gray-700"
                    >
                      API Key
                    </label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <input
                        type="password"
                        name="ondcSettings.apiKey"
                        id="ondcSettings.apiKey"
                        value={storeSettings.ondcSettings.apiKey}
                        onChange={handleInputChange}
                        disabled={!isEditing}
                        className="shadow-sm focus:ring-blue-500 focus:border-blue-500 block w-full sm:text-sm border-gray-300 rounded-md"
                      />
                      {isEditing && (
                        <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                          <button
                            type="button"
                            className="text-gray-400 hover:text-gray-500 focus:outline-none"
                          >
                            <span className="sr-only">Show API Key</span>
                            <svg
                              className="h-5 w-5"
                              xmlns="http://www.w3.org/2000/svg"
                              fill="none"
                              viewBox="0 0 24 24"
                              stroke="currentColor"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </form>
          )}

          {activeTab !== 'general' && activeTab !== 'ondc' && (
            <div className="text-center py-12">
              <svg
                className="mx-auto h-12 w-12 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No settings configured</h3>
              <p className="mt-1 text-sm text-gray-500">This settings section is coming soon.</p>
              <div className="mt-6">
                <button
                  type="button"
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Configure {SETTINGS_TABS.find(tab => tab.id === activeTab)?.name}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
