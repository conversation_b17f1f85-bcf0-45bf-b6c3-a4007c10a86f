'use client';

import { useState } from 'react';
import ErrorPage from '../../components/ErrorPage';
import { handleApiError, shouldShowErrorPage, getErrorPageStatusCode } from '../../lib/error';
import { ApiError } from '../../types/api';

export default function TestErrorPage() {
  const [error, setError] = useState<ApiError | null>(null);
  const [showError, setShowError] = useState(false);

  const simulateNetworkError = () => {
    const networkError = new Error('Network error. Please check your connection and try again.');
    const apiError = handleApiError(networkError);
    apiError.status = 0; // Simulate network error
    apiError.code = 'ECONNABORTED';
    setError(apiError);
    setShowError(shouldShowErrorPage(apiError));
  };

  const simulate500Error = () => {
    const serverError = new Error('Internal server error occurred.');
    const apiError = handleApiError(serverError);
    apiError.status = 500;
    setError(apiError);
    setShowError(shouldShowErrorPage(apiError));
  };

  const simulate404Error = () => {
    const notFoundError = new Error('The requested resource was not found.');
    const apiError = handleApiError(notFoundError);
    apiError.status = 404;
    setError(apiError);
    setShowError(shouldShowErrorPage(apiError));
  };

  const simulate401Error = () => {
    const authError = new Error('You are not authorized to access this resource.');
    const apiError = handleApiError(authError);
    apiError.status = 401;
    setError(apiError);
    setShowError(shouldShowErrorPage(apiError));
  };

  const clearError = () => {
    setError(null);
    setShowError(false);
  };

  if (showError && error) {
    return (
      <ErrorPage
        statusCode={getErrorPageStatusCode(error)}
        title="Test Error Page"
        message={error.message}
        showRetry={true}
        onRetry={clearError}
        showBackButton={true}
        backUrl="/test-error"
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">Error Page Testing</h1>
          
          <div className="space-y-4">
            <button
              onClick={simulateNetworkError}
              className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
            >
              Simulate Network Error (503)
            </button>
            
            <button
              onClick={simulate500Error}
              className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
            >
              Simulate Server Error (500)
            </button>
            
            <button
              onClick={simulate404Error}
              className="w-full bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700 transition-colors"
            >
              Simulate Not Found (404) - No Error Page
            </button>
            
            <button
              onClick={simulate401Error}
              className="w-full bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700 transition-colors"
            >
              Simulate Unauthorized (401)
            </button>
          </div>

          {error && !showError && (
            <div className="mt-6 p-4 bg-yellow-100 border border-yellow-400 rounded">
              <h3 className="font-medium text-yellow-800">Error Details (No Error Page Shown)</h3>
              <p className="text-sm text-yellow-700 mt-1">Status: {error.status}</p>
              <p className="text-sm text-yellow-700">Message: {error.message}</p>
              <p className="text-sm text-yellow-700">Code: {error.code}</p>
              <button
                onClick={clearError}
                className="mt-2 text-sm text-yellow-800 underline hover:no-underline"
              >
                Clear Error
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
