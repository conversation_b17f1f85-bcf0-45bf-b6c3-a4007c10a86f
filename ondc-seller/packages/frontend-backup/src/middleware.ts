import { NextRequest, NextResponse } from 'next/server';

// Default tenant ID to use if no tenant is specified
const DEFAULT_TENANT_ID = 'default';

// Cookie name for storing the tenant ID
const TENANT_COOKIE_NAME = 'x-tenant-id';

// Cookie options
const COOKIE_OPTIONS = {
  // Max age: 30 days
  maxAge: 30 * 24 * 60 * 60,
  // Use secure cookies in production
  secure: process.env.NODE_ENV === 'production',
  // Only send cookie over HTTPS in production
  httpOnly: true,
  // Make cookie accessible from JavaScript
  sameSite: 'lax' as const,
  // Set path to root so cookie is available everywhere
  path: '/',
};

/**
 * Extract tenant ID from subdomain
 * @param hostname The hostname from the request
 * @returns The tenant ID or null if not found
 */
function extractTenantFromSubdomain(hostname: string): string | null {
  // Skip for localhost or IP addresses
  if (
    hostname === 'localhost' ||
    hostname === '127.0.0.1' ||
    /^(\d{1,3}\.){3}\d{1,3}$/.test(hostname)
  ) {
    return null;
  }

  // Get the parts of the hostname
  const parts = hostname.split('.');

  // If we have a subdomain (e.g., tenant1.example.com)
  if (parts.length > 2) {
    return parts[0];
  }

  return null;
}

/**
 * Next.js middleware function
 * This runs before the request is processed by the application
 */
export async function middleware(request: NextRequest) {
  // Clone the request headers to modify them
  const requestHeaders = new Headers(request.headers);
  
  // Get the URL and hostname
  const url = request.nextUrl;
  const hostname = request.headers.get('host') || 'localhost';
  
  // Initialize response
  let response = NextResponse.next();
  
  // Try to get tenant ID from different sources (in order of priority)
  let tenantId: string | null = null;
  
  // 1. Try to get from subdomain
  tenantId = extractTenantFromSubdomain(hostname);
  
  // 2. If not found in subdomain, try to get from header
  if (!tenantId) {
    tenantId = request.headers.get('x-tenant-id');
  }
  
  // 3. If not found in header, try to get from cookie
  if (!tenantId) {
    tenantId = request.cookies.get(TENANT_COOKIE_NAME)?.value || null;
  }
  
  // 4. If still not found, use default tenant ID
  if (!tenantId) {
    tenantId = DEFAULT_TENANT_ID;
  }
  
  // Log the tenant ID for debugging (remove in production)
  console.log(`[Middleware] Tenant ID: ${tenantId}`);
  
  // Set the tenant ID in the request headers
  requestHeaders.set('x-tenant-id', tenantId);
  
  // Create a new response with the modified headers
  response = NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
  
  // Set the tenant ID in a cookie for client-side access
  response.cookies.set(TENANT_COOKIE_NAME, tenantId, COOKIE_OPTIONS);
  
  return response;
}

/**
 * Configure which routes this middleware applies to
 * This middleware will run on all routes except for static files and API routes that don't need tenant context
 */
export const config = {
  // Match all request paths except for:
  // - _next/static (static files)
  // - _next/image (image optimization files)
  // - favicon.ico (favicon file)
  // - public folder files
  matcher: [
    // Match all pages
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
    // Match all API routes except for those that don't need tenant context
    '/api/:path*',
  ],
};
