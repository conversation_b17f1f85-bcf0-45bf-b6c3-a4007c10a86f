'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { useCart } from '../hooks/useCart';
import { Cart } from '../lib/medusa/cart-api';

// Define the cart context type
interface CartContextType {
  cart: Cart | null;
  loading: boolean;
  error: Error | null;
  addItem: (variantId: string, quantity?: number) => Promise<void>;
  updateItem: (lineItemId: string, quantity: number) => Promise<void>;
  removeItem: (lineItemId: string) => Promise<void>;
  clearCart: () => void;
  refreshCart: () => Promise<void>;
  isItemInCart: (variantId: string) => boolean;
  getItemQuantity: (variantId: string) => number;
  totalItems: number;
  totalUniqueItems: number;
  subtotal: number;
  total: number;
  isEmpty: boolean;
}

// Create the cart context
const CartContext = createContext<CartContextType | undefined>(undefined);

// Props for the cart provider
interface CartProviderProps {
  children: ReactNode;
}

/**
 * Cart provider component
 * @param children React children
 * @returns Cart provider component
 */
export function CartProvider({ children }: CartProviderProps) {
  const cart = useCart();

  return <CartContext.Provider value={cart}>{children}</CartContext.Provider>;
}

/**
 * Hook to use the cart context
 * @returns Cart context
 */
export function useCartContext(): CartContextType {
  const context = useContext(CartContext);

  if (context === undefined) {
    throw new Error('useCartContext must be used within a CartProvider');
  }

  return context;
}

export default CartProvider;
