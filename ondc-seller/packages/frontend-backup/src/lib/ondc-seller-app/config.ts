/**
 * ONDC Seller App Configuration
 * 
 * Consolidated configuration from ondc-seller-app package
 * Provides environment-based configuration for ONDC integration
 */

export interface ONDCConfig {
  // ONDC Network Configuration
  participantId: string;
  subscriberId: string;
  subscriberUrl: string;
  registryUrl: string;
  
  // Authentication & Security
  authSigningKey: string;
  encryptionPublicKey: string;
  encryptionPrivateKey: string;
  
  // BPP Configuration
  bppId: string;
  bppUri: string;
  
  // Integration URLs
  medusaUrl: string;
  medusaApiKey?: string;
  
  // Environment
  environment: 'development' | 'staging' | 'production';
  
  // Feature Flags
  enableLogistics: boolean;
  enableCOD: boolean;
  enableReturns: boolean;
}

/**
 * Get ONDC configuration from environment variables
 */
export function getONDCConfig(): ONDCConfig {
  return {
    // ONDC Network Configuration
    participantId: process.env.ONDC_PARTICIPANT_ID || 'default-participant',
    subscriberId: process.env.ONDC_SUBSCRIBER_ID || 'default-subscriber',
    subscriberUrl: process.env.ONDC_SUBSCRIBER_URL || 'https://localhost:3000/api/ondc',
    registryUrl: process.env.ONDC_REGISTRY_URL || 'https://registry.ondc.org',
    
    // Authentication & Security
    authSigningKey: process.env.ONDC_AUTH_SIGNING_KEY || 'default-signing-key',
    encryptionPublicKey: process.env.ONDC_ENCRYPTION_PUBLIC_KEY || 'default-public-key',
    encryptionPrivateKey: process.env.ONDC_ENCRYPTION_PRIVATE_KEY || 'default-private-key',
    
    // BPP Configuration
    bppId: process.env.ONDC_BPP_ID || 'ondc-seller-platform',
    bppUri: process.env.ONDC_BPP_URI || 'https://localhost:3000/api/ondc',
    
    // Integration URLs
    medusaUrl: process.env.NEXT_PUBLIC_MEDUSA_URL || 'http://localhost:9000',
    medusaApiKey: process.env.MEDUSA_API_KEY,
    
    // Environment
    environment: (process.env.NODE_ENV as 'development' | 'staging' | 'production') || 'development',
    
    // Feature Flags
    enableLogistics: process.env.ONDC_ENABLE_LOGISTICS === 'true',
    enableCOD: process.env.ONDC_ENABLE_COD === 'true',
    enableReturns: process.env.ONDC_ENABLE_RETURNS === 'true',
  };
}

/**
 * Validate ONDC configuration
 */
export function validateONDCConfig(config: ONDCConfig): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  if (!config.participantId || config.participantId === 'default-participant') {
    errors.push('ONDC_PARTICIPANT_ID is required');
  }
  
  if (!config.subscriberId || config.subscriberId === 'default-subscriber') {
    errors.push('ONDC_SUBSCRIBER_ID is required');
  }
  
  if (!config.subscriberUrl || config.subscriberUrl.includes('localhost')) {
    if (config.environment === 'production') {
      errors.push('ONDC_SUBSCRIBER_URL must be a valid public URL in production');
    }
  }
  
  if (!config.bppId || config.bppId === 'ondc-seller-platform') {
    errors.push('ONDC_BPP_ID is required');
  }
  
  if (!config.bppUri || config.bppUri.includes('localhost')) {
    if (config.environment === 'production') {
      errors.push('ONDC_BPP_URI must be a valid public URL in production');
    }
  }
  
  if (config.environment === 'production') {
    if (!config.authSigningKey || config.authSigningKey === 'default-signing-key') {
      errors.push('ONDC_AUTH_SIGNING_KEY is required in production');
    }
    
    if (!config.encryptionPublicKey || config.encryptionPublicKey === 'default-public-key') {
      errors.push('ONDC_ENCRYPTION_PUBLIC_KEY is required in production');
    }
    
    if (!config.encryptionPrivateKey || config.encryptionPrivateKey === 'default-private-key') {
      errors.push('ONDC_ENCRYPTION_PRIVATE_KEY is required in production');
    }
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Get tenant-specific ONDC configuration
 */
export function getTenantONDCConfig(tenantId: string): ONDCConfig {
  const baseConfig = getONDCConfig();
  
  return {
    ...baseConfig,
    // Override with tenant-specific values if available
    participantId: process.env[`ONDC_PARTICIPANT_ID_${tenantId.toUpperCase()}`] || baseConfig.participantId,
    subscriberId: process.env[`ONDC_SUBSCRIBER_ID_${tenantId.toUpperCase()}`] || baseConfig.subscriberId,
    bppId: process.env[`ONDC_BPP_ID_${tenantId.toUpperCase()}`] || `${baseConfig.bppId}-${tenantId}`,
  };
}

/**
 * ONDC Domain Constants
 */
export const ONDC_DOMAINS = {
  RETAIL: 'ONDC:RET10',
  GROCERY: 'ONDC:RET11',
  FASHION: 'ONDC:RET12',
  ELECTRONICS: 'ONDC:RET13',
  HOME_KITCHEN: 'ONDC:RET14',
  BEAUTY_PERSONAL_CARE: 'ONDC:RET15',
  HEALTH_WELLNESS: 'ONDC:RET16',
} as const;

/**
 * ONDC Action Constants
 */
export const ONDC_ACTIONS = {
  SEARCH: 'search',
  ON_SEARCH: 'on_search',
  SELECT: 'select',
  ON_SELECT: 'on_select',
  INIT: 'init',
  ON_INIT: 'on_init',
  CONFIRM: 'confirm',
  ON_CONFIRM: 'on_confirm',
  STATUS: 'status',
  ON_STATUS: 'on_status',
  TRACK: 'track',
  ON_TRACK: 'on_track',
  CANCEL: 'cancel',
  ON_CANCEL: 'on_cancel',
  UPDATE: 'update',
  ON_UPDATE: 'on_update',
  RATING: 'rating',
  ON_RATING: 'on_rating',
  SUPPORT: 'support',
  ON_SUPPORT: 'on_support',
} as const;

/**
 * Default ONDC configuration for development
 */
export const DEFAULT_ONDC_CONFIG: Partial<ONDCConfig> = {
  environment: 'development',
  enableLogistics: true,
  enableCOD: false,
  enableReturns: true,
};

export default {
  getONDCConfig,
  validateONDCConfig,
  getTenantONDCConfig,
  ONDC_DOMAINS,
  ONDC_ACTIONS,
  DEFAULT_ONDC_CONFIG,
};
