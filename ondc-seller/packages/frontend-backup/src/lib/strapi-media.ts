/**
 * Media upload functionality for Strapi API client
 * 
 * This module provides functions to upload and manage media files in Strapi CMS.
 */

import axios from 'axios';
import { getCurrentTenantId } from './tenant';
import { processStrapiError } from './strapi-enhanced';

// Configuration
const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN || 'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

/**
 * Upload a file to Strapi media library
 * @param file - File to upload
 * @param info - Additional file information
 * @returns Promise with the uploaded file data
 */
export async function uploadMedia(file: File, info = {}): Promise<any> {
  try {
    console.log(`Uploading file ${file.name} to Strapi media library`);
    
    // Get tenant ID
    const tenantId = getCurrentTenantId();
    
    // Create form data
    const formData = new FormData();
    formData.append('files', file);
    
    // Add file info if provided
    if (Object.keys(info).length > 0) {
      formData.append('fileInfo', JSON.stringify(info));
    }
    
    // Add tenant ID
    formData.append('tenant', tenantId);
    
    // Upload file
    const response = await axios.post(`${STRAPI_URL}/api/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${STRAPI_API_TOKEN}`
      }
    });
    
    console.log(`File ${file.name} uploaded successfully`);
    
    return {
      files: response.data,
      error: null
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error uploading file ${file.name}:`, processedError);
    
    return {
      files: null,
      error: processedError
    };
  }
}

/**
 * Upload multiple files to Strapi media library
 * @param files - Files to upload
 * @param info - Additional file information
 * @returns Promise with the uploaded files data
 */
export async function uploadMultipleMedia(files: File[], info = {}): Promise<any> {
  try {
    console.log(`Uploading ${files.length} files to Strapi media library`);
    
    // Get tenant ID
    const tenantId = getCurrentTenantId();
    
    // Create form data
    const formData = new FormData();
    
    // Add all files
    files.forEach(file => {
      formData.append('files', file);
    });
    
    // Add file info if provided
    if (Object.keys(info).length > 0) {
      formData.append('fileInfo', JSON.stringify(info));
    }
    
    // Add tenant ID
    formData.append('tenant', tenantId);
    
    // Upload files
    const response = await axios.post(`${STRAPI_URL}/api/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${STRAPI_API_TOKEN}`
      }
    });
    
    console.log(`${files.length} files uploaded successfully`);
    
    return {
      files: response.data,
      error: null
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error uploading multiple files:`, processedError);
    
    return {
      files: null,
      error: processedError
    };
  }
}

/**
 * Upload a file from a URL to Strapi media library
 * @param url - URL of the file to upload
 * @param info - Additional file information
 * @returns Promise with the uploaded file data
 */
export async function uploadMediaFromUrl(url: string, info = {}): Promise<any> {
  try {
    console.log(`Uploading file from URL ${url} to Strapi media library`);
    
    // Get tenant ID
    const tenantId = getCurrentTenantId();
    
    // Create request data
    const data = {
      url,
      info: Object.keys(info).length > 0 ? info : undefined,
      tenant: tenantId
    };
    
    // Upload file
    const response = await axios.post(`${STRAPI_URL}/api/upload/url`, data, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${STRAPI_API_TOKEN}`
      }
    });
    
    console.log(`File from URL ${url} uploaded successfully`);
    
    return {
      files: response.data,
      error: null
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error uploading file from URL ${url}:`, processedError);
    
    return {
      files: null,
      error: processedError
    };
  }
}

/**
 * Get information about a file in Strapi media library
 * @param id - File ID
 * @returns Promise with the file data
 */
export async function getMediaInfo(id: string | number): Promise<any> {
  try {
    console.log(`Getting information about file ${id} from Strapi media library`);
    
    // Get file info
    const response = await axios.get(`${STRAPI_URL}/api/upload/files/${id}`, {
      headers: {
        Authorization: `Bearer ${STRAPI_API_TOKEN}`
      }
    });
    
    return {
      file: response.data,
      error: null
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error getting information about file ${id}:`, processedError);
    
    return {
      file: null,
      error: processedError
    };
  }
}

/**
 * Delete a file from Strapi media library
 * @param id - File ID
 * @returns Promise with the deletion result
 */
export async function deleteMedia(id: string | number): Promise<any> {
  try {
    console.log(`Deleting file ${id} from Strapi media library`);
    
    // Delete file
    const response = await axios.delete(`${STRAPI_URL}/api/upload/files/${id}`, {
      headers: {
        Authorization: `Bearer ${STRAPI_API_TOKEN}`
      }
    });
    
    console.log(`File ${id} deleted successfully`);
    
    return {
      success: true,
      error: null
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error deleting file ${id}:`, processedError);
    
    return {
      success: false,
      error: processedError
    };
  }
}

export default {
  uploadMedia,
  uploadMultipleMedia,
  uploadMediaFromUrl,
  getMediaInfo,
  deleteMedia
};
