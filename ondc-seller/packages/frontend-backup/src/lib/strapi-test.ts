/**
 * Strapi CMS Connection Test Utility
 * 
 * This module provides functions to test the connection to Strapi CMS.
 */

import axios from 'axios';
import { getCurrentTenantId } from './tenant';

// Configuration
const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN || 'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

/**
 * Test the connection to Strapi CMS
 * @returns Promise with the test result
 */
export async function testStrapiConnection() {
  const tenantId = getCurrentTenantId();
  const url = new URL(`${STRAPI_URL}/api/banners`);
  
  // Add tenant ID as a query parameter if available
  if (tenantId) {
    url.searchParams.append('tenant', tenantId);
  }
  
  try {
    console.log(`Testing Strapi connection: ${url.toString()}`);
    const response = await axios.get(url.toString(), {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${STRAPI_API_TOKEN}`,
      },
      timeout: 5000, // 5 seconds timeout
    });
    
    return {
      success: true,
      status: response.status,
      statusText: response.statusText,
      data: response.data,
    };
  } catch (error) {
    console.error('Error testing Strapi connection:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        status: error.response?.status || 0,
        statusText: error.response?.statusText || error.message,
        error: error.message,
      };
    }
    
    return {
      success: false,
      status: 0,
      statusText: 'Unknown error',
      error: String(error),
    };
  }
}

/**
 * Test the Strapi API token
 * @returns Promise with the test result
 */
export async function testStrapiApiToken() {
  try {
    console.log(`Testing Strapi API token`);
    const response = await axios.get(`${STRAPI_URL}/api/users/me`, {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${STRAPI_API_TOKEN}`,
      },
      timeout: 5000, // 5 seconds timeout
    });
    
    return {
      success: true,
      status: response.status,
      statusText: response.statusText,
      data: response.data,
    };
  } catch (error) {
    console.error('Error testing Strapi API token:', error);
    
    if (axios.isAxiosError(error)) {
      return {
        success: false,
        status: error.response?.status || 0,
        statusText: error.response?.statusText || error.message,
        error: error.message,
      };
    }
    
    return {
      success: false,
      status: 0,
      statusText: 'Unknown error',
      error: String(error),
    };
  }
}

export default {
  testStrapiConnection,
  testStrapiApiToken,
};
