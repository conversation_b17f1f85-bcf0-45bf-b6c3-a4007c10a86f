/**
 * Tests for the Strapi API caching layer
 */

import { 
  generateCacheKey, 
  isCacheValid, 
  getFromCache, 
  storeInCache, 
  clearCache,
  clearCollectionCache,
  getCachedCollection,
  getCachedItem
} from '../strapi-cache';

// Mock the strapi-enhanced module
jest.mock('../strapi-enhanced', () => ({
  getCollection: jest.fn(),
  getItem: jest.fn()
}));

// Import the mocked functions
import { getCollection, getItem } from '../strapi-enhanced';

describe('Strapi Cache', () => {
  beforeEach(() => {
    // Clear the cache before each test
    clearCache();
    
    // Reset mock function calls
    jest.clearAllMocks();
  });
  
  describe('generateCacheKey', () => {
    it('should generate a cache key from collection and params', () => {
      const key = generateCacheKey('products', { populate: '*', sort: 'title:asc' });
      expect(key).toBe('products:{"populate":"*","sort":"title:asc"}');
    });
    
    it('should handle empty params', () => {
      const key = generateCacheKey('products');
      expect(key).toBe('products:{}');
    });
  });
  
  describe('isCacheValid', () => {
    it('should return true for fresh cache entries', () => {
      const entry = {
        data: { items: [] },
        timestamp: Date.now() - 1000 // 1 second ago
      };
      
      expect(isCacheValid(entry)).toBe(true);
    });
    
    it('should return false for expired cache entries', () => {
      const entry = {
        data: { items: [] },
        timestamp: Date.now() - (6 * 60 * 1000) // 6 minutes ago
      };
      
      expect(isCacheValid(entry)).toBe(false);
    });
  });
  
  describe('getFromCache and storeInCache', () => {
    it('should store and retrieve data from cache', () => {
      const key = 'products:{}';
      const data = { items: [{ id: 1, title: 'Test Product' }] };
      
      // Initially, cache should be empty
      expect(getFromCache(key)).toBeNull();
      
      // Store data in cache
      storeInCache(key, data);
      
      // Now we should get the data back
      expect(getFromCache(key)).toEqual(data);
    });
  });
  
  describe('clearCache and clearCollectionCache', () => {
    it('should clear the entire cache', () => {
      // Store some data in cache
      storeInCache('products:{}', { items: [] });
      storeInCache('banners:{}', { items: [] });
      
      // Verify data is in cache
      expect(getFromCache('products:{}')).not.toBeNull();
      expect(getFromCache('banners:{}')).not.toBeNull();
      
      // Clear cache
      clearCache();
      
      // Verify cache is empty
      expect(getFromCache('products:{}')).toBeNull();
      expect(getFromCache('banners:{}')).toBeNull();
    });
    
    it('should clear cache for a specific collection', () => {
      // Store some data in cache
      storeInCache('products:{}', { items: [] });
      storeInCache('products:{"sort":"title:asc"}', { items: [] });
      storeInCache('banners:{}', { items: [] });
      
      // Clear cache for products
      clearCollectionCache('products');
      
      // Verify products cache is empty but banners cache is not
      expect(getFromCache('products:{}')).toBeNull();
      expect(getFromCache('products:{"sort":"title:asc"}')).toBeNull();
      expect(getFromCache('banners:{}')).not.toBeNull();
    });
  });
  
  describe('getCachedCollection', () => {
    it('should return cached data if available', async () => {
      // Mock data
      const mockData = { 
        items: [{ id: 1, title: 'Test Product' }],
        pagination: { page: 1, pageSize: 10, total: 1 },
        error: null
      };
      
      // Store data in cache
      storeInCache('products:{}', mockData);
      
      // Call getCachedCollection
      const result = await getCachedCollection('products');
      
      // Verify result is from cache
      expect(result).toEqual(mockData);
      
      // Verify getCollection was not called
      expect(getCollection).not.toHaveBeenCalled();
    });
    
    it('should fetch fresh data if not in cache', async () => {
      // Mock data
      const mockData = { 
        items: [{ id: 1, title: 'Test Product' }],
        pagination: { page: 1, pageSize: 10, total: 1 },
        error: null
      };
      
      // Mock getCollection to return the mock data
      (getCollection as jest.Mock).mockResolvedValue(mockData);
      
      // Call getCachedCollection
      const result = await getCachedCollection('products');
      
      // Verify result
      expect(result).toEqual(mockData);
      
      // Verify getCollection was called
      expect(getCollection).toHaveBeenCalledWith('products', {});
      
      // Verify data was stored in cache
      expect(getFromCache('products:{}')).toEqual(mockData);
    });
    
    it('should bypass cache when bypassCache is true', async () => {
      // Mock data
      const mockData = { 
        items: [{ id: 1, title: 'Test Product' }],
        pagination: { page: 1, pageSize: 10, total: 1 },
        error: null
      };
      
      // Store data in cache
      storeInCache('products:{}', mockData);
      
      // Mock getCollection to return different data
      const freshData = { 
        items: [{ id: 1, title: 'Updated Product' }],
        pagination: { page: 1, pageSize: 10, total: 1 },
        error: null
      };
      (getCollection as jest.Mock).mockResolvedValue(freshData);
      
      // Call getCachedCollection with bypassCache=true
      const result = await getCachedCollection('products', {}, true);
      
      // Verify result is fresh data, not cached data
      expect(result).toEqual(freshData);
      
      // Verify getCollection was called
      expect(getCollection).toHaveBeenCalledWith('products', {});
    });
  });
  
  describe('getCachedItem', () => {
    it('should return cached data if available', async () => {
      // Mock data
      const mockData = { 
        item: { id: 1, title: 'Test Product' },
        error: null
      };
      
      // Store data in cache
      storeInCache('products/1:{}', mockData);
      
      // Call getCachedItem
      const result = await getCachedItem('products', 1);
      
      // Verify result is from cache
      expect(result).toEqual(mockData);
      
      // Verify getItem was not called
      expect(getItem).not.toHaveBeenCalled();
    });
    
    it('should fetch fresh data if not in cache', async () => {
      // Mock data
      const mockData = { 
        item: { id: 1, title: 'Test Product' },
        error: null
      };
      
      // Mock getItem to return the mock data
      (getItem as jest.Mock).mockResolvedValue(mockData);
      
      // Call getCachedItem
      const result = await getCachedItem('products', 1);
      
      // Verify result
      expect(result).toEqual(mockData);
      
      // Verify getItem was called
      expect(getItem).toHaveBeenCalledWith('products', 1, {});
      
      // Verify data was stored in cache
      expect(getFromCache('products/1:{}')).toEqual(mockData);
    });
  });
});
