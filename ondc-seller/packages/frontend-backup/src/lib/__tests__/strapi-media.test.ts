/**
 * Tests for the Strapi media upload functionality
 */

import axios from 'axios';
import { 
  uploadMedia,
  uploadMultipleMedia,
  uploadMediaFromUrl,
  getMediaInfo,
  deleteMedia
} from '../strapi-media';

// Mock axios
jest.mock('axios');

// Mock getCurrentTenantId
jest.mock('../tenant', () => ({
  getCurrentTenantId: jest.fn(() => 'test-tenant')
}));

// Mock processStrapiError
jest.mock('../strapi-enhanced', () => ({
  processStrapiError: jest.fn(error => ({
    status: 0,
    statusText: 'Error',
    message: error.message || 'Unknown error'
  }))
}));

describe('Strapi Media', () => {
  beforeEach(() => {
    // Reset mock function calls
    jest.clearAllMocks();
  });
  
  describe('uploadMedia', () => {
    it('should upload a file', async () => {
      // Mock file
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      
      // Mock axios.post to return success
      (axios.post as jest.Mock).mockResolvedValue({
        data: [
          {
            id: 1,
            name: 'test.jpg',
            url: '/uploads/test.jpg'
          }
        ]
      });
      
      // Call uploadMedia
      const result = await uploadMedia(mockFile);
      
      // Verify result
      expect(result).toEqual({
        files: [
          {
            id: 1,
            name: 'test.jpg',
            url: '/uploads/test.jpg'
          }
        ],
        error: null
      });
      
      // Verify axios.post was called with correct parameters
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/upload'),
        expect.any(FormData),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'multipart/form-data',
            Authorization: expect.stringContaining('Bearer ')
          })
        })
      );
    });
    
    it('should handle errors', async () => {
      // Mock file
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      
      // Mock axios.post to throw an error
      (axios.post as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      // Call uploadMedia
      const result = await uploadMedia(mockFile);
      
      // Verify result
      expect(result).toEqual({
        files: null,
        error: expect.objectContaining({
          message: 'Network error'
        })
      });
    });
  });
  
  describe('uploadMultipleMedia', () => {
    it('should upload multiple files', async () => {
      // Mock files
      const mockFiles = [
        new File(['test1'], 'test1.jpg', { type: 'image/jpeg' }),
        new File(['test2'], 'test2.jpg', { type: 'image/jpeg' })
      ];
      
      // Mock axios.post to return success
      (axios.post as jest.Mock).mockResolvedValue({
        data: [
          {
            id: 1,
            name: 'test1.jpg',
            url: '/uploads/test1.jpg'
          },
          {
            id: 2,
            name: 'test2.jpg',
            url: '/uploads/test2.jpg'
          }
        ]
      });
      
      // Call uploadMultipleMedia
      const result = await uploadMultipleMedia(mockFiles);
      
      // Verify result
      expect(result).toEqual({
        files: [
          {
            id: 1,
            name: 'test1.jpg',
            url: '/uploads/test1.jpg'
          },
          {
            id: 2,
            name: 'test2.jpg',
            url: '/uploads/test2.jpg'
          }
        ],
        error: null
      });
      
      // Verify axios.post was called with correct parameters
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/upload'),
        expect.any(FormData),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'multipart/form-data',
            Authorization: expect.stringContaining('Bearer ')
          })
        })
      );
    });
  });
  
  describe('uploadMediaFromUrl', () => {
    it('should upload a file from a URL', async () => {
      // Mock URL
      const mockUrl = 'https://example.com/image.jpg';
      
      // Mock axios.post to return success
      (axios.post as jest.Mock).mockResolvedValue({
        data: [
          {
            id: 1,
            name: 'image.jpg',
            url: '/uploads/image.jpg'
          }
        ]
      });
      
      // Call uploadMediaFromUrl
      const result = await uploadMediaFromUrl(mockUrl);
      
      // Verify result
      expect(result).toEqual({
        files: [
          {
            id: 1,
            name: 'image.jpg',
            url: '/uploads/image.jpg'
          }
        ],
        error: null
      });
      
      // Verify axios.post was called with correct parameters
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/upload/url'),
        {
          url: mockUrl,
          info: undefined,
          tenant: 'test-tenant'
        },
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Authorization: expect.stringContaining('Bearer ')
          })
        })
      );
    });
  });
  
  describe('getMediaInfo', () => {
    it('should get information about a file', async () => {
      // Mock file ID
      const mockId = 1;
      
      // Mock axios.get to return success
      (axios.get as jest.Mock).mockResolvedValue({
        data: {
          id: 1,
          name: 'test.jpg',
          url: '/uploads/test.jpg'
        }
      });
      
      // Call getMediaInfo
      const result = await getMediaInfo(mockId);
      
      // Verify result
      expect(result).toEqual({
        file: {
          id: 1,
          name: 'test.jpg',
          url: '/uploads/test.jpg'
        },
        error: null
      });
      
      // Verify axios.get was called with correct parameters
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining(`/api/upload/files/${mockId}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer ')
          })
        })
      );
    });
  });
  
  describe('deleteMedia', () => {
    it('should delete a file', async () => {
      // Mock file ID
      const mockId = 1;
      
      // Mock axios.delete to return success
      (axios.delete as jest.Mock).mockResolvedValue({
        data: {
          id: 1
        }
      });
      
      // Call deleteMedia
      const result = await deleteMedia(mockId);
      
      // Verify result
      expect(result).toEqual({
        success: true,
        error: null
      });
      
      // Verify axios.delete was called with correct parameters
      expect(axios.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/api/upload/files/${mockId}`),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: expect.stringContaining('Bearer ')
          })
        })
      );
    });
  });
});
