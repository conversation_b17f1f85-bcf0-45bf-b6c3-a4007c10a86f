/**
 * Tests for the enhanced Strapi API client
 */

import axios from 'axios';
import { 
  processStrapiError, 
  processStrapiItem, 
  getFallbackBanners,
  getCollection,
  getItem,
  getBanners
} from '../strapi-enhanced';

// Mock axios
jest.mock('axios', () => {
  return {
    isAxiosError: jest.fn(),
    create: jest.fn(() => ({
      interceptors: {
        request: {
          use: jest.fn()
        }
      }
    }))
  };
});

// Mock getCurrentTenantId
jest.mock('../tenant', () => ({
  getCurrentTenantId: jest.fn(() => 'test-tenant')
}));

describe('Enhanced Strapi API Client', () => {
  describe('processStrapiError', () => {
    it('should process axios error with response', () => {
      // Mock axios.isAxiosError to return true
      (axios.isAxiosError as jest.Mock).mockReturnValue(true);
      
      const mockError = {
        response: {
          status: 404,
          statusText: 'Not Found',
          data: { message: 'Resource not found' }
        },
        message: 'Request failed with status code 404'
      };
      
      const result = processStrapiError(mockError);
      
      expect(result).toEqual({
        status: 404,
        statusText: 'Not Found',
        data: { message: 'Resource not found' },
        message: 'Request failed with status code 404'
      });
    });
    
    it('should process axios error with request but no response', () => {
      // Mock axios.isAxiosError to return true
      (axios.isAxiosError as jest.Mock).mockReturnValue(true);
      
      const mockError = {
        request: {},
        message: 'Network Error'
      };
      
      const result = processStrapiError(mockError);
      
      expect(result).toEqual({
        status: 0,
        statusText: 'No response received',
        message: 'Network error - no response received from Strapi'
      });
    });
    
    it('should process non-axios error', () => {
      // Mock axios.isAxiosError to return false
      (axios.isAxiosError as jest.Mock).mockReturnValue(false);
      
      const mockError = {
        message: 'Generic error'
      };
      
      const result = processStrapiError(mockError);
      
      expect(result).toEqual({
        status: 0,
        statusText: 'Unknown error',
        message: 'Generic error'
      });
    });
  });
  
  describe('processStrapiItem', () => {
    it('should process item with attributes', () => {
      const mockItem = {
        id: 1,
        attributes: {
          title: 'Test Title',
          description: 'Test Description',
          image: {
            data: {
              id: 2,
              attributes: {
                url: '/uploads/test.jpg',
                alternativeText: 'Test Image',
                caption: 'Test Caption',
                width: 800,
                height: 600,
                formats: {
                  thumbnail: { url: '/uploads/thumbnail_test.jpg' }
                }
              }
            }
          }
        }
      };
      
      const result = processStrapiItem(mockItem);
      
      expect(result).toEqual({
        id: 1,
        title: 'Test Title',
        description: 'Test Description',
        image: {
          id: 2,
          url: '/uploads/test.jpg',
          alternativeText: 'Test Image',
          caption: 'Test Caption',
          width: 800,
          height: 600,
          formats: {
            thumbnail: { url: '/uploads/thumbnail_test.jpg' }
          }
        }
      });
    });
    
    it('should handle null item', () => {
      const result = processStrapiItem(null);
      expect(result).toBeNull();
    });
    
    it('should handle item without attributes', () => {
      const mockItem = {
        id: 1,
        title: 'Test Title',
        description: 'Test Description'
      };
      
      const result = processStrapiItem(mockItem);
      
      expect(result).toEqual({
        id: 1,
        title: 'Test Title',
        description: 'Test Description'
      });
    });
  });
  
  describe('getFallbackBanners', () => {
    it('should return fallback banners', () => {
      const result = getFallbackBanners();
      
      expect(result).toHaveProperty('items');
      expect(result.items).toHaveLength(2);
      expect(result.items[0]).toHaveProperty('title', 'Welcome to ONDC Seller Platform');
      expect(result.items[1]).toHaveProperty('title', 'Discover Quality Products');
      expect(result.error).toBeNull();
    });
  });
});
