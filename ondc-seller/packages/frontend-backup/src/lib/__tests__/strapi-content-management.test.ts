/**
 * Tests for the Strapi content management features
 */

import { 
  createItem,
  updateItem,
  deleteItem
} from '../strapi-content-management';

// Mock the strapi-enhanced module
jest.mock('../strapi-enhanced', () => ({
  fetchAPI: jest.fn()
}));

// Mock the strapi-cache module
jest.mock('../strapi-cache', () => ({
  clearCollectionCache: jest.fn()
}));

// Import the mocked functions
import { fetchAPI } from '../strapi-enhanced';
import { clearCollectionCache } from '../strapi-cache';

describe('Strapi Content Management', () => {
  beforeEach(() => {
    // Reset mock function calls
    jest.clearAllMocks();
  });
  
  describe('createItem', () => {
    it('should create a new item', async () => {
      // Mock data
      const mockData = {
        data: {
          id: 1,
          attributes: {
            title: 'Test Item',
            description: 'Test Description'
          }
        }
      };
      
      // Mock fetchAPI to return the mock data
      (fetchAPI as jest.Mock).mockResolvedValue(mockData);
      
      // Call createItem
      const result = await createItem('test-collection', {
        title: 'Test Item',
        description: 'Test Description'
      });
      
      // Verify result
      expect(result).toEqual({
        item: mockData.data,
        error: null
      });
      
      // Verify fetchAPI was called with correct parameters
      expect(fetchAPI).toHaveBeenCalledWith('/test-collection', {
        method: 'POST',
        data: {
          data: {
            title: 'Test Item',
            description: 'Test Description'
          }
        }
      });
      
      // Verify cache was cleared
      expect(clearCollectionCache).toHaveBeenCalledWith('test-collection');
    });
    
    it('should handle errors', async () => {
      // Mock error
      const mockError = {
        error: { message: 'API error' }
      };
      
      // Mock fetchAPI to return the error
      (fetchAPI as jest.Mock).mockResolvedValue(mockError);
      
      // Call createItem
      const result = await createItem('test-collection', {
        title: 'Test Item'
      });
      
      // Verify result
      expect(result).toEqual({
        error: mockError.error
      });
      
      // Verify cache was not cleared
      expect(clearCollectionCache).not.toHaveBeenCalled();
    });
    
    it('should handle exceptions', async () => {
      // Mock fetchAPI to throw an error
      (fetchAPI as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      // Call createItem
      const result = await createItem('test-collection', {
        title: 'Test Item'
      });
      
      // Verify result
      expect(result).toEqual({
        item: null,
        error: { message: 'Network error' }
      });
      
      // Verify cache was not cleared
      expect(clearCollectionCache).not.toHaveBeenCalled();
    });
  });
  
  describe('updateItem', () => {
    it('should update an existing item', async () => {
      // Mock data
      const mockData = {
        data: {
          id: 1,
          attributes: {
            title: 'Updated Item',
            description: 'Updated Description'
          }
        }
      };
      
      // Mock fetchAPI to return the mock data
      (fetchAPI as jest.Mock).mockResolvedValue(mockData);
      
      // Call updateItem
      const result = await updateItem('test-collection', 1, {
        title: 'Updated Item',
        description: 'Updated Description'
      });
      
      // Verify result
      expect(result).toEqual({
        item: mockData.data,
        error: null
      });
      
      // Verify fetchAPI was called with correct parameters
      expect(fetchAPI).toHaveBeenCalledWith('/test-collection/1', {
        method: 'PUT',
        data: {
          data: {
            title: 'Updated Item',
            description: 'Updated Description'
          }
        }
      });
      
      // Verify cache was cleared
      expect(clearCollectionCache).toHaveBeenCalledWith('test-collection');
    });
    
    it('should handle errors', async () => {
      // Mock error
      const mockError = {
        error: { message: 'API error' }
      };
      
      // Mock fetchAPI to return the error
      (fetchAPI as jest.Mock).mockResolvedValue(mockError);
      
      // Call updateItem
      const result = await updateItem('test-collection', 1, {
        title: 'Updated Item'
      });
      
      // Verify result
      expect(result).toEqual({
        error: mockError.error
      });
      
      // Verify cache was not cleared
      expect(clearCollectionCache).not.toHaveBeenCalled();
    });
  });
  
  describe('deleteItem', () => {
    it('should delete an item', async () => {
      // Mock data
      const mockData = {
        data: {
          id: 1
        }
      };
      
      // Mock fetchAPI to return the mock data
      (fetchAPI as jest.Mock).mockResolvedValue(mockData);
      
      // Call deleteItem
      const result = await deleteItem('test-collection', 1);
      
      // Verify result
      expect(result).toEqual({
        success: true,
        error: null
      });
      
      // Verify fetchAPI was called with correct parameters
      expect(fetchAPI).toHaveBeenCalledWith('/test-collection/1', {
        method: 'DELETE'
      });
      
      // Verify cache was cleared
      expect(clearCollectionCache).toHaveBeenCalledWith('test-collection');
    });
    
    it('should handle errors', async () => {
      // Mock error
      const mockError = {
        error: { message: 'API error' }
      };
      
      // Mock fetchAPI to return the error
      (fetchAPI as jest.Mock).mockResolvedValue(mockError);
      
      // Call deleteItem
      const result = await deleteItem('test-collection', 1);
      
      // Verify result
      expect(result).toEqual({
        error: mockError.error
      });
      
      // Verify cache was not cleared
      expect(clearCollectionCache).not.toHaveBeenCalled();
    });
  });
});
