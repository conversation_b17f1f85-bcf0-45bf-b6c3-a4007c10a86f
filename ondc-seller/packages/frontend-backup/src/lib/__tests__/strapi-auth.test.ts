/**
 * Tests for the Strapi authentication and authorization
 */

import axios from 'axios';
import { 
  isAuthenticated,
  getAuthToken,
  getUser,
  getAuthHeaders,
  storeAuthData,
  clearAuthData,
  login,
  register,
  logout,
  getCurrentUser
} from '../strapi-auth';

// Mock axios
jest.mock('axios');

// Mock getCurrentTenantId
jest.mock('../tenant', () => ({
  getCurrentTenantId: jest.fn(() => 'test-tenant')
}));

// Mock processStrapiError
jest.mock('../strapi-enhanced', () => ({
  processStrapiError: jest.fn(error => ({
    status: 0,
    statusText: 'Error',
    message: error.message || 'Unknown error'
  }))
}));

// Mock sessionStorage
const mockSessionStorage = (() => {
  let store: Record<string, string> = {};
  
  return {
    getItem: jest.fn((key: string) => store[key] || null),
    setItem: jest.fn((key: string, value: string) => {
      store[key] = value;
    }),
    removeItem: jest.fn((key: string) => {
      delete store[key];
    }),
    clear: jest.fn(() => {
      store = {};
    })
  };
})();

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

describe('Strapi Auth', () => {
  beforeEach(() => {
    // Reset mock function calls
    jest.clearAllMocks();
    
    // Clear session storage
    mockSessionStorage.clear();
    
    // Clear auth data
    clearAuthData();
  });
  
  describe('isAuthenticated', () => {
    it('should return false when not authenticated', () => {
      expect(isAuthenticated()).toBe(false);
    });
    
    it('should return true when authenticated with valid token', () => {
      // Store auth data with future expiry
      storeAuthData('test-token', { id: 1 }, 3600);
      
      expect(isAuthenticated()).toBe(true);
    });
    
    it('should return false when token is expired', () => {
      // Store auth data with expired token
      storeAuthData('test-token', { id: 1 }, -3600);
      
      expect(isAuthenticated()).toBe(false);
    });
  });
  
  describe('getAuthToken', () => {
    it('should return null when not authenticated', () => {
      expect(getAuthToken()).toBeNull();
    });
    
    it('should return token when authenticated', () => {
      // Store auth data
      storeAuthData('test-token', { id: 1 });
      
      expect(getAuthToken()).toBe('test-token');
    });
  });
  
  describe('getUser', () => {
    it('should return null when not authenticated', () => {
      expect(getUser()).toBeNull();
    });
    
    it('should return user data when authenticated', () => {
      // Store auth data
      const userData = { id: 1, username: 'test' };
      storeAuthData('test-token', userData);
      
      expect(getUser()).toEqual(userData);
    });
  });
  
  describe('getAuthHeaders', () => {
    it('should return API token when not authenticated', () => {
      const headers = getAuthHeaders();
      
      expect(headers).toHaveProperty('Authorization');
      expect(headers.Authorization).toContain('Bearer ');
      expect(headers.Authorization).not.toBe('Bearer test-token');
    });
    
    it('should return auth token when authenticated', () => {
      // Store auth data
      storeAuthData('test-token', { id: 1 });
      
      const headers = getAuthHeaders();
      
      expect(headers).toHaveProperty('Authorization');
      expect(headers.Authorization).toBe('Bearer test-token');
    });
  });
  
  describe('storeAuthData and clearAuthData', () => {
    it('should store and clear auth data', () => {
      // Store auth data
      const userData = { id: 1, username: 'test' };
      storeAuthData('test-token', userData);
      
      // Verify data is stored
      expect(getAuthToken()).toBe('test-token');
      expect(getUser()).toEqual(userData);
      
      // Clear auth data
      clearAuthData();
      
      // Verify data is cleared
      expect(getAuthToken()).toBeNull();
      expect(getUser()).toBeNull();
    });
  });
  
  describe('login', () => {
    it('should login successfully', async () => {
      // Mock axios.post to return success
      (axios.post as jest.Mock).mockResolvedValue({
        data: {
          jwt: 'test-token',
          user: {
            id: 1,
            username: 'test',
            email: '<EMAIL>'
          }
        }
      });
      
      // Call login
      const result = await login('<EMAIL>', 'password');
      
      // Verify result
      expect(result).toEqual({
        user: {
          id: 1,
          username: 'test',
          email: '<EMAIL>'
        },
        error: null
      });
      
      // Verify axios.post was called with correct parameters
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/local'),
        {
          identifier: '<EMAIL>',
          password: 'password',
          tenant: 'test-tenant'
        }
      );
      
      // Verify auth data is stored
      expect(getAuthToken()).toBe('test-token');
      expect(getUser()).toEqual({
        id: 1,
        username: 'test',
        email: '<EMAIL>'
      });
    });
    
    it('should handle login errors', async () => {
      // Mock axios.post to throw an error
      (axios.post as jest.Mock).mockRejectedValue(new Error('Invalid credentials'));
      
      // Call login
      const result = await login('<EMAIL>', 'wrong-password');
      
      // Verify result
      expect(result).toEqual({
        user: null,
        error: expect.objectContaining({
          message: 'Invalid credentials'
        })
      });
      
      // Verify auth data is not stored
      expect(getAuthToken()).toBeNull();
      expect(getUser()).toBeNull();
    });
  });
  
  describe('register', () => {
    it('should register successfully', async () => {
      // Mock axios.post to return success
      (axios.post as jest.Mock).mockResolvedValue({
        data: {
          jwt: 'test-token',
          user: {
            id: 1,
            username: 'test',
            email: '<EMAIL>'
          }
        }
      });
      
      // Call register
      const result = await register('test', '<EMAIL>', 'password');
      
      // Verify result
      expect(result).toEqual({
        user: {
          id: 1,
          username: 'test',
          email: '<EMAIL>'
        },
        error: null
      });
      
      // Verify axios.post was called with correct parameters
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/api/auth/local/register'),
        {
          username: 'test',
          email: '<EMAIL>',
          password: 'password',
          tenant: 'test-tenant'
        }
      );
      
      // Verify auth data is stored
      expect(getAuthToken()).toBe('test-token');
      expect(getUser()).toEqual({
        id: 1,
        username: 'test',
        email: '<EMAIL>'
      });
    });
  });
  
  describe('getCurrentUser', () => {
    it('should return null when not authenticated', async () => {
      // Call getCurrentUser
      const result = await getCurrentUser();
      
      // Verify result
      expect(result).toEqual({
        user: null,
        error: expect.objectContaining({
          message: 'Not authenticated'
        })
      });
      
      // Verify axios.get was not called
      expect(axios.get).not.toHaveBeenCalled();
    });
    
    it('should get current user when authenticated', async () => {
      // Store auth data
      storeAuthData('test-token', { id: 1 });
      
      // Mock axios.get to return success
      (axios.get as jest.Mock).mockResolvedValue({
        data: {
          id: 1,
          username: 'test',
          email: '<EMAIL>'
        }
      });
      
      // Call getCurrentUser
      const result = await getCurrentUser();
      
      // Verify result
      expect(result).toEqual({
        user: {
          id: 1,
          username: 'test',
          email: '<EMAIL>'
        },
        error: null
      });
      
      // Verify axios.get was called with correct parameters
      expect(axios.get).toHaveBeenCalledWith(
        expect.stringContaining('/api/users/me'),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: 'Bearer test-token'
          })
        })
      );
    });
  });
});
