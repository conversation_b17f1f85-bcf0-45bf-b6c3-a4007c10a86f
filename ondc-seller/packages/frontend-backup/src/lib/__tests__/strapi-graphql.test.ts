/**
 * Tests for the Strapi GraphQL client
 */

import axios from 'axios';
import { 
  fetchGraphQL,
  getProductsWithRelations,
  getProductWithRelations,
  getCategoriesWithProducts,
  getBanners,
  getPageBySlug,
  processGraphQLResponse
} from '../strapi-graphql';

// Mock axios
jest.mock('axios');

// Mock getCurrentTenantId
jest.mock('../tenant', () => ({
  getCurrentTenantId: jest.fn(() => 'test-tenant')
}));

// Mock processStrapiError
jest.mock('../strapi-enhanced', () => ({
  processStrapiError: jest.fn(error => ({
    status: 0,
    statusText: 'Error',
    message: error.message || 'Unknown error'
  }))
}));

// Mock getAuthHeaders
jest.mock('../strapi-auth', () => ({
  getAuthHeaders: jest.fn(() => ({
    Authorization: 'Bearer test-token'
  }))
}));

describe('Strapi GraphQL', () => {
  beforeEach(() => {
    // Reset mock function calls
    jest.clearAllMocks();
  });
  
  describe('fetchGraphQL', () => {
    it('should execute a GraphQL query', async () => {
      // Mock query
      const query = 'query { products { data { id } } }';
      
      // Mock axios.post to return success
      (axios.post as jest.Mock).mockResolvedValue({
        data: {
          data: {
            products: {
              data: [
                { id: 1 },
                { id: 2 }
              ]
            }
          }
        }
      });
      
      // Call fetchGraphQL
      const result = await fetchGraphQL(query);
      
      // Verify result
      expect(result).toEqual({
        data: {
          products: {
            data: [
              { id: 1 },
              { id: 2 }
            ]
          }
        },
        errors: null
      });
      
      // Verify axios.post was called with correct parameters
      expect(axios.post).toHaveBeenCalledWith(
        expect.stringContaining('/graphql'),
        {
          query,
          variables: {
            tenant: 'test-tenant'
          }
        },
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Authorization: 'Bearer test-token'
          })
        })
      );
    });
    
    it('should handle GraphQL errors', async () => {
      // Mock query
      const query = 'query { products { data { id } } }';
      
      // Mock axios.post to return GraphQL errors
      (axios.post as jest.Mock).mockResolvedValue({
        data: {
          errors: [
            { message: 'Field "products" not found' }
          ]
        }
      });
      
      // Call fetchGraphQL
      const result = await fetchGraphQL(query);
      
      // Verify result
      expect(result).toEqual({
        data: null,
        errors: [
          { message: 'Field "products" not found' }
        ]
      });
    });
    
    it('should handle network errors', async () => {
      // Mock query
      const query = 'query { products { data { id } } }';
      
      // Mock axios.post to throw an error
      (axios.post as jest.Mock).mockRejectedValue(new Error('Network error'));
      
      // Call fetchGraphQL
      const result = await fetchGraphQL(query);
      
      // Verify result
      expect(result).toEqual({
        data: null,
        errors: [
          expect.objectContaining({
            message: 'Network error'
          })
        ]
      });
    });
  });
  
  describe('getProductsWithRelations', () => {
    it('should fetch products with relations', async () => {
      // Mock fetchGraphQL to return success
      jest.spyOn(global, 'fetchGraphQL').mockResolvedValue({
        data: {
          products: {
            data: [
              {
                id: 1,
                attributes: {
                  title: 'Test Product',
                  price: 100
                }
              }
            ]
          }
        },
        errors: null
      });
      
      // Call getProductsWithRelations
      const result = await getProductsWithRelations();
      
      // Verify result
      expect(result).toEqual({
        data: {
          products: {
            data: [
              {
                id: 1,
                attributes: {
                  title: 'Test Product',
                  price: 100
                }
              }
            ]
          }
        },
        errors: null
      });
      
      // Verify fetchGraphQL was called with correct query
      expect(fetchGraphQL).toHaveBeenCalledWith(
        expect.stringContaining('query GetProductsWithRelations'),
        {}
      );
    });
  });
  
  describe('getProductWithRelations', () => {
    it('should fetch a product with relations', async () => {
      // Mock fetchGraphQL to return success
      jest.spyOn(global, 'fetchGraphQL').mockResolvedValue({
        data: {
          product: {
            data: {
              id: 1,
              attributes: {
                title: 'Test Product',
                price: 100
              }
            }
          }
        },
        errors: null
      });
      
      // Call getProductWithRelations
      const result = await getProductWithRelations(1);
      
      // Verify result
      expect(result).toEqual({
        data: {
          product: {
            data: {
              id: 1,
              attributes: {
                title: 'Test Product',
                price: 100
              }
            }
          }
        },
        errors: null
      });
      
      // Verify fetchGraphQL was called with correct query and variables
      expect(fetchGraphQL).toHaveBeenCalledWith(
        expect.stringContaining('query GetProductWithRelations'),
        { id: 1 }
      );
    });
  });
  
  describe('processGraphQLResponse', () => {
    it('should process collection type response', () => {
      const response = {
        data: {
          products: {
            data: [
              {
                id: 1,
                attributes: {
                  title: 'Test Product 1',
                  price: 100
                }
              },
              {
                id: 2,
                attributes: {
                  title: 'Test Product 2',
                  price: 200
                }
              }
            ]
          }
        }
      };
      
      const result = processGraphQLResponse(response);
      
      expect(result).toEqual([
        {
          id: 1,
          title: 'Test Product 1',
          price: 100
        },
        {
          id: 2,
          title: 'Test Product 2',
          price: 200
        }
      ]);
    });
    
    it('should process single type response', () => {
      const response = {
        data: {
          product: {
            data: {
              id: 1,
              attributes: {
                title: 'Test Product',
                price: 100
              }
            }
          }
        }
      };
      
      const result = processGraphQLResponse(response);
      
      expect(result).toEqual({
        id: 1,
        title: 'Test Product',
        price: 100
      });
    });
    
    it('should handle null response', () => {
      expect(processGraphQLResponse(null)).toBeNull();
      expect(processGraphQLResponse({})).toBeNull();
      expect(processGraphQLResponse({ data: {} })).toBeNull();
    });
  });
});
