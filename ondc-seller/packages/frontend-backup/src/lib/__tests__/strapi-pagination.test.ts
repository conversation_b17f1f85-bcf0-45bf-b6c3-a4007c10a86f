/**
 * Tests for the Strapi API pagination utilities
 */

import { 
  getPaginatedCollection,
  getCachedPaginatedCollection,
  generatePaginationControls
} from '../strapi-pagination';

// Mock the strapi-enhanced module
jest.mock('../strapi-enhanced', () => ({
  getCollection: jest.fn()
}));

// Mock the strapi-cache module
jest.mock('../strapi-cache', () => ({
  getCachedCollection: jest.fn()
}));

// Import the mocked functions
import { getCollection } from '../strapi-enhanced';
import { getCachedCollection } from '../strapi-cache';

describe('Strapi Pagination', () => {
  beforeEach(() => {
    // Reset mock function calls
    jest.clearAllMocks();
  });
  
  describe('getPaginatedCollection', () => {
    it('should fetch paginated data with default pagination', async () => {
      // Mock data
      const mockData = { 
        items: [{ id: 1, title: 'Test Product' }],
        pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 },
        error: null
      };
      
      // Mock getCollection to return the mock data
      (getCollection as jest.Mock).mockResolvedValue(mockData);
      
      // Call getPaginatedCollection
      const result = await getPaginatedCollection('products');
      
      // Verify result
      expect(result).toEqual(mockData);
      
      // Verify getCollection was called with correct parameters
      expect(getCollection).toHaveBeenCalledWith('products', {
        'pagination[page]': 1,
        'pagination[pageSize]': 25
      });
    });
    
    it('should fetch paginated data with custom pagination', async () => {
      // Mock data
      const mockData = { 
        items: [{ id: 1, title: 'Test Product' }],
        pagination: { page: 2, pageSize: 10, pageCount: 5, total: 50 },
        error: null
      };
      
      // Mock getCollection to return the mock data
      (getCollection as jest.Mock).mockResolvedValue(mockData);
      
      // Call getPaginatedCollection
      const result = await getPaginatedCollection('products', { page: 2, pageSize: 10 });
      
      // Verify result
      expect(result).toEqual(mockData);
      
      // Verify getCollection was called with correct parameters
      expect(getCollection).toHaveBeenCalledWith('products', {
        'pagination[page]': 2,
        'pagination[pageSize]': 10
      });
    });
    
    it('should handle errors', async () => {
      // Mock error
      const mockError = { 
        error: { message: 'API error' }
      };
      
      // Mock getCollection to return the error
      (getCollection as jest.Mock).mockResolvedValue(mockError);
      
      // Call getPaginatedCollection
      const result = await getPaginatedCollection('products');
      
      // Verify result
      expect(result).toEqual({
        items: [],
        pagination: {
          page: 1,
          pageSize: 25,
          pageCount: 0,
          total: 0
        },
        error: mockError.error
      });
    });
  });
  
  describe('getCachedPaginatedCollection', () => {
    it('should fetch cached paginated data', async () => {
      // Mock data
      const mockData = { 
        items: [{ id: 1, title: 'Test Product' }],
        pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 },
        error: null
      };
      
      // Mock getCachedCollection to return the mock data
      (getCachedCollection as jest.Mock).mockResolvedValue(mockData);
      
      // Call getCachedPaginatedCollection
      const result = await getCachedPaginatedCollection('products');
      
      // Verify result
      expect(result).toEqual(mockData);
      
      // Verify getCachedCollection was called with correct parameters
      expect(getCachedCollection).toHaveBeenCalledWith('products', {
        'pagination[page]': 1,
        'pagination[pageSize]': 25
      }, false);
    });
    
    it('should bypass cache when bypassCache is true', async () => {
      // Mock data
      const mockData = { 
        items: [{ id: 1, title: 'Test Product' }],
        pagination: { page: 1, pageSize: 25, pageCount: 1, total: 1 },
        error: null
      };
      
      // Mock getCachedCollection to return the mock data
      (getCachedCollection as jest.Mock).mockResolvedValue(mockData);
      
      // Call getCachedPaginatedCollection with bypassCache=true
      const result = await getCachedPaginatedCollection('products', {}, {}, true);
      
      // Verify result
      expect(result).toEqual(mockData);
      
      // Verify getCachedCollection was called with bypassCache=true
      expect(getCachedCollection).toHaveBeenCalledWith('products', {
        'pagination[page]': 1,
        'pagination[pageSize]': 25
      }, true);
    });
  });
  
  describe('generatePaginationControls', () => {
    it('should generate pagination controls for few pages', () => {
      const pagination = {
        page: 2,
        pageSize: 10,
        pageCount: 3,
        total: 30
      };
      
      const controls = generatePaginationControls(pagination);
      
      expect(controls).toEqual([1, 2, 3]);
    });
    
    it('should generate pagination controls for many pages', () => {
      const pagination = {
        page: 5,
        pageSize: 10,
        pageCount: 10,
        total: 100
      };
      
      const controls = generatePaginationControls(pagination);
      
      // With default maxPages=5 and current page=5, we should see pages 3,4,5,6,7
      expect(controls).toEqual([3, 4, 5, 6, 7]);
    });
    
    it('should handle pagination controls near the start', () => {
      const pagination = {
        page: 2,
        pageSize: 10,
        pageCount: 10,
        total: 100
      };
      
      const controls = generatePaginationControls(pagination);
      
      // With default maxPages=5 and current page=2, we should see pages 1,2,3,4,5
      expect(controls).toEqual([1, 2, 3, 4, 5]);
    });
    
    it('should handle pagination controls near the end', () => {
      const pagination = {
        page: 9,
        pageSize: 10,
        pageCount: 10,
        total: 100
      };
      
      const controls = generatePaginationControls(pagination);
      
      // With default maxPages=5 and current page=9, we should see pages 6,7,8,9,10
      expect(controls).toEqual([6, 7, 8, 9, 10]);
    });
    
    it('should handle custom maxPages', () => {
      const pagination = {
        page: 5,
        pageSize: 10,
        pageCount: 10,
        total: 100
      };
      
      const controls = generatePaginationControls(pagination, 3);
      
      // With maxPages=3 and current page=5, we should see pages 4,5,6
      expect(controls).toEqual([4, 5, 6]);
    });
  });
});
