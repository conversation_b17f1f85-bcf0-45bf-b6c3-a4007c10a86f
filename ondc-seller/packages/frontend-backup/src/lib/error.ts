import axios, { AxiosError } from 'axios';
import { ApiError } from '../types/api';

/**
 * Maximum number of retry attempts for network failures
 */
const MAX_RETRY_ATTEMPTS = 3;

/**
 * Base delay for exponential backoff (in milliseconds)
 */
const BASE_RETRY_DELAY = 1000;

/**
 * Handle API errors and transform them into a consistent format
 * @param error The error object from axios
 * @returns A standardized API error object
 */
export const handleApiError = (error: unknown): ApiError => {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError<any>;

    // Handle Medusa API error format
    if (axiosError.response?.data?.message) {
      return {
        message: axiosError.response.data.message,
        code: axiosError.response.data.code || axiosError.code,
        status: axiosError.response?.status,
        details: axiosError.response?.data?.errors || axiosError.response?.data?.detail,
      };
    }

    // Handle network errors
    if (
      axiosError.code === 'ECONNABORTED' ||
      axiosError.code === 'ETIMEDOUT' ||
      !axiosError.response
    ) {
      return {
        message: 'Network error. Please check your connection and try again.',
        code: axiosError.code,
        status: 0,
      };
    }

    // Handle other axios errors
    return {
      message: axiosError.message || 'An unexpected error occurred',
      code: axiosError.code,
      status: axiosError.response?.status,
    };
  }

  // Handle non-axios errors
  return {
    message: error instanceof Error ? error.message : 'An unexpected error occurred',
    code: 'UNKNOWN_ERROR',
  };
};

/**
 * Determine if an error should show an error page instead of fallback data
 * @param error The API error
 * @returns Whether to show error page
 */
export const shouldShowErrorPage = (error: ApiError): boolean => {
  // Always show error page for network errors
  if (
    error.status === 0 ||
    error.code === 'ECONNABORTED' ||
    error.code === 'ETIMEDOUT' ||
    error.code === 'NETWORK_ERROR'
  ) {
    return true;
  }

  // Show error page for fetch network errors (when backend is not available)
  if (
    error.code === 'UNKNOWN_ERROR' &&
    error.message &&
    (error.message.includes('fetch') ||
      error.message.includes('Failed to fetch') ||
      error.message.includes('Network error') ||
      error.message.includes('connection') ||
      error.message.includes('AbortError'))
  ) {
    return true;
  }

  // Show error page for server errors (5xx)
  if (error.status && error.status >= 500) {
    return true;
  }

  // Show error page for authentication/authorization errors
  if (error.status === 401 || error.status === 403) {
    return true;
  }

  // For other errors (4xx), we might want to show specific error messages
  // but not necessarily a full error page
  return false;
};

/**
 * Get appropriate status code for error page display
 * @param error The API error
 * @returns Status code to display
 */
export const getErrorPageStatusCode = (error: ApiError): number => {
  if (
    error.status === 0 ||
    error.code === 'ECONNABORTED' ||
    error.code === 'ETIMEDOUT' ||
    error.code === 'NETWORK_ERROR'
  ) {
    return 503; // Service Unavailable
  }

  // Handle fetch network errors
  if (
    error.code === 'UNKNOWN_ERROR' &&
    error.message &&
    (error.message.includes('fetch') ||
      error.message.includes('Failed to fetch') ||
      error.message.includes('Network error') ||
      error.message.includes('connection') ||
      error.message.includes('AbortError'))
  ) {
    return 503; // Service Unavailable
  }

  return error.status || 500;
};

/**
 * Retry a function with exponential backoff
 * @param fn The function to retry
 * @param retryAttempt The current retry attempt (default: 0)
 * @returns The result of the function
 */
export const retryWithBackoff = async <T>(fn: () => Promise<T>, retryAttempt = 0): Promise<T> => {
  try {
    // First check if the backend is available
    if (retryAttempt === 0) {
      try {
        // Create an AbortController with timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 2000);

        // Try to fetch the health endpoint
        const healthResponse = await fetch(
          `${process.env.NEXT_PUBLIC_MEDUSA_URL || 'http://localhost:9000'}/health`,
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            signal: controller.signal,
          }
        ).catch(() => null);

        // Clear the timeout
        clearTimeout(timeoutId);

        // If health check fails, throw a specific error
        if (!healthResponse || !healthResponse.ok) {
          console.warn('Backend health check failed in retryWithBackoff');
          throw new Error('Backend service is currently unavailable');
        }
      } catch (healthError) {
        console.warn('Backend health check error in retryWithBackoff:', healthError);
        throw new Error('Backend service is currently unavailable');
      }
    }

    return await fn();
  } catch (error) {
    // If it's a backend unavailable error, don't retry
    if (error instanceof Error && error.message === 'Backend service is currently unavailable') {
      throw error;
    }

    // Don't retry if we've reached the maximum number of attempts
    if (retryAttempt >= MAX_RETRY_ATTEMPTS) {
      throw error;
    }

    // Only retry on network errors
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      if (
        axiosError.code !== 'ECONNABORTED' &&
        axiosError.code !== 'ETIMEDOUT' &&
        axiosError.response
      ) {
        throw error;
      }
    } else if (
      !(
        error instanceof Error &&
        (error.name === 'AbortError' ||
          error.message.includes('aborted') ||
          error.message.includes('network'))
      )
    ) {
      throw error;
    }

    // Calculate delay with exponential backoff
    const delay = BASE_RETRY_DELAY * Math.pow(2, retryAttempt);

    // Log retry attempt
    console.log(
      `Retrying API call (attempt ${retryAttempt + 1}/${MAX_RETRY_ATTEMPTS}) after ${delay}ms`
    );

    // Wait for the delay
    await new Promise(resolve => setTimeout(resolve, delay));

    // Retry the function
    return retryWithBackoff(fn, retryAttempt + 1);
  }
};
