/**
 * Authentication and authorization for Strapi API client
 * 
 * This module provides functions to authenticate with Strapi CMS
 * and manage user sessions.
 */

import axios from 'axios';
import { processStrapiError } from './strapi-enhanced';
import { getCurrentTenantId } from './tenant';

// Configuration
const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const STRAPI_API_TOKEN = process.env.STRAPI_API_TOKEN || 'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// Storage keys
const AUTH_TOKEN_KEY = 'strapi_auth_token';
const TOKEN_EXPIRY_KEY = 'strapi_token_expiry';
const USER_DATA_KEY = 'strapi_user';

// In-memory storage for server-side rendering
let authToken: string | null = null;
let tokenExpiry: number | null = null;
let userData: any | null = null;

/**
 * Check if the user is authenticated
 * @returns Whether the user is authenticated
 */
export function isAuthenticated(): boolean {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    const token = sessionStorage.getItem(AUTH_TOKEN_KEY);
    const expiry = sessionStorage.getItem(TOKEN_EXPIRY_KEY);
    
    if (token && expiry) {
      const expiryTime = parseInt(expiry, 10);
      return Date.now() < expiryTime;
    }
    
    return false;
  }
  
  // Server-side
  return !!authToken && !!tokenExpiry && Date.now() < tokenExpiry;
}

/**
 * Get the authentication token
 * @returns Authentication token or null if not authenticated
 */
export function getAuthToken(): string | null {
  if (!isAuthenticated()) {
    return null;
  }
  
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    return sessionStorage.getItem(AUTH_TOKEN_KEY);
  }
  
  // Server-side
  return authToken;
}

/**
 * Get the authenticated user data
 * @returns User data or null if not authenticated
 */
export function getUser(): any | null {
  // Check if we're in a browser environment
  if (typeof window !== 'undefined') {
    const userJson = sessionStorage.getItem(USER_DATA_KEY);
    return userJson ? JSON.parse(userJson) : null;
  }
  
  // Server-side
  return userData;
}

/**
 * Store authentication data
 * @param token - Authentication token
 * @param user - User data
 * @param expiresIn - Token expiry time in seconds
 */
export function storeAuthData(token: string, user: any, expiresIn: number = 30 * 24 * 60 * 60): void {
  const expiryTime = Date.now() + expiresIn * 1000;
  
  // Store in memory
  authToken = token;
  tokenExpiry = expiryTime;
  userData = user;
  
  // Store in browser if available
  if (typeof window !== 'undefined') {
    sessionStorage.setItem(AUTH_TOKEN_KEY, token);
    sessionStorage.setItem(TOKEN_EXPIRY_KEY, expiryTime.toString());
    sessionStorage.setItem(USER_DATA_KEY, JSON.stringify(user));
  }
  
  console.log('Authentication data stored');
}

/**
 * Clear authentication data
 */
export function clearAuthData(): void {
  // Clear memory
  authToken = null;
  tokenExpiry = null;
  userData = null;
  
  // Clear browser storage if available
  if (typeof window !== 'undefined') {
    sessionStorage.removeItem(AUTH_TOKEN_KEY);
    sessionStorage.removeItem(TOKEN_EXPIRY_KEY);
    sessionStorage.removeItem(USER_DATA_KEY);
  }
  
  console.log('Authentication data cleared');
}

/**
 * Get authentication headers
 * @returns Headers object with authentication token
 */
export function getAuthHeaders(): Record<string, string> {
  const token = getAuthToken();
  
  if (token) {
    return {
      Authorization: `Bearer ${token}`
    };
  }
  
  // Fall back to API token
  return {
    Authorization: `Bearer ${STRAPI_API_TOKEN}`
  };
}

/**
 * Login to Strapi
 * @param identifier - Username or email
 * @param password - Password
 * @returns Promise with login result
 */
export async function login(identifier: string, password: string): Promise<any> {
  try {
    console.log(`Logging in user ${identifier}`);
    
    // Get tenant ID
    const tenantId = getCurrentTenantId();
    
    // Login
    const response = await axios.post(`${STRAPI_URL}/api/auth/local`, {
      identifier,
      password,
      tenant: tenantId
    });
    
    const { jwt, user } = response.data;
    
    if (jwt) {
      // Store authentication data
      storeAuthData(jwt, user);
      
      console.log(`User ${identifier} logged in successfully`);
      
      return {
        user,
        error: null
      };
    }
    
    return {
      user: null,
      error: { message: 'No token received' }
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error logging in user ${identifier}:`, processedError);
    
    return {
      user: null,
      error: processedError
    };
  }
}

/**
 * Register a new user
 * @param username - Username
 * @param email - Email
 * @param password - Password
 * @returns Promise with registration result
 */
export async function register(username: string, email: string, password: string): Promise<any> {
  try {
    console.log(`Registering user ${username}`);
    
    // Get tenant ID
    const tenantId = getCurrentTenantId();
    
    // Register
    const response = await axios.post(`${STRAPI_URL}/api/auth/local/register`, {
      username,
      email,
      password,
      tenant: tenantId
    });
    
    const { jwt, user } = response.data;
    
    if (jwt) {
      // Store authentication data
      storeAuthData(jwt, user);
      
      console.log(`User ${username} registered successfully`);
      
      return {
        user,
        error: null
      };
    }
    
    return {
      user: null,
      error: { message: 'No token received' }
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error registering user ${username}:`, processedError);
    
    return {
      user: null,
      error: processedError
    };
  }
}

/**
 * Logout the current user
 */
export function logout(): void {
  console.log('Logging out user');
  clearAuthData();
}

/**
 * Get the current user
 * @returns Promise with current user data
 */
export async function getCurrentUser(): Promise<any> {
  try {
    console.log('Getting current user');
    
    // Check if we're authenticated
    if (!isAuthenticated()) {
      console.log('Not authenticated');
      return {
        user: null,
        error: { message: 'Not authenticated' }
      };
    }
    
    // Get user from API
    const response = await axios.get(`${STRAPI_URL}/api/users/me`, {
      headers: getAuthHeaders()
    });
    
    console.log('Current user retrieved successfully');
    
    return {
      user: response.data,
      error: null
    };
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error('Error getting current user:', processedError);
    
    // Clear auth data if unauthorized
    if (axios.isAxiosError(error) && error.response?.status === 401) {
      clearAuthData();
    }
    
    return {
      user: null,
      error: processedError
    };
  }
}

export default {
  isAuthenticated,
  getAuthToken,
  getUser,
  getAuthHeaders,
  login,
  register,
  logout,
  getCurrentUser
};
