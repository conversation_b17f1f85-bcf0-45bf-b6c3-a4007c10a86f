/**
 * Content management features for Strapi API client
 * 
 * This module provides functions to create, update, and delete content in Strapi CMS.
 */

import { fetchAPI } from './strapi-enhanced';
import { clearCollectionCache } from './strapi-cache';

/**
 * Create a new item in a collection
 * @param collection - Collection name
 * @param data - Item data
 * @returns Promise with the created item
 */
export async function createItem(collection: string, data = {}): Promise<any> {
  try {
    console.log(`Creating item in collection ${collection}`);
    
    const response = await fetchAPI(`/${collection}`, {
      method: 'POST',
      data: { data }
    });
    
    if (response.error) {
      console.error(`Error creating item in collection ${collection}:`, response.error);
      return { error: response.error };
    }
    
    // Clear cache for this collection
    clearCollectionCache(collection);
    
    return {
      item: response.data,
      error: null
    };
  } catch (error) {
    console.error(`Error in createItem(${collection}):`, error);
    return { 
      item: null,
      error: { message: error.message || 'Unknown error occurred' }
    };
  }
}

/**
 * Update an existing item in a collection
 * @param collection - Collection name
 * @param id - Item ID
 * @param data - Updated item data
 * @returns Promise with the updated item
 */
export async function updateItem(collection: string, id: string | number, data = {}): Promise<any> {
  try {
    console.log(`Updating item ${id} in collection ${collection}`);
    
    const response = await fetchAPI(`/${collection}/${id}`, {
      method: 'PUT',
      data: { data }
    });
    
    if (response.error) {
      console.error(`Error updating item ${id} in collection ${collection}:`, response.error);
      return { error: response.error };
    }
    
    // Clear cache for this collection
    clearCollectionCache(collection);
    
    return {
      item: response.data,
      error: null
    };
  } catch (error) {
    console.error(`Error in updateItem(${collection}, ${id}):`, error);
    return { 
      item: null,
      error: { message: error.message || 'Unknown error occurred' }
    };
  }
}

/**
 * Delete an item from a collection
 * @param collection - Collection name
 * @param id - Item ID
 * @returns Promise with the deletion result
 */
export async function deleteItem(collection: string, id: string | number): Promise<any> {
  try {
    console.log(`Deleting item ${id} from collection ${collection}`);
    
    const response = await fetchAPI(`/${collection}/${id}`, {
      method: 'DELETE'
    });
    
    if (response.error) {
      console.error(`Error deleting item ${id} from collection ${collection}:`, response.error);
      return { error: response.error };
    }
    
    // Clear cache for this collection
    clearCollectionCache(collection);
    
    return {
      success: true,
      error: null
    };
  } catch (error) {
    console.error(`Error in deleteItem(${collection}, ${id}):`, error);
    return { 
      success: false,
      error: { message: error.message || 'Unknown error occurred' }
    };
  }
}

/**
 * Create a new banner
 * @param data - Banner data
 * @returns Promise with the created banner
 */
export async function createBanner(data: any): Promise<any> {
  return createItem('banners', data);
}

/**
 * Update an existing banner
 * @param id - Banner ID
 * @param data - Updated banner data
 * @returns Promise with the updated banner
 */
export async function updateBanner(id: string | number, data: any): Promise<any> {
  return updateItem('banners', id, data);
}

/**
 * Delete a banner
 * @param id - Banner ID
 * @returns Promise with the deletion result
 */
export async function deleteBanner(id: string | number): Promise<any> {
  return deleteItem('banners', id);
}

/**
 * Create a new page
 * @param data - Page data
 * @returns Promise with the created page
 */
export async function createPage(data: any): Promise<any> {
  return createItem('pages', data);
}

/**
 * Update an existing page
 * @param id - Page ID
 * @param data - Updated page data
 * @returns Promise with the updated page
 */
export async function updatePage(id: string | number, data: any): Promise<any> {
  return updateItem('pages', id, data);
}

/**
 * Delete a page
 * @param id - Page ID
 * @returns Promise with the deletion result
 */
export async function deletePage(id: string | number): Promise<any> {
  return deleteItem('pages', id);
}

/**
 * Create a new product category
 * @param data - Product category data
 * @returns Promise with the created product category
 */
export async function createProductCategory(data: any): Promise<any> {
  return createItem('product-categories', data);
}

/**
 * Update an existing product category
 * @param id - Product category ID
 * @param data - Updated product category data
 * @returns Promise with the updated product category
 */
export async function updateProductCategory(id: string | number, data: any): Promise<any> {
  return updateItem('product-categories', id, data);
}

/**
 * Delete a product category
 * @param id - Product category ID
 * @returns Promise with the deletion result
 */
export async function deleteProductCategory(id: string | number): Promise<any> {
  return deleteItem('product-categories', id);
}

export default {
  createItem,
  updateItem,
  deleteItem,
  createBanner,
  updateBanner,
  deleteBanner,
  createPage,
  updatePage,
  deletePage,
  createProductCategory,
  updateProductCategory,
  deleteProductCategory
};
