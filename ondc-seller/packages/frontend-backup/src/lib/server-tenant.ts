import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';

// Default tenant ID for server-side rendering
const DEFAULT_TENANT_ID = 'default';

// Cookie name for storing the tenant ID
const TENANT_COOKIE_NAME = 'x-tenant-id';

/**
 * Get the current tenant ID from the request headers
 * This function is meant to be used in server components and API routes
 * @returns The current tenant ID or the default tenant ID
 */
export function getTenantIdFromRequest(): string {
  try {
    // Get headers from the request
    const headersList = headers();
    
    // Try to get tenant ID from the x-tenant-id header (set by middleware)
    const tenantId = headersList.get('x-tenant-id');
    
    return tenantId || DEFAULT_TENANT_ID;
  } catch (error) {
    console.error('Error getting tenant ID from request:', error);
    return DEFAULT_TENANT_ID;
  }
}

/**
 * Get the current tenant ID from the NextRequest object
 * This function is meant to be used in API route handlers
 * @param req The NextRequest object
 * @returns The current tenant ID or the default tenant ID
 */
export function getTenantIdFromNextRequest(req: NextRequest): string {
  try {
    // Try to get tenant ID from the x-tenant-id header (set by middleware)
    const tenantId = req.headers.get('x-tenant-id');
    
    // If not found in header, try to get from cookie
    if (!tenantId) {
      const cookieTenantId = req.cookies.get(TENANT_COOKIE_NAME)?.value;
      if (cookieTenantId) {
        return cookieTenantId;
      }
    }
    
    return tenantId || DEFAULT_TENANT_ID;
  } catch (error) {
    console.error('Error getting tenant ID from NextRequest:', error);
    return DEFAULT_TENANT_ID;
  }
}

/**
 * Add tenant ID to the response headers and cookies
 * This function is meant to be used in API route handlers
 * @param res The NextResponse object
 * @param tenantId The tenant ID to set
 * @returns The modified NextResponse object
 */
export function addTenantToResponse(res: NextResponse, tenantId: string): NextResponse {
  // Set the tenant ID in the response headers
  res.headers.set('x-tenant-id', tenantId);
  
  // Set the tenant ID in a cookie for client-side access
  res.cookies.set(TENANT_COOKIE_NAME, tenantId, {
    // Max age: 30 days
    maxAge: 30 * 24 * 60 * 60,
    // Use secure cookies in production
    secure: process.env.NODE_ENV === 'production',
    // Only send cookie over HTTPS in production
    httpOnly: true,
    // Make cookie accessible from JavaScript
    sameSite: 'lax',
    // Set path to root so cookie is available everywhere
    path: '/',
  });
  
  return res;
}

export default {
  getTenantIdFromRequest,
  getTenantIdFromNextRequest,
  addTenantToResponse,
};
