/**
 * Strapi API client for the ONDC Seller Platform
 */

import { getCurrentTenantId } from './tenant';

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const STRAPI_API_TOKEN =
  process.env.STRAPI_API_TOKEN ||
  'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

/**
 * Process Strapi image data to ensure URLs are absolute
 * @param {any} image - The image object from Strapi
 * @returns {any} Processed image object
 */
export function processStrapiImage(image: any): any {
  if (!image) return null;

  // Handle Strapi v4 format with data property
  if (image.data) {
    const imageData = image.data;
    // Handle both single image and collection
    if (Array.isArray(imageData) && imageData.length > 0) {
      const firstImage = imageData[0];
      image = {
        id: firstImage.id,
        ...(firstImage.attributes || firstImage),
      };
    } else if (imageData) {
      image = {
        id: imageData.id,
        ...(imageData.attributes || imageData),
      };
    }
  }

  // Handle formats if present
  if (image.formats) {
    // Prefer larger formats if available
    const format =
      image.formats.large || image.formats.medium || image.formats.small || image.formats.thumbnail;
    if (format && format.url) {
      image.url = format.url;
    }
  }

  // Transform image URLs to absolute URLs if they're relative
  if (image.url && !image.url.startsWith('http')) {
    // Ensure URL starts with a slash
    const imageUrl = image.url.startsWith('/') ? image.url : `/${image.url}`;
    image.url = `${STRAPI_URL}${imageUrl}`;
  }

  return image;
}

/**
 * Process Strapi banner data
 * @param {any} item - The item object from Strapi
 * @returns {any} Processed item object
 */
export function processStrapiItem(item: any): any {
  if (!item) return null;

  // Process image if it exists
  if (item.image) {
    item.image = processStrapiImage(item.image);
  }

  // Add CTA text and link for banners if not present
  if (item.link && !item.cta_link) {
    item.cta_link = item.link;
    item.cta_text = item.cta_text || 'Shop Now';
  }

  return item;
}

/**
 * Fetch data from Strapi API
 * @param endpoint - API endpoint
 * @param options - Fetch options
 * @returns Promise with the response data
 */
export async function fetchAPI(endpoint: string, options = {}) {
  // Get tenant ID safely (will return default during SSR)
  let tenantId = 'default';

  // Only attempt to get tenant ID on client side
  if (typeof window !== 'undefined') {
    try {
      // Use a safe method to get tenant ID that handles all edge cases
      tenantId = getCurrentTenantId();
    } catch (error) {
      console.error('Error getting tenant ID:', error);
      // Continue with default tenant ID
    }
  }

  // Prepare request options with authorization
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${STRAPI_API_TOKEN}`,
    },
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
  };

  // Add tenant ID as a query parameter if available
  try {
    const url = new URL(`${STRAPI_URL}/api${endpoint}`);
    if (tenantId) {
      url.searchParams.append('tenant', tenantId);
    }

    console.log(`Fetching from Strapi: ${url.toString()}`);
    const response = await fetch(url.toString(), mergedOptions);

    if (!response.ok) {
      console.error(`Error fetching from Strapi API: ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error(`Error fetching from Strapi API: ${error}`);
    return null;
  }
}

/**
 * Get all items from a collection
 * @param collection - Collection name
 * @param params - Query parameters
 * @returns Promise with the collection data
 */
export async function getCollection(collection: string, params = {}) {
  const queryString = new URLSearchParams({
    populate: '*',
    ...params,
  }).toString();

  return fetchAPI(`/${collection}?${queryString}`);
}

/**
 * Get a single item from a collection
 * @param collection - Collection name
 * @param id - Item ID
 * @param params - Query parameters
 * @returns Promise with the item data
 */
export async function getItem(collection: string, id: string | number, params = {}) {
  const queryString = new URLSearchParams({
    populate: '*',
    ...params,
  }).toString();

  return fetchAPI(`/${collection}/${id}?${queryString}`);
}

/**
 * Get a single item by slug
 * @param collection - Collection name
 * @param slug - Item slug
 * @param params - Query parameters
 * @returns Promise with the item data
 */
export async function getItemBySlug(collection: string, slug: string, params = {}) {
  const queryString = new URLSearchParams({
    populate: '*',
    'filters[slug][$eq]': slug,
    ...params,
  }).toString();

  const response = await fetchAPI(`/${collection}?${queryString}`);
  return response.data[0] || null;
}

/**
 * Get all sellers
 * @param params - Query parameters
 * @returns Promise with the sellers data
 */
export async function getSellers(params = {}) {
  return getCollection('sellers', params);
}

/**
 * Get a seller by ID
 * @param id - Seller ID
 * @param params - Query parameters
 * @returns Promise with the seller data
 */
export async function getSeller(id: string | number, params = {}) {
  return getItem('sellers', id, params);
}

/**
 * Get all product categories
 * @param params - Query parameters
 * @returns Promise with the product categories data
 */
export async function getProductCategories(params = {}) {
  return getCollection('product-categories', params);
}

/**
 * Get a product category by ID
 * @param id - Product category ID
 * @param params - Query parameters
 * @returns Promise with the product category data
 */
export async function getProductCategory(id: string | number, params = {}) {
  return getItem('product-categories', id, params);
}

/**
 * Get a product category by slug
 * @param slug - Product category slug
 * @param params - Query parameters
 * @returns Promise with the product category data
 */
export async function getProductCategoryBySlug(slug: string, params = {}) {
  return getItemBySlug('product-categories', slug, params);
}

/**
 * Get all products
 * @param params - Query parameters
 * @returns Promise with the products data
 */
export async function getProducts(params = {}) {
  return getCollection('products', params);
}

/**
 * Get a product by ID
 * @param id - Product ID
 * @param params - Query parameters
 * @returns Promise with the product data
 */
export async function getProduct(id: string | number, params = {}) {
  return getItem('products', id, params);
}

/**
 * Get a product by slug
 * @param slug - Product slug
 * @param params - Query parameters
 * @returns Promise with the product data
 */
export async function getProductBySlug(slug: string, params = {}) {
  return getItemBySlug('products', slug, params);
}

/**
 * Get all banners
 * @param params - Query parameters
 * @returns Promise with the banners data
 */
export async function getBanners(params = {}) {
  try {
    const data = await getCollection('banners', {
      'filters[active][$eq]': true,
      populate: 'image',
      ...params,
    });

    if (!data || !data.data) {
      // If no data, use fallback for banners (this is acceptable for banners)
      return getFallbackBanners();
    }

    // Process banner images
    const processedBanners = data.data.map((banner: any) => {
      const item = {
        id: banner.id,
        ...(banner.attributes || banner),
      };
      return processStrapiItem(item);
    });

    return {
      items: processedBanners,
      pagination: data.meta?.pagination,
    };
  } catch (error) {
    console.error('Error fetching banners:', error);
    // For banners, we can use fallback since they're not critical
    return getFallbackBanners();
  }
}

/**
 * Get fallback banners when Strapi is unavailable
 * @returns Object with fallback banner data
 */
function getFallbackBanners() {
  console.log('Using fallback banners');
  return {
    items: [
      {
        id: 1,
        title: 'Welcome to ONDC Seller Platform',
        subtitle: 'Connect with millions of buyers across India',
        image: {
          url: '/images/placeholder.png',
        },
        cta_text: 'Browse Products',
        cta_link: '/products',
      },
      {
        id: 2,
        title: 'Discover Quality Products',
        subtitle: 'From trusted sellers across the country',
        image: {
          url: '/images/placeholder.png',
        },
        cta_text: 'Shop Now',
        cta_link: '/products',
      },
    ],
  };
}

/**
 * Get a page by slug
 * @param slug - Page slug
 * @param params - Query parameters
 * @returns Promise with the page data
 */
export async function getPageBySlug(slug: string, params = {}) {
  try {
    const data = await getItemBySlug('pages', slug, {
      populate: '*',
      ...params,
    });

    if (!data) {
      return getFallbackPage(slug);
    }

    // Process page content
    return processStrapiItem(data);
  } catch (error) {
    console.error(`Error fetching page with slug ${slug}:`, error);
    return getFallbackPage(slug);
  }
}

/**
 * Get fallback page content when Strapi is unavailable
 * @param slug - Page slug
 * @returns Object with fallback page data
 */
function getFallbackPage(slug: string) {
  console.log(`Using fallback page for ${slug}`);

  if (slug === 'about-us') {
    return {
      id: 1,
      title: 'About Us',
      content:
        '<h2>Welcome to ONDC Seller Platform</h2><p>We are a marketplace connecting buyers and sellers across India.</p><p>This is a temporary static page while our CMS is undergoing maintenance.</p>',
    };
  } else if (slug === 'contact') {
    return {
      id: 2,
      title: 'Contact Us',
      content:
        '<h2>Contact Us</h2><p>For any inquiries, please email <NAME_EMAIL></p><p>This is a temporary static page while our CMS is undergoing maintenance.</p>',
    };
  } else if (slug === 'terms') {
    return {
      id: 3,
      title: 'Terms & Conditions',
      content:
        '<h2>Terms & Conditions</h2><p>Please read our terms and conditions carefully.</p><p>This is a temporary static page while our CMS is undergoing maintenance.</p>',
    };
  } else if (slug === 'privacy') {
    return {
      id: 4,
      title: 'Privacy Policy',
      content:
        '<h2>Privacy Policy</h2><p>Your privacy is important to us.</p><p>This is a temporary static page while our CMS is undergoing maintenance.</p>',
    };
  } else {
    return null;
  }
}
