/**
 * MSW (Mock Service Worker) setup for browser
 * This file sets up MSW for development and testing
 */

// Import handlers only when needed to prevent issues
let handlers: any[] = [];

const loadHandlers = async () => {
  try {
    // Only import handlers if we're in browser environment
    if (typeof window !== 'undefined') {
      const mockApi = await import('./mock-api');
      handlers = mockApi.handlers || [];
      console.log('🔶 MSW: Loaded', handlers.length, 'handlers');
    }
  } catch (error) {
    console.warn('⚠️ MSW: Failed to import handlers:', error);
    handlers = [];
  }
};

// Setup MSW worker for browser (only in browser environment)
let worker: any = null;
let workerInitialized = false;

// Initialize worker only when needed and in browser environment
const initializeWorker = async () => {
  // Don't initialize if already tried or if MSW is disabled
  if (workerInitialized || typeof window === 'undefined') {
    return worker;
  }

  // Check if MSW should be enabled before initializing
  const envEnabled = process.env.NEXT_PUBLIC_MSW_ENABLED === 'true';
  const storageEnabled =
    typeof window !== 'undefined' && localStorage.getItem('msw-enabled') === 'true';

  if (!envEnabled && !storageEnabled) {
    console.log(
      '🔶 MSW: Skipping worker initialization - MSW is disabled, using local API routes with MSW data structure'
    );
    workerInitialized = true;
    return null;
  }

  // For now, skip MSW initialization due to import issues
  console.warn('⚠️ MSW: Service Worker initialization temporarily disabled due to import issues');
  console.log('🔶 MSW: Using local API routes with shared MSW data structure for consistency');
  workerInitialized = true;
  return null;

  // TODO: Re-enable when MSW import issues are resolved
  /*
  try {
    // Load handlers first
    await loadHandlers();

    // Dynamic import to prevent issues during SSR (MSW v2)
    // Use require for MSW browser module as a workaround for Next.js module resolution issues
    let setupWorker;
    try {
      // Try using require for the browser module
      const browserModule = require('msw/lib/browser');
      setupWorker = browserModule.setupWorker;
      console.log('🔶 MSW: Successfully loaded setupWorker via require');
    } catch (error) {
      console.warn('⚠️ MSW: Failed to require msw/lib/browser:', error);
      try {
        // Fallback to dynamic import
        const browserModule = await import('msw/lib/browser');
        setupWorker = browserModule.setupWorker;
        console.log('🔶 MSW: Successfully loaded setupWorker via import');
      } catch (importError) {
        console.error('❌ MSW: Failed to import setupWorker:', importError);
        throw importError;
      }
    }

    // Ensure handlers are available
    if (!handlers || handlers.length === 0) {
      console.warn('⚠️ MSW: No handlers available');
      worker = null;
      return null;
    }

    worker = setupWorker(...handlers);
    console.log('🔶 MSW: Worker initialized with', handlers.length, 'handlers');
  } catch (error) {
    console.log('inside catch::::::');

    console.error('❌ MSW: Failed to initialize worker:', error);
    worker = null;
  }

  workerInitialized = true;
  return worker;
  */
};

// Start MSW in development mode
export const startMSW = async () => {
  // Only proceed if we're in browser environment and development mode
  if (typeof window === 'undefined' || process.env.NODE_ENV !== 'development') {
    console.log('🔶 MSW: Skipping - not in browser or not in development mode');
    return false;
  }

  try {
    // Initialize worker if not already done
    const currentWorker = await initializeWorker();

    if (!currentWorker) {
      console.warn('⚠️ MSW: Worker not available');
      return false;
    }

    // Check if service worker is supported
    if (!('serviceWorker' in navigator)) {
      console.warn('⚠️ MSW: Service Worker not supported in this browser');
      return false;
    }

    await currentWorker.start({
      onUnhandledRequest: 'bypass', // Don't warn about unhandled requests
      serviceWorker: {
        url: '/mockServiceWorker.js',
      },
      quiet: false, // Show MSW logs for debugging
    });

    console.log('✅ MSW: Mock Service Worker started successfully');
    return true;
  } catch (error) {
    console.error('❌ MSW: Failed to start Mock Service Worker:', error);
    return false;
  }
};

// Stop MSW
export const stopMSW = async () => {
  if (typeof window === 'undefined') {
    console.log('🔶 MSW: Skipping stop - not in browser environment');
    return false;
  }

  if (!worker) {
    console.log('🔶 MSW: No worker to stop');
    return true; // Consider this a success since MSW is not running
  }

  try {
    await worker.stop();
    console.log('✅ MSW: Mock Service Worker stopped successfully');
    return true;
  } catch (error) {
    console.error('❌ MSW: Failed to stop Mock Service Worker:', error);
    return false;
  }
};

// Check if MSW is enabled via environment variable
export const isMSWEnabled = () => {
  return process.env.NEXT_PUBLIC_MSW_ENABLED === 'true';
};

// MSW control functions for runtime toggling
export const enableMSW = async () => {
  if (typeof window === 'undefined') {
    console.log('🔶 MSW: Cannot enable - not in browser environment');
    return false;
  }

  try {
    localStorage.setItem('msw-enabled', 'true');
    console.log('🔶 MSW: Enabled in localStorage');
    return await startMSW();
  } catch (error) {
    console.error('❌ MSW: Failed to enable:', error);
    return false;
  }
};

export const disableMSW = async () => {
  if (typeof window === 'undefined') {
    console.log('🔶 MSW: Cannot disable - not in browser environment');
    return false;
  }

  try {
    localStorage.setItem('msw-enabled', 'false');
    console.log('🔶 MSW: Disabled in localStorage');
    return await stopMSW();
  } catch (error) {
    console.error('❌ MSW: Failed to disable:', error);
    return false;
  }
};

export const isMSWEnabledInStorage = () => {
  if (typeof window === 'undefined') {
    return false;
  }

  try {
    return localStorage.getItem('msw-enabled') === 'true';
  } catch (error) {
    console.warn('⚠️ MSW: Failed to read localStorage:', error);
    return false;
  }
};
