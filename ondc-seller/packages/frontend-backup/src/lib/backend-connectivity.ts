'use client';

import { useState, useEffect, useCallback } from 'react';
import { getCurrentTenantId } from './tenant';

// Configuration
const MEDUSA_URL = process.env.NEXT_PUBLIC_MEDUSA_URL || 'http://localhost:9000';
const HEALTH_CHECK_TIMEOUT = 2000; // 2 seconds
const RETRY_INTERVAL = 30000; // 30 seconds

// Backend connectivity status
export type BackendStatus = 'unknown' | 'available' | 'unavailable';

// Backend connectivity state
interface BackendConnectivityState {
  status: BackendStatus;
  lastChecked: Date | null;
  lastAvailable: Date | null;
  checkCount: number;
  error: Error | null;
  isUsingMockData: boolean;
}

// Initial state
const initialState: BackendConnectivityState = {
  status: 'unknown',
  lastChecked: null,
  lastAvailable: null,
  checkCount: 0,
  error: null,
  isUsingMockData: false,
};

// Event listeners
type StatusChangeListener = (status: BackendStatus, isUsingMockData: boolean) => void;
const statusChangeListeners: StatusChangeListener[] = [];

// Current state
let currentState: BackendConnectivityState = { ...initialState };

// Interval ID for periodic checks
let checkIntervalId: NodeJS.Timeout | null = null;

/**
 * Check if the backend is available
 * @returns Promise with the backend availability status
 */
export const checkBackendAvailability = async (): Promise<boolean> => {
  const tenantId = getCurrentTenantId();

  try {
    // Create an AbortController with timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), HEALTH_CHECK_TIMEOUT);

    // Log the check attempt
    console.log(`[Backend Connectivity] Checking backend availability for tenant: ${tenantId}`);

    // Try to fetch the health endpoint
    const response = await fetch(`${MEDUSA_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'x-tenant-id': tenantId,
      },
      signal: controller.signal,
    }).catch(error => {
      console.warn(`[Backend Connectivity] Health check fetch error: ${error.message}`);
      return null;
    });

    // Clear the timeout
    clearTimeout(timeoutId);

    // Update state
    const isAvailable = response && response.ok;
    updateState({
      status: isAvailable ? 'available' : 'unavailable',
      lastChecked: new Date(),
      lastAvailable: isAvailable ? new Date() : currentState.lastAvailable,
      checkCount: currentState.checkCount + 1,
      error: isAvailable ? null : new Error('Backend health check failed'),
      isUsingMockData: !isAvailable,
    });

    // Log the result
    console.log(
      `[Backend Connectivity] Backend ${isAvailable ? 'is available' : 'is unavailable'} for tenant: ${tenantId}`
    );

    return isAvailable;
  } catch (error) {
    // Update state
    updateState({
      status: 'unavailable',
      lastChecked: new Date(),
      checkCount: currentState.checkCount + 1,
      error:
        error instanceof Error ? error : new Error('Unknown error checking backend availability'),
      isUsingMockData: true,
    });

    // Log the error
    console.error(
      `[Backend Connectivity] Error checking backend availability for tenant: ${tenantId}`,
      error
    );

    return false;
  }
};

/**
 * Update the backend connectivity state
 * @param newState Partial state to update
 */
const updateState = (newState: Partial<BackendConnectivityState>): void => {
  // Update current state
  currentState = { ...currentState, ...newState };

  // Notify listeners
  if (newState.status && newState.status !== 'unknown') {
    notifyStatusChangeListeners(newState.status, currentState.isUsingMockData);
  }
};

/**
 * Notify status change listeners
 * @param status New backend status
 * @param isUsingMockData Whether mock data is being used
 */
const notifyStatusChangeListeners = (status: BackendStatus, isUsingMockData: boolean): void => {
  statusChangeListeners.forEach(listener => {
    try {
      listener(status, isUsingMockData);
    } catch (error) {
      console.error('[Backend Connectivity] Error in status change listener:', error);
    }
  });
};

/**
 * Start periodic backend availability checks
 */
export const startPeriodicChecks = (): void => {
  // Stop any existing checks
  stopPeriodicChecks();

  // Perform an initial check
  checkBackendAvailability();

  // Start periodic checks
  checkIntervalId = setInterval(checkBackendAvailability, RETRY_INTERVAL);

  console.log(
    `[Backend Connectivity] Started periodic checks every ${RETRY_INTERVAL / 1000} seconds`
  );
};

/**
 * Stop periodic backend availability checks
 */
export const stopPeriodicChecks = (): void => {
  if (checkIntervalId) {
    clearInterval(checkIntervalId);
    checkIntervalId = null;
    console.log('[Backend Connectivity] Stopped periodic checks');
  }
};

/**
 * Add a status change listener
 * @param listener Function to call when status changes
 * @returns Function to remove the listener
 */
export const addStatusChangeListener = (listener: StatusChangeListener): (() => void) => {
  statusChangeListeners.push(listener);

  // Return a function to remove the listener
  return () => {
    const index = statusChangeListeners.indexOf(listener);
    if (index !== -1) {
      statusChangeListeners.splice(index, 1);
    }
  };
};

/**
 * Get the current backend connectivity state
 * @returns Current backend connectivity state
 */
export const getBackendConnectivityState = (): BackendConnectivityState => {
  return { ...currentState };
};

/**
 * Check if the backend is currently available
 * @returns Whether the backend is available
 */
export const isBackendAvailable = (): boolean => {
  return currentState.status === 'available';
};

/**
 * Check if mock data is being used
 * @returns Whether mock data is being used
 */
export const isUsingMockData = (): boolean => {
  return currentState.isUsingMockData;
};

/**
 * Force a backend availability check
 * @returns Promise with the backend availability status
 */
export const forceCheck = async (): Promise<boolean> => {
  return checkBackendAvailability();
};

// Initialize on module load if in browser environment
// NOTE: Automatic startup disabled to prevent unnecessary backend checks
// The backend connectivity system is now only used when explicitly needed
if (typeof window !== 'undefined') {
  // Automatic startup disabled - start checks manually if needed
  // startPeriodicChecks();

  // Stop checks when the window is unloaded
  window.addEventListener('beforeunload', stopPeriodicChecks);
}

export default {
  checkBackendAvailability,
  startPeriodicChecks,
  stopPeriodicChecks,
  addStatusChangeListener,
  getBackendConnectivityState,
  isBackendAvailable,
  isUsingMockData,
  forceCheck,
};
