import { getCurrentTenantId } from './tenant';
import { medusaApi } from './api';
import { mockMedusaApi } from './mock-api';
import { isMSWEnabled } from './msw';
import { productCategories } from '../data/categories';

// Create a local API instance for frontend API routes
const localApi = {
  get: async (url: string, config?: any) => {
    const baseURL =
      typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000';
    const fullUrl = `${baseURL}/api${url}`;

    const headers = {
      'Content-Type': 'application/json',
      ...config?.headers,
    };

    // Add query parameters
    const queryParams = new URLSearchParams();
    if (config?.params) {
      Object.entries(config.params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, String(value));
        }
      });
    }

    const urlWithParams = queryParams.toString() ? `${fullUrl}?${queryParams}` : fullUrl;

    console.log('[LocalAPI] Making request to:', urlWithParams);
    console.log('[LocalAPI] Headers:', headers);

    const response = await fetch(urlWithParams, {
      method: 'GET',
      headers,
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return { data };
  },
};

// Use MSW-enabled medusaApi when MSW is enabled, otherwise use local API for development
const api = isMSWEnabled()
  ? medusaApi
  : process.env.NODE_ENV === 'development'
    ? localApi
    : medusaApi;

// Types
export interface CategoryData {
  id: string;
  name: string;
  description?: string;
  parent_id?: string;
  handle: string;
  metadata?: Record<string, any>;
}

export interface SubcategoryData extends CategoryData {
  parent_category?: CategoryData;
}

export interface ProductVariant {
  id: string;
  title: string;
  prices: Array<{
    amount: number;
    currency_code: string;
  }>;
  inventory_quantity: number;
}

export interface Product {
  id: string;
  title: string;
  description: string | null;
  thumbnail: string | null;
  handle: string;
  status: string;
  variants: ProductVariant[];
  tenant_id?: string;
  images?: Array<{
    id: string;
    url: string;
  }>;
}

export interface PaginationData {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

export interface CategoryProductsResponse {
  products: Product[];
  category: CategoryData | null;
  subcategory?: SubcategoryData | null;
  pagination: PaginationData | null;
  error: string | null;
}

export interface ProductsQueryParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  filters?: Record<string, string[]>;
  priceRange?: {
    min: number;
    max: number;
  } | null;
  inStockOnly?: boolean;
}

/**
 * Get category information from the static data
 * @param categoryId - Category ID
 * @returns Category data or null if not found
 */
export function getCategoryInfo(categoryId: string): CategoryData | null {
  const category = productCategories.find(cat => cat.id === categoryId);
  if (!category) return null;

  return {
    id: category.id,
    name: category.name,
    handle: category.href.split('/').pop() || category.id,
    description: `Explore our ${category.name} collection featuring top brands and latest products.`,
  };
}

/**
 * Get subcategory information from the static data
 * @param categoryId - Parent category ID
 * @param subcategoryId - Subcategory ID
 * @returns Subcategory data or null if not found
 */
export function getSubcategoryInfo(
  categoryId: string,
  subcategoryId: string
): SubcategoryData | null {
  console.log(
    `[getSubcategoryInfo] Looking for subcategory ${subcategoryId} in category ${categoryId}`
  );

  // Find the category
  const category = productCategories.find(cat => cat.id === categoryId);
  console.log(`[getSubcategoryInfo] Found category:`, category ? category.name : 'null');
  if (!category) return null;

  // Find the subcategory
  console.log(
    `[getSubcategoryInfo] Subcategories in ${category.name}:`,
    category.subcategories.map(sub => sub.id)
  );
  const subcategory = category.subcategories.find(sub => sub.id === subcategoryId);
  console.log(`[getSubcategoryInfo] Found subcategory:`, subcategory ? subcategory.name : 'null');
  if (!subcategory) return null;

  return {
    id: subcategory.id,
    name: subcategory.name,
    handle: subcategory.href.split('/').pop() || subcategory.id,
    parent_id: category.id,
    parent_category: {
      id: category.id,
      name: category.name,
      handle: category.href.split('/').pop() || category.id,
    },
    description: `Explore our ${subcategory.name} collection in the ${category.name} category.`,
  };
}

/**
 * Get products for a specific category
 * @param categoryId - Category ID
 * @param params - Query parameters for pagination, sorting, and filtering
 * @param tenantId - Optional tenant ID
 * @returns Promise with category products data
 */
export async function getCategoryProducts(
  categoryId: string,
  params: ProductsQueryParams = {},
  tenantId: string | null = null
): Promise<CategoryProductsResponse> {
  // Set tenant ID if provided, otherwise get from current context
  const currentTenantId = tenantId || getCurrentTenantId();

  try {
    // Get category information
    const categoryInfo = getCategoryInfo(categoryId);

    if (!categoryInfo) {
      return {
        products: [],
        category: null,
        pagination: null,
        error: `Category ${categoryId} not found`,
      };
    }

    // Prepare query parameters
    const queryParams: Record<string, any> = {
      limit: params.pageSize || 12,
      offset: ((params.page || 1) - 1) * (params.pageSize || 12),
    };

    // Add category filter
    queryParams.category_id = categoryId;

    // Add sorting
    if (params.sortBy) {
      switch (params.sortBy) {
        case 'price-asc':
          queryParams.order = 'variants.prices.amount:asc';
          break;
        case 'price-desc':
          queryParams.order = 'variants.prices.amount:desc';
          break;
        case 'name-asc':
          queryParams.order = 'title:asc';
          break;
        case 'name-desc':
          queryParams.order = 'title:desc';
          break;
        case 'newest':
        default:
          queryParams.order = 'created_at:desc';
          break;
      }
    }

    // Add in-stock filter
    if (params.inStockOnly) {
      queryParams.inventory_quantity_gt = 0;
    }

    // Add price range filter
    if (params.priceRange) {
      if (params.priceRange.min > 0) {
        queryParams.price_gt = params.priceRange.min;
      }
      if (params.priceRange.max < Infinity) {
        queryParams.price_lt = params.priceRange.max;
      }
    }

    // Add custom filters
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, values]) => {
        if (values.length > 0) {
          queryParams[`metadata.${key}`] = values.join(',');
        }
      });
    }

    // Add tenant ID to headers
    const headers = {
      'x-tenant-id': currentTenantId,
    };

    // Fetch products from API (Medusa or mock)
    const response = await api.get('/store/products', {
      params: queryParams,
      headers,
    });
    console.log('getCategoryProducts response:', response.data);

    // Process response
    const data = response.data;

    return {
      products: data.products || [],
      category: categoryInfo,
      pagination: {
        page: params.page || 1,
        pageSize: params.pageSize || 12,
        pageCount: Math.ceil((data.count || 0) / (params.pageSize || 12)),
        total: data.count || 0,
      },
      error: null,
    };
  } catch (error) {
    console.error(`Error fetching category ${categoryId} products:`, error);

    // In development mode, return mock data
    if (process.env.NODE_ENV === 'development') {
      console.log(`[getCategoryProducts] Using mock data for ${categoryId}`);

      // Generate mock products for this category
      const mockProducts = Array.from({ length: 8 }, (_, i) => ({
        id: `mock_${categoryId}_${i + 1}`,
        title: `${categoryId.charAt(0).toUpperCase() + categoryId.slice(1)} Product ${i + 1}`,
        description: `This is a mock ${categoryId} product for testing.`,
        thumbnail: `https://placehold.co/400x300?text=${categoryId}+${i + 1}`,
        handle: `${categoryId}-product-${i + 1}`,
        status: 'published',
        variants: [
          {
            id: `mock_variant_${i + 1}`,
            title: 'Default Variant',
            prices: [{ amount: 1000 * (i + 1), currency_code: 'inr' }],
            inventory_quantity: 10,
          },
        ],
        tenant_id: getCurrentTenantId(),
      }));

      return {
        products: mockProducts,
        category: getCategoryInfo(categoryId),
        pagination: {
          page: params.page || 1,
          pageSize: params.pageSize || 12,
          pageCount: 1,
          total: mockProducts.length,
        },
        error: null,
      };
    }

    // In production, return empty products with error
    return {
      products: [],
      category: getCategoryInfo(categoryId),
      pagination: null,
      error: error instanceof Error ? error.message : 'Failed to load category products',
    };
  }
}

/**
 * Get products for a specific subcategory
 * @param categoryId - Parent category ID
 * @param subcategoryId - Subcategory ID
 * @param params - Query parameters for pagination, sorting, and filtering
 * @param tenantId - Optional tenant ID
 * @returns Promise with subcategory products data
 */
export async function getSubcategoryProducts(
  categoryId: string,
  subcategoryId: string,
  params: ProductsQueryParams = {},
  tenantId: string | null = null
): Promise<CategoryProductsResponse> {
  console.log(
    `[getSubcategoryProducts] Called with categoryId=${categoryId}, subcategoryId=${subcategoryId}`
  );
  console.log(`[getSubcategoryProducts] Params:`, params);

  // Set tenant ID if provided, otherwise get from current context
  const currentTenantId = tenantId || getCurrentTenantId();
  console.log(`[getSubcategoryProducts] Using tenant ID: ${currentTenantId}`);

  try {
    // Get subcategory information
    const subcategoryInfo = getSubcategoryInfo(categoryId, subcategoryId);
    console.log(`[getSubcategoryProducts] Subcategory info:`, subcategoryInfo);

    if (!subcategoryInfo) {
      console.error(
        `[getSubcategoryProducts] Subcategory ${subcategoryId} not found in category ${categoryId}`
      );
      return {
        products: [],
        category: getCategoryInfo(categoryId),
        subcategory: null,
        pagination: null,
        error: `Subcategory ${subcategoryId} not found in category ${categoryId}`,
      };
    }

    // Prepare query parameters
    const queryParams: Record<string, any> = {
      limit: params.pageSize || 12,
      offset: ((params.page || 1) - 1) * (params.pageSize || 12),
    };

    // Add subcategory filter - use parent_category_id and subcategory_id
    queryParams.parent_category_id = categoryId;
    queryParams.category_id = subcategoryId;

    // For debugging, also add a subcategory_id parameter
    queryParams.subcategory_id = subcategoryId;

    // Add sorting
    if (params.sortBy) {
      switch (params.sortBy) {
        case 'price-asc':
          queryParams.order = 'variants.prices.amount:asc';
          break;
        case 'price-desc':
          queryParams.order = 'variants.prices.amount:desc';
          break;
        case 'name-asc':
          queryParams.order = 'title:asc';
          break;
        case 'name-desc':
          queryParams.order = 'title:desc';
          break;
        case 'newest':
        default:
          queryParams.order = 'created_at:desc';
          break;
      }
    }

    // Add in-stock filter
    if (params.inStockOnly) {
      queryParams.inventory_quantity_gt = 0;
    }

    // Add price range filter
    if (params.priceRange) {
      if (params.priceRange.min > 0) {
        queryParams.price_gt = params.priceRange.min;
      }
      if (params.priceRange.max < Infinity) {
        queryParams.price_lt = params.priceRange.max;
      }
    }

    // Add custom filters
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, values]) => {
        if (values.length > 0) {
          queryParams[`metadata.${key}`] = values.join(',');
        }
      });
    }

    // Add tenant ID to headers
    const headers = {
      'x-tenant-id': currentTenantId,
    };

    console.log(`[getSubcategoryProducts] API request headers:`, headers);
    console.log(`[getSubcategoryProducts] API request params:`, queryParams);
    console.log(
      `[getSubcategoryProducts] Using API:`,
      isMSWEnabled() ? 'medusaApi (MSW enabled)' : 'mockMedusaApi'
    );

    // Fetch products from API (Medusa or mock)
    console.log(`[getSubcategoryProducts] Making API request to /store/products`);
    try {
      const response = await api.get('/store/products', {
        params: queryParams,
        headers,
      });

      console.log(`[getSubcategoryProducts] API request successful`);

      // Log response details
      if ('status' in response) {
        console.log(`[getSubcategoryProducts] API response status:`, response.status);
      }
      console.log(`[getSubcategoryProducts] API response data:`, response.data);

      // Process response
      const data = response.data;

      return {
        products: data.products || [],
        category: getCategoryInfo(categoryId),
        subcategory: subcategoryInfo,
        pagination: {
          page: params.page || 1,
          pageSize: params.pageSize || 12,
          pageCount: Math.ceil((data.count || 0) / (params.pageSize || 12)),
          total: data.count || 0,
        },
        error: null,
      };
    } catch (apiError) {
      console.error(`[getSubcategoryProducts] API request failed:`, apiError);
      throw apiError; // Re-throw to be caught by outer catch block
    }
  } catch (error) {
    console.error(`Error fetching subcategory ${subcategoryId} products:`, error);

    // In development mode, return mock data
    if (process.env.NODE_ENV === 'development') {
      console.log(`[getSubcategoryProducts] Using mock data for ${categoryId}/${subcategoryId}`);

      // Generate mock products for this subcategory
      const mockProducts = Array.from({ length: 6 }, (_, i) => ({
        id: `mock_${subcategoryId}_${i + 1}`,
        title: `${subcategoryId.charAt(0).toUpperCase() + subcategoryId.slice(1)} Product ${i + 1}`,
        description: `This is a mock ${subcategoryId} product for testing.`,
        thumbnail: `https://placehold.co/400x300?text=${subcategoryId}+${i + 1}`,
        handle: `${subcategoryId}-product-${i + 1}`,
        status: 'published',
        variants: [
          {
            id: `mock_variant_${i + 1}`,
            title: 'Default Variant',
            prices: [{ amount: 1000 * (i + 1), currency_code: 'inr' }],
            inventory_quantity: 10,
          },
        ],
        tenant_id: getCurrentTenantId(),
      }));

      return {
        products: mockProducts,
        category: getCategoryInfo(categoryId),
        subcategory: getSubcategoryInfo(categoryId, subcategoryId),
        pagination: {
          page: params.page || 1,
          pageSize: params.pageSize || 12,
          pageCount: 1,
          total: mockProducts.length,
        },
        error: null,
      };
    }

    // In production, return empty products with error
    return {
      products: [],
      category: getCategoryInfo(categoryId),
      subcategory: getSubcategoryInfo(categoryId, subcategoryId),
      pagination: null,
      error: error instanceof Error ? error.message : 'Failed to load subcategory products',
    };
  }
}
