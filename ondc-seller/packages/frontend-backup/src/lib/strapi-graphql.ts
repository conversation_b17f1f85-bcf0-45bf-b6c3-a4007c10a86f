/**
 * GraphQL client for Strapi API
 *
 * This module provides functions to fetch data from Strapi using GraphQL
 * for more efficient data fetching.
 */

import axios from 'axios';
import { getCurrentTenantId } from './tenant';
import { processStrapiError } from './strapi-enhanced';
import { getAuthHeaders } from './strapi-auth';

// Configuration
const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';

// Flag to track if GraphQL is available to avoid repeated failed requests
let isGraphQLAvailable = true;

/**
 * Use REST API as a fallback when GraphQL is not available
 * @param query - Original GraphQL query
 * @param variables - Query variables
 * @returns Promise with the REST API response formatted to match GraphQL
 */
async function useRestFallback(query: string, variables: any): Promise<any> {
  try {
    // Extract collection name from the query (simplified approach)
    const collectionMatch = query.match(/{\s*(\w+)/);
    if (!collectionMatch || !collectionMatch[1]) {
      throw new Error('Could not determine collection from GraphQL query');
    }

    const collection = collectionMatch[1];
    console.log(`Falling back to REST API for collection: ${collection}`);

    // Import the REST API functions
    const { getCollection } = await import('./strapi-enhanced');

    console.log(`Using REST API fallback for collection: ${collection}`);

    // Fetch data using REST API
    const restData = await getCollection(collection, { populate: '*' });

    console.log('REST API response:', restData);

    // Transform to match GraphQL response format
    const transformedData = {
      data: {
        [collection]: {
          data: restData.items,
        },
      },
      errors: null,
    };

    console.log('Transformed data for GraphQL compatibility:', transformedData);

    return transformedData;
  } catch (error) {
    console.error('Error in REST fallback:', error);
    return {
      data: null,
      errors: [{ message: error.message || 'Unknown error in REST fallback' }],
    };
  }
}

/**
 * Execute a GraphQL query
 * @param query - GraphQL query
 * @param variables - Query variables
 * @returns Promise with the query result
 */
export async function fetchGraphQL(query: string, variables = {}): Promise<any> {
  try {
    console.log('Executing GraphQL query');

    // Get tenant ID
    const tenantId = getCurrentTenantId();

    // Add tenant ID to variables
    const variablesWithTenant = {
      ...variables,
      tenant: tenantId,
    };

    // If GraphQL is not available, skip the GraphQL request and go straight to REST fallback
    if (!isGraphQLAvailable) {
      console.log('GraphQL is not available, using REST API directly');
      return await useRestFallback(query, variablesWithTenant);
    }

    try {
      console.log(`Attempting GraphQL request to ${STRAPI_URL}/graphql`);
      console.log('GraphQL query:', query);
      console.log('GraphQL variables:', variablesWithTenant);

      // Execute query
      const response = await axios.post(
        `${STRAPI_URL}/graphql`,
        {
          query,
          variables: variablesWithTenant,
        },
        {
          headers: {
            'Content-Type': 'application/json',
            ...getAuthHeaders(),
          },
        }
      );

      console.log('GraphQL response:', response.data);

      // Check for GraphQL errors
      if (response.data.errors) {
        console.error('GraphQL errors:', response.data.errors);
        throw new Error('GraphQL errors: ' + JSON.stringify(response.data.errors));
      }

      return {
        data: response.data.data,
        errors: null,
      };
    } catch (graphqlError) {
      console.warn('GraphQL request failed, falling back to REST API:', graphqlError);

      // If we get a 405 Method Not Allowed, it means GraphQL is not available
      // Mark it as unavailable to avoid future requests
      if (graphqlError.response && graphqlError.response.status === 405) {
        console.log('GraphQL endpoint returned 405 Method Not Allowed, marking as unavailable');
        isGraphQLAvailable = false;
      }

      return await useRestFallback(query, variablesWithTenant);
    }
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error('Error executing GraphQL query with fallback:', processedError);

    return {
      data: null,
      errors: [processedError],
    };
  }
}

/**
 * Get products with related data using GraphQL
 * @returns Promise with products data
 */
export async function getProductsWithRelations(): Promise<any> {
  const query = `
    query GetProductsWithRelations($tenant: String) {
      products(filters: { tenant: { eq: $tenant } }) {
        data {
          id
          attributes {
            title
            description
            price
            thumbnail {
              data {
                attributes {
                  url
                }
              }
            }
            categories {
              data {
                id
                attributes {
                  name
                }
              }
            }
            seller {
              data {
                id
                attributes {
                  name
                }
              }
            }
          }
        }
      }
    }
  `;

  return fetchGraphQL(query);
}

/**
 * Get a single product with related data using GraphQL
 * @param id - Product ID
 * @returns Promise with product data
 */
export async function getProductWithRelations(id: string | number): Promise<any> {
  const query = `
    query GetProductWithRelations($id: ID!, $tenant: String) {
      product(id: $id, filters: { tenant: { eq: $tenant } }) {
        data {
          id
          attributes {
            title
            description
            price
            thumbnail {
              data {
                attributes {
                  url
                }
              }
            }
            images {
              data {
                attributes {
                  url
                }
              }
            }
            categories {
              data {
                id
                attributes {
                  name
                }
              }
            }
            seller {
              data {
                id
                attributes {
                  name
                }
              }
            }
          }
        }
      }
    }
  `;

  return fetchGraphQL(query, { id });
}

/**
 * Get categories with products using GraphQL
 * @returns Promise with categories data
 */
export async function getCategoriesWithProducts(): Promise<any> {
  const query = `
    query GetCategoriesWithProducts($tenant: String) {
      categories(filters: { tenant: { eq: $tenant } }) {
        data {
          id
          attributes {
            name
            description
            products {
              data {
                id
                attributes {
                  title
                  price
                  thumbnail {
                    data {
                      attributes {
                        url
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  `;

  return fetchGraphQL(query);
}

/**
 * Get banners using GraphQL
 * @returns Promise with banners data
 */
export async function getBanners(): Promise<any> {
  const query = `
    query GetBanners($tenant: String) {
      banners(filters: { tenant: { eq: $tenant }, active: { eq: true } }) {
        data {
          id
          attributes {
            title
            subtitle
            cta_text
            cta_link
            image {
              data {
                attributes {
                  url
                  width
                  height
                }
              }
            }
          }
        }
      }
    }
  `;

  return fetchGraphQL(query);
}

/**
 * Get a page by slug using GraphQL
 * @param slug - Page slug
 * @returns Promise with page data
 */
export async function getPageBySlug(slug: string): Promise<any> {
  const query = `
    query GetPageBySlug($slug: String!, $tenant: String) {
      pages(filters: { slug: { eq: $slug }, tenant: { eq: $tenant } }) {
        data {
          id
          attributes {
            title
            content
            featured_image {
              data {
                attributes {
                  url
                }
              }
            }
            seo {
              title
              description
              keywords
            }
          }
        }
      }
    }
  `;

  return fetchGraphQL(query, { slug });
}

/**
 * Process GraphQL response to normalize data structure
 * @param response - GraphQL response
 * @returns Normalized data
 */
export function processGraphQLResponse(response: any): any {
  if (!response || !response.data) {
    return null;
  }

  // Get the first key in the data object
  const key = Object.keys(response.data)[0];

  if (!key) {
    return null;
  }

  const data = response.data[key];

  // Handle collection types
  if (data.data && Array.isArray(data.data)) {
    return data.data.map((item: any) => {
      return {
        id: item.id,
        ...item.attributes,
      };
    });
  }

  // Handle single types
  if (data.data) {
    return {
      id: data.data.id,
      ...data.data.attributes,
    };
  }

  return data;
}

export default {
  fetchGraphQL,
  getProductsWithRelations,
  getProductWithRelations,
  getCategoriesWithProducts,
  getBanners,
  getPageBySlug,
  processGraphQLResponse,
};
