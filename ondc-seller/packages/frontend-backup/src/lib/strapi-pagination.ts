/**
 * Pagination utilities for Strapi API client
 *
 * This module provides pagination functionality for the Strapi API client
 * to handle large collections efficiently.
 */

import { getCollection } from './strapi-enhanced';
import { getCachedCollection } from './strapi-cache';

// Default pagination settings
export const DEFAULT_PAGE_SIZE = 25;
export const DEFAULT_PAGE = 1;

/**
 * Interface for pagination parameters
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
}

/**
 * Interface for pagination metadata
 */
export interface PaginationMeta {
  page: number;
  pageSize: number;
  pageCount: number;
  total: number;
}

/**
 * Interface for paginated response
 */
export interface PaginatedResponse<T> {
  items: T[];
  pagination: PaginationMeta;
  error: any | null;
}

/**
 * Get a paginated collection from Strapi
 * @param collection - Collection name
 * @param pagination - Pagination parameters
 * @param params - Additional query parameters
 * @returns Promise with paginated data
 */
export async function getPaginatedCollection<T = any>(
  collection: string,
  pagination: PaginationParams = {},
  params = {}
): Promise<PaginatedResponse<T>> {
  const page = pagination.page || DEFAULT_PAGE;
  const pageSize = pagination.pageSize || DEFAULT_PAGE_SIZE;

  try {
    const queryParams = {
      ...params,
      'pagination[page]': page,
      'pagination[pageSize]': pageSize,
    };

    const result = await getCollection(collection, queryParams);

    if (result.error) {
      console.error(`Error fetching paginated collection ${collection}:`, result.error);
      return {
        items: [],
        pagination: {
          page,
          pageSize,
          pageCount: 0,
          total: 0,
        },
        error: result.error,
      };
    }

    return {
      items: result.items || [],
      pagination: result.pagination || {
        page,
        pageSize,
        pageCount: 0,
        total: 0,
      },
      error: null,
    };
  } catch (error) {
    console.error(`Error in getPaginatedCollection(${collection}):`, error);
    return {
      items: [],
      pagination: {
        page,
        pageSize,
        pageCount: 0,
        total: 0,
      },
      error: { message: error.message || 'Unknown error occurred' },
    };
  }
}

/**
 * Get a paginated collection from Strapi with caching
 * @param collection - Collection name
 * @param pagination - Pagination parameters
 * @param params - Additional query parameters
 * @param bypassCache - Whether to bypass the cache
 * @returns Promise with paginated data
 */
export async function getCachedPaginatedCollection<T = any>(
  collection: string,
  pagination: PaginationParams = {},
  params = {},
  bypassCache = false
): Promise<PaginatedResponse<T>> {
  const page = pagination.page || DEFAULT_PAGE;
  const pageSize = pagination.pageSize || DEFAULT_PAGE_SIZE;

  try {
    const queryParams = {
      ...params,
      'pagination[page]': page,
      'pagination[pageSize]': pageSize,
    };

    const result = await getCachedCollection(collection, queryParams, bypassCache);

    if (result.error) {
      console.error(`Error fetching cached paginated collection ${collection}:`, result.error);
      return {
        items: [],
        pagination: {
          page,
          pageSize,
          pageCount: 0,
          total: 0,
        },
        error: result.error,
      };
    }

    return {
      items: result.items || [],
      pagination: result.pagination || {
        page,
        pageSize,
        pageCount: 0,
        total: 0,
      },
      error: null,
    };
  } catch (error) {
    console.error(`Error in getCachedPaginatedCollection(${collection}):`, error);
    return {
      items: [],
      pagination: {
        page,
        pageSize,
        pageCount: 0,
        total: 0,
      },
      error: { message: error.message || 'Unknown error occurred' },
    };
  }
}

/**
 * Generate pagination controls for UI
 * @param pagination - Pagination metadata
 * @param maxPages - Maximum number of page links to show
 * @returns Array of page numbers to display
 */
export function generatePaginationControls(pagination: PaginationMeta, maxPages = 5): number[] {
  const { page, pageCount } = pagination;

  if (pageCount <= maxPages) {
    // If we have fewer pages than the max, show all pages
    return Array.from({ length: pageCount }, (_, i) => i + 1);
  }

  // Calculate the range of pages to show
  const halfMax = Math.floor(maxPages / 2);
  let startPage = Math.max(page - halfMax, 1);
  let endPage = Math.min(startPage + maxPages - 1, pageCount);

  // Adjust if we're near the end
  if (endPage === pageCount) {
    startPage = Math.max(endPage - maxPages + 1, 1);
  }

  return Array.from({ length: endPage - startPage + 1 }, (_, i) => startPage + i);
}

/**
 * Get paginated products from Strapi
 * @param pagination - Pagination parameters
 * @param params - Additional query parameters
 * @returns Promise with paginated product data
 */
export async function getPaginatedProducts(
  pagination: PaginationParams = {},
  params = {}
): Promise<PaginatedResponse<any>> {
  return getPaginatedCollection('products', pagination, {
    populate: '*',
    ...params,
  });
}

/**
 * Get paginated products from Strapi with caching
 * @param pagination - Pagination parameters
 * @param params - Additional query parameters
 * @param bypassCache - Whether to bypass the cache
 * @returns Promise with paginated product data
 */
export async function getCachedPaginatedProducts(
  pagination: PaginationParams = {},
  params = {},
  bypassCache = false
): Promise<PaginatedResponse<any>> {
  return getCachedPaginatedCollection(
    'products',
    pagination,
    {
      populate: '*',
      ...params,
    },
    bypassCache
  );
}

export default {
  getPaginatedCollection,
  getCachedPaginatedCollection,
  generatePaginationControls,
  getPaginatedProducts,
  getCachedPaginatedProducts,
  DEFAULT_PAGE_SIZE,
  DEFAULT_PAGE,
};
