/**
 * Strapi API Client for ONDC Seller Platform
 *
 * This module exports all Strapi API features in a single package.
 */

// Import all Strapi API modules
import * as StrapiEnhanced from './strapi-enhanced';
import * as StrapiCache from './strapi-cache';
import * as StrapiPagination from './strapi-pagination';
import * as StrapiContentManagement from './strapi-content-management';
import * as StrapiMedia from './strapi-media';
import * as StrapiAuth from './strapi-auth';
import * as StrapiGraphQL from './strapi-graphql';

// Configuration
const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const STRAPI_API_TOKEN =
  process.env.STRAPI_API_TOKEN ||
  'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// Export all modules
export const enhanced = StrapiEnhanced;
export const cache = StrapiCache;
export const pagination = StrapiPagination;
export const contentManagement = StrapiContentManagement;
export const media = StrapiMedia;
export const auth = StrapiAuth;
export const graphql = StrapiGraphQL;

// Export configuration
export const config = {
  STRAPI_URL,
  STRAPI_API_TOKEN,
};

// Export common functions
export const {
  fetchAPI,
  getCollection,
  getItem,
  getBanners,
  getFallbackBanners,
  processStrapiItem,
  processStrapiError,
} = StrapiEnhanced;

export const {
  getCachedCollection,
  getCachedItem,
  getCachedBanners,
  clearCache,
  clearCollectionCache,
} = StrapiCache;

export const {
  getPaginatedCollection,
  getCachedPaginatedCollection,
  generatePaginationControls,
  getPaginatedProducts,
  getCachedPaginatedProducts,
} = StrapiPagination;

export const {
  createItem,
  updateItem,
  deleteItem,
  createBanner,
  updateBanner,
  deleteBanner,
  createPage,
  updatePage,
  deletePage,
  createProductCategory,
  updateProductCategory,
  deleteProductCategory,
} = StrapiContentManagement;

export const { uploadMedia, uploadMultipleMedia, uploadMediaFromUrl, getMediaInfo, deleteMedia } =
  StrapiMedia;

export const {
  isAuthenticated,
  getAuthToken,
  getUser,
  getAuthHeaders,
  login,
  register,
  logout,
  getCurrentUser,
} = StrapiAuth;

export const {
  fetchGraphQL,
  getProductsWithRelations,
  getProductWithRelations,
  getCategoriesWithProducts,
  getPageBySlug,
  processGraphQLResponse,
} = StrapiGraphQL;

// Default export
export default {
  // Configuration
  config,

  // Modules
  enhanced,
  cache,
  pagination,
  contentManagement,
  media,
  auth,
  graphql,

  // Common functions
  fetchAPI,
  getCollection,
  getItem,
  getBanners,
  getFallbackBanners,
  processStrapiItem,
  processStrapiError,

  getCachedCollection,
  getCachedItem,
  getCachedBanners,
  clearCache,
  clearCollectionCache,

  getPaginatedCollection,
  getCachedPaginatedCollection,
  generatePaginationControls,
  getPaginatedProducts,
  getCachedPaginatedProducts,

  createItem,
  updateItem,
  deleteItem,
  createBanner,
  updateBanner,
  deleteBanner,
  createPage,
  updatePage,
  deletePage,
  createProductCategory,
  updateProductCategory,
  deleteProductCategory,

  uploadMedia,
  uploadMultipleMedia,
  uploadMediaFromUrl,
  getMediaInfo,
  deleteMedia,

  isAuthenticated,
  getAuthToken,
  getUser,
  getAuthHeaders,
  login,
  register,
  logout,
  getCurrentUser,

  fetchGraphQL,
  getProductsWithRelations,
  getProductWithRelations,
  getCategoriesWithProducts,
  getPageBySlug,
  processGraphQLResponse,
};
