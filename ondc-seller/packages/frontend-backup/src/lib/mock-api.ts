/**
 * MSW (Mock Service Worker) handlers for API mocking
 * This provides realistic network-level mocking for development and testing
 */

import { http, HttpResponse } from 'msw';
import {
  MOCK_PRODUCTS,
  MOCK_COLLECTIONS,
  getFilteredProducts,
  createMockCart,
  createMockCartItem,
} from './msw-data';

// MSW handlers
export const handlers = [
  // Health check endpoint
  http.get('http://localhost:9000/health', () => {
    return HttpResponse.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'MSW Mock Server',
    });
  }),

  // Products endpoint
  http.get('http://localhost:9000/store/products', ({ request }) => {
    const url = new URL(request.url);
    const tenantId = request.headers.get('x-tenant-id') || 'default';
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const offset = parseInt(url.searchParams.get('offset') || '0');

    // Get category and subcategory filters
    const categoryId = url.searchParams.get('category_id');
    const subcategoryId = url.searchParams.get('subcategory_id');
    const parentCategoryId = url.searchParams.get('parent_category_id');

    console.log('[MSW] Products request:', {
      categoryId,
      subcategoryId,
      parentCategoryId,
      tenantId,
      limit,
      offset,
    });

    // Use shared data structure for consistency
    const result = getFilteredProducts(tenantId, categoryId || '', subcategoryId, limit, offset);

    console.log('[MSW] Returning products:', {
      count: result.count,
      total: result.total,
      products: result.products.map(p => ({ id: p.id, title: p.title })),
    });

    return HttpResponse.json(result);
  }),

  // Single product endpoint
  http.get('http://localhost:9000/store/products/:id', ({ params, request }) => {
    const { id } = params;
    const tenantId = request.headers.get('x-tenant-id') || 'default';

    const product = MOCK_PRODUCTS.find(p => p.id === id && p.tenant_id === tenantId);

    if (!product) {
      return HttpResponse.json(
        {
          message: `Product not found for tenant: ${tenantId}`,
          code: 'not_found',
        },
        { status: 404 }
      );
    }

    return HttpResponse.json({ product });
  }),

  // Collections endpoint
  http.get('http://localhost:9000/store/collections', ({ request }) => {
    const tenantId = request.headers.get('x-tenant-id') || 'default';

    // Use shared collections data
    const collections = MOCK_COLLECTIONS.map(col => ({
      ...col,
      tenant_id: tenantId,
    }));

    return HttpResponse.json({
      collections,
      count: collections.length,
      offset: 0,
      limit: 10,
    });
  }),

  // Auth endpoint
  http.post('http://localhost:9000/store/auth', async ({ request }) => {
    const body = (await request.json()) as any;

    return HttpResponse.json({
      user: {
        id: 'usr_01',
        email: body.email || '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
      },
      customer: {
        id: 'cus_01',
        email: body.email || '<EMAIL>',
      },
      token: 'mock-api-token',
      refresh_token: 'mock-refresh-token',
    });
  }),

  // Cart endpoints
  http.get('http://localhost:9000/store/carts/:id', ({ params, request }) => {
    const { id } = params;
    const tenantId = request.headers.get('x-tenant-id') || 'default';

    // Use shared cart data structure
    return HttpResponse.json({
      cart: createMockCart(id as string, tenantId),
    });
  }),

  http.post('http://localhost:9000/store/carts', ({ request }) => {
    const tenantId = request.headers.get('x-tenant-id') || 'default';

    // Create new cart using shared data structure
    const cartId = `cart_${Date.now()}`;
    return HttpResponse.json({
      cart: createMockCart(cartId, tenantId),
    });
  }),

  http.post('http://localhost:9000/store/carts/:id/line-items', async ({ params, request }) => {
    const { id } = params;
    const body = (await request.json()) as any;
    const tenantId = request.headers.get('x-tenant-id') || 'default';

    try {
      // Use shared cart item creation function
      const cartItem = createMockCartItem(id as string, body.variant_id, body.quantity || 1);

      // Create cart with the new item using shared data structure
      const cart = createMockCart(id as string, tenantId);
      cart.items = [cartItem];
      cart.total = cartItem.total;
      cart.subtotal = cartItem.total;

      return HttpResponse.json({ cart });
    } catch (error) {
      return HttpResponse.json({ message: 'Product variant not found' }, { status: 404 });
    }
  }),
];

// Legacy compatibility export for existing code
export const getMockProducts = (categoryId: string, subcategoryId?: string) => {
  // Filter products based on category/subcategory (in a real implementation)
  // Here we're just returning all mock products
  return {
    products: MOCK_PRODUCTS,
    count: MOCK_PRODUCTS.length,
  };
};

// Legacy mock API implementation for backward compatibility
export const mockMedusaApi = {
  get: async (url: string, config?: any) => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Handle different API endpoints
    if (url === '/store/products') {
      const categoryId = config?.params?.category_id || '';
      const subcategoryId = config?.params?.subcategory_id || '';
      const parentCategoryId = config?.params?.parent_category_id || '';

      console.log('[Legacy Mock API] Products request:', {
        categoryId,
        subcategoryId,
        parentCategoryId,
        params: config?.params,
      });

      // Generate category-specific products
      const targetCategory = subcategoryId || categoryId;
      if (targetCategory) {
        const mockProducts = Array.from({ length: 6 }, (_, i) => ({
          id: `legacy_${targetCategory}_${i + 1}`,
          title: `${targetCategory.charAt(0).toUpperCase() + targetCategory.slice(1)} Product ${i + 1}`,
          description: `This is a legacy mock ${targetCategory} product for testing.`,
          thumbnail: `https://picsum.photos/400/300?random=legacy${targetCategory}${i + 1}`,
          handle: `${targetCategory}-product-${i + 1}`,
          status: 'published',
          variants: [
            {
              id: `legacy_variant_${i + 1}`,
              title: 'Default Variant',
              prices: [
                {
                  amount: 1000 * (i + 1) + Math.floor(Math.random() * 30000),
                  currency_code: 'inr',
                },
              ],
              inventory_quantity: 10 + Math.floor(Math.random() * 15),
            },
          ],
          tenant_id: 'default',
        }));

        console.log('[Legacy Mock API] Generated products:', mockProducts.length);

        return {
          data: {
            products: mockProducts,
            count: mockProducts.length,
          },
        };
      }

      return {
        data: getMockProducts(categoryId),
      };
    }

    // Default fallback
    return {
      data: {
        products: [],
        count: 0,
      },
    };
  },
  post: async (url: string, data?: any, config?: any) => {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 500));

    // Mock implementation for POST requests
    return {
      data: {
        success: true,
      },
    };
  },
};
