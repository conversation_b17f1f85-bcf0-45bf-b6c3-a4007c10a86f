import axios from 'axios';

const ONDC_API_URL = process.env.NEXT_PUBLIC_ONDC_API_URL || 'http://localhost:5000';

const ondcApi = axios.create({
  baseURL: ONDC_API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Add request interceptor for authentication
ondcApi.interceptors.request.use(
  (config) => {
    // Get token from localStorage if available
    const token = typeof window !== 'undefined' ? localStorage.getItem('ondc_token') : null;
    
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // In development mode, use demo credentials
    if (process.env.NODE_ENV === 'development' && !token) {
      const username = process.env.DEV_USERNAME || 'demo';
      const password = process.env.DEV_PASSWORD || 'demo';
      const demoAuth = btoa(`${username}:${password}`);
      config.headers.Authorization = `Basic ${demoAuth}`;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Authentication API
export const authApi = {
  login: (username: string, password: string) => 
    ondcApi.post('/auth/login', { username, password }),
  register: (userData: any) => 
    ondcApi.post('/auth/register', userData),
  logout: () => 
    ondcApi.post('/auth/logout'),
  getProfile: () => 
    ondcApi.get('/auth/profile'),
};

// Catalog API
export const catalogApi = {
  getProducts: (params?: any) => 
    ondcApi.get('/catalog/products', { params }),
  getProduct: (id: string) => 
    ondcApi.get(`/catalog/products/${id}`),
  createProduct: (productData: any) => 
    ondcApi.post('/catalog/products', productData),
  updateProduct: (id: string, productData: any) => 
    ondcApi.put(`/catalog/products/${id}`, productData),
  deleteProduct: (id: string) => 
    ondcApi.delete(`/catalog/products/${id}`),
};

// Order API
export const orderApi = {
  getOrders: (params?: any) => 
    ondcApi.get('/orders', { params }),
  getOrder: (id: string) => 
    ondcApi.get(`/orders/${id}`),
  updateOrderStatus: (id: string, status: string) => 
    ondcApi.put(`/orders/${id}/status`, { status }),
};

// ONDC Network API
export const ondcNetworkApi = {
  search: (searchParams: any) => 
    ondcApi.post('/ondc/search', searchParams),
  select: (selectParams: any) => 
    ondcApi.post('/ondc/select', selectParams),
  init: (initParams: any) => 
    ondcApi.post('/ondc/init', initParams),
  confirm: (confirmParams: any) => 
    ondcApi.post('/ondc/confirm', confirmParams),
  status: (statusParams: any) => 
    ondcApi.post('/ondc/status', statusParams),
  cancel: (cancelParams: any) => 
    ondcApi.post('/ondc/cancel', cancelParams),
};

export default ondcApi;
