import axios from 'axios';
import { catalogApi } from './api';

const MEDUSA_URL = process.env.NEXT_PUBLIC_MEDUSA_URL || 'http://localhost:9000';

const medusaApi = axios.create({
  baseURL: MEDUSA_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

/**
 * Sync products from Medusa to ONDC
 * This function fetches products from Medusa and syncs them to ONDC
 */
export const syncProductsToONDC = async () => {
  try {
    // Fetch products from Medusa
    const medusaResponse = await medusaApi.get('/store/products');
    const medusaProducts = medusaResponse.data.products;
    
    // Transform Medusa products to ONDC format
    const ondcProducts = medusaProducts.map((product: any) => {
      // Get the default variant and price
      const defaultVariant = product.variants[0];
      const defaultPrice = defaultVariant?.prices[0];
      
      return {
        id: product.id,
        name: product.title,
        description: product.description,
        price: defaultPrice ? defaultPrice.amount / 100 : 0, // Convert from cents to whole currency
        currency: defaultPrice ? defaultPrice.currency_code.toUpperCase() : 'INR',
        inventory: defaultVariant ? defaultVariant.inventory_quantity : 0,
        images: product.images.map((image: any) => image.url),
        categories: product.categories ? product.categories.map((category: any) => category.name) : [],
        variants: product.variants.map((variant: any) => ({
          id: variant.id,
          name: variant.title,
          price: variant.prices[0] ? variant.prices[0].amount / 100 : 0,
          inventory: variant.inventory_quantity,
          options: variant.options.map((option: any) => ({
            name: option.option.title,
            value: option.value,
          })),
        })),
      };
    });
    
    // Sync each product to ONDC
    for (const product of ondcProducts) {
      try {
        // Check if product exists in ONDC
        const ondcResponse = await catalogApi.getProduct(product.id);
        
        if (ondcResponse.status === 200) {
          // Update existing product
          await catalogApi.updateProduct(product.id, product);
        }
      } catch (error) {
        // Product doesn't exist, create it
        await catalogApi.createProduct(product);
      }
    }
    
    return { success: true, message: 'Products synced successfully', count: ondcProducts.length };
  } catch (error) {
    console.error('Error syncing products to ONDC:', error);
    return { success: false, message: 'Failed to sync products', error };
  }
};

/**
 * Sync orders from ONDC to Medusa
 * This function fetches orders from ONDC and syncs them to Medusa
 */
export const syncOrdersToMedusa = async () => {
  try {
    // Fetch orders from ONDC
    const ondcResponse = await catalogApi.getOrders();
    const ondcOrders = ondcResponse.data;
    
    // Transform ONDC orders to Medusa format and create them in Medusa
    for (const order of ondcOrders) {
      try {
        // Create a cart in Medusa
        const cartResponse = await medusaApi.post('/store/carts');
        const cartId = cartResponse.data.cart.id;
        
        // Add items to cart
        for (const item of order.items) {
          await medusaApi.post(`/store/carts/${cartId}/line-items`, {
            variant_id: item.variant_id,
            quantity: item.quantity,
          });
        }
        
        // Add shipping and billing address
        if (order.shipping_address) {
          await medusaApi.post(`/store/carts/${cartId}/shipping-address`, {
            address: order.shipping_address,
          });
        }
        
        if (order.billing_address) {
          await medusaApi.post(`/store/carts/${cartId}/billing-address`, {
            address: order.billing_address,
          });
        }
        
        // Complete cart and create order
        await medusaApi.post(`/store/carts/${cartId}/complete`);
      } catch (error) {
        console.error(`Error syncing order ${order.id} to Medusa:`, error);
      }
    }
    
    return { success: true, message: 'Orders synced successfully', count: ondcOrders.length };
  } catch (error) {
    console.error('Error syncing orders to Medusa:', error);
    return { success: false, message: 'Failed to sync orders', error };
  }
};

export default {
  syncProductsToONDC,
  syncOrdersToMedusa,
};
