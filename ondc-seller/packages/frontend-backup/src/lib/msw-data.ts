/**
 * Shared MSW Data Structure
 * This file contains the data structure used by MSW handlers and local API routes
 * to ensure consistency across the application
 */

// Mock product data - shared between MSW and local API routes
export const MOCK_PRODUCTS = [
  {
    id: 'prod_01',
    title: 'Smartphone X',
    description: 'Latest smartphone with advanced features',
    thumbnail: 'https://picsum.photos/300/300?random=1',
    handle: 'smartphone-x',
    status: 'published',
    variants: [
      {
        id: 'var_01',
        title: 'Default',
        prices: [{ amount: 49999, currency_code: 'inr' }],
        inventory_quantity: 10,
      },
    ],
    images: [
      { id: 'img_01', url: 'https://picsum.photos/600/600?random=1' },
      { id: 'img_02', url: 'https://picsum.photos/600/600?random=2' },
    ],
    tenant_id: 'default',
  },
  {
    id: 'prod_02',
    title: 'Laptop Pro',
    description: 'High-performance laptop for professionals',
    thumbnail: 'https://picsum.photos/300/300?random=3',
    handle: 'laptop-pro',
    status: 'published',
    variants: [
      {
        id: 'var_02',
        title: 'Default',
        prices: [{ amount: 89999, currency_code: 'inr' }],
        inventory_quantity: 5,
      },
    ],
    images: [
      { id: 'img_03', url: 'https://picsum.photos/600/600?random=3' },
      { id: 'img_04', url: 'https://picsum.photos/600/600?random=4' },
    ],
    tenant_id: 'default',
  },
  {
    id: 'prod_03',
    title: 'Wireless Earbuds',
    description: 'Premium wireless earbuds with noise cancellation',
    thumbnail: 'https://picsum.photos/300/300?random=5',
    handle: 'wireless-earbuds',
    status: 'published',
    variants: [
      {
        id: 'var_03',
        title: 'Default',
        prices: [{ amount: 9999, currency_code: 'inr' }],
        inventory_quantity: 20,
      },
    ],
    images: [
      { id: 'img_05', url: 'https://picsum.photos/600/600?random=5' },
      { id: 'img_06', url: 'https://picsum.photos/600/600?random=6' },
    ],
    tenant_id: 'default',
  },
  {
    id: 'prod_04',
    title: 'Smart Watch',
    description: 'Advanced fitness tracking and smart features',
    thumbnail: 'https://picsum.photos/300/300?random=7',
    handle: 'smart-watch',
    status: 'published',
    variants: [
      {
        id: 'var_04',
        title: 'Default',
        prices: [{ amount: 25999, currency_code: 'inr' }],
        inventory_quantity: 15,
      },
    ],
    images: [
      { id: 'img_07', url: 'https://picsum.photos/600/600?random=7' },
      { id: 'img_08', url: 'https://picsum.photos/600/600?random=8' },
    ],
    tenant_id: 'default',
  },
];

/**
 * Generate category-specific products using MSW data structure
 * This ensures consistency between MSW handlers and local API routes
 */
export function generateCategoryProducts(
  categoryId: string, 
  subcategoryId?: string, 
  limit: number = 8,
  tenantId: string = 'default'
) {
  const targetCategory = subcategoryId || categoryId;
  
  console.log('[MSW Data] Generating products for category:', targetCategory);

  return Array.from({ length: limit }, (_, i) => ({
    id: `mock_${targetCategory}_${i + 1}`,
    title: `${targetCategory.charAt(0).toUpperCase() + targetCategory.slice(1)} Product ${i + 1}`,
    description: `This is a mock ${targetCategory} product for testing. High quality and affordable.`,
    thumbnail: `https://picsum.photos/400/300?random=${targetCategory}${i + 1}`,
    handle: `${targetCategory}-product-${i + 1}`,
    status: 'published',
    variants: [
      {
        id: `mock_variant_${targetCategory}_${i + 1}`,
        title: 'Default Variant',
        prices: [
          { amount: 1000 * (i + 1) + Math.floor(Math.random() * 50000), currency_code: 'inr' },
        ],
        inventory_quantity: 10 + Math.floor(Math.random() * 20),
      },
    ],
    images: [
      {
        id: `img_${targetCategory}_${i + 1}_1`,
        url: `https://picsum.photos/600/600?random=${targetCategory}${i + 1}`,
      },
      {
        id: `img_${targetCategory}_${i + 1}_2`,
        url: `https://picsum.photos/600/600?random=${targetCategory}${i + 1}0`,
      },
    ],
    tenant_id: tenantId,
  }));
}

/**
 * Get products with filtering and pagination
 * Used by both MSW handlers and local API routes
 */
export function getFilteredProducts(
  tenantId: string = 'default',
  categoryId?: string,
  subcategoryId?: string,
  limit: number = 10,
  offset: number = 0
) {
  // Filter products by tenant
  let products = MOCK_PRODUCTS.filter(product => product.tenant_id === tenantId);

  // Generate category-specific mock products if we have category filters
  if (categoryId || subcategoryId) {
    products = generateCategoryProducts(categoryId || '', subcategoryId, 8, tenantId);
  }

  // Apply pagination
  const paginatedProducts = products.slice(offset, offset + limit);

  return {
    products: paginatedProducts,
    count: paginatedProducts.length,
    total: products.length,
    offset,
    limit,
  };
}

/**
 * Mock collections data
 */
export const MOCK_COLLECTIONS = [
  {
    id: 'col_01',
    title: 'Electronics',
    handle: 'electronics',
    tenant_id: 'default',
  },
  {
    id: 'col_02',
    title: 'Accessories',
    handle: 'accessories',
    tenant_id: 'default',
  },
];

/**
 * Mock cart data structure
 */
export function createMockCart(cartId: string, tenantId: string = 'default') {
  return {
    id: cartId,
    email: null,
    billing_address: null,
    shipping_address: null,
    items: [],
    region: {
      id: 'reg_01',
      name: 'India',
      currency_code: 'inr',
    },
    payment_sessions: [],
    payment_session: null,
    total: 0,
    subtotal: 0,
    tax_total: 0,
    discount_total: 0,
    shipping_total: 0,
    gift_card_total: 0,
    gift_card_tax_total: 0,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    tenant_id: tenantId,
  };
}

/**
 * Create mock cart item
 */
export function createMockCartItem(
  cartId: string,
  variantId: string,
  quantity: number = 1
) {
  // Find the product variant
  const product = MOCK_PRODUCTS.find(p => p.variants.some(v => v.id === variantId));
  
  if (!product) {
    throw new Error('Product variant not found');
  }

  const variant = product.variants.find(v => v.id === variantId);
  
  return {
    id: `item_${Date.now()}`,
    cart_id: cartId,
    title: product.title,
    description: product.description,
    thumbnail: product.thumbnail,
    variant: variant,
    variant_id: variantId,
    quantity: quantity,
    unit_price: variant?.prices[0]?.amount || 0,
    total: (variant?.prices[0]?.amount || 0) * quantity,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  };
}
