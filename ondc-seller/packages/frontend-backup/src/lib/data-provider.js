/**
 * Data Provider for ONDC Seller Platform
 *
 * This module provides functions to fetch and combine data from both
 * Strapi CMS and Medusa Commerce backend.
 */

import api from '../../lib/api';

/**
 * Get homepage data including static banners and featured products from Medusa
 * @param {string} tenantId - Optional tenant ID
 * @returns {Promise<Object>} Homepage data
 */
export async function getHomepageData(tenantId = null) {
  console.log('=== Getting homepage data with tenantId:', tenantId, '===');
  console.log('NOTE: Strapi integration is temporarily disabled. Using static banner data.');

  // Set tenant ID if provided
  if (tenantId) {
    api.setTenantId(tenantId);
    console.log('Tenant ID set in API client');
  }

  try {
    console.log('Fetching featured products from Medusa...');

    // Static banner data (temporary replacement for Strapi data)
    const staticBanners = [
      {
        id: 1,
        title: 'Welcome to ONDC Seller Platform',
        subtitle: 'Connect with millions of buyers across India',
        image: {
          url: '/images/placeholder.png',
        },
        cta_text: 'Browse Products',
        cta_link: '/products',
      },
      {
        id: 2,
        title: 'Discover Quality Products',
        subtitle: 'From trusted sellers across the country',
        image: {
          url: '/images/placeholder.png',
        },
        cta_text: 'Shop Now',
        cta_link: '/products',
      },
    ];

    // Only fetch products from Medusa
    let featuredProducts = [];
    try {
      const productsResult = await api.medusa.getProducts({ is_featured: true });

      if (productsResult && productsResult.items) {
        featuredProducts = productsResult.items;
        console.log('Successfully fetched featured products. Count:', featuredProducts.length);

        // Log first product for debugging
        if (featuredProducts.length > 0) {
          console.log('First product:', {
            id: featuredProducts[0].id,
            title: featuredProducts[0].title,
            hasThumbnail: !!featuredProducts[0].thumbnail,
          });
        }
      }
    } catch (productsError) {
      console.error('Error fetching featured products:', productsError);
    }

    // Return data with static banners and products from Medusa
    const result = {
      banners: staticBanners,
      featuredProducts,
      error:
        featuredProducts.length === 0 ? 'Could not load products. Please try again later.' : null,
    };

    console.log('Returning homepage data:', {
      bannersCount: staticBanners.length,
      productsCount: featuredProducts.length,
      hasErrors: featuredProducts.length === 0,
    });

    return result;
  } catch (error) {
    console.error('Error fetching homepage data:', error);
    return {
      banners: [],
      featuredProducts: [],
      error: 'Failed to load homepage data: ' + error.message,
    };
  }
}

/**
 * Get product details from Medusa
 * @param {string} productId - Product ID
 * @param {string} tenantId - Optional tenant ID
 * @returns {Promise<Object>} Product data
 */
export async function getProductDetails(productId, tenantId = null) {
  console.log(
    'NOTE: Strapi integration is temporarily disabled. Only fetching product data from Medusa.'
  );

  // Set tenant ID if provided
  if (tenantId) {
    api.setTenantId(tenantId);
  }

  try {
    // Fetch product from Medusa
    const medusaProduct = await api.medusa.getProduct(productId);

    if (!medusaProduct) {
      throw new Error('Product not found');
    }

    // Return only Medusa data
    return {
      ...medusaProduct,
      content: null, // No Strapi content
      error: null,
    };
  } catch (error) {
    console.error(`Error fetching product ${productId}:`, error);
    return {
      error: error.message || 'Failed to load product details',
    };
  }
}

/**
 * Get category products from Medusa
 * @param {string} categoryId - Category ID
 * @param {string} tenantId - Optional tenant ID
 * @returns {Promise<Object>} Category data with products
 */
export async function getCategoryProducts(categoryId, tenantId = null) {
  console.log(
    'NOTE: Strapi integration is temporarily disabled. Only fetching category data from Medusa.'
  );

  // Set tenant ID if provided
  if (tenantId) {
    api.setTenantId(tenantId);
  }

  try {
    // Fetch category products from Medusa
    const medusaProducts = await api.medusa.getProducts({ collection_id: categoryId });

    return {
      products: medusaProducts?.items || [],
      category: null, // No Strapi category content
      pagination: medusaProducts?.pagination || null,
      error: null,
    };
  } catch (error) {
    console.error(`Error fetching category ${categoryId}:`, error);
    return {
      products: [],
      category: null,
      pagination: null,
      error: error.message || 'Failed to load category products',
    };
  }
}

export default {
  getHomepageData,
  getProductDetails,
  getCategoryProducts,
};
