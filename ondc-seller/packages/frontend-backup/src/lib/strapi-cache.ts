/**
 * Caching layer for Strapi API client
 *
 * This module provides caching functionality for the Strapi API client
 * to improve performance and reduce API calls.
 */

import { getCollection, getItem } from './strapi-enhanced';

// Cache configuration
export const CACHE_TTL = 5 * 60 * 1000; // 5 minutes in milliseconds
export const CACHE_ENABLED = process.env.NEXT_PUBLIC_STRAPI_CACHE_ENABLED !== 'false';

// Cache storage
interface CacheEntry {
  data: any;
  timestamp: number;
}

interface CacheStore {
  [key: string]: CacheEntry;
}

// In-memory cache
const cache: CacheStore = {};

/**
 * Generate a cache key from collection name and parameters
 * @param collection - Collection name
 * @param params - Query parameters
 * @returns Cache key
 */
export function generateCacheKey(collection: string, params: any = {}): string {
  return `${collection}:${JSON.stringify(params)}`;
}

/**
 * Check if a cache entry is valid (not expired)
 * @param entry - Cache entry
 * @returns Whether the entry is valid
 */
export function isCacheValid(entry: CacheEntry): boolean {
  return Date.now() - entry.timestamp < CACHE_TTL;
}

/**
 * Get data from cache if available and valid
 * @param key - Cache key
 * @returns Cached data or null if not available
 */
export function getFromCache(key: string): any | null {
  if (!CACHE_ENABLED) return null;

  const entry = cache[key];

  if (entry && isCacheValid(entry)) {
    console.log(`Using cached data for ${key}`);
    return entry.data;
  }

  return null;
}

/**
 * Store data in cache
 * @param key - Cache key
 * @param data - Data to store
 */
export function storeInCache(key: string, data: any): void {
  if (!CACHE_ENABLED) return;

  cache[key] = {
    data,
    timestamp: Date.now(),
  };

  console.log(`Stored data in cache for ${key}`);
}

/**
 * Clear the entire cache
 */
export function clearCache(): void {
  Object.keys(cache).forEach(key => {
    delete cache[key];
  });

  console.log('Cache cleared');
}

/**
 * Clear cache entries for a specific collection
 * @param collection - Collection name
 */
export function clearCollectionCache(collection: string): void {
  Object.keys(cache).forEach(key => {
    if (key.startsWith(`${collection}:`)) {
      delete cache[key];
    }
  });

  console.log(`Cache cleared for collection: ${collection}`);
}

/**
 * Get all items from a collection with caching
 * @param collection - Collection name
 * @param params - Query parameters
 * @param bypassCache - Whether to bypass the cache
 * @returns Promise with the collection data
 */
export async function getCachedCollection(
  collection: string,
  params = {},
  bypassCache = false
): Promise<any> {
  const cacheKey = generateCacheKey(collection, params);

  // Try to get from cache first
  if (!bypassCache) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) return cachedData;
  }

  // Fetch fresh data
  const data = await getCollection(collection, params);

  // Store in cache if successful
  if (!data.error) {
    storeInCache(cacheKey, data);
  }

  return data;
}

/**
 * Get a single item from a collection with caching
 * @param collection - Collection name
 * @param id - Item ID
 * @param params - Query parameters
 * @param bypassCache - Whether to bypass the cache
 * @returns Promise with the item data
 */
export async function getCachedItem(
  collection: string,
  id: string | number,
  params = {},
  bypassCache = false
): Promise<any> {
  const cacheKey = generateCacheKey(`${collection}/${id}`, params);

  // Try to get from cache first
  if (!bypassCache) {
    const cachedData = getFromCache(cacheKey);
    if (cachedData) return cachedData;
  }

  // Fetch fresh data
  const data = await getItem(collection, id, params);

  // Store in cache if successful
  if (!data.error) {
    storeInCache(cacheKey, data);
  }

  return data;
}

/**
 * Get all banners with caching
 * @param params - Query parameters
 * @param bypassCache - Whether to bypass the cache
 * @returns Promise with the banners data
 */
export async function getCachedBanners(params = {}, bypassCache = false): Promise<any> {
  return getCachedCollection(
    'banners',
    {
      'filters[active][$eq]': true,
      populate: 'image',
      ...params,
    },
    bypassCache
  );
}

export default {
  getCachedCollection,
  getCachedItem,
  getCachedBanners,
  clearCache,
  clearCollectionCache,
  CACHE_TTL,
};
