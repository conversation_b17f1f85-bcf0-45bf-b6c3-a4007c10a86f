'use client'

import Medusa from "@medusajs/medusa-js"
import { getCurrentTenantId } from "./tenant"

// Initialize the Medusa client
const MEDUSA_BACKEND_URL = process.env.NEXT_PUBLIC_MEDUSA_BACKEND_URL || 'http://localhost:9001'
const PUBLISHABLE_API_KEY = 'pk_test_123456'

let medusaClient: Medusa | null = null

/**
 * Get the Medusa client instance
 * @returns Medusa client instance
 */
export const getMedusaClient = (): Medusa => {
  if (!medusaClient) {
    medusaClient = new Medusa({
      baseUrl: MEDUSA_BACKEND_URL,
      maxRetries: 3,
      publishableApiKey: PUBLISHABLE_API_KEY,
    })

    // Add tenant ID to all requests
    medusaClient.client.hooks.beforeRequest.push((options) => {
      const tenantId = getCurrentTenantId()
      options.headers = options.headers || {}
      options.headers['x-tenant-id'] = tenantId
      console.log('Adding tenant ID to request:', tenantId)
      return options
    })
  }

  return medusaClient
}

/**
 * Reset the Medusa client instance
 * This is useful when the tenant ID changes
 */
export const resetMedusaClient = (): void => {
  medusaClient = null
}

export default getMedusaClient
