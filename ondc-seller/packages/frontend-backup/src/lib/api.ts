import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  ProductQueryParams,
  ProductResponse,
  OrderCreateData,
  OrderResponse,
  InventoryUpdateData,
  InventoryLevelResponse,
  LoginData,
  AuthResponse,
} from '../types/api';
import { getCurrentTenantId } from './tenant';
import { handleApiError, retryWithBackoff } from './error';

// API URLs
const MEDUSA_URL = process.env.NEXT_PUBLIC_MEDUSA_URL || 'http://localhost:9000';
const API_URL = process.env.NEXT_PUBLIC_API_URL || MEDUSA_URL;

// Create axios instance for Medusa API
export const medusaApi = axios.create({
  baseURL: MEDUSA_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-publishable-api-key': process.env.NEXT_PUBLIC_MEDUSA_PUBLISHABLE_KEY || 'pk_test_123',
  },
  withCredentials: true,
  timeout: 10000, // 10 seconds
});

// Create axios instance for general API
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
  timeout: 10000, // 10 seconds
});

// Add request interceptor for authentication and multi-tenancy
const addAuthAndTenantHeaders = (config: AxiosRequestConfig) => {
  // Add tenant ID header to every request
  config.headers = config.headers || {};

  // Get tenant ID from cookie first (set by middleware)
  let tenantId = null;

  // Try to get from cookie (client-side only)
  if (typeof document !== 'undefined') {
    const cookies = document.cookie.split(';');
    const tenantCookie = cookies.find(cookie => cookie.trim().startsWith('x-tenant-id='));

    if (tenantCookie) {
      tenantId = tenantCookie.split('=')[1].trim();
    }
  }

  // If not found in cookie, use the tenant context
  if (!tenantId) {
    tenantId = getCurrentTenantId();
  }

  // Set the tenant ID header
  config.headers['x-tenant-id'] = tenantId;

  // Get token from localStorage if available
  const token = typeof window !== 'undefined' ? localStorage.getItem('medusa_token') : null;

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  // In development mode, use demo credentials
  if (process.env.NODE_ENV === 'development' && !token) {
    const username = process.env.DEV_USERNAME || 'demo';
    const password = process.env.DEV_PASSWORD || 'demo';
    const demoAuth = btoa(`${username}:${password}`);
    config.headers.Authorization = `Basic ${demoAuth}`;
  }

  return config;
};

// Add request interceptor to both API instances
medusaApi.interceptors.request.use(addAuthAndTenantHeaders, error => Promise.reject(error));

api.interceptors.request.use(addAuthAndTenantHeaders, error => Promise.reject(error));

// Add response interceptor for token refresh
const handleTokenRefresh = async (error: any) => {
  const originalRequest = error.config;

  // If the error is due to an expired token and we haven't already tried to refresh
  if (error.response?.status === 401 && !originalRequest._retry) {
    originalRequest._retry = true;

    try {
      // Attempt to refresh the token
      const refreshToken = localStorage.getItem('medusa_refresh_token');

      if (refreshToken) {
        const response = await axios.post(`${MEDUSA_URL}/store/auth/token`, {
          refresh_token: refreshToken,
        });

        const { access_token, refresh_token } = response.data;

        // Update tokens in localStorage
        localStorage.setItem('medusa_token', access_token);
        localStorage.setItem('medusa_refresh_token', refresh_token);

        // Update the authorization header
        originalRequest.headers.Authorization = `Bearer ${access_token}`;

        // Retry the original request
        return axios(originalRequest);
      }
    } catch (refreshError) {
      // If token refresh fails, clear tokens and redirect to login
      localStorage.removeItem('medusa_token');
      localStorage.removeItem('medusa_refresh_token');

      // If we're in a browser context, redirect to login
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }
  }

  return Promise.reject(error);
};

// Add response interceptor to both API instances
medusaApi.interceptors.response.use(response => response, handleTokenRefresh);

api.interceptors.response.use(response => response, handleTokenRefresh);

/**
 * Fetch products from the Medusa API
 * @param params Optional query parameters for filtering
 * @returns Promise with typed product response
 */
export const fetchProducts = async (params?: ProductQueryParams): Promise<ProductResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.get('/store/products', { params });
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error fetching products:', apiError);
      throw apiError;
    }
  });
};

/**
 * Create an order in the Medusa API
 * @param orderData Order data object with items, shipping address, etc.
 * @returns Promise with typed order response
 */
export const createOrder = async (orderData: OrderCreateData): Promise<OrderResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.post('/store/orders', orderData);
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error creating order:', apiError);
      throw apiError;
    }
  });
};

/**
 * Update inventory in the Medusa API
 * @param inventoryId Inventory ID to update
 * @param updateData Inventory update data
 * @returns Promise with typed inventory response
 */
export const updateInventory = async (
  inventoryId: string,
  updateData: InventoryUpdateData
): Promise<InventoryLevelResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.post(`/admin/inventories/${inventoryId}`, updateData);
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error updating inventory:', apiError);
      throw apiError;
    }
  });
};

// Products API
export const productsApi = {
  getAll: (params?: ProductQueryParams) => fetchProducts(params),
  getById: (id: string) => medusaApi.get(`/store/products/${id}`),
  getFeatured: () => api.get('/products/featured'),
  create: (data: any) => medusaApi.post('/admin/products', data),
  update: (id: string, data: any) => medusaApi.post(`/admin/products/${id}`, data),
  delete: (id: string) => medusaApi.delete(`/admin/products/${id}`),
};

// Orders API
export const ordersApi = {
  getAll: () => medusaApi.get('/admin/orders'),
  getById: (id: string) => medusaApi.get(`/admin/orders/${id}`),
  create: (data: OrderCreateData) => createOrder(data),
  update: (id: string, data: any) => medusaApi.post(`/admin/orders/${id}`, data),
  fulfill: (id: string) => medusaApi.post(`/admin/orders/${id}/fulfillment`),
  cancel: (id: string) => medusaApi.post(`/admin/orders/${id}/cancel`),
};

// Inventory API
export const inventoryApi = {
  update: (id: string, data: InventoryUpdateData) => updateInventory(id, data),
  getById: (id: string) => medusaApi.get(`/admin/inventories/${id}`),
};

// Auth API
export const authApi = {
  login: (email: string, password: string) => medusaApi.post('/store/auth', { email, password }),
  logout: () => medusaApi.delete('/store/auth'),
  getSession: () => medusaApi.get('/store/auth'),
};

export default api;
