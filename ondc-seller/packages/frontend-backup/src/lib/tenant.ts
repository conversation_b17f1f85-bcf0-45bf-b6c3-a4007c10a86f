import { Tenant } from '../types/api';

// Default tenant ID for development mode
const DEFAULT_TENANT_ID = 'default';

// Cookie name for storing the tenant ID
const TENANT_COOKIE_NAME = 'x-tenant-id';

/**
 * Get the current tenant ID from cookies, local storage, or context
 * @returns The current tenant ID or the default tenant ID
 */
export const getCurrentTenantId = (): string => {
  // Always return default tenant ID during server-side rendering
  if (typeof window === 'undefined') {
    return DEFAULT_TENANT_ID;
  }

  try {
    // First try to get from cookie (set by middleware)
    const cookies = document.cookie.split(';');
    const tenantCookie = cookies.find(cookie => cookie.trim().startsWith(`${TENANT_COOKIE_NAME}=`));

    if (tenantCookie) {
      const tenantId = tenantCookie.split('=')[1].trim();
      if (tenantId) {
        return tenantId;
      }
    }

    // If not in cookie, try localStorage
    if ('localStorage' in window) {
      const storedTenantId = localStorage.getItem('currentTenantId');
      if (storedTenantId) {
        return storedTenantId;
      }
    }

    return DEFAULT_TENANT_ID;
  } catch (error) {
    // Handle any errors (privacy modes, quota exceeded, etc.)
    console.error('Error accessing tenant ID:', error);
    return DEFAULT_TENANT_ID;
  }
};

/**
 * Set the current tenant ID in local storage and cookie
 * @param tenantId The tenant ID to set
 */
export const setCurrentTenantId = (tenantId: string): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Set in localStorage if available
    if ('localStorage' in window) {
      localStorage.setItem('currentTenantId', tenantId);
    }

    // Set in cookie for middleware access
    // Calculate expiry date (30 days from now)
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 30);

    // Set cookie with secure flag in production
    const secure = process.env.NODE_ENV === 'production' ? '; Secure' : '';
    document.cookie = `${TENANT_COOKIE_NAME}=${tenantId}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax${secure}`;

    // Reload the page to apply the new tenant ID via middleware
    // This ensures all API requests use the new tenant ID
    window.location.reload();
  } catch (error) {
    console.error('Error setting tenant ID:', error);
  }
};

/**
 * Clear the current tenant ID from local storage and cookie
 */
export const clearCurrentTenantId = (): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear from localStorage if available
    if ('localStorage' in window) {
      localStorage.removeItem('currentTenantId');
    }

    // Clear from cookie by setting expiry in the past
    document.cookie = `${TENANT_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`;

    // Reload the page to apply the default tenant ID via middleware
    window.location.reload();
  } catch (error) {
    console.error('Error clearing tenant ID:', error);
  }
};

/**
 * Get the current tenant object from local storage
 * @returns The current tenant object or null
 */
export const getCurrentTenant = (): Tenant | null => {
  if (typeof window === 'undefined') {
    return null;
  }

  try {
    // Check if localStorage is available
    if (!('localStorage' in window)) {
      return null;
    }

    const storedTenant = localStorage.getItem('currentTenant');
    if (!storedTenant) return null;

    // Safely parse JSON with error handling
    try {
      return JSON.parse(storedTenant);
    } catch (parseError) {
      console.error('Error parsing tenant JSON:', parseError);
      // If JSON is invalid, remove it to prevent future errors
      localStorage.removeItem('currentTenant');
      return null;
    }
  } catch (error) {
    console.error('Error accessing tenant from localStorage:', error);
    return null;
  }
};

/**
 * Set the current tenant object in local storage
 * @param tenant The tenant object to set
 */
export const setCurrentTenant = (tenant: Tenant): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Check if localStorage is available
    if (!('localStorage' in window)) {
      console.warn('localStorage is not available, cannot set tenant');
      return;
    }

    // Validate tenant object has required properties
    if (!tenant || !tenant.id) {
      console.error('Invalid tenant object provided');
      return;
    }

    localStorage.setItem('currentTenant', JSON.stringify(tenant));
    setCurrentTenantId(tenant.id);
  } catch (error) {
    console.error('Error setting tenant in localStorage:', error);
  }
};

export default {
  getCurrentTenantId,
  setCurrentTenantId,
  clearCurrentTenantId,
  getCurrentTenant,
  setCurrentTenant,
};

/**
 * Clear the current tenant from local storage and cookie
 */
export const clearCurrentTenant = (): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    // Clear from localStorage if available
    if ('localStorage' in window) {
      localStorage.removeItem('currentTenant');
      localStorage.removeItem('currentTenantId');
    }

    // Clear from cookie by setting expiry in the past
    document.cookie = `${TENANT_COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; SameSite=Lax`;

    // Reload the page to apply the default tenant ID via middleware
    window.location.reload();
  } catch (error) {
    console.error('Error clearing tenant:', error);
  }
};
