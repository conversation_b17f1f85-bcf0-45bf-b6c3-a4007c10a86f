/**
 * Enhanced Strapi API client for the ONDC Seller Platform
 *
 * This module provides an improved implementation of the Strapi API client
 * with better error handling, caching, pagination, and more features.
 */

import { getCurrentTenantId } from './tenant';
import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

// Configuration
const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || 'http://localhost:1339';
const STRAPI_API_TOKEN =
  process.env.STRAPI_API_TOKEN ||
  'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// Create axios instance for Strapi API
const strapiAxios = axios.create({
  baseURL: `${STRAPI_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
    Authorization: `Bearer ${STRAPI_API_TOKEN}`,
  },
});

// Add request interceptor to include tenant ID
strapiAxios.interceptors.request.use(
  config => {
    // Get tenant ID safely (will return default during SSR)
    let tenantId = 'default';

    // Only attempt to get tenant ID on client side
    if (typeof window !== 'undefined') {
      try {
        tenantId = getCurrentTenantId();
      } catch (error) {
        console.error('Error getting tenant ID:', error);
        // Continue with default tenant ID
      }
    }

    // Add tenant ID as a query parameter
    if (config.params) {
      config.params.tenant = tenantId;
    } else {
      config.params = { tenant: tenantId };
    }

    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

/**
 * Process Strapi API error with detailed information
 * @param error - Error object from Strapi API
 * @returns Processed error object with detailed information
 */
export function processStrapiError(error: any): any {
  if (axios.isAxiosError(error)) {
    const axiosError = error as AxiosError;

    if (axiosError.response) {
      console.error(`Strapi API error (${axiosError.response.status}):`, axiosError.response.data);

      // Handle specific error codes
      if (axiosError.response.status === 401) {
        console.error('Authentication error: Invalid or expired token');
        // Could trigger token refresh here if implemented
      } else if (axiosError.response.status === 403) {
        console.error('Permission denied to access resource');
      } else if (axiosError.response.status === 404) {
        console.error('Resource not found');
      } else if (axiosError.response.status >= 500) {
        console.error('Strapi server error');
      }

      return {
        status: axiosError.response.status,
        statusText: axiosError.response.statusText,
        data: axiosError.response.data,
        message: axiosError.message,
      };
    } else if (axiosError.request) {
      console.error('No response received from Strapi:', axiosError.request);
      return {
        status: 0,
        statusText: 'No response received',
        message: 'Network error - no response received from Strapi',
      };
    }
  }

  console.error('Error setting up request:', error);
  return {
    status: 0,
    statusText: 'Unknown error',
    message: error.message || 'Unknown error occurred',
  };
}

/**
 * Process Strapi item to normalize the data structure
 * @param item - Item from Strapi API
 * @returns Normalized item
 */
export function processStrapiItem(item: any): any {
  if (!item) return null;

  // If the item has attributes, merge them with the item
  if (item.attributes) {
    const { attributes, ...rest } = item;

    // Process media fields
    const processed = { ...rest, ...attributes };

    // Handle media fields (image, images, etc.)
    Object.keys(processed).forEach(key => {
      const value = processed[key];

      // Handle single media
      if (value && value.data && value.data.attributes && value.data.attributes.url) {
        processed[key] = {
          id: value.data.id,
          url: value.data.attributes.url,
          alternativeText: value.data.attributes.alternativeText,
          caption: value.data.attributes.caption,
          width: value.data.attributes.width,
          height: value.data.attributes.height,
          formats: value.data.attributes.formats,
        };
      }

      // Handle media collections
      if (value && value.data && Array.isArray(value.data)) {
        processed[key] = value.data.map((media: any) => ({
          id: media.id,
          url: media.attributes.url,
          alternativeText: media.attributes.alternativeText,
          caption: media.attributes.caption,
          width: media.attributes.width,
          height: media.attributes.height,
          formats: media.attributes.formats,
        }));
      }
    });

    return processed;
  }

  return item;
}

/**
 * Fetch data from Strapi API with improved error handling
 * @param endpoint - API endpoint
 * @param options - Fetch options
 * @returns Promise with the response data
 */
export async function fetchAPI(endpoint: string, options: AxiosRequestConfig = {}): Promise<any> {
  try {
    console.log(`Fetching from Strapi: ${endpoint}`);
    const response = await strapiAxios(endpoint, options);
    return response.data;
  } catch (error) {
    const processedError = processStrapiError(error);
    console.error(`Error fetching from Strapi API (${endpoint}):`, processedError);
    return { error: processedError };
  }
}

/**
 * Get all items from a collection with improved error handling
 * @param collection - Collection name
 * @param params - Query parameters
 * @returns Promise with the collection data
 */
export async function getCollection(collection: string, params = {}): Promise<any> {
  try {
    const queryParams = {
      populate: '*',
      ...params,
    };

    const data = await fetchAPI(`/${collection}`, { params: queryParams });

    if (data.error) {
      console.warn(`Error fetching collection ${collection}:`, data.error);
      return { error: data.error };
    }

    if (!data || !data.data) {
      console.warn(`No data received for collection ${collection}`);
      return { error: { message: 'No data received' } };
    }

    // Process items
    const items = Array.isArray(data.data)
      ? data.data.map((item: any) => processStrapiItem(item))
      : [];

    return {
      items,
      pagination: data.meta?.pagination,
      error: null,
    };
  } catch (error) {
    console.error(`Error in getCollection(${collection}):`, error);
    return {
      items: [],
      pagination: null,
      error: { message: error.message || 'Unknown error occurred' },
    };
  }
}

/**
 * Get a single item from a collection with improved error handling
 * @param collection - Collection name
 * @param id - Item ID
 * @param params - Query parameters
 * @returns Promise with the item data
 */
export async function getItem(collection: string, id: string | number, params = {}): Promise<any> {
  try {
    const queryParams = {
      populate: '*',
      ...params,
    };

    const data = await fetchAPI(`/${collection}/${id}`, { params: queryParams });

    if (data.error) {
      console.warn(`Error fetching item ${id} from collection ${collection}:`, data.error);
      return { error: data.error };
    }

    if (!data || !data.data) {
      console.warn(`Item ${id} not found in collection ${collection}`);
      return { error: { message: 'Item not found' } };
    }

    // Process item
    const item = processStrapiItem(data.data);

    return {
      item,
      error: null,
    };
  } catch (error) {
    console.error(`Error in getItem(${collection}, ${id}):`, error);
    return {
      item: null,
      error: { message: error.message || 'Unknown error occurred' },
    };
  }
}

/**
 * Get fallback banners when Strapi is unavailable
 * @returns Object with fallback banner data
 */
export function getFallbackBanners(): any {
  console.log('Using fallback banners');

  return {
    items: [
      {
        id: 1,
        title: 'Welcome to ONDC Seller Platform',
        subtitle: 'Connect with millions of buyers across India',
        image: {
          url: '/images/placeholder.png',
        },
        cta_text: 'Browse Products',
        cta_link: '/products',
      },
      {
        id: 2,
        title: 'Discover Quality Products',
        subtitle: 'From trusted sellers across the country',
        image: {
          url: '/images/placeholder.png',
        },
        cta_text: 'Shop Now',
        cta_link: '/products',
      },
    ],
    pagination: null,
    error: null,
  };
}

/**
 * Get all banners with improved error handling and fallbacks
 * @param params - Query parameters
 * @returns Promise with the banners data
 */
export async function getBanners(params = {}): Promise<any> {
  try {
    const data = await getCollection('banners', {
      'filters[active][$eq]': true,
      populate: 'image',
      ...params,
    });

    if (data.error || !data.items || data.items.length === 0) {
      console.warn('No banner data received from Strapi, using fallback');
      return getFallbackBanners();
    }

    return data;
  } catch (error) {
    console.error('Error fetching banners:', error);
    return getFallbackBanners();
  }
}

export default {
  fetchAPI,
  getCollection,
  getItem,
  getBanners,
  getFallbackBanners,
  processStrapiItem,
  processStrapiError,
};
