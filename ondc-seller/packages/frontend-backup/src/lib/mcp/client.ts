import axios from 'axios';
import { getCurrentTenantId } from '../tenant';

// MCP server URL - use same origin for API calls
const MCP_SERVER_URL =
  process.env.NEXT_PUBLIC_MCP_SERVER_URL ||
  (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');

// Create axios instance for MCP API
const mcpClient = axios.create({
  baseURL: MCP_SERVER_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds
});

// Add request interceptor for tenant ID
mcpClient.interceptors.request.use(
  config => {
    // Add tenant ID header to every request
    config.headers = config.headers || {};
    config.headers['x-tenant-id'] = getCurrentTenantId();

    return config;
  },
  error => Promise.reject(error)
);

/**
 * Call an MCP tool with the given name and parameters
 * @param toolName The name of the MCP tool to call
 * @param params The parameters to pass to the tool
 * @param context Additional context to pass to the tool
 * @returns The result of the tool call
 */
export const callMcpTool = async (
  toolName: string,
  params: Record<string, any>,
  context?: Record<string, any>
) => {
  try {
    // If context contains headers, add them to the request
    if (context?.headers) {
      Object.entries(context.headers).forEach(([key, value]) => {
        mcpClient.defaults.headers.common[key] = value as string;
      });
    }

    const response = await mcpClient.post('/api/mcp', {
      tool: toolName,
      params: params,
      context: context,
    });

    console.log(`MCP client response for ${toolName}:`, response.data);

    if (response.data.error) {
      throw new Error(response.data.error.message || 'Error calling MCP tool');
    }

    return response.data;
  } catch (error) {
    console.error(`Error calling MCP tool ${toolName}:`, error);
    throw error;
  }
};

/**
 * Get a list of available MCP tools
 * @returns A list of available MCP tools
 */
export const getMcpTools = async () => {
  try {
    const response = await mcpClient.post('/api/mcp', {
      tool: 'list_tools',
      params: {},
    });

    if (response.data.error) {
      throw new Error(response.data.error.message || 'Error getting MCP tools');
    }

    return response.data;
  } catch (error) {
    console.error('Error getting MCP tools:', error);
    throw error;
  }
};

// Export common MCP tool wrappers
export const mcpTools = {
  // Product tools
  getProducts: (params: any, context?: any) => callMcpTool('getProducts', params, context),
  getProduct: (id: string, context?: any) => callMcpTool('getProduct', { id }, context),
  createProduct: (data: any, context?: any) => callMcpTool('createProduct', data, context),
  updateProduct: (id: string, data: any, context?: any) =>
    callMcpTool('updateProduct', { id, ...data }, context),
  deleteProduct: (id: string, context?: any) => callMcpTool('deleteProduct', { id }, context),

  // Order tools
  getOrders: (params: any, context?: any) => callMcpTool('getOrders', params, context),
  getOrder: (id: string, context?: any) => callMcpTool('getOrder', { id }, context),
  createOrder: (data: any, context?: any) => callMcpTool('createOrder', data, context),
  updateOrder: (id: string, data: any, context?: any) =>
    callMcpTool('updateOrder', { id, ...data }, context),

  // Inventory tools
  getInventoryItems: (params: any, context?: any) =>
    callMcpTool('getInventoryItems', params, context),
  updateInventoryItem: (id: string, data: any, context?: any) =>
    callMcpTool('updateInventoryItem', { id, ...data }, context),
};

export default mcpClient;
