import { useState, useCallback } from 'react';
import { callMcpTool } from './client';
import { getCurrentTenantId } from '../tenant';

/**
 * Hook for calling MCP tools
 * @param toolName The name of the MCP tool to call
 * @returns An object with the tool call state and a function to call the tool
 */
export function useMcpTool<TParams extends Record<string, any>, TResult>(toolName: string) {
  const [data, setData] = useState<TResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const execute = useCallback(
    async (params: TParams) => {
      setIsLoading(true);
      setError(null);

      try {
        // Get the current tenant ID (only on client side)
        let tenantId = 'default';
        if (typeof window !== 'undefined') {
          tenantId = getCurrentTenantId();
          console.log('Using tenant ID:', tenantId);
        }

        // Add tenant ID to the request context
        const context = {
          headers: {
            'x-tenant-id': tenantId
          }
        };

        const result = await callMcpTool(toolName, params, context);
        setData(result as TResult);
        return result as TResult;
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));
        setError(error);
        throw error;
      } finally {
        setIsLoading(false);
      }
    },
    [toolName]
  );

  return {
    data,
    isLoading,
    error,
    execute,
  };
}

/**
 * Hook for getting products using MCP
 */
export function useGetProducts() {
  return useMcpTool<{ limit?: number; offset?: number; category?: string }, any>('getProducts');
}

/**
 * Hook for getting a product by ID using MCP
 */
export function useGetProduct() {
  return useMcpTool<{ id: string }, any>('getProduct');
}

/**
 * Hook for creating a product using MCP
 */
export function useCreateProduct() {
  return useMcpTool<any, any>('createProduct');
}

/**
 * Hook for updating a product using MCP
 */
export function useUpdateProduct() {
  return useMcpTool<{ id: string } & Record<string, any>, any>('updateProduct');
}

/**
 * Hook for getting orders using MCP
 */
export function useGetOrders() {
  return useMcpTool<{ limit?: number; offset?: number }, any>('getOrders');
}

/**
 * Hook for getting an order by ID using MCP
 */
export function useGetOrder() {
  return useMcpTool<{ id: string }, any>('getOrder');
}

/**
 * Hook for updating inventory using MCP
 */
export function useUpdateInventory() {
  return useMcpTool<{ id: string; quantity: number }, any>('updateInventoryItem');
}

export default useMcpTool;
