'use client';

import { useState, useEffect, useCallback } from 'react';
import { cartApi, Cart } from '../lib/medusa/cart-api';

interface UseCartReturn {
  cart: Cart | null;
  loading: boolean;
  error: Error | null;
  addItem: (variantId: string, quantity?: number) => Promise<void>;
  updateItem: (lineItemId: string, quantity: number) => Promise<void>;
  removeItem: (lineItemId: string) => Promise<void>;
  clearCart: () => void;
  refreshCart: () => Promise<void>;
  isItemInCart: (variantId: string) => boolean;
  getItemQuantity: (variantId: string) => number;
  totalItems: number;
  totalUniqueItems: number;
  subtotal: number;
  total: number;
  isEmpty: boolean;
  isUsingMockData: boolean;
}

/**
 * Hook for managing cart state and operations
 * @returns Cart state and operations
 */
export function useCart(): UseCartReturn {
  const [cart, setCart] = useState<Cart | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);

  // Track if we're using mock data (determined by cart API responses)
  const [isUsingMockData, setIsUsingMockData] = useState(false);

  // Load cart on mount
  useEffect(() => {
    const loadCart = async () => {
      try {
        setLoading(true);
        console.log('[useCart] Loading cart');

        // Try to get or create a cart
        const cartData = await cartApi.getOrCreateCart();
        setCart(cartData);

        // Check if we're using mock data based on cart ID
        const usingMock = cartData.id === 'mock-cart-id';
        setIsUsingMockData(usingMock);

        // Clear error if we successfully got a cart
        setError(null);
      } catch (err) {
        console.error('[useCart] Error loading cart:', err);
        setError(err instanceof Error ? err : new Error('Failed to load cart'));
        // Assume we're using mock data if there's an error
        setIsUsingMockData(true);
      } finally {
        setLoading(false);
      }
    };

    loadCart();
  }, []); // Only run on mount

  // Add item to cart
  const addItem = useCallback(async (variantId: string, quantity: number = 1) => {
    try {
      setLoading(true);
      console.log(`[useCart] Adding item ${variantId} (quantity: ${quantity}) to cart`);

      const updatedCart = await cartApi.addToCart(variantId, quantity);
      setCart(updatedCart);
      setError(null);

      // Check if we're using mock data based on cart ID
      const usingMock = updatedCart.id === 'mock-cart-id';
      setIsUsingMockData(usingMock);

      console.log(`[useCart] Successfully added item to cart:`, updatedCart);
    } catch (err) {
      console.error('[useCart] Error adding item to cart:', err);
      setError(err instanceof Error ? err : new Error('Failed to add item to cart'));
      // Assume we're using mock data if there's an error
      setIsUsingMockData(true);
    } finally {
      setLoading(false);
    }
  }, []);

  // Update item in cart
  const updateItem = useCallback(async (lineItemId: string, quantity: number) => {
    try {
      setLoading(true);
      const updatedCart = await cartApi.updateCartItem(lineItemId, quantity);
      setCart(updatedCart);
      setError(null);
    } catch (err) {
      console.error('Error updating cart item:', err);
      setError(err instanceof Error ? err : new Error('Failed to update cart item'));
    } finally {
      setLoading(false);
    }
  }, []);

  // Remove item from cart
  const removeItem = useCallback(async (lineItemId: string) => {
    try {
      setLoading(true);
      const updatedCart = await cartApi.removeCartItem(lineItemId);
      setCart(updatedCart);
      setError(null);
    } catch (err) {
      console.error('Error removing cart item:', err);
      setError(err instanceof Error ? err : new Error('Failed to remove cart item'));
    } finally {
      setLoading(false);
    }
  }, []);

  // Clear cart
  const clearCart = useCallback(() => {
    cartApi.clearCartId();
    setCart(null);
  }, []);

  // Refresh cart
  const refreshCart = useCallback(async () => {
    try {
      setLoading(true);
      const refreshedCart = await cartApi.refreshCart();
      setCart(refreshedCart);
      setError(null);
    } catch (err) {
      console.error('Error refreshing cart:', err);
      setError(err instanceof Error ? err : new Error('Failed to refresh cart'));
    } finally {
      setLoading(false);
    }
  }, []);

  // Check if item is in cart
  const isItemInCart = useCallback(
    (variantId: string): boolean => {
      if (!cart || !cart.items || cart.items.length === 0) {
        return false;
      }

      return cart.items.some(item => item.variant && item.variant.id === variantId);
    },
    [cart]
  );

  // Get item quantity
  const getItemQuantity = useCallback(
    (variantId: string): number => {
      if (!cart || !cart.items || cart.items.length === 0) {
        return 0;
      }

      const item = cart.items.find(item => item.variant && item.variant.id === variantId);
      return item ? item.quantity : 0;
    },
    [cart]
  );

  // Calculate cart totals
  const totalItems = cart?.items?.reduce((acc, item) => acc + item.quantity, 0) || 0;
  const totalUniqueItems = cart?.items?.length || 0;
  const subtotal = cart?.subtotal || 0;
  const total = cart?.total || 0;
  const isEmpty = totalUniqueItems === 0;

  return {
    cart,
    loading,
    error,
    addItem,
    updateItem,
    removeItem,
    clearCart,
    refreshCart,
    isItemInCart,
    getItemQuantity,
    totalItems,
    totalUniqueItems,
    subtotal,
    total,
    isEmpty,
    isUsingMockData,
  };
}

export default useCart;
