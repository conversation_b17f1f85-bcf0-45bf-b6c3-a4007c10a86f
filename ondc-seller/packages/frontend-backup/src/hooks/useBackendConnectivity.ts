'use client';

import { useState, useEffect } from 'react';
import {
  BackendStatus,
  addStatusChangeListener,
  getBackendConnectivityState,
  forceCheck,
  isBackendAvailable as getIsBackendAvailable,
  isUsingMockData as getIsUsingMockData,
} from '../lib/backend-connectivity';

interface UseBackendConnectivityReturn {
  status: BackendStatus;
  isAvailable: boolean;
  isUsingMockData: boolean;
  lastChecked: Date | null;
  lastAvailable: Date | null;
  checkCount: number;
  error: Error | null;
  checkNow: () => Promise<boolean>;
}

/**
 * Hook for monitoring backend connectivity
 * @returns Backend connectivity state and functions
 */
export function useBackendConnectivity(): UseBackendConnectivityReturn {
  // Get initial state
  const initialState = getBackendConnectivityState();
  
  // State
  const [status, setStatus] = useState<BackendStatus>(initialState.status);
  const [isAvailable, setIsAvailable] = useState<boolean>(initialState.status === 'available');
  const [isUsingMockData, setIsUsingMockData] = useState<boolean>(initialState.isUsingMockData);
  const [lastChecked, setLastChecked] = useState<Date | null>(initialState.lastChecked);
  const [lastAvailable, setLastAvailable] = useState<Date | null>(initialState.lastAvailable);
  const [checkCount, setCheckCount] = useState<number>(initialState.checkCount);
  const [error, setError] = useState<Error | null>(initialState.error);
  
  // Listen for status changes
  useEffect(() => {
    // Update state when status changes
    const handleStatusChange = (newStatus: BackendStatus, usingMockData: boolean) => {
      setStatus(newStatus);
      setIsAvailable(newStatus === 'available');
      setIsUsingMockData(usingMockData);
      
      // Get the latest state
      const state = getBackendConnectivityState();
      setLastChecked(state.lastChecked);
      setLastAvailable(state.lastAvailable);
      setCheckCount(state.checkCount);
      setError(state.error);
      
      // Log status change
      console.log(`[useBackendConnectivity] Backend status changed: ${newStatus}, using mock data: ${usingMockData}`);
    };
    
    // Add listener
    const removeListener = addStatusChangeListener(handleStatusChange);
    
    // Remove listener on cleanup
    return () => {
      removeListener();
    };
  }, []);
  
  // Force a check
  const checkNow = async () => {
    return forceCheck();
  };
  
  return {
    status,
    isAvailable,
    isUsingMockData,
    lastChecked,
    lastAvailable,
    checkCount,
    error,
    checkNow,
  };
}

/**
 * Get the current backend availability status
 * @returns Whether the backend is available
 */
export function isBackendAvailable(): boolean {
  return getIsBackendAvailable();
}

/**
 * Get whether mock data is being used
 * @returns Whether mock data is being used
 */
export function isUsingMockData(): boolean {
  return getIsUsingMockData();
}

export default useBackendConnectivity;
