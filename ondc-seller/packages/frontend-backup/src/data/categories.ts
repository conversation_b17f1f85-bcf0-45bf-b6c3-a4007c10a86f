// Product categories data for mega-menu
export interface SubCategory {
  id: string;
  name: string;
  href: string;
}

export interface Category {
  id: string;
  name: string;
  href: string;
  subcategories: SubCategory[];
  featured?: FeaturedItem[];
}

export interface FeaturedItem {
  id: string;
  name: string;
  href: string;
  imageSrc: string;
  price: number;
  currency: string;
}

export const productCategories: Category[] = [
  {
    id: 'electronics',
    name: 'Electronics',
    href: '/products/category/electronics',
    subcategories: [
      {
        id: 'smartphones',
        name: 'Smartphones',
        href: '/products/category/electronics/smartphones',
      },
      {
        id: 'laptops',
        name: 'Laptops & Computers',
        href: '/products/category/electronics/laptops',
      },
      { id: 'tablets', name: 'Tablets', href: '/products/category/electronics/tablets' },
      { id: 'audio', name: 'Audio & Headphones', href: '/products/category/electronics/audio' },
      {
        id: 'cameras',
        name: 'Cameras & Photography',
        href: '/products/category/electronics/cameras',
      },
      {
        id: 'wearables',
        name: 'Wearable Technology',
        href: '/products/category/electronics/wearables',
      },
      {
        id: 'accessories',
        name: 'Accessories',
        href: '/products/category/electronics/accessories',
      },
    ],
    featured: [
      {
        id: 'feat-1',
        name: 'Latest Smartphone',
        href: '/products/latest-smartphone',
        imageSrc: '/images/categories/electronics.svg',
        price: 49999,
        currency: 'INR',
      },
      {
        id: 'feat-2',
        name: 'Wireless Earbuds',
        href: '/products/wireless-earbuds',
        imageSrc: '/images/categories/electronics.svg',
        price: 8999,
        currency: 'INR',
      },
    ],
  },
  {
    id: 'fashion',
    name: 'Fashion',
    href: '/products/category/fashion',
    subcategories: [
      { id: 'mens', name: "Men's Clothing", href: '/products/category/fashion/mens' },
      { id: 'womens', name: "Women's Clothing", href: '/products/category/fashion/womens' },
      { id: 'kids', name: "Kids' Clothing", href: '/products/category/fashion/kids' },
      { id: 'footwear', name: 'Footwear', href: '/products/category/fashion/footwear' },
      { id: 'accessories', name: 'Accessories', href: '/products/category/fashion/accessories' },
      { id: 'jewelry', name: 'Jewelry & Watches', href: '/products/category/fashion/jewelry' },
      { id: 'bags', name: 'Bags & Luggage', href: '/products/category/fashion/bags' },
    ],
    featured: [
      {
        id: 'feat-3',
        name: 'Summer Collection',
        href: '/products/summer-collection',
        imageSrc: '/images/categories/fashion.svg',
        price: 2499,
        currency: 'INR',
      },
      {
        id: 'feat-4',
        name: 'Designer Watches',
        href: '/products/designer-watches',
        imageSrc: '/images/categories/fashion.svg',
        price: 12999,
        currency: 'INR',
      },
    ],
  },
  {
    id: 'home',
    name: 'Home & Kitchen',
    href: '/products/category/home',
    subcategories: [
      { id: 'furniture', name: 'Furniture', href: '/products/category/home/<USER>' },
      { id: 'decor', name: 'Home Decor', href: '/products/category/home/<USER>' },
      { id: 'kitchen', name: 'Kitchen & Dining', href: '/products/category/home/<USER>' },
      { id: 'appliances', name: 'Appliances', href: '/products/category/home/<USER>' },
      { id: 'bedding', name: 'Bedding & Bath', href: '/products/category/home/<USER>' },
      { id: 'storage', name: 'Storage & Organization', href: '/products/category/home/<USER>' },
      { id: 'garden', name: 'Garden & Outdoor', href: '/products/category/home/<USER>' },
    ],
    featured: [
      {
        id: 'feat-5',
        name: 'Smart Home Devices',
        href: '/products/smart-home',
        imageSrc: '/images/categories/home-kitchen.svg',
        price: 7999,
        currency: 'INR',
      },
      {
        id: 'feat-6',
        name: 'Kitchen Essentials',
        href: '/products/kitchen-essentials',
        imageSrc: '/images/categories/home-kitchen.svg',
        price: 4999,
        currency: 'INR',
      },
    ],
  },
  {
    id: 'beauty',
    name: 'Beauty & Personal Care',
    href: '/products/category/beauty',
    subcategories: [
      { id: 'skincare', name: 'Skincare', href: '/products/category/beauty/skincare' },
      { id: 'makeup', name: 'Makeup', href: '/products/category/beauty/makeup' },
      { id: 'haircare', name: 'Hair Care', href: '/products/category/beauty/haircare' },
      { id: 'fragrance', name: 'Fragrance', href: '/products/category/beauty/fragrance' },
      {
        id: 'personal-care',
        name: 'Personal Care',
        href: '/products/category/beauty/personal-care',
      },
      {
        id: 'mens-grooming',
        name: "Men's Grooming",
        href: '/products/category/beauty/mens-grooming',
      },
      {
        id: 'luxury-beauty',
        name: 'Luxury Beauty',
        href: '/products/category/beauty/luxury-beauty',
      },
    ],
    featured: [
      {
        id: 'feat-7',
        name: 'Premium Skincare Set',
        href: '/products/premium-skincare',
        imageSrc: '/images/categories/beauty.svg',
        price: 3999,
        currency: 'INR',
      },
      {
        id: 'feat-8',
        name: 'Luxury Perfumes',
        href: '/products/luxury-perfumes',
        imageSrc: '/images/categories/beauty.svg',
        price: 6999,
        currency: 'INR',
      },
    ],
  },
  {
    id: 'grocery',
    name: 'Grocery & Gourmet',
    href: '/products/category/grocery',
    subcategories: [
      {
        id: 'fresh-produce',
        name: 'Fresh Produce',
        href: '/products/category/grocery/fresh-produce',
      },
      { id: 'dairy', name: 'Dairy & Eggs', href: '/products/category/grocery/dairy' },
      { id: 'pantry', name: 'Pantry Staples', href: '/products/category/grocery/pantry' },
      { id: 'snacks', name: 'Snacks & Beverages', href: '/products/category/grocery/snacks' },
      { id: 'organic', name: 'Organic Foods', href: '/products/category/grocery/organic' },
      { id: 'gourmet', name: 'Gourmet & Specialty', href: '/products/category/grocery/gourmet' },
      { id: 'health-foods', name: 'Health Foods', href: '/products/category/grocery/health-foods' },
    ],
    featured: [
      {
        id: 'feat-9',
        name: 'Organic Gift Basket',
        href: '/products/organic-basket',
        imageSrc: '/images/categories/grocery.svg',
        price: 2499,
        currency: 'INR',
      },
      {
        id: 'feat-10',
        name: 'Gourmet Tea Collection',
        href: '/products/tea-collection',
        imageSrc: '/images/categories/grocery.svg',
        price: 1499,
        currency: 'INR',
      },
    ],
  },
];

// Popular search terms for search suggestions
export const popularSearchTerms = [
  'smartphone',
  'laptop',
  'headphones',
  'smart watch',
  'wireless earbuds',
  'camera',
  'television',
  'air conditioner',
  'refrigerator',
  'washing machine',
  'furniture',
  'home decor',
  'kitchen appliances',
  'skincare',
  'makeup',
  'perfume',
  'organic food',
  'fashion',
  'shoes',
  'accessories',
];

// Format price with currency
export const formatPrice = (price: number, currency: string = 'INR') => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: currency,
    maximumFractionDigits: 0,
  }).format(price);
};
