/**
 * Mock Products Data for UI Components
 * Transforms MSW data structure into a format suitable for UI components
 */

import { MOCK_PRODUCTS } from '../lib/msw-data';

export interface MockProduct {
  id: string;
  name: string;
  description: string;
  imageSrc: string;
  price: number;
  originalPrice?: number;
  category: string;
  subcategory?: string;
  rating?: number;
  reviews?: number;
  inStock: boolean;
  featured?: boolean;
}

// Transform MSW products to UI-friendly format
export const mockProducts: MockProduct[] = [
  {
    id: 'prod_01',
    name: 'Premium Smartphone X Pro',
    description: 'Latest flagship smartphone with advanced AI camera, 5G connectivity, and all-day battery life. Perfect for photography enthusiasts and power users.',
    imageSrc: 'https://picsum.photos/400/400?random=smartphone1',
    price: 49999,
    originalPrice: 59999,
    category: 'Electronics',
    subcategory: 'Smartphones',
    rating: 4.5,
    reviews: 128,
    inStock: true,
    featured: true,
  },
  {
    id: 'prod_02',
    name: 'UltraBook Pro 15"',
    description: 'High-performance laptop with Intel i7 processor, 16GB RAM, and 512GB SSD. Ideal for professionals and content creators.',
    imageSrc: 'https://picsum.photos/400/400?random=laptop1',
    price: 89999,
    originalPrice: 99999,
    category: 'Electronics',
    subcategory: 'Laptops',
    rating: 4.7,
    reviews: 89,
    inStock: true,
    featured: true,
  },
  {
    id: 'prod_03',
    name: 'Wireless Noise-Cancelling Headphones',
    description: 'Premium over-ear headphones with active noise cancellation, 30-hour battery life, and studio-quality sound.',
    imageSrc: 'https://picsum.photos/400/400?random=headphones1',
    price: 15999,
    originalPrice: 19999,
    category: 'Electronics',
    subcategory: 'Audio',
    rating: 4.6,
    reviews: 256,
    inStock: true,
    featured: true,
  },
  {
    id: 'prod_04',
    name: 'Smart Fitness Watch',
    description: 'Advanced fitness tracking with heart rate monitoring, GPS, sleep tracking, and 7-day battery life.',
    imageSrc: 'https://picsum.photos/400/400?random=smartwatch1',
    price: 25999,
    originalPrice: 29999,
    category: 'Electronics',
    subcategory: 'Wearables',
    rating: 4.4,
    reviews: 167,
    inStock: true,
    featured: true,
  },
  {
    id: 'prod_05',
    name: 'Professional DSLR Camera',
    description: '24MP full-frame camera with 4K video recording, dual card slots, and weather sealing for professional photography.',
    imageSrc: 'https://picsum.photos/400/400?random=camera1',
    price: 125999,
    originalPrice: 139999,
    category: 'Electronics',
    subcategory: 'Cameras',
    rating: 4.8,
    reviews: 45,
    inStock: true,
    featured: true,
  },
  {
    id: 'prod_06',
    name: 'Gaming Mechanical Keyboard',
    description: 'RGB backlit mechanical keyboard with Cherry MX switches, programmable keys, and aluminum frame.',
    imageSrc: 'https://picsum.photos/400/400?random=keyboard1',
    price: 8999,
    originalPrice: 11999,
    category: 'Electronics',
    subcategory: 'Accessories',
    rating: 4.3,
    reviews: 312,
    inStock: true,
    featured: false,
  },
  {
    id: 'prod_07',
    name: 'Wireless Charging Pad',
    description: 'Fast wireless charging pad compatible with all Qi-enabled devices. Sleek design with LED indicator.',
    imageSrc: 'https://picsum.photos/400/400?random=charger1',
    price: 2999,
    originalPrice: 3999,
    category: 'Electronics',
    subcategory: 'Accessories',
    rating: 4.2,
    reviews: 89,
    inStock: true,
    featured: false,
  },
  {
    id: 'prod_08',
    name: 'Bluetooth Portable Speaker',
    description: 'Waterproof portable speaker with 360-degree sound, 12-hour battery, and voice assistant support.',
    imageSrc: 'https://picsum.photos/400/400?random=speaker1',
    price: 6999,
    originalPrice: 8999,
    category: 'Electronics',
    subcategory: 'Audio',
    rating: 4.5,
    reviews: 178,
    inStock: true,
    featured: false,
  },
  {
    id: 'prod_09',
    name: 'Smart Home Security Camera',
    description: '1080p HD security camera with night vision, motion detection, and cloud storage. Easy setup and mobile app control.',
    imageSrc: 'https://picsum.photos/400/400?random=security1',
    price: 4999,
    originalPrice: 6999,
    category: 'Electronics',
    subcategory: 'Smart Home',
    rating: 4.1,
    reviews: 234,
    inStock: true,
    featured: false,
  },
  {
    id: 'prod_10',
    name: 'USB-C Hub with HDMI',
    description: 'Multi-port USB-C hub with HDMI 4K output, USB 3.0 ports, SD card reader, and power delivery support.',
    imageSrc: 'https://picsum.photos/400/400?random=hub1',
    price: 3499,
    originalPrice: 4499,
    category: 'Electronics',
    subcategory: 'Accessories',
    rating: 4.0,
    reviews: 156,
    inStock: true,
    featured: false,
  },
  {
    id: 'prod_11',
    name: 'Ergonomic Office Chair',
    description: 'Premium ergonomic office chair with lumbar support, adjustable height, and breathable mesh back.',
    imageSrc: 'https://picsum.photos/400/400?random=chair1',
    price: 18999,
    originalPrice: 24999,
    category: 'Furniture',
    subcategory: 'Office',
    rating: 4.6,
    reviews: 67,
    inStock: true,
    featured: false,
  },
  {
    id: 'prod_12',
    name: 'Standing Desk Converter',
    description: 'Adjustable standing desk converter that transforms any desk into a sit-stand workstation. Easy height adjustment.',
    imageSrc: 'https://picsum.photos/400/400?random=desk1',
    price: 12999,
    originalPrice: 15999,
    category: 'Furniture',
    subcategory: 'Office',
    rating: 4.4,
    reviews: 92,
    inStock: true,
    featured: false,
  },
];

// Helper functions
export const getFeaturedProducts = (limit: number = 6): MockProduct[] => {
  return mockProducts.filter(product => product.featured).slice(0, limit);
};

export const getProductsByCategory = (category: string, limit?: number): MockProduct[] => {
  const filtered = mockProducts.filter(product => 
    product.category.toLowerCase() === category.toLowerCase()
  );
  return limit ? filtered.slice(0, limit) : filtered;
};

export const getProductsBySubcategory = (subcategory: string, limit?: number): MockProduct[] => {
  const filtered = mockProducts.filter(product => 
    product.subcategory?.toLowerCase() === subcategory.toLowerCase()
  );
  return limit ? filtered.slice(0, limit) : filtered;
};

export const getProductById = (id: string): MockProduct | undefined => {
  return mockProducts.find(product => product.id === id);
};

// Export for backward compatibility with MSW data
export const transformMswToMockProducts = () => {
  return MOCK_PRODUCTS.map(mswProduct => ({
    id: mswProduct.id,
    name: mswProduct.title,
    description: mswProduct.description,
    imageSrc: mswProduct.thumbnail,
    price: mswProduct.variants[0]?.prices[0]?.amount || 0,
    category: 'Electronics',
    inStock: (mswProduct.variants[0]?.inventory_quantity || 0) > 0,
    featured: false,
  }));
};
