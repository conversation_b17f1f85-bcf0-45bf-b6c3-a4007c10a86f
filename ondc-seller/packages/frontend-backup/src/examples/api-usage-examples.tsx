/**
 * API Integration Documentation
 *
 * This file documents the API integration between the Next.js frontend and Medusa backend.
 * It shows how to use the API adapter functions in your components.
 */

// Import the API functions
// In your components, you would import them like this:
// import { fetchProducts, createOrder, updateInventory } from '../lib/api';
// import { OrderCreateData, InventoryUpdateData } from '../types/api';

// Example usage in a React component:
/*
import { useEffect, useState } from 'react';
import { fetchProducts } from '../lib/api';
import { Product } from '../types/api';

export default function ProductList() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadProducts() {
      try {
        setLoading(true);
        const response = await fetchProducts({ limit: 10 });
        setProducts(response.products);
      } catch (err: any) {
        setError(err.message || 'Failed to load products');
      } finally {
        setLoading(false);
      }
    }

    loadProducts();
  }, []);

  if (loading) return <div>Loading products...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h1>Products</h1>
      <ul>
        {products.map(product => (
          <li key={product.id}>{product.title}</li>
        ))}
      </ul>
    </div>
  );
}
*/

// Example usage for createOrder
/*
import { createOrder } from '../lib/api';
import { OrderCreateData } from '../types/api';

export default function CheckoutPage() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [orderId, setOrderId] = useState<string | null>(null);

  const handleCheckout = async (formData: any) => {
    try {
      setLoading(true);

      // Prepare order data from form
      const orderData: OrderCreateData = {
        email: formData.email,
        billing_address: {
          first_name: formData.firstName,
          last_name: formData.lastName,
          address_1: formData.address,
          city: formData.city,
          postal_code: formData.postalCode,
          country_code: formData.countryCode,
        },
        shipping_address: {
          // Same as billing address in this example
          first_name: formData.firstName,
          last_name: formData.lastName,
          address_1: formData.address,
          city: formData.city,
          postal_code: formData.postalCode,
          country_code: formData.countryCode,
        },
        items: formData.cartItems.map((item: any) => ({
          variant_id: item.variantId,
          quantity: item.quantity,
        })),
        region_id: formData.regionId,
      };

      // Create the order
      const response = await createOrder(orderData);

      // Store the order ID for confirmation
      setOrderId(response.order.id);

      // Redirect to confirmation page
      router.push(`/order-confirmation/${response.order.id}`);
    } catch (err: any) {
      setError(err.message || 'Failed to create order');
    } finally {
      setLoading(false);
    }
  };

  // Component JSX...
}
*/

// Example usage for updateInventory
/*
import { updateInventory } from '../lib/api';
import { InventoryUpdateData } from '../types/api';

export default function InventoryManagement() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleUpdateInventory = async (inventoryId: string, quantity: number) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(false);

      // Prepare inventory update data
      const updateData: InventoryUpdateData = {
        stocked_quantity: quantity,
      };

      // Update the inventory
      await updateInventory(inventoryId, updateData);

      // Show success message
      setSuccess(true);
    } catch (err: any) {
      setError(err.message || 'Failed to update inventory');
    } finally {
      setLoading(false);
    }
  };

  // Component JSX...
}
*/
