const axios = require('axios');

// Configuration
const STRAPI_BASE_URL = 'http://localhost:1339';
const MEDUSA_BASE_URL = 'http://localhost:9001';
const STRAPI_API_TOKEN = 'baf318e16d6f90dd6aea345fb8ea61b46caf027e63804a278a595a556ed9d71ba2a3e621c6b230fa66b9b6932ba36e65943b22128db2f61804920248671cb49918a8c7c55ded9c2009099f23199d5d67d135b99f34a45712ecfd3a728e162039dcbc1f745adee909af70e2cbcb7a7240f822958f365bfc6193754a08252be5d2';

// Create axios instances for both APIs
const strapiAPI = axios.create({
  baseURL: STRAPI_BASE_URL,
  headers: {
    Authorization: `Bearer ${STRAPI_API_TOKEN}`
  }
});

const medusaAPI = axios.create({
  baseURL: MEDUSA_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'x-publishable-api-key': 'pk_test_123'
  }
});

// Test Strapi API
async function testStrapiAPI() {
  try {
    console.log('Testing Strapi API...');
    const response = await strapiAPI.get('/api/banners?filters[active][$eq]=true&populate=image');
    console.log('Strapi API response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error testing Strapi API:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    return null;
  }
}

// Test Medusa API
async function testMedusaAPI() {
  try {
    console.log('Testing Medusa API...');
    const response = await medusaAPI.get('/store/products');
    console.log('Medusa API response:', JSON.stringify(response.data, null, 2));
    return response.data;
  } catch (error) {
    console.error('Error testing Medusa API:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    return null;
  }
}

// Run tests
async function runTests() {
  console.log('=== Running API Tests ===');
  
  // Test Strapi API
  const strapiData = await testStrapiAPI();
  
  // Test Medusa API
  const medusaData = await testMedusaAPI();
  
  console.log('=== Tests Completed ===');
  
  return {
    strapiData,
    medusaData
  };
}

// Execute tests
runTests();
