# Use Node.js 18 as the base image
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package.json files for all workspaces
COPY package.json ./
COPY packages/frontend/package.json ./packages/frontend/
COPY packages/prisma/package.json ./packages/prisma/

# Install dependencies
RUN npm install

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN cd packages/prisma && \
    npm run db:generate

# Build the Next.js application
RUN npm run build --workspace=@ondc-seller/frontend

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy the build output
COPY --from=builder /app/packages/frontend/public ./packages/frontend/public
COPY --from=builder --chown=nextjs:nodejs /app/packages/frontend/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/packages/frontend/.next/static ./packages/frontend/.next/static

# Set the correct permission for prerender cache
RUN mkdir -p /app/packages/frontend/.next/cache
RUN chown -R nextjs:nodejs /app/packages/frontend/.next

# Switch to non-root user
USER nextjs

# Expose the port
EXPOSE 3000

# Set the environment variables
ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Start the Next.js application
CMD ["node", "packages/frontend/server.js"]
