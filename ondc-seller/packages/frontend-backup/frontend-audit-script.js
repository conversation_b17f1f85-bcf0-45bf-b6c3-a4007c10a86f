const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

const BASE_URL = 'http://localhost:3001';
const AUDIT_RESULTS_DIR = './audit-results';

if (!fs.existsSync(AUDIT_RESULTS_DIR)) {
  fs.mkdirSync(AUDIT_RESULTS_DIR, { recursive: true });
}

const auditResults = {
  timestamp: new Date().toISOString(),
  summary: { totalTests: 0, passed: 0, failed: 0, warnings: 0 },
  tests: [],
  errors: []
};

function addTestResult(name, status, details) {
  auditResults.tests.push({ name, status, details, timestamp: new Date().toISOString() });
  auditResults.summary.totalTests++;
  
  if (status === 'pass') auditResults.summary.passed++;
  else if (status === 'fail') auditResults.summary.failed++;
  else if (status === 'warning') auditResults.summary.warnings++;
  
  console.log(`[${status.toUpperCase()}] ${name}: ${details}`);
}

async function runFrontendAudit() {
  console.log('🔍 Starting Frontend Audit...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  page.on('console', msg => console.log(`[BROWSER] ${msg.text()}`));
  page.on('pageerror', error => {
    auditResults.errors.push({ message: error.message, timestamp: new Date().toISOString() });
  });
  
  try {
    console.log('\n📄 Testing Homepage...');
    const startTime = Date.now();
    await page.goto(BASE_URL, { waitUntil: 'networkidle2', timeout: 30000 });
    const loadTime = Date.now() - startTime;
    
    if (loadTime < 5000) {
      addTestResult('Homepage Load Time', 'pass', `${loadTime}ms`);
    } else {
      addTestResult('Homepage Load Time', 'warning', `${loadTime}ms (slow)`);
    }
    
    await page.screenshot({ path: './audit-results/homepage.png' });
    
    console.log('\n🧭 Testing Navigation...');
    const nav = await page.$('header, nav');
    addTestResult('Navigation', nav ? 'pass' : 'fail', nav ? 'Found' : 'Not found');
    
    const logo = await page.$('h1');
    if (logo) {
      const text = await page.evaluate(el => el.textContent, logo);
      addTestResult('Logo', 'pass', `Found: ${text}`);
    } else {
      addTestResult('Logo', 'fail', 'Not found');
    }
    
    console.log('\n📦 Testing Products Page...');
    await page.goto(`${BASE_URL}/products`, { waitUntil: 'networkidle2', timeout: 30000 });
    await page.screenshot({ path: './audit-results/products.png' });
    
    const products = await page.$$('.product-card, [data-testid="product"]');
    addTestResult('Product Listings', products.length > 0 ? 'pass' : 'fail', `Found ${products.length} products`);
    
    if (products.length > 0) {
      const addToCartBtn = await products[0].$('button');
      if (addToCartBtn) {
        addTestResult('Add to Cart Button', 'pass', 'Found on first product');
        
        try {
          await addToCartBtn.click();
          await page.waitForTimeout(2000);
          addTestResult('Add to Cart Click', 'pass', 'Button clicked successfully');
          await page.screenshot({ path: './audit-results/cart-clicked.png' });
        } catch (error) {
          addTestResult('Add to Cart Click', 'fail', error.message);
        }
      } else {
        addTestResult('Add to Cart Button', 'fail', 'Not found - PRIORITY ISSUE');
      }
    }
    
  } catch (error) {
    console.error('Audit error:', error);
    auditResults.errors.push({ message: error.message, timestamp: new Date().toISOString() });
  } finally {
    await browser.close();
  }
  
  fs.writeFileSync('./audit-results/report.json', JSON.stringify(auditResults, null, 2));
  
  console.log('\n🎉 Audit Complete!');
  console.log(`Tests: ${auditResults.summary.totalTests}`);
  console.log(`Passed: ${auditResults.summary.passed}`);
  console.log(`Failed: ${auditResults.summary.failed}`);
  console.log(`Warnings: ${auditResults.summary.warnings}`);
}

runFrontendAudit().catch(console.error);
