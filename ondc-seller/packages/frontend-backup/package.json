{"name": "@ondc-seller/frontend-backup", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "clean": "rimraf .turbo node_modules .next"}, "dependencies": {"@medusajs/medusa-js": "^6.1.10", "@ondc-seller/prisma": "*", "axios": "^1.6.2", "express": "^5.1.0", "next": "^14.0.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-query": "^3.39.3", "styled-jsx": "^5.1.1", "swr": "^2.2.4", "tailwindcss": "^3.3.6"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@types/jest": "^29.5.14", "@types/node": "^20.17.48", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.21", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.0.11", "postcss": "^8.5.3", "puppeteer": "^24.9.0", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "msw": {"workerDirectory": ["public"]}}