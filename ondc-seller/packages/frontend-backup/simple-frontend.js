const express = require('express');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(express.static('public'));
app.use(express.json());

// Template function for HTML pages
function createHTMLPage(title, content, scripts = '') {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title} - ONDC Seller Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            transition: all 0.3s ease;
        }
        .cart-count {
            background: #ef4444;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 12px;
            position: absolute;
            top: -8px;
            right: -8px;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="container mx-auto px-4 py-3">
            <div class="flex justify-between items-center">
                <h1 class="text-2xl font-bold text-blue-600">ONDC Seller Platform (Consolidated)</h1>

                <!-- Tenant Selector -->
                <div class="flex items-center space-x-4">
                    <select id="tenantSelector" class="border rounded px-3 py-1">
                        <option value="default">Default Tenant</option>
                        <option value="electronics">Electronics Store</option>
                        <option value="fashion">Fashion Store</option>
                    </select>

                    <!-- Cart -->
                    <div class="relative">
                        <button id="cartButton" class="flex items-center text-gray-700 hover:text-blue-600">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"></path>
                            </svg>
                            <span id="cartCount" class="cart-count" style="display: none;">0</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <nav class="bg-blue-600 text-white">
            <div class="container mx-auto px-4">
                <div class="flex space-x-6 py-3">
                    <a href="/" class="hover:text-blue-200">Home</a>
                    <a href="/products" class="hover:text-blue-200">Products</a>
                    <a href="/cart" class="hover:text-blue-200">Cart</a>
                    <a href="/ondc" class="hover:text-blue-200">ONDC Integration</a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        ${content}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 ONDC Seller Platform - Consolidated Architecture</p>
            <p class="text-sm text-gray-400 mt-2">Backend: Port 9000 | Frontend: Port 3000 | Strapi: Port 1339</p>
        </div>
    </footer>

    <!-- Toast Container -->
    <div id="toastContainer"></div>

    <!-- Scripts -->
    <script>
        // Global state
        let currentTenant = 'default';
        let cart = JSON.parse(localStorage.getItem('cart') || '[]');

        // Update cart count
        function updateCartCount() {
            const cartCount = document.getElementById('cartCount');
            const count = cart.reduce((sum, item) => sum + item.quantity, 0);
            if (count > 0) {
                cartCount.textContent = count;
                cartCount.style.display = 'block';
            } else {
                cartCount.style.display = 'none';
            }
        }

        // Show toast notification
        function showToast(message, type = 'success') {
            const toast = document.createElement('div');
            toast.className = 'toast px-4 py-2 rounded shadow-lg ' + (type === 'success' ? 'bg-green-500' : 'bg-red-500') + ' text-white';
            toast.textContent = message;

            document.getElementById('toastContainer').appendChild(toast);

            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => toast.remove(), 300);
            }, 3000);
        }

        // Add to cart function
        function addToCart(productId, productName, price) {
            const existingItem = cart.find(item => item.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({
                    id: productId,
                    name: productName,
                    price: price,
                    quantity: 1,
                    tenant: currentTenant
                });
            }

            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            showToast('Added "' + productName + '" to cart!');
        }

        // Tenant selector change
        document.getElementById('tenantSelector').addEventListener('change', function(e) {
            currentTenant = e.target.value;
            showToast('Switched to ' + e.target.options[e.target.selectedIndex].text);
            // Reload products for new tenant
            if (window.loadProducts) {
                window.loadProducts();
            }
        });

        // Initialize
        updateCartCount();

        ${scripts}
    </script>
</body>
</html>`;
}

// Home page
app.get('/', (req, res) => {
  const content = `
    <div class="text-center">
        <h1 class="text-4xl font-bold text-gray-800 mb-6">Welcome to ONDC Seller Platform</h1>
        <p class="text-xl text-gray-600 mb-8">Consolidated Architecture - All Services Running</p>

        <div class="grid md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-green-600 mb-2">✅ Backend Service</h3>
                <p class="text-gray-600">Running on port 9000</p>
                <p class="text-sm text-gray-500">Multi-tenant API with cart functionality</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-green-600 mb-2">✅ Strapi CMS</h3>
                <p class="text-gray-600">Running on port 1339</p>
                <p class="text-sm text-gray-500">Content management system</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold text-blue-600 mb-2">🚀 Frontend Service</h3>
                <p class="text-gray-600">Running on port 3000</p>
                <p class="text-sm text-gray-500">Consolidated ONDC integration</p>
            </div>
        </div>

        <div class="bg-blue-50 p-6 rounded-lg">
            <h2 class="text-2xl font-semibold mb-4">Consolidation Achievements</h2>
            <div class="grid md:grid-cols-2 gap-4 text-left">
                <div>
                    <h4 class="font-semibold text-blue-600">Package Reduction</h4>
                    <p class="text-sm">10 → 8 packages (20% reduction)</p>
                </div>
                <div>
                    <h4 class="font-semibold text-blue-600">ONDC Integration</h4>
                    <p class="text-sm">Migrated to frontend API routes</p>
                </div>
                <div>
                    <h4 class="font-semibold text-blue-600">Multi-tenancy</h4>
                    <p class="text-sm">Enhanced across all services</p>
                </div>
                <div>
                    <h4 class="font-semibold text-blue-600">Cart System</h4>
                    <p class="text-sm">Fully functional with persistence</p>
                </div>
            </div>
        </div>

        <div class="mt-8">
            <a href="/products" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 mr-4">View Products</a>
            <a href="/ondc" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700">Test ONDC</a>
        </div>
    </div>
  `;

  res.send(createHTMLPage('Home', content));
});

// Products page
app.get('/products', (req, res) => {
  const content = `
    <div>
        <h1 class="text-3xl font-bold mb-6">Products</h1>
        <div class="mb-4">
            <p class="text-gray-600">Current Tenant: <span id="currentTenant" class="font-semibold text-blue-600">default</span></p>
        </div>

        <div id="productsGrid" class="grid md:grid-cols-3 gap-6">
            <div class="text-center py-8">
                <p class="text-gray-500">Loading products...</p>
            </div>
        </div>
    </div>
  `;

  const scripts = `
    async function loadProducts() {
        try {
            const response = await fetch('http://localhost:9000/store/products?tenant=' + currentTenant);
            const data = await response.json();

            document.getElementById('currentTenant').textContent = currentTenant;

            const grid = document.getElementById('productsGrid');
            grid.innerHTML = data.products.map(product => \`
                <div class="bg-white p-6 rounded-lg shadow hover:shadow-lg transition-shadow">
                    <img src="\${product.thumbnail}" alt="\${product.title}" class="w-full h-48 object-cover rounded mb-4">
                    <h3 class="text-lg font-semibold mb-2">\${product.title}</h3>
                    <p class="text-gray-600 mb-4">\${product.description}</p>
                    <div class="flex justify-between items-center">
                        <span class="text-xl font-bold text-blue-600">₹\${product.price}</span>
                        <button onclick="addToCart('\${product.id}', '\${product.title}', \${product.price})"
                                class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
                            Add to Cart
                        </button>
                    </div>
                    <p class="text-sm text-gray-500 mt-2">Stock: \${product.inventory_quantity}</p>
                </div>
            \`).join('');
        } catch (error) {
            console.error('Error loading products:', error);
            document.getElementById('productsGrid').innerHTML = \`
                <div class="col-span-3 text-center py-8">
                    <p class="text-red-500">Error loading products. Make sure backend is running on port 9000.</p>
                </div>
            \`;
        }
    }

    window.loadProducts = loadProducts;
    loadProducts();
  `;

  res.send(createHTMLPage('Products', content, scripts));
});

// Cart page
app.get('/cart', (req, res) => {
  const content = `
    <div>
        <h1 class="text-3xl font-bold mb-6">Shopping Cart</h1>
        <div id="cartItems">
            <div class="text-center py-8">
                <p class="text-gray-500">Loading cart...</p>
            </div>
        </div>
    </div>
  `;

  const scripts = `
    function renderCart() {
        const cartItems = document.getElementById('cartItems');

        if (cart.length === 0) {
            cartItems.innerHTML = \`
                <div class="text-center py-8">
                    <p class="text-gray-500 mb-4">Your cart is empty</p>
                    <a href="/products" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">Shop Now</a>
                </div>
            \`;
            return;
        }

        const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);

        cartItems.innerHTML = \`
            <div class="bg-white rounded-lg shadow">
                \${cart.map(item => \`
                    <div class="flex items-center justify-between p-4 border-b">
                        <div>
                            <h3 class="font-semibold">\${item.name}</h3>
                            <p class="text-gray-600">₹\${item.price} each</p>
                            <p class="text-sm text-gray-500">Tenant: \${item.tenant}</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2">
                                <button onclick="updateQuantity('\${item.id}', -1)" class="bg-gray-200 px-2 py-1 rounded">-</button>
                                <span class="px-3">\${item.quantity}</span>
                                <button onclick="updateQuantity('\${item.id}', 1)" class="bg-gray-200 px-2 py-1 rounded">+</button>
                            </div>
                            <span class="font-semibold">₹\${item.price * item.quantity}</span>
                            <button onclick="removeFromCart('\${item.id}')" class="text-red-500 hover:text-red-700">Remove</button>
                        </div>
                    </div>
                \`).join('')}
                <div class="p-4">
                    <div class="flex justify-between items-center text-xl font-bold">
                        <span>Total: ₹\${total}</span>
                        <button class="bg-green-600 text-white px-6 py-2 rounded hover:bg-green-700">Checkout</button>
                    </div>
                </div>
            </div>
        \`;
    }

    function updateQuantity(productId, change) {
        const item = cart.find(item => item.id === productId);
        if (item) {
            item.quantity += change;
            if (item.quantity <= 0) {
                removeFromCart(productId);
                return;
            }
            localStorage.setItem('cart', JSON.stringify(cart));
            updateCartCount();
            renderCart();
        }
    }

    function removeFromCart(productId) {
        cart = cart.filter(item => item.id !== productId);
        localStorage.setItem('cart', JSON.stringify(cart));
        updateCartCount();
        renderCart();
        showToast('Item removed from cart');
    }

    renderCart();
  `;

  res.send(createHTMLPage('Cart', content, scripts));
});

// ONDC Integration page
app.get('/ondc', (req, res) => {
  const content = `
    <div>
        <h1 class="text-3xl font-bold mb-6">ONDC Integration</h1>
        <p class="text-gray-600 mb-6">Test the consolidated ONDC functionality</p>

        <div class="grid md:grid-cols-2 gap-6">
            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">ONDC Search</h3>
                <button onclick="testONDCSearch()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mb-4">
                    Test Search API
                </button>
                <div id="searchResult" class="text-sm bg-gray-100 p-3 rounded"></div>
            </div>

            <div class="bg-white p-6 rounded-lg shadow">
                <h3 class="text-lg font-semibold mb-4">ONDC Select</h3>
                <button onclick="testONDCSelect()" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 mb-4">
                    Test Select API
                </button>
                <div id="selectResult" class="text-sm bg-gray-100 p-3 rounded"></div>
            </div>
        </div>

        <div class="mt-8 bg-blue-50 p-6 rounded-lg">
            <h3 class="text-lg font-semibold mb-4">Consolidated ONDC Features</h3>
            <ul class="list-disc list-inside space-y-2 text-gray-700">
                <li>ONDC API routes migrated to frontend (/api/ondc/*)</li>
                <li>Multi-tenant ONDC configuration</li>
                <li>Development and production mode support</li>
                <li>Enhanced error handling and logging</li>
            </ul>
        </div>
    </div>
  `;

  const scripts = `
    async function testONDCSearch() {
        const resultDiv = document.getElementById('searchResult');
        resultDiv.textContent = 'Testing ONDC Search...';

        try {
            const response = await fetch('/api/ondc/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-tenant-id': currentTenant
                },
                body: JSON.stringify({
                    context: {
                        domain: 'ONDC:RET10',
                        country: 'IND',
                        city: 'std:080',
                        action: 'search',
                        core_version: '1.2.0',
                        bap_id: 'test-bap',
                        bap_uri: 'https://test-bap.com',
                        bpp_id: 'test-bpp',
                        bpp_uri: 'https://test-bpp.com',
                        transaction_id: 'test-txn-' + Date.now(),
                        message_id: 'test-msg-' + Date.now(),
                        timestamp: new Date().toISOString()
                    },
                    message: {
                        intent: {
                            item: {
                                descriptor: {
                                    name: 'electronics'
                                }
                            }
                        }
                    }
                })
            });

            const data = await response.json();
            resultDiv.innerHTML = \`<pre>\${JSON.stringify(data, null, 2)}</pre>\`;
        } catch (error) {
            resultDiv.textContent = 'Error: ' + error.message;
        }
    }

    async function testONDCSelect() {
        const resultDiv = document.getElementById('selectResult');
        resultDiv.textContent = 'Testing ONDC Select...';

        try {
            const response = await fetch('/api/ondc/select', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'x-tenant-id': currentTenant
                },
                body: JSON.stringify({
                    context: {
                        domain: 'ONDC:RET10',
                        country: 'IND',
                        city: 'std:080',
                        action: 'select',
                        core_version: '1.2.0',
                        bap_id: 'test-bap',
                        bap_uri: 'https://test-bap.com',
                        bpp_id: 'test-bpp',
                        bpp_uri: 'https://test-bpp.com',
                        transaction_id: 'test-txn-' + Date.now(),
                        message_id: 'test-msg-' + Date.now(),
                        timestamp: new Date().toISOString()
                    },
                    message: {
                        order: {
                            provider: { id: 'test-provider' },
                            items: [{ id: 'test-item', quantity: { count: 1 } }],
                            billing: { name: 'Test User', email: '<EMAIL>', phone: '**********' },
                            fulfillment: { type: 'Delivery' }
                        }
                    }
                })
            });

            const data = await response.json();
            resultDiv.innerHTML = \`<pre>\${JSON.stringify(data, null, 2)}</pre>\`;
        } catch (error) {
            resultDiv.textContent = 'Error: ' + error.message;
        }
    }
  `;

  res.send(createHTMLPage('ONDC Integration', content, scripts));
});

// API routes for ONDC (proxy to the consolidated API routes)
app.post('/api/ondc/search', async (req, res) => {
  try {
    // In a real implementation, this would use the consolidated ONDC functionality
    // For now, return a mock response
    const tenantId = req.headers['x-tenant-id'] || 'default';

    res.json({
      context: {
        ...req.body.context,
        action: 'on_search',
        timestamp: new Date().toISOString(),
      },
      message: {
        catalog: {
          'bpp/descriptor': {
            name: 'ONDC Seller Platform (Consolidated)',
            symbol: 'OSP',
            short_desc: 'Multi-tenant ONDC seller platform',
          },
          'bpp/providers': [
            {
              id: tenantId,
              descriptor: {
                name: 'Seller ' + tenantId,
                symbol: tenantId.toUpperCase(),
              },
              items: [
                {
                  id: 'item_1',
                  descriptor: {
                    name: 'Consolidated Sample Product',
                    short_desc: 'Product from consolidated ONDC integration',
                  },
                  price: {
                    currency: 'INR',
                    value: '999.00',
                  },
                },
              ],
            },
          ],
        },
      },
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/ondc/select', async (req, res) => {
  try {
    const tenantId = req.headers['x-tenant-id'] || 'default';

    res.json({
      context: {
        ...req.body.context,
        action: 'on_select',
        timestamp: new Date().toISOString(),
      },
      message: {
        order: {
          ...req.body.message.order,
          quote: {
            price: {
              currency: 'INR',
              value: '1099.00',
            },
            breakup: [
              {
                title: 'Consolidated Product',
                price: {
                  currency: 'INR',
                  value: '999.00',
                },
              },
              {
                title: 'Delivery charges',
                price: {
                  currency: 'INR',
                  value: '100.00',
                },
              },
            ],
          },
        },
      },
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Start server
app.listen(PORT, () => {
  console.log('🚀 ONDC Seller Platform Frontend (Consolidated) running on port ' + PORT);
  console.log('🏠 Homepage: http://localhost:' + PORT);
  console.log('🛍️  Products: http://localhost:' + PORT + '/products');
  console.log('🛒 Cart: http://localhost:' + PORT + '/cart');
  console.log('🔗 ONDC: http://localhost:' + PORT + '/ondc');
  console.log('📊 Backend API: http://localhost:9000');
  console.log('📝 Strapi CMS: http://localhost:1339');
});

module.exports = app;
