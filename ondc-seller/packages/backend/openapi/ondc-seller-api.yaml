openapi: 3.0.3
info:
  title: ONDC Seller API
  description: API for the ONDC Seller Platform
  version: 0.0.1
  contact:
    name: ONDC Seller Team
    email: <EMAIL>
servers:
  - url: http://localhost:9000
    description: Local development server
  - url: https://api.example.com
    description: Production server
tags:
  - name: Products
    description: Product management
  - name: Orders
    description: Order management
  - name: Auth
    description: Authentication
paths:
  /health:
    get:
      summary: Health check
      description: Check if the API is running
      operationId: getHealth
      tags:
        - System
      responses:
        '200':
          description: API is running
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: ok
                  timestamp:
                    type: string
                    format: date-time
                    example: '2023-12-01T12:00:00Z'
  /products/featured:
    get:
      summary: Get featured products
      description: Get a list of featured products
      operationId: getFeaturedProducts
      tags:
        - Products
      responses:
        '200':
          description: List of featured products
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /store/products:
    get:
      summary: Get all products
      description: Get a list of all products
      operationId: getProducts
      tags:
        - Products
      parameters:
        - name: limit
          in: query
          description: Maximum number of products to return
          schema:
            type: integer
            default: 10
        - name: offset
          in: query
          description: Number of products to skip
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: List of products
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  count:
                    type: integer
                    example: 100
                  offset:
                    type: integer
                    example: 0
                  limit:
                    type: integer
                    example: 10
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /store/products/{id}:
    get:
      summary: Get a product by ID
      description: Get a product by its ID
      operationId: getProductById
      tags:
        - Products
      parameters:
        - name: id
          in: path
          description: Product ID
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Product details
          content:
            application/json:
              schema:
                type: object
                properties:
                  product:
                    $ref: '#/components/schemas/Product'
        '404':
          description: Product not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
  /admin/orders:
    get:
      summary: Get all orders
      description: Get a list of all orders
      operationId: getOrders
      tags:
        - Orders
      parameters:
        - name: limit
          in: query
          description: Maximum number of orders to return
          schema:
            type: integer
            default: 10
        - name: offset
          in: query
          description: Number of orders to skip
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: List of orders
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  count:
                    type: integer
                    example: 100
                  offset:
                    type: integer
                    example: 0
                  limit:
                    type: integer
                    example: 10
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Error'
components:
  schemas:
    Product:
      type: object
      properties:
        id:
          type: string
          example: prod_123
        title:
          type: string
          example: Organic Tomatoes
        description:
          type: string
          example: Organically grown tomatoes, perfect for salads and cooking.
        handle:
          type: string
          example: organic-tomatoes
        is_giftcard:
          type: boolean
          example: false
        thumbnail:
          type: string
          example: https://example.com/images/tomatoes-thumb.jpg
        images:
          type: array
          items:
            type: string
          example: ['https://example.com/images/tomatoes.jpg']
        variants:
          type: array
          items:
            $ref: '#/components/schemas/ProductVariant'
        created_at:
          type: string
          format: date-time
          example: '2023-12-01T12:00:00Z'
        updated_at:
          type: string
          format: date-time
          example: '2023-12-01T12:00:00Z'
    ProductVariant:
      type: object
      properties:
        id:
          type: string
          example: var_123
        title:
          type: string
          example: Small Pack
        prices:
          type: array
          items:
            $ref: '#/components/schemas/Price'
        inventory_quantity:
          type: integer
          example: 100
        options:
          type: array
          items:
            $ref: '#/components/schemas/Option'
    Price:
      type: object
      properties:
        id:
          type: string
          example: price_123
        currency_code:
          type: string
          example: inr
        amount:
          type: integer
          example: 4000
    Option:
      type: object
      properties:
        id:
          type: string
          example: opt_123
        value:
          type: string
          example: Small
    Order:
      type: object
      properties:
        id:
          type: string
          example: order_123
        status:
          type: string
          enum: [pending, processing, shipped, delivered, cancelled]
          example: pending
        customer:
          $ref: '#/components/schemas/Customer'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'
        total:
          type: integer
          example: 4500
        created_at:
          type: string
          format: date-time
          example: '2023-12-01T12:00:00Z'
        updated_at:
          type: string
          format: date-time
          example: '2023-12-01T12:00:00Z'
    OrderItem:
      type: object
      properties:
        id:
          type: string
          example: item_123
        title:
          type: string
          example: Organic Tomatoes - Small Pack
        quantity:
          type: integer
          example: 1
        unit_price:
          type: integer
          example: 4000
        variant:
          $ref: '#/components/schemas/ProductVariant'
    Customer:
      type: object
      properties:
        id:
          type: string
          example: cus_123
        email:
          type: string
          format: email
          example: <EMAIL>
        first_name:
          type: string
          example: John
        last_name:
          type: string
          example: Doe
    Error:
      type: object
      properties:
        error:
          type: string
          example: Something went wrong
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - bearerAuth: []
