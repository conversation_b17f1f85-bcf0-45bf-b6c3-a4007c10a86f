const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');

const app = express();
const PORT = process.env.PORT || 9000;

// Middleware
app.use(cors({
  origin: ['http://localhost:3000', 'http://localhost:5000'],
  credentials: true
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Multi-tenancy middleware
app.use((req, res, next) => {
  const tenantId = req.headers['x-tenant-id'] || req.query.tenant || 'default';
  req.tenantId = tenantId;
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.path} - Tenant: ${tenantId}`);
  next();
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    service: 'ONDC Seller Platform Backend',
    version: '2.0.0-consolidated',
    timestamp: new Date().toISOString(),
    tenant: req.tenantId
  });
});

// Mock Medusa Store API endpoints
app.get('/store/products', (req, res) => {
  const { tenant } = req.query;
  const tenantId = req.tenantId;
  
  const mockProducts = [
    {
      id: `product_${tenantId}_1`,
      title: `Sample Product 1 - ${tenantId}`,
      description: 'High-quality sample product for testing',
      thumbnail: 'https://via.placeholder.com/300x300',
      price: 999,
      inventory_quantity: 50,
      tenant_id: tenantId,
      variants: [
        {
          id: `variant_${tenantId}_1`,
          title: 'Default Variant',
          price: 999,
          inventory_quantity: 50
        }
      ]
    },
    {
      id: `product_${tenantId}_2`,
      title: `Sample Product 2 - ${tenantId}`,
      description: 'Another great product for testing',
      thumbnail: 'https://via.placeholder.com/300x300',
      price: 1499,
      inventory_quantity: 30,
      tenant_id: tenantId,
      variants: [
        {
          id: `variant_${tenantId}_2`,
          title: 'Default Variant',
          price: 1499,
          inventory_quantity: 30
        }
      ]
    }
  ];

  res.json({
    products: mockProducts,
    count: mockProducts.length,
    offset: 0,
    limit: 20
  });
});

// Mock cart endpoints
app.post('/store/carts', (req, res) => {
  const cartId = `cart_${req.tenantId}_${Date.now()}`;
  res.json({
    cart: {
      id: cartId,
      tenant_id: req.tenantId,
      items: [],
      total: 0,
      created_at: new Date().toISOString()
    }
  });
});

app.post('/store/carts/:id/line-items', (req, res) => {
  const { id } = req.params;
  const { variant_id, quantity } = req.body;
  
  res.json({
    cart: {
      id,
      tenant_id: req.tenantId,
      items: [
        {
          id: `item_${Date.now()}`,
          variant_id,
          quantity,
          unit_price: 999,
          total: 999 * quantity
        }
      ],
      total: 999 * quantity,
      updated_at: new Date().toISOString()
    }
  });
});

// Mock tenant endpoints
app.get('/admin/tenants', (req, res) => {
  const tenants = [
    {
      id: 'default',
      name: 'Default Tenant',
      domain: 'default.ondc-seller.com',
      status: 'active'
    },
    {
      id: 'electronics',
      name: 'Electronics Store',
      domain: 'electronics.ondc-seller.com',
      status: 'active'
    },
    {
      id: 'fashion',
      name: 'Fashion Store',
      domain: 'fashion.ondc-seller.com',
      status: 'active'
    }
  ];

  res.json({ tenants });
});

app.get('/admin/tenants/:id', (req, res) => {
  const { id } = req.params;
  
  res.json({
    tenant: {
      id,
      name: `Tenant ${id}`,
      domain: `${id}.ondc-seller.com`,
      status: 'active',
      settings: {
        ondcConfig: {
          participantId: `participant-${id}`,
          subscriberId: `subscriber-${id}`,
          bppId: `bpp-${id}`
        },
        features: {
          catalog: true,
          orders: true,
          payments: true,
          logistics: true
        }
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  res.status(500).json({
    error: 'Internal Server Error',
    message: err.message,
    tenant: req.tenantId
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not Found',
    message: `Endpoint ${req.method} ${req.path} not found`,
    tenant: req.tenantId
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 ONDC Seller Platform Backend (Consolidated) running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`🛍️  Store API: http://localhost:${PORT}/store/products`);
  console.log(`👥 Admin API: http://localhost:${PORT}/admin/tenants`);
  console.log(`🏢 Multi-tenancy: Use x-tenant-id header or ?tenant=<id> query param`);
});

module.exports = app;
