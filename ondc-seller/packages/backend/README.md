# ONDC Seller Enhanced Backend API

This enhanced backend provides comprehensive e-commerce functionality with MCP (Model Context Protocol) integration, advanced product management, order processing, customer management, and real-time monitoring.

## 🚀 Features

- **MCP Integration**: Execute Medusa operations through Model Context Protocol
- **Enhanced Product Management**: Advanced filtering, bulk operations, and analytics
- **Order Processing**: Complete order lifecycle management with status tracking
- **Customer Management**: Comprehensive customer operations and analytics
- **Cart & Checkout**: Full shopping cart functionality with payment integration
- **Inventory Management**: Real-time stock tracking and management
- **Payment Processing**: Multi-provider payment gateway integration
- **Webhook System**: Real-time event notifications
- **Health Monitoring**: Comprehensive system health and metrics
- **Multi-tenant Support**: Tenant isolation and feature management
- **Development Mode**: Hardcoded credentials (username: demo, password: demo)

## 📋 Prerequisites

- Node.js (v18 or later)
- npm (v9 or later)
- PostgreSQL (v14 or later)
- Redis (v6 or later)
- Medusa CLI

## 🛠️ Installation

1. Install dependencies:

```bash
npm install
```

2. Set up environment variables:

```bash
cp .env.example .env
```

3. Setup database:

```bash
npm run db:setup
```

4. Run migrations:

```bash
npm run migration:run
```

5. Start development server:

```bash
npm run dev
```

6. The server will be running at [http://localhost:9000](http://localhost:9000).

## 🔧 Environment Variables

```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ondc_seller
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-jwt-secret-key
NODE_ENV=development

# Multi-tenancy
VALID_TENANTS=tenant1,tenant2,dev_tenant
DEFAULT_TENANT_ID=dev_tenant
```

## 📚 API Endpoints Overview

### MCP Endpoints

- `GET /mcp/tools` - List available MCP tools
- `POST /mcp/tools/execute` - Execute MCP tool
- `POST /mcp/tools/batch` - Batch execute tools
- `GET /mcp/health` - MCP health check
- `GET /mcp/metrics` - MCP metrics

### Enhanced Product Endpoints

- `GET /api/products` - List products with advanced filtering
- `POST /api/products` - Create new product
- `GET /api/products/:id` - Get product details
- `PUT /api/products/:id` - Update product
- `DELETE /api/products/:id` - Delete product
- `POST /api/products/bulk-update` - Bulk update products

### Enhanced Order Endpoints

- `GET /api/orders` - List orders with filtering
- `GET /api/orders/:id` - Get order details
- `PUT /api/orders/:id/status` - Update order status
- `POST /api/orders/:id/cancel` - Cancel order
- `POST /api/orders/:id/returns` - Create return request
- `GET /api/orders/analytics` - Order analytics

### Customer Management

- `GET /api/customers` - List customers
- `POST /api/customers` - Create customer
- `GET /api/customers/:id` - Get customer details
- `PUT /api/customers/:id` - Update customer
- `GET /api/customers/:id/orders` - Customer orders
- `POST /api/customers/:id/addresses` - Add customer address

### Cart Management

- `POST /api/carts` - Create cart
- `GET /api/carts/:id` - Get cart
- `POST /api/carts/:id/line-items` - Add to cart
- `PUT /api/carts/:id/line-items/:line_id` - Update cart item
- `DELETE /api/carts/:id/line-items/:line_id` - Remove from cart
- `POST /api/carts/:id/complete` - Complete checkout

### Payment Processing

- `GET /api/payments/providers` - List payment providers
- `POST /api/payments/sessions` - Create payment session
- `POST /api/payments/:id/authorize` - Authorize payment
- `POST /api/payments/:id/capture` - Capture payment
- `POST /api/payments/:id/refund` - Process refund
- `POST /api/payments/webhooks/:provider` - Payment webhooks

### Inventory Management

- `GET /api/inventory` - List inventory items
- `GET /api/inventory/:id` - Get inventory item
- `PUT /api/inventory/:id` - Update inventory levels
- `POST /api/inventory/bulk-update` - Bulk update inventory
- `POST /api/inventory/:id/reserve` - Reserve inventory
- `GET /api/inventory/reports/low-stock` - Low stock report

### Webhook System

- `POST /api/webhooks` - Register webhook
- `GET /api/webhooks` - List webhooks
- `GET /api/webhooks/:id` - Get webhook details
- `PUT /api/webhooks/:id` - Update webhook
- `DELETE /api/webhooks/:id` - Delete webhook
- `POST /api/webhooks/:id/test` - Test webhook

### Health & Monitoring

- `GET /api/health` - Basic health check
- `GET /api/health/detailed` - Detailed health check
- `GET /api/health/metrics` - System metrics
- `GET /api/health/ready` - Readiness check
- `GET /api/health/live` - Liveness check
- `GET /api/health/dependencies` - Service dependencies

## 🧪 Testing

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:unit
npm run test:integration

# Run enhanced API tests
npm run test -- --testPathPattern=enhanced-api
```

## 🔒 Authentication

### Development Mode

Use hardcoded credentials:

- Username: `demo`
- Password: `demo`
- Email: `<EMAIL>`

### Production Mode

Use JWT tokens with `Authorization: Bearer <token>` header and `x-tenant-id` header.

## 📊 Available MCP Tools

| Tool Name                     | Description              | Category |
| ----------------------------- | ------------------------ | -------- |
| `medusa_store_list_products`  | List products from store | store    |
| `medusa_admin_create_product` | Create new product       | admin    |
| `medusa_admin_list_orders`    | List all orders          | admin    |
| `medusa_admin_list_customers` | List all customers       | admin    |
| `sync_products_to_ondc`       | Sync products to ONDC    | sync     |

## 📈 Performance Features

- Response compression
- Request logging
- Health monitoring
- Metrics collection
- Database connection pooling
- Redis caching
- Rate limiting

## 📄 API Documentation

Full OpenAPI 3.0 specification available at:

- File: `openapi/enhanced-backend-api.yaml`
- Swagger UI: http://localhost:9000/api/docs (when implemented)

## Build & Deploy

```bash
# Build for production
npm run build

# Start production server
npm start
```

## Directory Structure

- `src/api/routes/` - Enhanced API routes
- `src/services/` - Custom services including MCP service
- `src/middleware/` - Authentication, tenant validation, error handling
- `openapi/` - API specifications
- `src/__tests__/` - Comprehensive test suites
