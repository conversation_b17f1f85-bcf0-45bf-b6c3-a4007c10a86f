#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');
const swaggerUi = require('swagger-ui-dist');
const express = require('express');

// Load the OpenAPI specification
const openapiPath = path.join(__dirname, '../openapi/ondc-seller-api.yaml');
const openapiSpec = yaml.load(fs.readFileSync(openapiPath, 'utf8'));

// Create a directory for the generated documentation
const docsDir = path.join(__dirname, '../api-docs');
if (!fs.existsSync(docsDir)) {
  fs.mkdirSync(docsDir);
}

// Write the OpenAPI specification as JSON
fs.writeFileSync(
  path.join(docsDir, 'openapi.json'),
  JSON.stringify(openapiSpec, null, 2)
);

// Create an HTML file that loads Swagger UI
const htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ONDC Seller API Documentation</title>
  <link rel="stylesheet" type="text/css" href="./swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    
    *,
    *:before,
    *:after {
      box-sizing: inherit;
    }
    
    body {
      margin: 0;
      background: #fafafa;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  
  <script src="./swagger-ui-bundle.js"></script>
  <script src="./swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: "./openapi.json",
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        layout: "StandaloneLayout"
      });
      
      window.ui = ui;
    };
  </script>
</body>
</html>
`;

fs.writeFileSync(path.join(docsDir, 'index.html'), htmlContent);

// Copy Swagger UI assets
const swaggerUiAssetPath = swaggerUi.getAbsoluteFSPath();
const assetsToCopy = ['swagger-ui.css', 'swagger-ui-bundle.js', 'swagger-ui-standalone-preset.js'];

assetsToCopy.forEach(asset => {
  fs.copyFileSync(
    path.join(swaggerUiAssetPath, asset),
    path.join(docsDir, asset)
  );
});

console.log('API documentation generated successfully!');

// If this script is run directly, start a server to serve the documentation
if (require.main === module) {
  const app = express();
  const port = process.env.PORT || 8080;
  
  app.use(express.static(docsDir));
  
  app.listen(port, () => {
    console.log(`API documentation server running at http://localhost:${port}`);
  });
}
