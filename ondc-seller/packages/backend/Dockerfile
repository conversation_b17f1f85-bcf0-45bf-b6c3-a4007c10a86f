# Use Node.js 18 as the base image
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy package.json files for all workspaces
COPY package.json ./
COPY packages/backend/package.json ./packages/backend/
COPY packages/prisma/package.json ./packages/prisma/

# Install dependencies
RUN npm install

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Generate Prisma client
RUN cd packages/prisma && \
    npm run db:generate

# Build the backend application
RUN npm run build --workspace=@ondc-seller/backend

# Production image, copy all the files and run medusa
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 medusa

# Copy the build output
COPY --from=builder /app/packages/backend/dist ./packages/backend/dist
COPY --from=builder /app/packages/backend/medusa-config.js ./packages/backend/
COPY --from=builder /app/packages/backend/package.json ./packages/backend/
COPY --from=builder /app/packages/prisma ./packages/prisma/
COPY --from=builder /app/node_modules ./node_modules

# Set the correct permissions
RUN chown -R medusa:nodejs /app

# Switch to non-root user
USER medusa

# Expose the port
EXPOSE 9000

# Set the working directory to the backend package
WORKDIR /app/packages/backend

# Start the Medusa server
CMD ["npm", "run", "start"]
