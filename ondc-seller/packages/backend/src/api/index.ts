import { Router } from 'express';
import cors from 'cors';
import { ConfigModule } from '@medusajs/medusa';
import { getConfigFile } from 'medusa-core-utils';
import autoGeneratedRoutes from './routes/auto-generated';
import mcpRoutes from './routes/mcp';
import enhancedProductRoutes from './routes/enhanced-products';
import enhancedOrderRoutes from './routes/enhanced-orders';
import customerRoutes from './routes/customers';
import cartRoutes from './routes/carts';
import paymentRoutes from './routes/payments';
import inventoryRoutes from './routes/inventory';
import webhookRoutes from './routes/webhooks';
import healthRoutes from './routes/health';

export default (rootDirectory: string): Router | Router[] => {
  const router = Router();

  const { configModule } = getConfigFile<ConfigModule>(rootDirectory, 'medusa-config');
  const { projectConfig } = configModule;

  const corsOptions = {
    origin: projectConfig.store_cors.split(','),
    credentials: true,
  };

  router.use(cors(corsOptions));

  // Mount auto-generated routes
  router.use('/', autoGeneratedRoutes(rootDirectory));

  // Mount MCP routes
  router.use('/mcp', mcpRoutes(rootDirectory));

  // Mount enhanced e-commerce routes
  router.use('/api/products', enhancedProductRoutes(rootDirectory));
  router.use('/api/orders', enhancedOrderRoutes(rootDirectory));
  router.use('/api/customers', customerRoutes(rootDirectory));
  router.use('/api/carts', cartRoutes(rootDirectory));
  router.use('/api/payments', paymentRoutes(rootDirectory));
  router.use('/api/inventory', inventoryRoutes(rootDirectory));
  router.use('/api/webhooks', webhookRoutes(rootDirectory));
  router.use('/api/health', healthRoutes(rootDirectory));

  return router;
};
