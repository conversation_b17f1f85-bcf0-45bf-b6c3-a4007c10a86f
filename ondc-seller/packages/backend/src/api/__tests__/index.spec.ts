import { Router } from 'express';
import request from 'supertest';
import express from 'express';
import routes from '../index';

// Mock getConfigFile
jest.mock('medusa-core-utils', () => {
  return {
    getConfigFile: jest.fn(() => ({
      configModule: {
        projectConfig: {
          store_cors: 'http://localhost:3000',
        },
      },
    })),
  };
});

describe('API Routes', () => {
  let app;
  let mockProductService;

  beforeEach(() => {
    // Create a mock product service
    mockProductService = {
      list: jest.fn().mockResolvedValue([
        {
          id: 'prod_123',
          title: 'Test Product',
          variants: [{ id: 'var_123', prices: [{ amount: 1000 }] }],
          tags: [],
        },
      ]),
    };

    // Create a mock scope
    const mockScope = {
      resolve: jest.fn((service) => {
        if (service === 'productService') {
          return mockProductService;
        }
        return {};
      }),
    };

    // Create an Express app
    app = express();
    
    // Add middleware to mock the scope
    app.use((req, res, next) => {
      req.scope = mockScope;
      next();
    });
    
    // Add the routes
    app.use(routes(''));
  });

  describe('GET /health', () => {
    it('should return a 200 status code', async () => {
      const response = await request(app).get('/health');
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
    });
  });

  describe('GET /products/featured', () => {
    it('should return featured products', async () => {
      const response = await request(app).get('/products/featured');
      
      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('products');
      expect(Array.isArray(response.body.products)).toBe(true);
      
      // Verify that the product service was called with the correct parameters
      expect(mockProductService.list).toHaveBeenCalledWith(
        { is_giftcard: false },
        {
          relations: ['variants', 'variants.prices', 'tags'],
          take: 6,
          order: { created_at: 'DESC' },
        }
      );
    });

    it('should handle errors', async () => {
      // Mock the product service to throw an error
      mockProductService.list.mockRejectedValueOnce(new Error('Test error'));
      
      const response = await request(app).get('/products/featured');
      
      expect(response.status).toBe(500);
      expect(response.body).toHaveProperty('error', 'Test error');
    });
  });
});
