import { TransactionBaseService } from "@medusajs/medusa";
import { EntityManager } from "typeorm";

class OndcService extends TransactionBaseService {
  constructor(container) {
    super(container);
  }

  async getStats(config = {}) {
    const productService = this.container.productService;
    const orderService = this.container.orderService;
    const customerService = this.container.customerService;

    const manager = this.activeManager_ ?? this.manager_;

    return await manager.transaction(async (transactionManager) => {
      const [
        productsCount,
        ordersCount,
        customersCount,
      ] = await Promise.all([
        productService.count({}, { transactionManager }),
        orderService.count({}, { transactionManager }),
        customerService.count({}, { transactionManager }),
      ]);

      return {
        products: productsCount,
        orders: ordersCount,
        customers: customersCount,
        timestamp: new Date().toISOString(),
      };
    });
  }

  async withTransaction(transactionManager?: EntityManager): Promise<OndcService> {
    if (!transactionManager) {
      return this;
    }

    const cloned = new OndcService({
      ...this.container,
      manager: transactionManager,
    });

    cloned.transactionManager_ = transactionManager;
    return cloned;
  }
}

export default OndcService;
