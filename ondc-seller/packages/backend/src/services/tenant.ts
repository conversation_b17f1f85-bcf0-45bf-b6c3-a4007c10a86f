import { EntityManager } from "typeorm"

type TenantServiceProps = {
  manager: EntityManager
}

/**
 * Enhanced Tenant Service
 * 
 * Consolidated from backend-medusa package
 * Provides multi-tenant functionality for ONDC Seller Platform
 */
class TenantService {
  protected manager_: EntityManager
  protected transactionManager_: EntityManager | undefined

  constructor({ manager }: TenantServiceProps) {
    this.manager_ = manager
  }

  /**
   * Get the current tenant ID from the request
   * @param req - Express request object
   * @returns The current tenant ID
   */
  getCurrentTenantId(req: any): string {
    const tenantId = req.tenantId || req.headers['x-tenant-id'] || process.env.DEFAULT_TENANT_ID || 'default'
    return tenantId
  }

  /**
   * Validate that the tenant ID is valid
   * @param tenantId - The tenant ID to validate
   * @throws Error if the tenant ID is invalid
   */
  validateTenantId(tenantId: string): void {
    if (!tenantId) {
      throw new Error("Tenant ID is required")
    }
    
    if (tenantId.length < 2) {
      throw new Error("Tenant ID must be at least 2 characters long")
    }
    
    // Validate tenant ID format (alphanumeric and hyphens only)
    if (!/^[a-zA-Z0-9-]+$/.test(tenantId)) {
      throw new Error("Tenant ID can only contain alphanumeric characters and hyphens")
    }
  }

  /**
   * Add tenant ID to query options
   * @param options - Query options
   * @param tenantId - Tenant ID
   * @returns Query options with tenant ID
   */
  withTenantId(options: Record<string, any>, tenantId: string): Record<string, any> {
    return {
      ...options,
      where: {
        ...(options.where || {}),
        tenant_id: tenantId,
      },
    }
  }

  /**
   * Get tenant configuration
   * @param tenantId - Tenant ID
   * @returns Tenant configuration object
   */
  async getTenantConfig(tenantId: string): Promise<Record<string, any>> {
    this.validateTenantId(tenantId)
    
    // In production, this would fetch from database
    // For now, return default configuration
    return {
      id: tenantId,
      name: `Tenant ${tenantId}`,
      domain: `${tenantId}.ondc-seller.com`,
      settings: {
        ondcConfig: {
          participantId: process.env[`ONDC_PARTICIPANT_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_PARTICIPANT_ID,
          subscriberId: process.env[`ONDC_SUBSCRIBER_ID_${tenantId.toUpperCase()}`] || process.env.ONDC_SUBSCRIBER_ID,
          bppId: process.env[`ONDC_BPP_ID_${tenantId.toUpperCase()}`] || `${process.env.ONDC_BPP_ID}-${tenantId}`,
        },
        features: {
          catalog: true,
          orders: true,
          payments: true,
          logistics: process.env.ONDC_ENABLE_LOGISTICS === 'true',
        },
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }
  }

  /**
   * Update tenant configuration
   * @param tenantId - Tenant ID
   * @param config - Configuration updates
   * @returns Updated tenant configuration
   */
  async updateTenantConfig(tenantId: string, config: Record<string, any>): Promise<Record<string, any>> {
    this.validateTenantId(tenantId)
    
    const currentConfig = await this.getTenantConfig(tenantId)
    
    // Merge configurations
    const updatedConfig = {
      ...currentConfig,
      settings: {
        ...currentConfig.settings,
        ...config,
      },
      updatedAt: new Date(),
    }
    
    // In production, save to database
    console.log(`[TenantService] Updated config for tenant ${tenantId}:`, updatedConfig)
    
    return updatedConfig
  }

  /**
   * List all tenants
   * @returns Array of tenant configurations
   */
  async listTenants(): Promise<Record<string, any>[]> {
    // In production, fetch from database
    // For now, return mock tenants
    const defaultTenants = ['default', 'electronics', 'fashion', 'grocery']
    
    const tenants = await Promise.all(
      defaultTenants.map(tenantId => this.getTenantConfig(tenantId))
    )
    
    return tenants
  }

  /**
   * Check if tenant exists
   * @param tenantId - Tenant ID
   * @returns Boolean indicating if tenant exists
   */
  async tenantExists(tenantId: string): Promise<boolean> {
    try {
      this.validateTenantId(tenantId)
      await this.getTenantConfig(tenantId)
      return true
    } catch (error) {
      return false
    }
  }

  /**
   * Create new tenant
   * @param tenantData - Tenant data
   * @returns Created tenant configuration
   */
  async createTenant(tenantData: {
    id: string;
    name: string;
    domain?: string;
    settings?: Record<string, any>;
  }): Promise<Record<string, any>> {
    this.validateTenantId(tenantData.id)
    
    const exists = await this.tenantExists(tenantData.id)
    if (exists) {
      throw new Error(`Tenant ${tenantData.id} already exists`)
    }
    
    const tenantConfig = {
      id: tenantData.id,
      name: tenantData.name,
      domain: tenantData.domain || `${tenantData.id}.ondc-seller.com`,
      settings: {
        ondcConfig: {
          participantId: `participant-${tenantData.id}`,
          subscriberId: `subscriber-${tenantData.id}`,
          bppId: `bpp-${tenantData.id}`,
        },
        features: {
          catalog: true,
          orders: true,
          payments: true,
          logistics: true,
        },
        ...tenantData.settings,
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    }
    
    // In production, save to database
    console.log(`[TenantService] Created tenant:`, tenantConfig)
    
    return tenantConfig
  }
}

export default TenantService
