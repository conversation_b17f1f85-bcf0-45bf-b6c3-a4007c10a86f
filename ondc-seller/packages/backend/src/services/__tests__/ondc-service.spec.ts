import OndcService from "../ondc-service";

describe("OndcService", () => {
  let ondcService;
  let mockProductService;
  let mockOrderService;
  let mockCustomerService;
  let mockManager;

  beforeEach(() => {
    mockProductService = {
      count: jest.fn().mockResolvedValue(10),
    };
    
    mockOrderService = {
      count: jest.fn().mockResolvedValue(5),
    };
    
    mockCustomerService = {
      count: jest.fn().mockResolvedValue(3),
    };
    
    mockManager = {
      transaction: jest.fn().mockImplementation(async (callback) => {
        return await callback(mockManager);
      }),
    };
    
    ondcService = new OndcService({
      productService: mockProductService,
      orderService: mockOrderService,
      customerService: mockCustomerService,
      manager: mockManager,
    });
  });

  describe("getStats", () => {
    it("should return counts of products, orders, and customers", async () => {
      const result = await ondcService.getStats();
      
      expect(mockProductService.count).toHaveBeenCalled();
      expect(mockOrderService.count).toHaveBeenCalled();
      expect(mockCustomerService.count).toHaveBeenCalled();
      
      expect(result).toEqual({
        products: 10,
        orders: 5,
        customers: 3,
        timestamp: expect.any(String),
      });
    });
    
    it("should use transaction manager if provided", async () => {
      const transactionManager = {};
      await ondcService.withTransaction(transactionManager).getStats();
      
      expect(mockProductService.count).toHaveBeenCalledWith(
        {},
        { transactionManager }
      );
    });
  });

  describe("withTransaction", () => {
    it("should return self if no transaction manager is provided", async () => {
      const result = await ondcService.withTransaction();
      expect(result).toBe(ondcService);
    });
    
    it("should return a new instance with transaction manager", async () => {
      const transactionManager = {};
      const result = await ondcService.withTransaction(transactionManager);
      
      expect(result).not.toBe(ondcService);
      expect(result.transactionManager_).toBe(transactionManager);
    });
  });
});
