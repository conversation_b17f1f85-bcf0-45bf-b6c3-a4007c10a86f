{"name": "@ondc-seller/backend", "version": "0.0.1", "private": true, "description": "Medusa Commerce backend for ONDC Seller", "author": "ONDC Seller Team", "license": "MIT", "scripts": {"build": "medusa build", "clean": "rimraf .turbo node_modules dist", "dev": "medusa develop", "start": "medusa start", "seed": "medusa exec ./src/scripts/seed.ts", "test": "jest", "test:integration:http": "TEST_TYPE=integration:http NODE_OPTIONS=--experimental-vm-modules jest --silent=false --runInBand --forceExit", "test:integration:modules": "TEST_TYPE=integration:modules NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "test:unit": "TEST_TYPE=unit NODE_OPTIONS=--experimental-vm-modules jest --silent --runInBand --forceExit", "migration:create": "medusa db:generate", "migration:run": "medusa db:migrate", "migration:revert": "medusa db:rollback", "db:setup": "medusa db:setup", "lint": "eslint \"src/**/*.ts\"", "api:docs": "node scripts/generate-api-docs.js", "api:serve-docs": "node scripts/generate-api-docs.js serve"}, "dependencies": {"@medusajs/admin-sdk": "2.8.1", "@medusajs/cache-redis": "^2.8.1", "@medusajs/cli": "2.8.1", "@medusajs/event-bus-redis": "^2.8.1", "@medusajs/framework": "2.8.1", "@medusajs/medusa": "2.8.1", "@mikro-orm/core": "6.4.3", "@mikro-orm/knex": "6.4.3", "@mikro-orm/migrations": "6.4.3", "@mikro-orm/postgresql": "6.4.3", "@ondc-seller/prisma": "*", "awilix": "^8.0.1", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "morgan": "^1.10.0", "medusa-fulfillment-manual": "^1.1.38", "medusa-interfaces": "^1.3.7", "medusa-payment-manual": "^1.0.24", "pg": "^8.13.0", "sqlite3": "^5.1.7", "ts-node": "^10.9.2", "typeorm": "^0.3.17"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.5", "@babel/preset-env": "^7.23.5", "@babel/preset-typescript": "^7.23.3", "@medusajs/test-utils": "2.8.1", "@mikro-orm/cli": "6.4.3", "@swc/core": "1.5.7", "@swc/jest": "^0.2.36", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jest": "^29.5.11", "@types/node": "^20.10.4", "@types/react": "^18.3.2", "@types/react-dom": "^18.2.25", "@types/supertest": "^2.0.16", "babel-preset-medusa-package": "^1.1.19", "eslint": "^8.56.0", "jest": "^29.7.0", "js-yaml": "^4.1.0", "prop-types": "^15.8.1", "react": "^18.2.0", "react-dom": "^18.2.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "swagger-ui-dist": "^5.10.3", "ts-jest": "^29.1.1", "typescript": "^5.6.2", "vite": "^5.2.11"}}