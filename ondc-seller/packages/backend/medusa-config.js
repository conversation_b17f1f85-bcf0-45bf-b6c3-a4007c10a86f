const dotenv = require('dotenv');

let ENV_FILE_NAME = '';
switch (process.env.NODE_ENV) {
  case 'production':
    ENV_FILE_NAME = '.env.production';
    break;
  case 'staging':
    ENV_FILE_NAME = '.env.staging';
    break;
  case 'test':
    ENV_FILE_NAME = '.env.test';
    break;
  case 'development':
  default:
    ENV_FILE_NAME = '.env';
    break;
}

try {
  dotenv.config({ path: process.cwd() + '/' + ENV_FILE_NAME });
} catch (e) {
  console.error('Failed to load environment variables:', e);
}

const DATABASE_URL = process.env.DATABASE_URL || 'sqlite:///tmp/ondc_seller.db';
const REDIS_URL = process.env.REDIS_URL || 'redis://localhost:6379';

const plugins = [
  `medusa-fulfillment-manual`,
  `medusa-payment-manual`,
  // Multi-tenancy plugin temporarily disabled due to compatibility issues
  // {
  //   resolve: '@medusajs/medusa/dist/plugins/multi-tenancy',
  //   options: {
  //     tenant_metadata_fields: ['ondcId', 'sellerCategory', 'region'],
  //     enabled: process.env.ENABLE_MULTI_TENANCY === 'true',
  //     defaultTenantId: process.env.DEFAULT_TENANT_ID || 'default',
  //   },
  // },
];

const modules = {
  // Redis modules disabled for development
  // eventBus: {
  //   resolve: '@medusajs/event-bus-redis',
  //   options: {
  //     redisUrl: REDIS_URL,
  //   },
  // },
  // cacheService: {
  //   resolve: '@medusajs/cache-redis',
  //   options: {
  //     redisUrl: REDIS_URL,
  //   },
  // },
};

/** @type {import('@medusajs/medusa').ConfigModule} */
module.exports = {
  projectConfig: {
    redis_url: REDIS_URL,
    database_url: DATABASE_URL,
    database_type: 'sqlite',
    store_cors: process.env.STORE_CORS || 'http://localhost:3000,http://localhost:5000',
    admin_cors: process.env.ADMIN_CORS || 'http://localhost:7000',
    // database_extra: { ssl: { rejectUnauthorized: false } }, // Not needed for SQLite
  },
  plugins,
  modules,
};
