const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

async function start() {
  const app = express();

  // Enable CORS with more permissive settings for development
  const corsOptions = {
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps, curl, etc)
      // or any origin in development mode
      const allowedOrigins = (
        process.env.STORE_CORS ||
        'http://localhost:3000,http://localhost:3001,http://localhost:3002,http://localhost:3003'
      ).split(',');

      if (!origin || allowedOrigins.indexOf(origin) !== -1) {
        callback(null, true);
      } else {
        console.log(`CORS blocked request from origin: ${origin}`);
        callback(null, true); // Allow all origins in development mode
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'x-tenant-id', 'x-publishable-api-key'],
    exposedHeaders: ['Access-Control-Allow-Origin'],
  };

  // Pre-flight OPTIONS requests
  app.options('*', cors(corsOptions));

  // Apply CORS middleware
  app.use(cors(corsOptions));

  // Add custom CORS headers for problematic clients
  app.use((req, res, next) => {
    res.header('Access-Control-Allow-Origin', req.headers.origin || '*');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.header(
      'Access-Control-Allow-Headers',
      'Content-Type, Authorization, x-tenant-id, x-publishable-api-key'
    );
    res.header('Access-Control-Allow-Credentials', 'true');
    next();
  });

  app.use(bodyParser.json());

  // Health check endpoint
  app.get('/health', (req, res) => {
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString(),
    });
  });

  // Mock authentication endpoint for Medusa-MCP
  app.post('/store/auth', (req, res) => {
    // Return a standard Medusa auth response
    res.status(200).json({
      user: {
        id: 'usr_01',
        email: req.body.email || '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
      },
      customer: {
        id: 'cus_01',
        email: req.body.email || '<EMAIL>',
      },
      token: 'mock-api-token',
      refresh_token: 'mock-refresh-token',
    });
  });

  // Legacy auth endpoint for backward compatibility
  app.post('/auth/user/emailpass', (req, res) => {
    res.status(200).json({
      token: 'mock-api-token',
    });
  });

  // Mock products endpoint
  app.get('/store/products', (req, res) => {
    // Add tenant ID to response for debugging
    const tenantId = req.headers['x-tenant-id'] || 'default';

    // Get category filters from query parameters
    const categoryId = req.query.category_id;
    const subcategoryId = req.query.subcategory_id;
    const parentCategoryId = req.query.parent_category_id;
    const limit = parseInt(req.query.limit) || 10;
    const offset = parseInt(req.query.offset) || 0;

    console.log('[Mock Backend] Products request:', {
      categoryId,
      subcategoryId,
      parentCategoryId,
      tenantId,
      limit,
      offset,
    });

    // Return different products based on tenant ID and category
    let products = [];

    // Generate category-specific products if we have category filters
    if (categoryId || subcategoryId) {
      const targetCategory = subcategoryId || categoryId;
      console.log('[Mock Backend] Generating products for category:', targetCategory);

      // Generate mock products for the specific category/subcategory
      products = Array.from({ length: 8 }, (_, i) => ({
        id: `mock_${targetCategory}_${i + 1}`,
        title: `${targetCategory.charAt(0).toUpperCase() + targetCategory.slice(1)} Product ${i + 1}`,
        description: `This is a mock ${targetCategory} product for testing. High quality and affordable.`,
        thumbnail: `https://picsum.photos/400/300?random=${targetCategory}${i + 1}`,
        handle: `${targetCategory}-product-${i + 1}`,
        status: 'published',
        collection_id: `col_${targetCategory}`,
        collection: {
          id: `col_${targetCategory}`,
          title: `${targetCategory.charAt(0).toUpperCase() + targetCategory.slice(1)} Collection`,
          handle: `${targetCategory}-collection`,
        },
        variants: [
          {
            id: `mock_variant_${targetCategory}_${i + 1}`,
            title: 'Default Variant',
            prices: [
              { amount: 1000 * (i + 1) + Math.floor(Math.random() * 50000), currency_code: 'inr' },
            ],
            inventory_quantity: 10 + Math.floor(Math.random() * 20),
          },
        ],
        images: [
          {
            id: `img_${targetCategory}_${i + 1}_1`,
            url: `https://picsum.photos/600/600?random=${targetCategory}${i + 1}`,
          },
          {
            id: `img_${targetCategory}_${i + 1}_2`,
            url: `https://picsum.photos/600/600?random=${targetCategory}${i + 1}0`,
          },
        ],
        tenant_id: tenantId,
      }));
    } else if (tenantId === 'default') {
      products = [
        {
          id: 'prod_default_01',
          title: 'Default Tenant Product 1',
          description: 'This is a product for the default tenant',
          thumbnail: 'https://picsum.photos/200',
          handle: 'default-product-1',
          status: 'published',
          collection_id: 'col_01',
          collection: { id: 'col_01', title: 'Default Collection', handle: 'default-collection' },
          variants: [
            {
              id: 'variant_default_01',
              title: 'Default Variant',
              prices: [{ amount: 1000, currency_code: 'inr' }],
              inventory_quantity: 10,
            },
          ],
          images: [
            { id: 'img_default_01', url: 'https://picsum.photos/200' },
            { id: 'img_default_02', url: 'https://picsum.photos/200?random=1' },
          ],
          tenant_id: tenantId,
        },
        {
          id: 'prod_default_02',
          title: 'Default Tenant Product 2',
          description: 'Another product for the default tenant',
          thumbnail: 'https://picsum.photos/200?random=2',
          handle: 'default-product-2',
          status: 'published',
          collection_id: 'col_01',
          collection: { id: 'col_01', title: 'Default Collection', handle: 'default-collection' },
          variants: [
            {
              id: 'variant_default_02',
              title: 'Default Variant',
              prices: [{ amount: 2000, currency_code: 'inr' }],
              inventory_quantity: 5,
            },
          ],
          images: [
            { id: 'img_default_03', url: 'https://picsum.photos/200?random=2' },
            { id: 'img_default_04', url: 'https://picsum.photos/200?random=3' },
          ],
          tenant_id: tenantId,
        },
      ];
    } else if (tenantId === 'tenant1') {
      products = [
        {
          id: 'prod_tenant1_01',
          title: 'Tenant 1 Product A',
          description: 'This is a product for tenant 1',
          thumbnail: 'https://picsum.photos/200?random=10',
          handle: 'tenant1-product-a',
          status: 'published',
          collection_id: 'col_tenant1',
          collection: {
            id: 'col_tenant1',
            title: 'Tenant 1 Collection',
            handle: 'tenant1-collection',
          },
          variants: [
            {
              id: 'variant_tenant1_01',
              title: 'Premium Variant',
              prices: [{ amount: 3000, currency_code: 'inr' }],
              inventory_quantity: 8,
            },
          ],
          images: [
            { id: 'img_tenant1_01', url: 'https://picsum.photos/200?random=10' },
            { id: 'img_tenant1_02', url: 'https://picsum.photos/200?random=11' },
          ],
          tenant_id: tenantId,
        },
        {
          id: 'prod_tenant1_02',
          title: 'Tenant 1 Product B',
          description: 'Another product for tenant 1',
          thumbnail: 'https://picsum.photos/200?random=12',
          handle: 'tenant1-product-b',
          status: 'published',
          collection_id: 'col_tenant1',
          collection: {
            id: 'col_tenant1',
            title: 'Tenant 1 Collection',
            handle: 'tenant1-collection',
          },
          variants: [
            {
              id: 'variant_tenant1_02',
              title: 'Standard Variant',
              prices: [{ amount: 4000, currency_code: 'inr' }],
              inventory_quantity: 15,
            },
          ],
          images: [
            { id: 'img_tenant1_03', url: 'https://picsum.photos/200?random=12' },
            { id: 'img_tenant1_04', url: 'https://picsum.photos/200?random=13' },
          ],
          tenant_id: tenantId,
        },
      ];
    } else if (tenantId === 'tenant2') {
      products = [
        {
          id: 'prod_tenant2_01',
          title: 'Tenant 2 Special Product',
          description: 'This is a special product for tenant 2',
          thumbnail: 'https://picsum.photos/200?random=20',
          handle: 'tenant2-special-product',
          status: 'published',
          collection_id: 'col_tenant2',
          collection: {
            id: 'col_tenant2',
            title: 'Tenant 2 Collection',
            handle: 'tenant2-collection',
          },
          variants: [
            {
              id: 'variant_tenant2_01',
              title: 'Luxury Variant',
              prices: [{ amount: 5000, currency_code: 'inr' }],
              inventory_quantity: 3,
            },
          ],
          images: [
            { id: 'img_tenant2_01', url: 'https://picsum.photos/200?random=20' },
            { id: 'img_tenant2_02', url: 'https://picsum.photos/200?random=21' },
          ],
          tenant_id: tenantId,
        },
        {
          id: 'prod_tenant2_02',
          title: 'Tenant 2 Basic Product',
          description: 'A basic product for tenant 2',
          thumbnail: 'https://picsum.photos/200?random=22',
          handle: 'tenant2-basic-product',
          status: 'published',
          collection_id: 'col_tenant2',
          collection: {
            id: 'col_tenant2',
            title: 'Tenant 2 Collection',
            handle: 'tenant2-collection',
          },
          variants: [
            {
              id: 'variant_tenant2_02',
              title: 'Basic Variant',
              prices: [{ amount: 1500, currency_code: 'inr' }],
              inventory_quantity: 25,
            },
          ],
          images: [
            { id: 'img_tenant2_03', url: 'https://picsum.photos/200?random=22' },
            { id: 'img_tenant2_04', url: 'https://picsum.photos/200?random=23' },
          ],
          tenant_id: tenantId,
        },
      ];
    } else {
      // For any other tenant, return empty products
      products = [];
    }

    // Apply pagination
    const paginatedProducts = products.slice(offset, offset + limit);

    console.log('[Mock Backend] Returning products:', {
      count: paginatedProducts.length,
      total: products.length,
      products: paginatedProducts.map(p => ({ id: p.id, title: p.title })),
    });

    res.status(200).json({
      products: paginatedProducts,
      count: paginatedProducts.length,
      offset,
      limit,
      total: products.length,
    });
  });

  // Mock single product endpoint
  app.get('/store/products/:id', (req, res) => {
    const tenantId = req.headers['x-tenant-id'] || 'default';
    const productId = req.params.id;

    // Define product IDs for each tenant
    const defaultTenantProducts = ['prod_default_01', 'prod_default_02'];
    const tenant1Products = ['prod_tenant1_01', 'prod_tenant1_02'];
    const tenant2Products = ['prod_tenant2_01', 'prod_tenant2_02'];

    // Check if the product belongs to the current tenant
    let productFound = false;
    let product = null;

    if (tenantId === 'default' && defaultTenantProducts.includes(productId)) {
      productFound = true;
      if (productId === 'prod_default_01') {
        product = {
          id: 'prod_default_01',
          title: 'Default Tenant Product 1',
          description: 'This is a product for the default tenant',
          thumbnail: 'https://picsum.photos/200',
          handle: 'default-product-1',
          status: 'published',
          collection_id: 'col_01',
          collection: { id: 'col_01', title: 'Default Collection', handle: 'default-collection' },
          variants: [
            {
              id: 'variant_default_01',
              title: 'Default Variant',
              prices: [{ amount: 1000, currency_code: 'inr' }],
              inventory_quantity: 10,
            },
          ],
          images: [
            { id: 'img_default_01', url: 'https://picsum.photos/200' },
            { id: 'img_default_02', url: 'https://picsum.photos/200?random=1' },
          ],
          tenant_id: tenantId,
        };
      } else {
        product = {
          id: 'prod_default_02',
          title: 'Default Tenant Product 2',
          description: 'Another product for the default tenant',
          thumbnail: 'https://picsum.photos/200?random=2',
          handle: 'default-product-2',
          status: 'published',
          collection_id: 'col_01',
          collection: { id: 'col_01', title: 'Default Collection', handle: 'default-collection' },
          variants: [
            {
              id: 'variant_default_02',
              title: 'Default Variant',
              prices: [{ amount: 2000, currency_code: 'inr' }],
              inventory_quantity: 5,
            },
          ],
          images: [
            { id: 'img_default_03', url: 'https://picsum.photos/200?random=2' },
            { id: 'img_default_04', url: 'https://picsum.photos/200?random=3' },
          ],
          tenant_id: tenantId,
        };
      }
    } else if (tenantId === 'tenant1' && tenant1Products.includes(productId)) {
      productFound = true;
      if (productId === 'prod_tenant1_01') {
        product = {
          id: 'prod_tenant1_01',
          title: 'Tenant 1 Product A',
          description: 'This is a product for tenant 1',
          thumbnail: 'https://picsum.photos/200?random=10',
          handle: 'tenant1-product-a',
          status: 'published',
          collection_id: 'col_tenant1',
          collection: {
            id: 'col_tenant1',
            title: 'Tenant 1 Collection',
            handle: 'tenant1-collection',
          },
          variants: [
            {
              id: 'variant_tenant1_01',
              title: 'Premium Variant',
              prices: [{ amount: 3000, currency_code: 'inr' }],
              inventory_quantity: 8,
            },
          ],
          images: [
            { id: 'img_tenant1_01', url: 'https://picsum.photos/200?random=10' },
            { id: 'img_tenant1_02', url: 'https://picsum.photos/200?random=11' },
          ],
          tenant_id: tenantId,
        };
      } else {
        product = {
          id: 'prod_tenant1_02',
          title: 'Tenant 1 Product B',
          description: 'Another product for tenant 1',
          thumbnail: 'https://picsum.photos/200?random=12',
          handle: 'tenant1-product-b',
          status: 'published',
          collection_id: 'col_tenant1',
          collection: {
            id: 'col_tenant1',
            title: 'Tenant 1 Collection',
            handle: 'tenant1-collection',
          },
          variants: [
            {
              id: 'variant_tenant1_02',
              title: 'Standard Variant',
              prices: [{ amount: 4000, currency_code: 'inr' }],
              inventory_quantity: 15,
            },
          ],
          images: [
            { id: 'img_tenant1_03', url: 'https://picsum.photos/200?random=12' },
            { id: 'img_tenant1_04', url: 'https://picsum.photos/200?random=13' },
          ],
          tenant_id: tenantId,
        };
      }
    } else if (tenantId === 'tenant2' && tenant2Products.includes(productId)) {
      productFound = true;
      if (productId === 'prod_tenant2_01') {
        product = {
          id: 'prod_tenant2_01',
          title: 'Tenant 2 Special Product',
          description: 'This is a special product for tenant 2',
          thumbnail: 'https://picsum.photos/200?random=20',
          handle: 'tenant2-special-product',
          status: 'published',
          collection_id: 'col_tenant2',
          collection: {
            id: 'col_tenant2',
            title: 'Tenant 2 Collection',
            handle: 'tenant2-collection',
          },
          variants: [
            {
              id: 'variant_tenant2_01',
              title: 'Luxury Variant',
              prices: [{ amount: 5000, currency_code: 'inr' }],
              inventory_quantity: 3,
            },
          ],
          images: [
            { id: 'img_tenant2_01', url: 'https://picsum.photos/200?random=20' },
            { id: 'img_tenant2_02', url: 'https://picsum.photos/200?random=21' },
          ],
          tenant_id: tenantId,
        };
      } else {
        product = {
          id: 'prod_tenant2_02',
          title: 'Tenant 2 Basic Product',
          description: 'A basic product for tenant 2',
          thumbnail: 'https://picsum.photos/200?random=22',
          handle: 'tenant2-basic-product',
          status: 'published',
          collection_id: 'col_tenant2',
          collection: {
            id: 'col_tenant2',
            title: 'Tenant 2 Collection',
            handle: 'tenant2-collection',
          },
          variants: [
            {
              id: 'variant_tenant2_02',
              title: 'Basic Variant',
              prices: [{ amount: 1500, currency_code: 'inr' }],
              inventory_quantity: 25,
            },
          ],
          images: [
            { id: 'img_tenant2_03', url: 'https://picsum.photos/200?random=22' },
            { id: 'img_tenant2_04', url: 'https://picsum.photos/200?random=23' },
          ],
          tenant_id: tenantId,
        };
      }
    }

    if (productFound && product) {
      res.status(200).json({
        product,
      });
    } else {
      res.status(404).json({
        message: `Product not found for tenant: ${tenantId}`,
        code: 'not_found',
      });
    }
  });

  // Mock collections endpoint
  app.get('/store/collections', (req, res) => {
    const tenantId = req.headers['x-tenant-id'] || 'default';

    res.status(200).json({
      collections: [
        {
          id: 'col_01',
          title: 'Collection 1',
          handle: 'collection-1',
          tenant_id: tenantId,
        },
      ],
      count: 1,
      offset: 0,
      limit: 10,
    });
  });

  // Start the server
  const PORT = process.env.PORT || 9000;
  app.listen(PORT, () => {
    console.log(`Mock Medusa server is running on port ${PORT}`);
  });
}

start().catch(error => {
  console.error('Failed to start server:', error);
  process.exit(1);
});
