# Medusa API Endpoints Documentation for ONDC Seller Platform

This document provides a comprehensive overview of all Medusa API endpoints available in the ONDC Seller Platform, indicating which ones are currently used and which are unused.

## Table of Contents

1. [Store API Endpoints](#1-store-api-endpoints)
2. [Admin API Endpoints](#2-admin-api-endpoints)
3. [API Usage Status](#3-api-usage-status)
4. [Implementation Details](#4-implementation-details)
5. [Recommendations for Future Development](#5-recommendations-for-future-development)

## 1. Store API Endpoints

### Auth

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/store/auth` | Customer Login (JWT) | Used |
| POST   | `/store/auth/session` | Customer Login (session) | Unused |
| GET    | `/store/auth/email-exists` | Check if Email Exists | Unused |
| GET    | `/store/auth/me` | Get Current Customer | Used |
| DELETE | `/store/auth` | Customer Logout | Used |

### Carts

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/store/carts` | Create a Cart | Used |
| GET    | `/store/carts/:cart_id` | Get a Cart | Used |
| POST   | `/store/carts/:cart_id` | Update a Cart | Unused |
| POST   | `/store/carts/:cart_id/items` | Add a Line Item | Used |
| POST   | `/store/carts/:cart_id/items/:item_id` | Update a Line Item | Unused |
| DELETE | `/store/carts/:cart_id/items/:item_id` | Delete a Line Item | Unused |
| POST   | `/store/carts/:cart_id/shipping-address` | Add Shipping Address | Used |
| POST   | `/store/carts/:cart_id/billing-address` | Add Billing Address | Used |
| POST   | `/store/carts/:cart_id/shipping-methods` | Add Shipping Method | Unused |
| POST   | `/store/carts/:cart_id/complete` | Complete a Cart | Used |
| POST   | `/store/carts/:cart_id/payment-sessions` | Create Payment Session | Unused |
| POST   | `/store/carts/:cart_id/payment-sessions/:session_id/refresh` | Refresh Payment Session | Unused |
| DELETE | `/store/carts/:cart_id/payment-sessions/:session_id` | Delete Payment Session | Unused |
| POST   | `/store/carts/:cart_id/taxes` | Calculate Cart Taxes | Unused |
| DELETE | `/store/carts/:cart_id/discounts/:code` | Remove Discount | Unused |

### Customers

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/store/customers` | Create a Customer | Unused |
| GET    | `/store/customers/:customer_id` | Get a Customer | Unused |
| PUT    | `/store/customers/:customer_id` | Update a Customer | Unused |
| POST   | `/store/customers/password-reset` | Request Password Reset | Unused |
| POST   | `/store/customers/password-reset/:token` | Reset Password | Unused |
| GET    | `/store/customers/:customer_id/orders` | List Orders | Unused |
| GET    | `/store/customers/:customer_id/payment-methods` | Get Saved Payment Methods | Unused |
| POST   | `/store/customers/:customer_id/addresses` | Add Shipping Address | Unused |
| PUT    | `/store/customers/:customer_id/addresses/:address_id` | Update Shipping Address | Unused |
| DELETE | `/store/customers/:customer_id/addresses/:address_id` | Delete Shipping Address | Unused |

### Products

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/store/products` | List Products | Used |
| GET    | `/store/products/search?q=:query` | Search Products | Unused |
| GET    | `/store/products/:product_id` | Get a Product | Used |
| GET    | `/store/products/:product_id/variants` | Get Product Variants | Unused |
| GET    | `/store/variants/:variant_id` | Get a Product Variant | Unused |
| GET    | `/store/product-tags` | List Product Tags | Unused |
| GET    | `/store/product-categories` | List Product Categories | Unused |
| GET    | `/store/product-categories/:category_id` | Get a Product Category | Unused |
| GET    | `/store/collections` | List Collections | Used |
| GET    | `/store/collections/:collection_id` | Get a Collection | Used |
| GET    | `/store/product-types` | List Product Types | Unused |

### Orders

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/store/orders` | Create an Order | Used |
| GET    | `/store/orders/:order_id` | Get an Order | Unused |
| GET    | `/store/orders/lookup?cart_id=:cart_id` | Look up Order by Cart | Unused |
| GET    | `/store/orders/cart/:cart_id` | Get Order by Cart ID | Unused |
| POST   | `/store/orders/:order_id/claim` | Claim Order | Unused |
| POST   | `/store/orders/:order_id/claim/verify` | Verify Order Claim | Unused |

### Other Store Endpoints

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/store/gift-cards/:code` | Get Gift Card by Code | Unused |
| GET    | `/store/regions` | List Regions | Unused |
| GET    | `/store/regions/:region_id` | Get a Region | Unused |
| POST   | `/store/returns` | Create Return | Unused |
| GET    | `/store/return-reasons` | List Return Reasons | Unused |
| GET    | `/store/return-reasons/:reason_id` | Get a Return Reason | Unused |
| GET    | `/store/shipping-options` | Get Shipping Options | Unused |
| GET    | `/store/shipping-options?cart_id=:cart_id` | List Shipping Options for Cart | Unused |
| POST   | `/store/swaps` | Create a Swap | Unused |
| GET    | `/store/swaps?cart_id=:cart_id` | Get by Cart ID | Unused |
| GET    | `/store/swaps/:swap_id` | Get a Swap | Unused |

## 2. Admin API Endpoints

### Auth

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/admin/auth` | Get Current User | Unused |
| POST   | `/admin/auth` | User Login | Unused |
| POST   | `/admin/auth/jwt` | User Login (JWT) | Unused |
| DELETE | `/admin/auth` | User Logout | Unused |

### Products

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/admin/products` | Create a Product | Used |
| GET    | `/admin/products` | List Products | Unused |
| GET    | `/admin/products/:id` | Get a Product | Unused |
| POST   | `/admin/products/:id` | Update a Product | Used |
| DELETE | `/admin/products/:id` | Delete a Product | Used |

### Orders

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| GET    | `/admin/orders` | List Orders | Used |
| GET    | `/admin/orders/:id` | Get Order | Used |
| PUT    | `/admin/orders/:id` | Update Order | Used |
| DELETE | `/admin/orders/:id` | Cancel Order | Used |
| POST   | `/admin/orders/:id/fulfillments` | Create Fulfillment | Used |
| POST   | `/admin/orders/:id/fulfillments/:fulfillment_id/ship` | Ship Fulfillment | Unused |

### Inventory

| Method | Endpoint | Description | Usage Status |
|--------|----------|-------------|--------------|
| POST   | `/admin/inventory` | Create Inventory Item | Unused |
| GET    | `/admin/inventory` | List Inventory Items | Unused |
| GET    | `/admin/inventory-levels` | List Inventory Levels | Unused |
| POST   | `/admin/inventory/locations/:location_id/levels` | Create Location Level | Unused |
| GET    | `/admin/inventory/:id` | Get Inventory Item | Unused |
| PUT    | `/admin/inventory/:id` | Update Inventory Item | Used |
| DELETE | `/admin/inventory/:id` | Delete Inventory Item | Unused |

## 3. API Usage Status

### Currently Used Endpoints

The ONDC Seller Platform currently uses the following Medusa API endpoints:

1. **Authentication**
   - `POST /store/auth` - Used for customer authentication
   - `GET /store/auth/me` - Used to fetch current customer information
   - `DELETE /store/auth` - Used for customer logout

2. **Products**
   - `GET /store/products` - Used to fetch product listings
   - `GET /store/products/:product_id` - Used to fetch product details
   - `POST /admin/products` - Used to create products
   - `POST /admin/products/:id` - Used to update products
   - `DELETE /admin/products/:id` - Used to delete products

3. **Cart & Checkout**
   - `POST /store/carts` - Used to create a cart
   - `GET /store/carts/:cart_id` - Used to fetch cart details
   - `POST /store/carts/:cart_id/items` - Used to add items to cart
   - `POST /store/carts/:cart_id/shipping-address` - Used to add shipping address
   - `POST /store/carts/:cart_id/billing-address` - Used to add billing address
   - `POST /store/carts/:cart_id/complete` - Used to complete checkout

4. **Orders**
   - `POST /store/orders` - Used to create orders
   - `GET /admin/orders` - Used to list orders
   - `GET /admin/orders/:id` - Used to get order details
   - `PUT /admin/orders/:id` - Used to update orders
   - `DELETE /admin/orders/:id` - Used to cancel orders
   - `POST /admin/orders/:id/fulfillments` - Used to create fulfillments

5. **Collections**
   - `GET /store/collections` - Used to fetch product collections
   - `GET /store/collections/:collection_id` - Used to fetch collection details

6. **Inventory**
   - `PUT /admin/inventory/:id` - Used to update inventory

### Unused Endpoints

Many Medusa API endpoints remain unused, including:

1. **Advanced Cart Features**
   - Payment sessions management
   - Shipping methods
   - Discounts
   - Taxes

2. **Customer Management**
   - All customer endpoints except authentication

3. **Product Variants & Categories**
   - Product variants
   - Product tags
   - Product categories
   - Product types

4. **Returns & Swaps**
   - All returns and swaps endpoints

5. **Admin Features**
   - Batch jobs
   - Currencies
   - Customer groups
   - Discounts
   - Draft orders
   - Gift cards
   - Invites
   - Notes
   - Notifications
   - Order edits

## 4. Implementation Details

### Multi-Tenancy Support

The ONDC Seller Platform implements multi-tenancy with Medusa by adding the `x-tenant-id` header to all API requests:

```javascript
// Add tenant ID to all requests
medusaClient.client.hooks.beforeRequest.push((options) => {
  const tenantId = getCurrentTenantId()
  options.headers = options.headers || {}
  options.headers['x-tenant-id'] = tenantId
  console.log('Adding tenant ID to request:', tenantId)
  return options
})
```

### Error Handling and Retries

The platform implements robust error handling and retry mechanisms for API calls:

```javascript
export const fetchProducts = async (params?: ProductQueryParams): Promise<ProductResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.get('/store/products', { params });
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error fetching products:', apiError);
      throw apiError;
    }
  });
};
```

## 5. Recommendations for Future Development

1. **Implement Advanced Cart Features**
   - Add support for payment sessions, shipping methods, and discounts
   - Implement tax calculation

2. **Enhance Customer Management**
   - Implement customer profile management
   - Add address book functionality
   - Implement order history

3. **Utilize Product Variants & Categories**
   - Implement product variant selection
   - Add category browsing and filtering
   - Use product tags for improved search

4. **Add Returns & Swaps Functionality**
   - Implement order returns process
   - Add product swap functionality

5. **Leverage Admin Features**
   - Implement discount management
   - Add gift card functionality
   - Utilize order editing capabilities
