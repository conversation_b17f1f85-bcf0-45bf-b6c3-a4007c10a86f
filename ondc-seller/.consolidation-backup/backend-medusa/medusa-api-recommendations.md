# Medusa API Usage Recommendations for ONDC Seller Platform

This document provides recommendations for improving the usage of Medusa API endpoints in the ONDC Seller Platform.

## Current Status

The ONDC Seller Platform currently uses Medusa primarily for:

1. **Product Management**: Fetching, creating, updating, and deleting products
2. **Cart & Checkout**: Basic cart creation, adding items, and completing checkout
3. **Order Management**: Creating and managing orders
4. **Collections**: Fetching product collections

However, many powerful Medusa features remain unused, including:

1. **Advanced Cart Features**: Payment sessions, shipping methods, discounts, taxes
2. **Customer Management**: Customer profiles, addresses, order history
3. **Product Variants & Categories**: Variant selection, category browsing, product tags
4. **Returns & Swaps**: Order returns, product swaps
5. **Admin Features**: Discount management, gift cards, order editing

## Recommendations

### 1. Implement Advanced Cart Features

**Current Implementation**:
```javascript
// Basic cart creation and item addition
async createCart() {
  try {
    const response = await medusaAPI.post('/store/carts');
    return response.data;
  } catch (error) {
    console.error('Error creating cart:', error);
    throw error;
  }
},

async addToCart(cartId, item) {
  try {
    const response = await medusaAPI.post(`/store/carts/${cartId}/line-items`, item);
    return response.data;
  } catch (error) {
    console.error('Error adding to cart:', error);
    throw error;
  }
}
```

**Recommended Implementation**:
```javascript
// Enhanced cart with payment sessions, shipping methods, and discounts
const cartApi = {
  create: () => medusaApi.post('/store/carts'),
  
  get: (cartId) => medusaApi.get(`/store/carts/${cartId}`),
  
  addItem: (cartId, item) => 
    medusaApi.post(`/store/carts/${cartId}/line-items`, item),
  
  updateItem: (cartId, itemId, item) => 
    medusaApi.post(`/store/carts/${cartId}/line-items/${itemId}`, item),
  
  removeItem: (cartId, itemId) => 
    medusaApi.delete(`/store/carts/${cartId}/line-items/${itemId}`),
  
  addShippingMethod: (cartId, shippingMethod) => 
    medusaApi.post(`/store/carts/${cartId}/shipping-methods`, shippingMethod),
  
  createPaymentSession: (cartId) => 
    medusaApi.post(`/store/carts/${cartId}/payment-sessions`),
  
  selectPaymentSession: (cartId, providerId) => 
    medusaApi.post(`/store/carts/${cartId}/payment-sessions/${providerId}/select`),
  
  applyDiscount: (cartId, code) => 
    medusaApi.post(`/store/carts/${cartId}/discounts`, { code }),
  
  removeDiscount: (cartId, code) => 
    medusaApi.delete(`/store/carts/${cartId}/discounts/${code}`),
  
  calculateTaxes: (cartId) => 
    medusaApi.post(`/store/carts/${cartId}/taxes`),
  
  complete: (cartId) => 
    medusaApi.post(`/store/carts/${cartId}/complete`),
};
```

### 2. Enhance Customer Management

**Current Implementation**:
```javascript
// Basic authentication only
export const authApi = {
  login: (email: string, password: string) => 
    medusaApi.post('/store/auth', { email, password }),
  logout: () => 
    medusaApi.delete('/store/auth'),
  getSession: () => 
    medusaApi.get('/store/auth'),
};
```

**Recommended Implementation**:
```javascript
// Enhanced customer management
export const customerApi = {
  // Authentication
  login: (email, password) => 
    medusaApi.post('/store/auth', { email, password }),
  logout: () => 
    medusaApi.delete('/store/auth'),
  getSession: () => 
    medusaApi.get('/store/auth'),
  
  // Customer management
  create: (customerData) => 
    medusaApi.post('/store/customers', customerData),
  get: (customerId) => 
    medusaApi.get(`/store/customers/${customerId}`),
  update: (customerId, customerData) => 
    medusaApi.put(`/store/customers/${customerId}`, customerData),
  
  // Password reset
  requestPasswordReset: (email) => 
    medusaApi.post('/store/customers/password-reset', { email }),
  resetPassword: (token, password) => 
    medusaApi.post(`/store/customers/password-reset/${token}`, { password }),
  
  // Orders
  getOrders: (customerId) => 
    medusaApi.get(`/store/customers/${customerId}/orders`),
  
  // Addresses
  addAddress: (customerId, address) => 
    medusaApi.post(`/store/customers/${customerId}/addresses`, address),
  updateAddress: (customerId, addressId, address) => 
    medusaApi.put(`/store/customers/${customerId}/addresses/${addressId}`, address),
  deleteAddress: (customerId, addressId) => 
    medusaApi.delete(`/store/customers/${customerId}/addresses/${addressId}`),
  
  // Payment methods
  getPaymentMethods: (customerId) => 
    medusaApi.get(`/store/customers/${customerId}/payment-methods`),
};
```

### 3. Utilize Product Variants & Categories

**Current Implementation**:
```javascript
// Basic product fetching
export const productsApi = {
  getAll: (params?: ProductQueryParams) => fetchProducts(params),
  getById: (id: string) => medusaApi.get(`/store/products/${id}`),
  // ...
};
```

**Recommended Implementation**:
```javascript
// Enhanced product API with variants, categories, and tags
export const productsApi = {
  // Products
  getAll: (params) => medusaApi.get('/store/products', { params }),
  getById: (id) => medusaApi.get(`/store/products/${id}`),
  search: (query) => medusaApi.get(`/store/products/search?q=${query}`),
  
  // Variants
  getVariants: (productId) => 
    medusaApi.get(`/store/products/${productId}/variants`),
  getVariantById: (variantId) => 
    medusaApi.get(`/store/variants/${variantId}`),
  
  // Categories
  getCategories: () => 
    medusaApi.get('/store/product-categories'),
  getCategoryById: (categoryId) => 
    medusaApi.get(`/store/product-categories/${categoryId}`),
  getProductsByCategory: (categoryId, params) => 
    medusaApi.get('/store/products', { 
      params: { 
        category_id: categoryId,
        ...params 
      } 
    }),
  
  // Tags
  getTags: () => 
    medusaApi.get('/store/product-tags'),
  getProductsByTag: (tagId, params) => 
    medusaApi.get('/store/products', { 
      params: { 
        tags: tagId,
        ...params 
      } 
    }),
  
  // Collections
  getCollections: () => 
    medusaApi.get('/store/collections'),
  getCollectionById: (collectionId) => 
    medusaApi.get(`/store/collections/${collectionId}`),
  getProductsByCollection: (collectionId, params) => 
    medusaApi.get('/store/products', { 
      params: { 
        collection_id: collectionId,
        ...params 
      } 
    }),
  
  // Types
  getTypes: () => 
    medusaApi.get('/store/product-types'),
  getProductsByType: (typeId, params) => 
    medusaApi.get('/store/products', { 
      params: { 
        type_id: typeId,
        ...params 
      } 
    }),
};
```

### 4. Implement Returns & Swaps Functionality

**Current Implementation**:
No implementation for returns and swaps.

**Recommended Implementation**:
```javascript
// Returns and swaps API
export const returnsApi = {
  // Returns
  create: (returnData) => 
    medusaApi.post('/store/returns', returnData),
  
  // Return reasons
  getReasons: () => 
    medusaApi.get('/store/return-reasons'),
  getReasonById: (reasonId) => 
    medusaApi.get(`/store/return-reasons/${reasonId}`),
};

export const swapsApi = {
  create: (swapData) => 
    medusaApi.post('/store/swaps', swapData),
  getByCartId: (cartId) => 
    medusaApi.get(`/store/swaps?cart_id=${cartId}`),
  getById: (swapId) => 
    medusaApi.get(`/store/swaps/${swapId}`),
};
```

### 5. Leverage Admin Features

**Current Implementation**:
```javascript
// Basic admin API usage
export const ordersApi = {
  getAll: () => medusaApi.get('/admin/orders'),
  getById: (id: string) => medusaApi.get(`/admin/orders/${id}`),
  update: (id: string, data: any) => medusaApi.post(`/admin/orders/${id}`, data),
  fulfill: (id: string) => medusaApi.post(`/admin/orders/${id}/fulfillment`),
  cancel: (id: string) => medusaApi.post(`/admin/orders/${id}/cancel`),
};
```

**Recommended Implementation**:
```javascript
// Enhanced admin API
export const adminApi = {
  // Orders
  orders: {
    getAll: (params) => medusaApi.get('/admin/orders', { params }),
    getById: (id) => medusaApi.get(`/admin/orders/${id}`),
    update: (id, data) => medusaApi.post(`/admin/orders/${id}`, data),
    cancel: (id) => medusaApi.post(`/admin/orders/${id}/cancel`),
    
    // Fulfillment
    createFulfillment: (id, fulfillmentData) => 
      medusaApi.post(`/admin/orders/${id}/fulfillments`, fulfillmentData),
    shipFulfillment: (id, fulfillmentId) => 
      medusaApi.post(`/admin/orders/${id}/fulfillments/${fulfillmentId}/ship`),
    
    // Claims
    createClaim: (id, claimData) => 
      medusaApi.post(`/admin/orders/${id}/claims`, claimData),
    createClaimFulfillment: (id, claimId, fulfillmentData) => 
      medusaApi.post(`/admin/orders/${id}/claims/${claimId}/fulfillments`, fulfillmentData),
    shipClaimFulfillment: (id, claimId, fulfillmentId) => 
      medusaApi.post(`/admin/orders/${id}/claims/${claimId}/fulfillments/${fulfillmentId}/ship`),
    
    // Returns
    requestReturn: (id, returnData) => 
      medusaApi.post(`/admin/orders/${id}/returns`, returnData),
    cancelReturn: (id, returnId) => 
      medusaApi.post(`/admin/orders/${id}/returns/${returnId}/cancel`),
    
    // Refunds
    createRefund: (id, refundData) => 
      medusaApi.post(`/admin/orders/${id}/refunds`, refundData),
    
    // Payments
    capturePayment: (id, paymentId) => 
      medusaApi.post(`/admin/orders/${id}/payments/${paymentId}/capture`),
    refundPayment: (id, paymentId, refundData) => 
      medusaApi.post(`/admin/orders/${id}/payments/${paymentId}/refund`, refundData),
  },
  
  // Order edits
  orderEdits: {
    getAll: () => medusaApi.get('/admin/order-edits'),
    create: (data) => medusaApi.post('/admin/order-edits', data),
    getById: (id) => medusaApi.get(`/admin/order-edits/${id}`),
    update: (id, data) => medusaApi.put(`/admin/order-edits/${id}`, data),
    confirm: (id) => medusaApi.post(`/admin/order-edits/${id}/confirm`),
    cancel: (id) => medusaApi.post(`/admin/order-edits/${id}/cancel`),
    delete: (id) => medusaApi.delete(`/admin/order-edits/${id}`),
    
    // Line items
    addLineItem: (id, itemData) => 
      medusaApi.post(`/admin/order-edits/${id}/line-items`, itemData),
    updateLineItem: (id, changeId, itemData) => 
      medusaApi.post(`/admin/order-edits/${id}/line-items/${changeId}`, itemData),
    deleteLineItem: (id, changeId) => 
      medusaApi.delete(`/admin/order-edits/${id}/line-items/${changeId}`),
  },
  
  // Discounts
  discounts: {
    create: (data) => medusaApi.post('/admin/discounts', data),
    getAll: () => medusaApi.get('/admin/discounts'),
    getById: (id) => medusaApi.get(`/admin/discounts/${id}`),
    update: (id, data) => medusaApi.put(`/admin/discounts/${id}`, data),
    delete: (id) => medusaApi.delete(`/admin/discounts/${id}`),
    
    // Conditions
    createCondition: (discountId, conditionData) => 
      medusaApi.post(`/admin/discounts/${discountId}/conditions`, conditionData),
    getCondition: (discountId, conditionId) => 
      medusaApi.get(`/admin/discounts/${discountId}/conditions/${conditionId}`),
    updateCondition: (discountId, conditionId, conditionData) => 
      medusaApi.put(`/admin/discounts/${discountId}/conditions/${conditionId}`, conditionData),
    deleteCondition: (discountId, conditionId) => 
      medusaApi.delete(`/admin/discounts/${discountId}/conditions/${conditionId}`),
    
    // Dynamic codes
    createDynamicCode: (discountId, codeData) => 
      medusaApi.post(`/admin/discounts/${discountId}/codes`, codeData),
    deleteDynamicCode: (discountId, codeId) => 
      medusaApi.delete(`/admin/discounts/${discountId}/codes/${codeId}`),
    getByCode: (code) => 
      medusaApi.get(`/admin/discounts/code/${code}`),
  },
};
```

## Implementation Priority

1. **High Priority**
   - Implement advanced cart features (payment sessions, shipping methods)
   - Enhance product browsing with categories and variants
   - Improve order management with fulfillment tracking

2. **Medium Priority**
   - Implement customer management features
   - Add returns and swaps functionality
   - Enhance admin features for order management

3. **Low Priority**
   - Implement discount management
   - Add gift card functionality
   - Implement order editing capabilities

## Conclusion

By implementing these recommendations, the ONDC Seller Platform can fully leverage the capabilities of Medusa Commerce, providing a more robust, efficient, and feature-rich experience for both customers and sellers.
