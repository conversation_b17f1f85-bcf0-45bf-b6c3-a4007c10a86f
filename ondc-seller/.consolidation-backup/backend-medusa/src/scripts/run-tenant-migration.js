const { Client } = require('pg');
require('dotenv').config();

async function createTenantTable() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check if tenant table already exists
    const checkTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'tenant'
      );
    `);

    if (checkTableResult.rows[0].exists) {
      console.log('Tenant table already exists');
      return;
    }

    // Create tenant table
    await client.query(`
      CREATE TABLE "tenant" (
        "id" varchar PRIMARY KEY,
        "name" varchar UNIQUE NOT NULL,
        "description" varchar NULL,
        "is_active" boolean DEFAULT true,
        "metadata" jsonb NULL,
        "created_at" timestamp with time zone DEFAULT now(),
        "updated_at" timestamp with time zone DEFAULT now()
      );
    `);
    console.log('Created tenant table');

    // Add tenant_id column to relevant tables
    const tables = [
      'product',
      'product_variant',
      'product_collection',
      'product_category',
      'product_tag',
      'product_option',
      'product_type',
      'product_image',
    ];

    for (const table of tables) {
      try {
        // Check if table exists
        const tableExistsResult = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = '${table}'
          );
        `);

        if (!tableExistsResult.rows[0].exists) {
          console.log(`Table ${table} does not exist, skipping`);
          continue;
        }

        // Check if tenant_id column already exists
        const columnExistsResult = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = '${table}' 
            AND column_name = 'tenant_id'
          );
        `);

        if (columnExistsResult.rows[0].exists) {
          console.log(`Column tenant_id already exists in table ${table}, skipping`);
          continue;
        }

        // Add tenant_id column
        await client.query(`
          ALTER TABLE "${table}" 
          ADD COLUMN "tenant_id" varchar NULL;
        `);
        console.log(`Added tenant_id column to ${table}`);
      } catch (error) {
        console.error(`Error adding tenant_id to ${table}:`, error);
      }
    }

    // Insert default tenant
    await client.query(`
      INSERT INTO "tenant" ("id", "name", "description", "is_active")
      VALUES ('default', 'Default Tenant', 'Default tenant for the system', true)
      ON CONFLICT (id) DO NOTHING;
    `);
    console.log('Inserted default tenant');

    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Error running migration:', error);
  } finally {
    await client.end();
    console.log('Disconnected from database');
  }
}

createTenantTable().catch(console.error);
