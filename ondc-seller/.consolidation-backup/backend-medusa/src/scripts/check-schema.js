const { Client } = require('pg');
require('dotenv').config();

async function checkSchema() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // List all tables in the database
    const tablesResult = await client.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);

    console.log('Tables in the database:');
    tablesResult.rows.forEach(row => {
      console.log(row.table_name);
    });

    // Check product_variant table schema
    const variantResult = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'product_variant';
    `);

    console.log('\nProduct variant table schema:');
    variantResult.rows.forEach(row => {
      console.log(`${row.column_name}: ${row.data_type}`);
    });

    // Check if price_list table exists
    const priceListExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'price_list'
      );
    `);

    console.log('\nPrice list table exists:', priceListExists.rows[0].exists);

    if (priceListExists.rows[0].exists) {
      // Check price_list table schema
      const priceListResult = await client.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'price_list';
      `);

      console.log('Price list table schema:');
      priceListResult.rows.forEach(row => {
        console.log(`${row.column_name}: ${row.data_type}`);
      });
    }

    // Check if price table exists
    const priceExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'price'
      );
    `);

    console.log('\nPrice table exists:', priceExists.rows[0].exists);

    if (priceExists.rows[0].exists) {
      // Check price table schema
      const priceResult = await client.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'price';
      `);

      console.log('Price table schema:');
      priceResult.rows.forEach(row => {
        console.log(`${row.column_name}: ${row.data_type}`);
      });
    }

    // Check if product_variant_price_set table exists
    const variantPriceSetExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'product_variant_price_set'
      );
    `);

    console.log('\nProduct variant price set table exists:', variantPriceSetExists.rows[0].exists);

    if (variantPriceSetExists.rows[0].exists) {
      // Check product_variant_price_set table schema
      const variantPriceSetResult = await client.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'product_variant_price_set';
      `);

      console.log('Product variant price set table schema:');
      variantPriceSetResult.rows.forEach(row => {
        console.log(`${row.column_name}: ${row.data_type}`);
      });
    }

    // Check if money_amount table exists
    const moneyAmountExists = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'money_amount'
      );
    `);

    console.log('\nMoney amount table exists:', moneyAmountExists.rows[0].exists);

    if (moneyAmountExists.rows[0].exists) {
      // Check money_amount table schema
      const moneyAmountResult = await client.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'money_amount';
      `);

      console.log('Money amount table schema:');
      moneyAmountResult.rows.forEach(row => {
        console.log(`${row.column_name}: ${row.data_type}`);
      });
    }

  } catch (error) {
    console.error('Error checking schema:', error);
  } finally {
    await client.end();
    console.log('Disconnected from database');
  }
}

checkSchema().catch(console.error);
