const { Client } = require('pg');
require('dotenv').config();
const crypto = require('crypto');

async function createAdmin() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check if user table exists
    const checkTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user'
      );
    `);

    if (!checkTableResult.rows[0].exists) {
      console.log('User table does not exist');
      return;
    }

    // Check if admin user already exists
    const checkUserResult = await client.query(`
      SELECT * FROM "user" WHERE email = '<EMAIL>';
    `);

    if (checkUserResult.rows.length > 0) {
      console.log('Admin user already exists');
      return;
    }

    // Generate password hash
    const password = 'admin123';
    const salt = crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(password, salt, 1000, 64, 'sha512').toString('hex');

    // Generate user ID
    const userId = 'usr_' + crypto.randomBytes(9).toString('hex');

    // Insert admin user
    await client.query(`
      INSERT INTO "user" (
        id, 
        email, 
        password_hash, 
        salt, 
        first_name, 
        last_name, 
        role, 
        created_at, 
        updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, NOW(), NOW()
      );
    `, [userId, '<EMAIL>', hash, salt, 'Admin', 'User', 'admin']);

    console.log('Created admin user with email: <EMAIL> and password: admin123');
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await client.end();
    console.log('Disconnected from database');
  }
}

createAdmin().catch(console.error);
