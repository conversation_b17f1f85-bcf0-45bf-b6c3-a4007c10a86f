const { Client } = require('pg');
require('dotenv').config();
const crypto = require('crypto');

async function createPublishableApiKey() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check if publishable_api_key table exists
    const checkTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'publishable_api_key'
      );
    `);

    if (!checkTableResult.rows[0].exists) {
      console.log('Publishable API key table does not exist');
      return;
    }

    // Generate API key ID
    const apiKeyId = 'pk_' + crypto.randomBytes(9).toString('hex');
    
    // Insert publishable API key
    await client.query(`
      INSERT INTO "publishable_api_key" (
        id,
        created_at,
        updated_at,
        created_by,
        revoked_by,
        revoked_at,
        title
      ) VALUES (
        $1,
        NOW(),
        NOW(),
        NULL,
        NULL,
        NULL,
        'Default API Key'
      );
    `, [apiKeyId]);

    console.log('Created publishable API key with ID:', apiKeyId);
    
    // Check if sales_channel table exists
    const checkSalesChannelTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'sales_channel'
      );
    `);
    
    if (checkSalesChannelTableResult.rows[0].exists) {
      // Get default sales channel
      const salesChannelResult = await client.query(`
        SELECT id FROM "sales_channel" LIMIT 1;
      `);
      
      if (salesChannelResult.rows.length > 0) {
        const salesChannelId = salesChannelResult.rows[0].id;
        
        // Check if publishable_api_key_sales_channel table exists
        const checkPkScTableResult = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name = 'publishable_api_key_sales_channel'
          );
        `);
        
        if (checkPkScTableResult.rows[0].exists) {
          // Link publishable API key to sales channel
          await client.query(`
            INSERT INTO "publishable_api_key_sales_channel" (
              sales_channel_id,
              publishable_api_key_id,
              created_at,
              updated_at
            ) VALUES (
              $1,
              $2,
              NOW(),
              NOW()
            );
          `, [salesChannelId, apiKeyId]);
          
          console.log('Linked publishable API key to sales channel:', salesChannelId);
        }
      } else {
        console.log('No sales channel found, creating one...');
        
        // Create sales channel
        const salesChannelId = 'sc_' + crypto.randomBytes(9).toString('hex');
        
        await client.query(`
          INSERT INTO "sales_channel" (
            id,
            created_at,
            updated_at,
            deleted_at,
            name,
            description,
            is_disabled
          ) VALUES (
            $1,
            NOW(),
            NOW(),
            NULL,
            'Default Sales Channel',
            'Default sales channel for the store',
            false
          );
        `, [salesChannelId]);
        
        console.log('Created sales channel with ID:', salesChannelId);
        
        // Link publishable API key to sales channel
        await client.query(`
          INSERT INTO "publishable_api_key_sales_channel" (
            sales_channel_id,
            publishable_api_key_id,
            created_at,
            updated_at
          ) VALUES (
            $1,
            $2,
            NOW(),
            NOW()
          );
        `, [salesChannelId, apiKeyId]);
        
        console.log('Linked publishable API key to sales channel:', salesChannelId);
      }
    }

    console.log('Publishable API key created successfully');
  } catch (error) {
    console.error('Error creating publishable API key:', error);
  } finally {
    await client.end();
    console.log('Disconnected from database');
  }
}

createPublishableApiKey().catch(console.error);
