import { DataSource } from "typeorm"
import { CreateTenantTable1716112000000 } from "../migrations/1716112000000-CreateTenantTable"

const run = async () => {
  const dataSource = new DataSource({
    type: "postgres",
    url: process.env.DATABASE_URL,
    entities: [],
  })

  await dataSource.initialize()

  try {
    console.log("Running CreateTenantTable migration...")
    const migration = new CreateTenantTable1716112000000()
    await migration.up(dataSource.createQueryRunner())
    console.log("CreateTenantTable migration completed successfully")
  } catch (error) {
    console.error("Error running CreateTenantTable migration:", error)
  } finally {
    await dataSource.destroy()
  }
}

run().catch(console.error)
