const { Client } = require('pg');
require('dotenv').config();
const crypto = require('crypto');

async function createTestProducts() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Generate product IDs
    const productId1 = 'prod_' + crypto.randomBytes(9).toString('hex');
    const productId2 = 'prod_' + crypto.randomBytes(9).toString('hex');

    // Generate variant IDs
    const variantId1 = 'variant_' + crypto.randomBytes(9).toString('hex');
    const variantId2 = 'variant_' + crypto.randomBytes(9).toString('hex');

    // Check if product table exists
    const checkProductTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'product'
      );
    `);

    if (!checkProductTableResult.rows[0].exists) {
      console.log('Product table does not exist');
      return;
    }

    // Check if product_variant table exists
    const checkVariantTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'product_variant'
      );
    `);

    if (!checkVariantTableResult.rows[0].exists) {
      console.log('Product variant table does not exist');
      return;
    }

    // Generate unique handles
    const timestamp = Date.now();
    const handle1 = `ondc-test-product-1-${timestamp}`;
    const handle2 = `ondc-test-product-2-${timestamp}`;

    // Insert test products
    await client.query(`
      INSERT INTO "product" (
        id,
        title,
        description,
        handle,
        thumbnail,
        status,
        created_at,
        updated_at,
        tenant_id
      ) VALUES
      ($1, 'ONDC Test Product 1', 'This is a test product for ONDC Seller Platform', $3, 'https://picsum.photos/200', 'published', NOW(), NOW(), 'default'),
      ($2, 'ONDC Test Product 2', 'Another test product for ONDC Seller Platform', $4, 'https://picsum.photos/200', 'published', NOW(), NOW(), 'default');
    `, [productId1, productId2, handle1, handle2]);

    console.log('Created test products with IDs:', productId1, productId2);

    // Insert product variants
    await client.query(`
      INSERT INTO "product_variant" (
        id,
        title,
        product_id,
        manage_inventory,
        created_at,
        updated_at,
        tenant_id
      ) VALUES
      ($1, 'Default Variant', $3, true, NOW(), NOW(), 'default'),
      ($2, 'Default Variant', $4, true, NOW(), NOW(), 'default');
    `, [variantId1, variantId2, productId1, productId2]);

    console.log('Created product variants with IDs:', variantId1, variantId2);

    // Create price list
    const priceListId = 'pl_' + crypto.randomBytes(9).toString('hex');

    // Check if price_list table exists
    const checkPriceListTableResult = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'price_list'
      );
    `);

    if (checkPriceListTableResult.rows[0].exists) {
      await client.query(`
        INSERT INTO "price_list" (
          id,
          title,
          description,
          type,
          status,
          created_at,
          updated_at
        ) VALUES
        ($1, 'Default Price List', 'Default price list for ONDC products', 'sale', 'active', NOW(), NOW());
      `, [priceListId]);

      console.log('Created price list with ID:', priceListId);

      // Create prices for variants
      const price1Id = 'price_' + crypto.randomBytes(9).toString('hex');
      const price2Id = 'price_' + crypto.randomBytes(9).toString('hex');

      // Check if price table exists
      const checkPriceTableResult = await client.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_schema = 'public'
          AND table_name = 'price'
        );
      `);

      if (checkPriceTableResult.rows[0].exists) {
        // Create price sets for variants
        const priceSet1Id = 'ps_' + crypto.randomBytes(9).toString('hex');
        const priceSet2Id = 'ps_' + crypto.randomBytes(9).toString('hex');

        // Check if price_set table exists
        const checkPriceSetTableResult = await client.query(`
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = 'public'
            AND table_name = 'price_set'
          );
        `);

        if (checkPriceSetTableResult.rows[0].exists) {
          // Insert price sets
          await client.query(`
            INSERT INTO "price_set" (
              id,
              created_at,
              updated_at
            ) VALUES
            ($1, NOW(), NOW()),
            ($2, NOW(), NOW());
          `, [priceSet1Id, priceSet2Id]);

          console.log('Created price sets with IDs:', priceSet1Id, priceSet2Id);

          // Check if product_variant_price_set table exists
          const checkVariantPriceSetTableResult = await client.query(`
            SELECT EXISTS (
              SELECT FROM information_schema.tables
              WHERE table_schema = 'public'
              AND table_name = 'product_variant_price_set'
            );
          `);

          if (checkVariantPriceSetTableResult.rows[0].exists) {
            // Generate IDs for product_variant_price_set
            const vpId1 = 'vps_' + crypto.randomBytes(9).toString('hex');
            const vpId2 = 'vps_' + crypto.randomBytes(9).toString('hex');

            // Link price sets to variants
            await client.query(`
              INSERT INTO "product_variant_price_set" (
                id,
                variant_id,
                price_set_id,
                created_at,
                updated_at
              ) VALUES
              ($1, $3, $5, NOW(), NOW()),
              ($2, $4, $6, NOW(), NOW());
            `, [vpId1, vpId2, variantId1, variantId2, priceSet1Id, priceSet2Id]);

            console.log('Linked price sets to variants');
          }

          // Insert prices
          await client.query(`
            INSERT INTO "price" (
              id,
              currency_code,
              amount,
              price_list_id,
              price_set_id,
              raw_amount,
              created_at,
              updated_at
            ) VALUES
            ($1, 'inr', 1000, $3, $5, '{"amount": 1000, "currency_code": "inr"}', NOW(), NOW()),
            ($2, 'inr', 2000, $4, $6, '{"amount": 2000, "currency_code": "inr"}', NOW(), NOW());
          `, [price1Id, price2Id, priceListId, priceListId, priceSet1Id, priceSet2Id]);

          console.log('Created prices for variants');
        } else {
          console.log('Price set table does not exist, skipping');
        }
      } else {
        console.log('Price table does not exist, skipping');
      }
    } else {
      console.log('Price list table does not exist, skipping');
    }

    console.log('Test products created successfully');
  } catch (error) {
    console.error('Error creating test products:', error);
  } finally {
    await client.end();
    console.log('Disconnected from database');
  }
}

createTestProducts().catch(console.error);
