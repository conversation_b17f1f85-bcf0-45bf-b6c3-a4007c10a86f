import { MigrationInterface, QueryRunner, Table, TableColumn } from "typeorm"

export class CreateTenantTable1716112000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: "tenant",
        columns: [
          {
            name: "id",
            type: "varchar",
            isPrimary: true,
          },
          {
            name: "name",
            type: "varchar",
            isUnique: true,
          },
          {
            name: "description",
            type: "varchar",
            isNullable: true,
          },
          {
            name: "is_active",
            type: "boolean",
            default: true,
          },
          {
            name: "metadata",
            type: "jsonb",
            isNullable: true,
          },
          {
            name: "created_at",
            type: "timestamp with time zone",
            default: "now()",
          },
          {
            name: "updated_at",
            type: "timestamp with time zone",
            default: "now()",
          },
        ],
      })
    )

    // Add tenant_id column to all relevant tables
    const tables = [
      "product",
      "product_variant",
      "product_collection",
      "product_category",
      "product_tag",
      "product_option",
      "product_type",
      "product_image",
    ]

    for (const table of tables) {
      try {
        const exists = await queryRunner.hasTable(table)
        if (exists) {
          await queryRunner.addColumn(
            table,
            new TableColumn({
              name: "tenant_id",
              type: "varchar",
              isNullable: true,
            })
          )
        }
      } catch (error) {
        console.log(`Error adding tenant_id to ${table}:`, error)
      }
    }

    // Insert default tenant
    await queryRunner.query(`
      INSERT INTO "tenant" ("id", "name", "description", "is_active")
      VALUES ('default', 'Default Tenant', 'Default tenant for the system', true)
    `)
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Remove tenant_id column from all relevant tables
    const tables = [
      "product",
      "product_variant",
      "product_collection",
      "product_category",
      "product_tag",
      "product_option",
      "product_type",
      "product_image",
    ]

    for (const table of tables) {
      try {
        const exists = await queryRunner.hasTable(table)
        if (exists) {
          await queryRunner.dropColumn(table, "tenant_id")
        }
      } catch (error) {
        console.log(`Error removing tenant_id from ${table}:`, error)
      }
    }

    await queryRunner.dropTable("tenant")
  }
}
