import { EntityManager } from "typeorm"

type TenantServiceProps = {
  manager: EntityManager
}

class TenantService {
  protected manager_: EntityManager
  protected transactionManager_: EntityManager | undefined

  constructor({ manager }: TenantServiceProps) {
    this.manager_ = manager
  }

  /**
   * Get the current tenant ID from the request
   * @param req - Express request object
   * @returns The current tenant ID
   */
  getCurrentTenantId(req: any): string {
    const tenantId = req.tenantId || process.env.DEFAULT_TENANT_ID || 'default'
    return tenantId
  }

  /**
   * Validate that the tenant ID is valid
   * @param tenantId - The tenant ID to validate
   * @throws Error if the tenant ID is invalid
   */
  validateTenantId(tenantId: string): void {
    if (!tenantId) {
      throw new Error("Tenant ID is required")
    }
  }

  /**
   * Add tenant ID to query options
   * @param options - Query options
   * @param tenantId - Tenant ID
   * @returns Query options with tenant ID
   */
  withTenantId(options: Record<string, any>, tenantId: string): Record<string, any> {
    return {
      ...options,
      where: {
        ...(options.where || {}),
        tenant_id: tenantId,
      },
    }
  }
}

export default TenantService
