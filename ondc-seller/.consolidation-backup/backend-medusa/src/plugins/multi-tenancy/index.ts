import { MedusaContainer } from "@medusajs/medusa/dist/types/global";

type PluginOptions = {
  enabled?: boolean;
  defaultTenantId?: string;
};

export default async (container: MedusaContainer, options: PluginOptions) => {
  const enabled = options.enabled || false;
  const defaultTenantId = options.defaultTenantId || "default";

  if (!enabled) {
    console.log("Multi-tenancy plugin is disabled");
    return;
  }

  console.log(`Multi-tenancy plugin is enabled with default tenant ID: ${defaultTenantId}`);

  // Add tenant ID to all requests
  const app = container.resolve("expressApp");
  
  app.use((req, res, next) => {
    const tenantId = req.headers["x-tenant-id"] || defaultTenantId;
    req.tenantId = tenantId;
    next();
  });

  // Add tenant ID to all database queries
  const entityManager = container.resolve("manager");
  
  // Add tenant ID to all entities that have a tenant_id column
  entityManager.connection.subscribers.push({
    name: "TenantSubscriber",
    
    beforeInsert(event) {
      if (event.metadata.columns.find(column => column.propertyName === "tenant_id")) {
        event.entity.tenant_id = event.queryRunner.data?.tenantId || defaultTenantId;
      }
    },
    
    beforeUpdate(event) {
      if (event.metadata.columns.find(column => column.propertyName === "tenant_id")) {
        event.entity.tenant_id = event.queryRunner.data?.tenantId || defaultTenantId;
      }
    },
    
    beforeFind(event) {
      if (event.metadata.columns.find(column => column.propertyName === "tenant_id")) {
        event.query.where = {
          ...event.query.where,
          tenant_id: event.queryRunner.data?.tenantId || defaultTenantId,
        };
      }
    },
  });
};
