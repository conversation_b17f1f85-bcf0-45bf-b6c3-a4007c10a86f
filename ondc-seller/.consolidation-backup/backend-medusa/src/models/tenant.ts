import {
  BeforeInsert,
  Column,
  Entity,
  Index,
  PrimaryColumn,
  CreateDateColumn,
  UpdateDateColumn
} from "typeorm"
import { generateEntityId } from "@medusajs/medusa/dist/utils"

@Entity()
export class Tenant {
  @PrimaryColumn()
  id: string

  @Index({ unique: true })
  @Column()
  name: string

  @Column({ nullable: true })
  description: string | null

  @Column({ default: true })
  is_active: boolean

  @Column({ type: "jsonb", nullable: true })
  metadata: Record<string, unknown> | null

  @CreateDateColumn({ type: "timestamptz" })
  created_at: Date

  @UpdateDateColumn({ type: "timestamptz" })
  updated_at: Date

  @BeforeInsert()
  private beforeInsert(): void {
    this.id = generateEntityId(this.id, "tenant")
  }
}

export default Tenant
