import { Repository } from "typeorm"
import { Tenant } from "../models/tenant"

export class TenantRepository extends Repository<Tenant> {
  public async findByName(name: string): Promise<Tenant | undefined> {
    const tenant = await this.findOne({
      where: { name },
    })
    return tenant || undefined
  }

  public async findById(id: string): Promise<Tenant | undefined> {
    const tenant = await this.findOne({
      where: { id: id },
    })
    return tenant || undefined
  }
}

export default TenantRepository
