# Medusa API Implementation Examples for ONDC Seller Platform

This document provides detailed examples of how Medusa API endpoints are implemented in the ONDC Seller Platform.

## Table of Contents

1. [Store API Implementation Examples](#1-store-api-implementation-examples)
2. [Admin API Implementation Examples](#2-admin-api-implementation-examples)
3. [Multi-Tenancy Implementation](#3-multi-tenancy-implementation)
4. [Error Handling and Retries](#4-error-handling-and-retries)

## 1. Store API Implementation Examples

### Authentication

**Login (JWT)**

```javascript
// Authentication API
export const authApi = {
  login: (email: string, password: string) => 
    medusaApi.post('/store/auth', { email, password }),
  logout: () => 
    medusaApi.delete('/store/auth'),
  getSession: () => 
    medusaApi.get('/store/auth'),
};

// Usage example
const handleLogin = async (email, password) => {
  try {
    const response = await authApi.login(email, password);
    const { customer, token } = response.data;
    
    // Store token in localStorage
    localStorage.setItem('medusa_token', token);
    
    return customer;
  } catch (error) {
    console.error('Login failed:', error);
    throw error;
  }
};
```

### Products

**List Products**

```javascript
// Products API
export const productsApi = {
  getAll: (params?: ProductQueryParams) => fetchProducts(params),
  getById: (id: string) => medusaApi.get(`/store/products/${id}`),
  // ...
};

// Implementation with error handling and retries
export const fetchProducts = async (params?: ProductQueryParams): Promise<ProductResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.get('/store/products', { params });
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error fetching products:', apiError);
      throw apiError;
    }
  });
};

// Usage example
const ProductList = () => {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadProducts = async () => {
      try {
        const response = await productsApi.getAll({ limit: 10 });
        setProducts(response.products);
      } catch (error) {
        console.error('Failed to load products:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadProducts();
  }, []);
  
  // Render products...
};
```

### Cart & Checkout

**Create Cart and Add Items**

```javascript
// Medusa API Methods
export const medusaClient = {
  // ...
  
  /**
   * Create a cart
   * @returns {Promise<Object>} Cart data
   */
  async createCart() {
    try {
      const response = await medusaAPI.post('/store/carts');
      return response.data;
    } catch (error) {
      console.error('Error creating cart:', error);
      throw error;
    }
  },

  /**
   * Add item to cart
   * @param {string} cartId - Cart ID
   * @param {Object} item - Item to add
   * @returns {Promise<Object>} Updated cart
   */
  async addToCart(cartId, item) {
    try {
      const response = await medusaAPI.post(`/store/carts/${cartId}/line-items`, item);
      return response.data;
    } catch (error) {
      console.error('Error adding to cart:', error);
      throw error;
    }
  }
};

// Usage example
const handleAddToCart = async (productId, variantId, quantity) => {
  try {
    // Get existing cart or create a new one
    let cartId = localStorage.getItem('cart_id');
    
    if (!cartId) {
      const cartResponse = await medusaClient.createCart();
      cartId = cartResponse.cart.id;
      localStorage.setItem('cart_id', cartId);
    }
    
    // Add item to cart
    const item = {
      variant_id: variantId,
      quantity: quantity
    };
    
    const response = await medusaClient.addToCart(cartId, item);
    return response.cart;
  } catch (error) {
    console.error('Failed to add item to cart:', error);
    throw error;
  }
};
```

**Complete Checkout**

```javascript
// Implementation in ONDC integration
export const syncOrdersToMedusa = async (ondcOrders) => {
  try {
    // Transform ONDC orders to Medusa format and create them in Medusa
    for (const order of ondcOrders) {
      try {
        // Create a cart in Medusa
        const cartResponse = await medusaApi.post('/store/carts');
        const cartId = cartResponse.data.cart.id;
        
        // Add items to cart
        for (const item of order.items) {
          await medusaApi.post(`/store/carts/${cartId}/line-items`, {
            variant_id: item.variant_id,
            quantity: item.quantity,
          });
        }
        
        // Add shipping and billing address
        if (order.shipping_address) {
          await medusaApi.post(`/store/carts/${cartId}/shipping-address`, {
            address: order.shipping_address,
          });
        }
        
        if (order.billing_address) {
          await medusaApi.post(`/store/carts/${cartId}/billing-address`, {
            address: order.billing_address,
          });
        }
        
        // Complete cart and create order
        await medusaApi.post(`/store/carts/${cartId}/complete`);
      } catch (error) {
        console.error(`Error syncing order ${order.id} to Medusa:`, error);
      }
    }
    
    return { success: true, message: 'Orders synced successfully', count: ondcOrders.length };
  } catch (error) {
    console.error('Error syncing orders to Medusa:', error);
    return { success: false, message: 'Failed to sync orders', error };
  }
};
```

### Collections

**Get Collections**

```javascript
// Medusa API Methods
export const medusaClient = {
  // ...
  
  /**
   * Get all collections
   * @returns {Promise<Object>} Collections data
   */
  async getCollections() {
    return this.get('collections');
  },

  /**
   * Get a collection by ID
   * @param {string} id - Collection ID
   * @returns {Promise<Object>} Collection data
   */
  async getCollection(id) {
    return this.get(`collections/${id}`);
  },
};

// Usage example
const CollectionsPage = () => {
  const [collections, setCollections] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadCollections = async () => {
      try {
        const response = await medusaClient.getCollections();
        setCollections(response.collections);
      } catch (error) {
        console.error('Failed to load collections:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadCollections();
  }, []);
  
  // Render collections...
};
```

## 2. Admin API Implementation Examples

### Products Management

**Create, Update, and Delete Products**

```javascript
// Products API
export const productsApi = {
  // ...
  create: (data: any) => medusaApi.post('/admin/products', data),
  update: (id: string, data: any) => medusaApi.post(`/admin/products/${id}`, data),
  delete: (id: string) => medusaApi.delete(`/admin/products/${id}`),
};

// Usage example
const handleCreateProduct = async (productData) => {
  try {
    const response = await productsApi.create(productData);
    return response.data.product;
  } catch (error) {
    console.error('Failed to create product:', error);
    throw error;
  }
};

const handleUpdateProduct = async (id, productData) => {
  try {
    const response = await productsApi.update(id, productData);
    return response.data.product;
  } catch (error) {
    console.error(`Failed to update product ${id}:`, error);
    throw error;
  }
};

const handleDeleteProduct = async (id) => {
  try {
    await productsApi.delete(id);
    return true;
  } catch (error) {
    console.error(`Failed to delete product ${id}:`, error);
    throw error;
  }
};
```

### Orders Management

**List and Update Orders**

```javascript
// Orders API
export const ordersApi = {
  getAll: () => medusaApi.get('/admin/orders'),
  getById: (id: string) => medusaApi.get(`/admin/orders/${id}`),
  update: (id: string, data: any) => medusaApi.post(`/admin/orders/${id}`, data),
  fulfill: (id: string) => medusaApi.post(`/admin/orders/${id}/fulfillment`),
  cancel: (id: string) => medusaApi.post(`/admin/orders/${id}/cancel`),
};

// Usage example
const OrdersManagementPage = () => {
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    const loadOrders = async () => {
      try {
        const response = await ordersApi.getAll();
        setOrders(response.data.orders);
      } catch (error) {
        console.error('Failed to load orders:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadOrders();
  }, []);
  
  const handleFulfillOrder = async (orderId) => {
    try {
      await ordersApi.fulfill(orderId);
      // Update orders list
      const response = await ordersApi.getAll();
      setOrders(response.data.orders);
    } catch (error) {
      console.error(`Failed to fulfill order ${orderId}:`, error);
    }
  };
  
  // Render orders...
};
```

### Inventory Management

**Update Inventory**

```javascript
// Inventory API
export const inventoryApi = {
  update: (id: string, data: InventoryUpdateData) => updateInventory(id, data),
  getById: (id: string) => medusaApi.get(`/admin/inventories/${id}`),
};

/**
 * Update inventory in the Medusa API
 * @param inventoryId Inventory ID to update
 * @param updateData Inventory update data
 * @returns Promise with typed inventory response
 */
export const updateInventory = async (
  inventoryId: string,
  updateData: InventoryUpdateData
): Promise<InventoryLevelResponse> => {
  return retryWithBackoff(async () => {
    try {
      const response = await medusaApi.post(`/admin/inventories/${inventoryId}`, updateData);
      return response.data;
    } catch (error) {
      const apiError = handleApiError(error);
      console.error('Error updating inventory:', apiError);
      throw apiError;
    }
  });
};

// Usage example
const handleUpdateInventory = async (inventoryId, quantity) => {
  try {
    const updateData = {
      inventory_quantity: quantity
    };
    
    const response = await inventoryApi.update(inventoryId, updateData);
    return response.inventory;
  } catch (error) {
    console.error(`Failed to update inventory ${inventoryId}:`, error);
    throw error;
  }
};
```

## 3. Multi-Tenancy Implementation

**Adding Tenant ID to All Requests**

```javascript
// Using Medusa JS Client
export const getMedusaClient = (): Medusa => {
  if (!medusaClient) {
    medusaClient = new Medusa({
      baseUrl: MEDUSA_BACKEND_URL,
      maxRetries: 3,
      publishableApiKey: PUBLISHABLE_API_KEY,
    })

    // Add tenant ID to all requests
    medusaClient.client.hooks.beforeRequest.push((options) => {
      const tenantId = getCurrentTenantId()
      options.headers = options.headers || {}
      options.headers['x-tenant-id'] = tenantId
      console.log('Adding tenant ID to request:', tenantId)
      return options
    })
  }

  return medusaClient
}

// Using Axios
// Add request interceptor for authentication and multi-tenancy
const addAuthAndTenantHeaders = (config: AxiosRequestConfig) => {
  // Get tenant ID from context or default
  const tenantId = getCurrentTenantId();
  
  // Set the tenant ID header
  config.headers = config.headers || {};
  config.headers['x-tenant-id'] = tenantId;

  // Authentication logic...
  
  return config;
};

// Add request interceptor to API instance
medusaApi.interceptors.request.use(addAuthAndTenantHeaders, error => Promise.reject(error));
```

## 4. Error Handling and Retries

**Implementing Retry with Backoff**

```javascript
/**
 * Retry a function with exponential backoff
 * @param fn Function to retry
 * @param maxRetries Maximum number of retries
 * @param baseDelay Base delay in milliseconds
 * @returns Promise with the result of the function
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries = 3,
  baseDelay = 300
): Promise<T> => {
  let retries = 0;
  
  const execute = async (): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      if (retries >= maxRetries) {
        throw error;
      }
      
      const delay = baseDelay * Math.pow(2, retries);
      console.log(`Retrying after ${delay}ms...`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      retries++;
      
      return execute();
    }
  };
  
  return execute();
};

/**
 * Handle API errors
 * @param error Error object
 * @returns Standardized API error
 */
export const handleApiError = (error: any): ApiError => {
  if (error.response) {
    // The request was made and the server responded with a status code
    // that falls out of the range of 2xx
    return {
      status: error.response.status,
      message: error.response.data.message || 'An error occurred',
      data: error.response.data,
    };
  } else if (error.request) {
    // The request was made but no response was received
    return {
      status: 0,
      message: 'No response received from server',
      data: null,
    };
  } else {
    // Something happened in setting up the request that triggered an Error
    return {
      status: 0,
      message: error.message || 'Unknown error',
      data: null,
    };
  }
};
```
