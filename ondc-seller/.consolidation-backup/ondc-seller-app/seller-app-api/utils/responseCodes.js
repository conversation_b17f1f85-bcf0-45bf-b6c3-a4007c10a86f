export const responseCodes = [
    {code : 30000,	type:"Seller App",message :	"Invalid request error" , description : "Generic invalid request error"},
    {code : 30001,	type:"Seller App",message :	"Provider not found" , description : "When Seller App is unable to find the provider id sent by the Buyer App"},
    {code : 30002,	type:"Seller App",message :	"Provider location not found" , description : "When Seller App is unable to find the provider location id sent by the Buyer App"},
    {code : 30003,	type:"Seller App",message :	"Provider category not found" , description : "When Seller App is unable to find the provider category id sent by the Buyer App"},
    {code : 30004,	type:"Seller App",message :	"Item not found" , description : "When Seller App is unable to find the item id sent by the Buyer App"},
    {code : 30005,	type:"Seller App",message :	"Invalid return request" , description : "Return reason is invalid"},
    {code : 30006,	type:"Seller App",message :	"Offer code invalid" , description : "Offer code is not valid anymore"},
    {code : 30007,	type:"Seller App",message :	"Offer fulfillment error" , description : "Offer cannot be fulfilled at this time"},
    {code : 30008,	type:"Seller App",message :	"Location Serviceability error" , description : "Pickup location not serviceable by Logistics Provider"},
    {code : 30009,	type:"Seller App",message :	"Location Serviceability error" , description : "Dropoff location not serviceable by Logistics Provider"},
    {code : 30010,	type:"Seller App",message :	"Location Serviceability error" , description : "Delivery distance exceeds the maximum serviceability distance"},
    {code : 30011,	type:"Seller App",message :	"Order Serviceability error" , description : "Delivery Partners not available"},
    {code : 30012,	type:"Seller App",message :	"Invalid cancellation request" , description : "Cancellation reason is invalid"},
    {code : 30013,	type:"Seller App",message :	"Invalid Fulfillment TAT" , description : "Fulfillment TAT is different from what was quoted earlier"},
    {code : 30014,	type:"Seller App",message :	"Cancellation unacceptable" , description : "Cancellation request is rejected as fulfillment TAT is not breached"},
    {code : 30015,	type:"Seller App",message :	"Invalid rating value" , description : "When the Seller App receives an invalid value as the rating value in value"},
    {code : 30016,	type:"Seller App",message :	"Invalid Signature" , description : "Cannot verify signature for request"},
    {code : 30017,	type:"Seller App",message :	"Merchant unavailable" , description : "Merchant is currently not taking orders"},
    {code : 30018,	type:"Seller App",message :	"Invalid Order" , description : "Order not found"},
    {code : 30019,	type:"Seller App",message :	"Order Confirm Error" , description : "Seller App is unable to confirm the order"},
    {code : 30020,	type:"Seller App",message :	"Order Confirm Failure" , description : "Seller App cannot confirm order as no response from Buyer App"},
    {code : 30021,	type:"Seller App",message :	"Merchant Inactive" , description : "Merchant is inactive"},
    {code : 30022,	type:"Seller App",message :	"Stale Request" , description : "Cannot process stale request"},
    {code : 30023,	type:"Seller App",message :	"Minimum order value error" , description : "Cart value is less than minimum order value"},
    {code : 40000,	type:"Seller App",message :	"Business Error" , description : "Generic business error"},
    {code : 40001,	type:"Seller App",message :	"Action not applicable" , description : "When an API endpoint is not implemented by the Seller App as it is not required for their use cases and a Buyer App calls one of these endpoints"},
    {code : 40002,	type:"Seller App",message :	"Item quantity unavailable" , description : "When the Seller App is unable to fulfill the required quantity for items in the order"},
    {code : 40003,	type:"Seller App",message :	"Quote unavailable" , description : "When the quote sent by the Buyer App is no longer available from the Seller App"},
    {code : 40004,	type:"Seller App",message :	"Payment not supported" , description : "When the payment object sent by the Buyer App is not supported by the Seller App"},
    {code : 40005,	type:"Seller App",message :	"Tracking not enabled" , description : "Tracking not enabled for any fulfillment in the order"},
    {code : 40006,	type:"Seller App",message :	"Fulfilment agent unavailable" , description : "When an agent for fulfilment is not available"},
    {code : 40007,	type:"Seller App",message :	"Finder fee not acceptable" , description : "Buyer finder fee is not acceptable"},
    {code : 40008,	type:"Seller App",message :	"Version not supported" , description : "Request version not supported"},
    {code : 50000,	type:"Seller App",message :	"Policy Error" , description : "Generic Policy Error"},
    {code : 50001,	type:"Seller App",message :	"Cancellation not possible" , description : "When the Seller App is unable to cancel the order due to it's cancellation policy"},
    {code : 50002,	type:"Seller App",message :	"Updation not possible" , description : "When the Seller App is unable to update the order due to it's updation policy"},
    {code : 50003,	type:"Seller App",message :	"Unsupported rating category" , description : "When the Seller App receives an entity to rate which is not supported"},
    {code : 50004,	type:"Seller App",message :	"Support unavailable" , description : "When the Seller App receives an object if for which it does not provide support"},
    {code : 50005,	type:"Seller App",message :	"Terms and Conditions unacceptable" , description : "Buyer App terms and conditions not acceptable to Seller App"},
    {code : 50006,	type:"Seller App",message :	"Order terminated" , description : "Order terminated as Buyer App did not accept terms proposed by Seller App"},
];