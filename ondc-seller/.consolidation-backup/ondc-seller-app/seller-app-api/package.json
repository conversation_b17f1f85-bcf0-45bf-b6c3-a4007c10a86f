{"name": "seller-api", "version": "2.0", "description": "ONDC", "main": "index.js", "author": "ONDC", "license": "ONDC", "scripts": {"start": "nodemon --exec babel-node ./bin/www", "build": "npx babel . --out-dir dist --ignore \"node_modules/*\" --copy-files", "serve": "node ./dist/bin/www"}, "dependencies": {"@hapi/joi": "^17.1.1", "aws-sdk": "^2.860.0", "axios": "^0.19.0", "bcryptjs": "^2.4.3", "body-parser": "^1.18.3", "clean": "^4.0.2", "cookie-parser": "~1.4.3", "cors": "^2.8.5", "debug": "~2.6.9", "email-templates": "^5.0.4", "express": "~4.16.0", "firebase-admin": "^11.0.1", "fs": "0.0.1-security", "ip": "^1.1.5", "jsonwebtoken": "^8.4.0", "lodash": "^4.17.11", "moment": "^2.24.0", "morgan": "~1.9.0", "mysql": "^2.17.1", "mysql2": "^1.6.5", "nconf": "^0.10.0", "node-cache": "^5.1.0", "node-rest-client": "^3.1.0", "nodemailer": "^6.0.0", "otp-generator": "^2.0.0", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "path": "^0.12.7", "pg": "^8.0.0", "pug": "^2.0.3", "regenerator-runtime": "^0.13.9", "request-promise": "^4.2.6", "sequelize": "^5.0.0", "uuid": "^3.4.0", "winston": "^3.5.1"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/node": "^7.8.7", "@babel/preset-env": "^7.9.6", "js-beautify": "^1.8.9", "nodemon": "^1.18.9"}, "engines": {"node": ">=16.0.0"}, "lint-staged": {"*.js": "eslint --cache --fix"}}