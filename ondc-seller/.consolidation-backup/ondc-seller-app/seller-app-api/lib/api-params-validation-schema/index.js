import authSchema from './authentication.validation.schema';
import userSchema from './user.validation.schema';
import labSchema from './lab.validation.schema';
import processSchema from './process.validation.schema';
import localTestMethodCodeSchema from './localTestMethodCode.validation.schema';
import testMethodCodeSchema from './testMethodCode.validation.schema';
import methodDescriptorSchema from './methodDescriptor.validation.schema';
import unitSchema from './unit.validation.schema';
import controlLotSchema from './controlLot.validation.schema';
import localAttributeSchema from './localAttribute.validation.schema';
import productSchema from './product.validation.schema';


exports.authSchema = authSchema;
exports.userSchema = userSchema;
exports.labSchema = labSchema;
exports.processSchema = processSchema;
exports.localTestMethodCodeSchema = localTestMethodCodeSchema;
exports.testMethodCodeSchema = testMethodCodeSchema;
exports.methodDescriptorSchema = methodDescriptorSchema;
exports.localAttributeSchema = localAttributeSchema;
exports.controlLotSchema = controlLotSchema;
exports.unitSchema = unitSchema;
exports.productSchema = productSchema;
