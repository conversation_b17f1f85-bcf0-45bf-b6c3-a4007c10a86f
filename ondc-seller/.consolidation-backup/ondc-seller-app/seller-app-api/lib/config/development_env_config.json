{"express": {"protocol": "http://", "useFqdnForApis": false, "ipAddress": "localhost", "fqdn": "", "port": 3001, "apiUrl": ""}, "auth": {"token": {"access": {"exp": 8640000, "secret": "wftd3hg5$g67h*fd5h6fbvcy6rtg5wftd3hg5$g67h*fd5xxx"}, "resetPasswordLink": {"exp": 86400, "secret": "de$rfdf5$g67*jhu*sdfbvcy3frd6r4e"}}}, "database": {"username": "akshay", "password": "", "name": "sellerapp", "host": "localhost", "port": "5432", "dialect": "postgres", "pool": {"max": 60, "min": 0, "acquire": 1000000, "idle": 10000, "evict": 10000}}, "email": {"transport": {"SMTP": {"host": "", "port": 587, "secure": false, "auth": {"user": "", "pass": ""}}, "local": {"sendmail": true, "newline": "unix", "path": "/usr/sbin/sendmail"}}, "sender": "<EMAIL>", "supportEmail": "<EMAIL>", "webClientUri": "http://************:3000"}, "cors": {"whitelistUrls": ["http://localhost:3000", "https://localhost:3000"]}, "directory": {"profilePictures": "PROFILE_PICTURES"}, "cookieOptions": {"httpOnly": true, "secure": false, "sameSite": false}, "general": {"exceptionEmailRecipientList": []}, "seller": {"serverUrl": "http://localhost:3019"}, "firebase": {"account": ""}, "sellerConfig": {"BPP_ID": "sellerapp-staging.datasyndicate.in", "BPP_URI": "https://7e3b-2401-4900-1c5d-2b13-814b-de08-9df3-b44e.ngrok-free.app", "BAP_ID": "sellerapp-staging.datasyndicate.in", "BAP_URI": "https://7e3b-2401-4900-1c5d-2b13-814b-de08-9df3-b44e.ngrok-free.app", "LOGISTICS_BAP_ID": "dev-ondc.shiprocket.in", "LOGISTICS_DELIVERY_TYPE": "Same Day Delivery", "storeOpenSchedule": {"time": {"days": "1,2,3,4,5,6,7", "schedule": {"holidays": ["2022-08-15", "2022-08-19"], "frequency": "PT4H", "times": ["1100", "1900"]}, "range": {"start": "1100", "end": "2100"}}}, "sellerPickupLocation": {"person": {"name": "<PERSON><PERSON>"}, "location": {"gps": "12.938382, 77.651775", "address": {"area_code": "560087", "name": "Fritoburger", "building": "12 Restaurant Tower", "locality": "JP Nagar 24th Main", "city": "Bengaluru", "state": "Karnataka", "country": "India"}}, "contact": {"phone": "98860 98860", "email": "<EMAIL>"}}}, "settlement_details": [{"settlement_counterparty": "buyer-app", "settlement_type": "upi", "upi_address": "gft@oksbi", "settlement_bank_account_no": "XXXXXXXXXX", "settlement_ifsc_code": "XXXXXXXXX"}]}