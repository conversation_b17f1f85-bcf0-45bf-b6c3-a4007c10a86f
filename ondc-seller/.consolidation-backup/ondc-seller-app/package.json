{"name": "@ondc-seller/ondc-seller-app", "version": "0.0.1", "private": true, "description": "ONDC Seller App integrated with Medusa Commerce", "scripts": {"start:seller": "cd seller && npm start", "start:api": "cd seller-app-api && npm start", "start:notifications": "cd notifications && npm start", "dev:seller": "cd seller && npm start", "dev:api": "cd seller-app-api && npm start", "dev:notifications": "cd notifications && npm start", "install:all": "npm run install:seller && npm run install:api && npm run install:notifications", "install:seller": "cd seller && npm install", "install:api": "cd seller-app-api && npm install", "install:notifications": "cd notifications && npm install", "build:seller": "cd seller && npm run build", "build:api": "cd seller-app-api && npm run build", "build:notifications": "cd notifications && npm run build", "build": "npm run build:seller && npm run build:api && npm run build:notifications", "dev": "concurrently \"npm run dev:seller\" \"npm run dev:api\" \"npm run dev:notifications\"", "start": "concurrently \"npm run start:seller\" \"npm run start:api\" \"npm run start:notifications\""}, "dependencies": {"concurrently": "^7.6.0"}}