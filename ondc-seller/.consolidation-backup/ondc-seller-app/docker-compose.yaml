version: '3'
services:
  strapi:
    build:
      context: .
      dockerfile: strapiDocker
    environment:
      DATABASE_CLIENT: postgres
      DATABASE_NAME: strapi
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: strapi
      DATABASE_PASSWORD: strapi
      DATABASE_SCHEMA: public
    ports:
      - '1337:1337'
    depends_on:
      - postgres

  seller:
    build:
      context: .
      dockerfile: sellerApiDocker
    environment:
      FIREBASE_SERVICE_ACCOUNT_JSON_PATH: lib/config/firebase-service-account.json
      STRAPI_SERVER_URL: http://strapi:1337
      DATABASE_NAME_SELLER_CLIENT: sellerapp
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_USERNAME: strapi
      DATABASE_PASSWORD: strapi
      NODE_ENV: production
      STRAPI_API_TOKEN: b716212a84567e1870bba07df14ab55a4b771fed217eb7b25dee9116df58835b11dece5c04555fc0c1c84de2673f0a3db16e4e045c76de9f00b9f6f8a8fbaa60138b7a3ade0a26c1753f96fb25a3faf0aaa0e92ceff4d40fc0a00dd613b5fda11562e40b68954f44355914149c572b60dac8e55742369bf9b0e8e10d13bb4262
    ports:
      - '3001:3001'
    depends_on:
      - strapi

  postgres:
    image: postgres
    environment:
      POSTGRES_DB: sellerApp
      POSTGRES_USER: strapi
      POSTGRES_PASSWORD: strapi
    volumes:
      - ./data:/var/lib/postgresql/data
