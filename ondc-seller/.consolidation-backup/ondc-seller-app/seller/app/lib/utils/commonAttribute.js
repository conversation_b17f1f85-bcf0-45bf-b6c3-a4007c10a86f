exports.templateAttributeKeys = {
    beautyandpersonalcare: [
        'color', 'colorName',
        'gender', 'concern',
        'ingredient', 'conscious',
        'preference', 'formulation',
        'skinType', 'brand'
    ],
    electronics: [
        'brand', 'model',
        'modelYear', 'color',
        'colorName', 'ram',
        'ramUnit', 'rom',
        'romUnit', 'storage',
        'storageUnit', 'storageType',
        'screenSize', 'primaryCamera',
        'secondaryCamera', 'cpu',
        'gpu', 'batteryCapacity',
        'osType', 'osVersion',
        'connectivity', 'formFactor',
        'compatibleDevices', 'specialFeature',
        'includes', 'weight',
        'length', 'breadth',
        'height', 'refurbrished'
    ],
    healthandwellness: [
        'brand', 'prescriptionRequired',
        'usageInstruction', 'remarks'
    ],
    homeandkitchen: [
        'brand', 'color',
        'colorName', 'material',
        'size', 'weight',
        'length', 'breadth',
        'height', 'model',
        'assemblyRequired', 'careInstructions',
        'specialFeatures'
    ],
    fashion: [
        'gender', 'color',
        'size', 'brand',
        'sizeChart', 'fabric',
        'strapMaterial', 'waterResistant',
        'display', 'glassMaterial',
        'colorName', 'sportType',
        'baseMetal', 'plating',
        'careInstructions', 'washType',
        'weight', 'length',
        'breadth', 'height',
        'features', 'fabricFinish',
        'material', 'materialFinish',
        'pattern', 'occasion',
        'season', 'trend', 'fit',
        'collar', 'neck', 'bundles',
        'maxSaleQuantity', 'hemline',
        'lining', 'numPockets', 'reversible',
        'bottomType', 'topType', 'frontStyling',
        'sleeveLength', 'sweatshirtType',
        'fragile', 'liquid', 'hazardous',
        'powerType', 'batteryLife', 'bluetooth',
        'callFunction', 'heartRateMonitor',
        'pedometer', 'sleepMonitor', 'spo2Monitor',
        'warrenty', 'buckleMaterial', 'buckleType',
        'waistRise', 'socksLength', 'footwearType',
        'insole', 'soleMaterial', 'toeShape', 'outsole',
        'fastenType', 'midsole', 'backpackStyle',
        'closureType', 'stoneType', 'gemType',
        'dialShape', 'frameType', 'frameShape',
        'frameColor', 'frameSize', 'frameMaterial',
        'frameStyle', 'faceShape', 'lensMaterial',
        'lensColor', 'laptopCompartment',
        'strapType', 'volume', 'lockType',
        'ornamentation', 'coverage', 'padding',
        'seam', 'waistBand', 'sustainability',
        'handcrafted', 'craftmark',
    ]
};