{"name": "seller", "version": "2.0", "description": "ONDC", "main": "index.js", "author": "ONDC", "license": "ONDC", "scripts": {"start": "nodemon --exec babel-node ./app/server.js --trace-deprecation", "cron": "nodemon --exec babel-node ./bin/cron.js", "build": "npx babel . --out-dir dist --ignore \"node_modules/*\" --copy-files", "dropall": "babel-node ./bin/dropAllDbs.js", "serve": "node ./dist/bin/www", "prepare": "cd .. && husky install", "pretest": "cross-env NODE_ENV=test npm run dropall", "test": "cross-env NODE_ENV=test nyc --reporter text-summary --reporter json-summary --reporter json --reporter co<PERSON><PERSON> --reporter lcov mocha --require @babel/register --timeout 10000"}, "dependencies": {"@jsreport/jsreport-chrome-pdf": "^3.1.0", "@jsreport/jsreport-core": "^3.4.2", "@jsreport/jsreport-ejs": "^3.0.0", "@jsreport/jsreport-html-to-xlsx": "^3.2.0", "aws-sdk": "^2.1043.0", "axios": "^0.24.0", "bcryptjs": "^2.4.3", "body-parser": "^1.18.3", "bull": "^4.5.5", "cookie-parser": "~1.4.3", "cors": "^2.8.5", "debug": "~2.6.9", "dotenv": "16.0.0", "email-templates": "^8.0.8", "express": "^4.17.1", "express-mung": "^0.5.1", "fs": "0.0.1-security", "get-routes": "2.1.5", "handlebars": "^4.7.7", "hpp": "^0.2.3", "ip": "^1.1.5", "joi": "^17.6.0", "jsonwebtoken": "^8.4.0", "moment": "^2.24.0", "moment-timezone": "^0.5.34", "mongodb": "^4.1.4", "mongoose": "^6.0.12", "morgan": "~1.9.0", "multer": "^1.4.5-lts.1", "nconf": "^0.10.0", "node-cache": "^5.1.2", "node-cron": "^3.0.0", "node-fetch": "^2.6.9", "nodemailer": "^6.7.5", "passport": "^0.4.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "path": "^0.12.7", "regenerator-runtime": "^0.13.9", "short-unique-id": "^4.4.4", "uuidv4": "^6.2.13", "winston": "^3.2.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.6", "@babel/node": "^7.8.7", "@babel/preset-env": "^7.9.6", "chai": "^4.3.4", "cross-env": "^7.0.3", "eslint": "^8.2.0", "husky": "^7.0.4", "js-beautify": "^1.8.9", "lint-staged": "^12.1.5", "mocha": "^9.1.3", "nock": "^13.2.1", "nodemon": "^2.0.15", "npm-run-all": "^4.1.5", "nyc": "^15.1.0", "sinon": "^13.0.1", "supertest": "^6.2.0"}, "engines": {"node": ">=16.0.0"}, "lint-staged": {"*.js": "eslint --cache --fix"}}