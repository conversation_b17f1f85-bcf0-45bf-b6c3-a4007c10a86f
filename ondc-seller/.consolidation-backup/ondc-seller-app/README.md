# ONDC Seller App Integration

This package integrates the official ONDC seller application with our Medusa backend in the Turborepo monorepo structure.

## Overview

The ONDC seller app consists of three main components:

1. **Seller Frontend**: A web application for sellers to manage their products, orders, and settings
2. **Seller API**: A backend API that handles ONDC protocol integration and business logic
3. **Notifications Service**: A service for handling real-time notifications

## Integration with Medusa

This integration connects the ONDC seller app with our Medusa Commerce backend, allowing:

- Product synchronization between Medusa and ONDC
- Order management across both platforms
- Unified seller experience

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm (v8 or later)
- MongoDB (for the ONDC seller app)
- PostgreSQL (for Medusa)
- Redis (for caching and pub/sub)

### Installation

1. Install dependencies for all components:

```bash
npm run install:all
```

2. Configure environment variables:

Copy the example environment file and update it with your configuration:

```bash
cp .env.example .env
```

3. Start the development server:

```bash
npm run dev
```

This will start all three components concurrently.

## Configuration

### Environment Variables

- `MEDUSA_URL`: URL of the Medusa backend (default: http://localhost:9000)
- `MEDUSA_API_KEY`: API key for the Medusa backend
- `ONDC_PARTICIPANT_ID`: Your ONDC participant ID
- `ONDC_SUBSCRIBER_ID`: Your ONDC subscriber ID
- `ONDC_SUBSCRIBER_URL`: Your ONDC subscriber URL
- `ONDC_REGISTRY_URL`: ONDC registry URL
- `ONDC_AUTH_SIGNING_KEY`: Your ONDC auth signing key
- `ONDC_ENCRYPTION_PUBLIC_KEY`: Your ONDC encryption public key
- `ONDC_ENCRYPTION_PRIVATE_KEY`: Your ONDC encryption private key

## Usage

### Syncing Products

Products from Medusa are automatically synced to the ONDC network through the seller app API.

### Managing Orders

Orders received through the ONDC network are synced to Medusa for unified order management.

### Notifications

Real-time notifications for new orders, status updates, and other events are handled by the notifications service.

## Development

### Directory Structure

```
packages/ondc-seller-app/
├── seller/              # Seller frontend application
├── seller-app-api/      # Seller backend API
├── notifications/       # Notifications service
├── .env                 # Environment variables
└── package.json         # Package configuration
```

### Building

```bash
npm run build
```

### Testing

```bash
npm run test
```

## Deployment

### Building for Production

```bash
npm run build
```

### Starting the Production Server

```bash
npm run start
```

## Troubleshooting

### Common Issues

- **API Connection Issues**: Make sure the Medusa backend is running and the `MEDUSA_URL` environment variable is set correctly.
- **Authentication Issues**: Verify your ONDC credentials in the environment variables.
- **MongoDB Connection Issues**: Ensure MongoDB is running and accessible.
