// import { permissions as perms } from '@wemotive-poc/seraph';

exports.EMAIL_TEMPLATES = {
    FORGOT_PASSWORD: 'FORGOT_PASSWORD',
    SIGN_UP: 'SIGN_UP',
    EXCEPTION_EMAIL: 'EXCEPTION_EMAIL',
    ACCOUNT_LOCKED_ORG_ADMIN:'ACCOUNT_LOCKED_ORG_ADMIN',
    ACCOUNT_LOCKED:'ACCOUNT_LOCKED'
};

exports.DEVICE_TYPE = {
    IOS_TYPE_ID: '********-0000-0000-0000-********5002',
    ANDROID_TYPE_ID: '********-0000-0000-0000-************',
};

exports.HEADERS = {
    ACCESS_TOKEN: 'access-token',
    AUTH_TOKEN: 'Authorization',
};

const ACCESS_TYPES = {
    PRIVATE: 'PRIVATE',
    PUBLIC: 'PUBLIC',
};

exports.ACCESS_TYPES = ACCESS_TYPES;


