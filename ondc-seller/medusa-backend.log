
> @ondc-seller/backend@0.0.1 dev
> medusa develop

[32minfo[39m:    Watching filesystem to reload dev server on file change
redisUrl not found. A fake redis instance will be used.
[32minfo[39m:    No link to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-fulfillment-manual/.medusa/server/src/links. skipped.
[32minfo[39m:    No link to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-payment-manual/.medusa/server/src/links. skipped.
[32minfo[39m:    No link to load from /home/<USER>/Git Projects/ondc/ondc-seller/packages/backend/src/links. skipped.
[33mwarn[39m:    Local Event Bus installed. This is not recommended for production.
[32minfo[39m:    No workflow to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-fulfillment-manual/.medusa/server/src/workflows. skipped.
[32minfo[39m:    No workflow to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-payment-manual/.medusa/server/src/workflows. skipped.
[32minfo[39m:    No workflow to load from /home/<USER>/Git Projects/ondc/ondc-seller/packages/backend/src/workflows. skipped.
[32minfo[39m:    No subscriber to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-payment-manual/.medusa/server/src/subscribers. skipped.
[32minfo[39m:    No subscriber to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-fulfillment-manual/.medusa/server/src/subscribers. skipped.
[32minfo[39m:    No subscriber to load from /home/<USER>/Git Projects/ondc/ondc-seller/packages/backend/src/subscribers. skipped.
[32minfo[39m:    No job to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-fulfillment-manual/.medusa/server/src/jobs. skipped.
[32minfo[39m:    No job to load from /home/<USER>/Git Projects/ondc/ondc-seller/packages/backend/node_modules/@medusajs/medusa/dist/jobs. skipped.
[32minfo[39m:    No job to load from /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/medusa-payment-manual/.medusa/server/src/jobs. skipped.
[32minfo[39m:    No job to load from /home/<USER>/Git Projects/ondc/ondc-seller/packages/backend/src/jobs. skipped.
- Creating server
✔ Server is ready on port: 9000 – 266ms
[32minfo[39m:    Admin URL → http://localhost:9000/app
