#!/bin/bash

# Script to start all required services for ONDC Seller Platform

# Set the base directory
BASE_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$BASE_DIR"

# Function to check if a port is in use
is_port_in_use() {
  lsof -i:"$1" >/dev/null 2>&1
  return $?
}

# Function to kill processes using a specific port
kill_port() {
  echo "Killing processes using port $1..."
  fuser -k "$1"/tcp >/dev/null 2>&1 || true
}

# Kill any existing processes
echo "Killing any existing processes..."
kill_port 9001  # Medusa backend/admin
kill_port 3000  # Frontend
kill_port 6277  # MCP server proxy
kill_port 6279  # MCP server proxy (alternate port)
kill_port 6274  # MCP inspector
kill_port 1337  # Strapi CMS

# Wait for ports to be released
sleep 2

# Start Medusa backend
echo "Starting Medusa backend..."
cd "$BASE_DIR/packages/backend"
npm run dev > "$BASE_DIR/medusa-backend.log" 2>&1 &
MEDUSA_PID=$!

# Wait for Medusa backend to start
echo "Waiting for Medusa backend to start..."
sleep 10

# Check if Medusa backend is running
if ! ps -p $MEDUSA_PID > /dev/null; then
  echo "Failed to start Medusa backend. Check medusa-backend.log for details."
  exit 1
fi

echo "Medusa backend started with PID: $MEDUSA_PID"

# Start MCP server
echo "Starting MCP server..."
cd "$BASE_DIR/packages/medusa-mcp"
npm run dev:mcp-proxy > "$BASE_DIR/mcp-server.log" 2>&1 &
MCP_PID=$!

# Wait for MCP server to start
echo "Waiting for MCP server to start..."
sleep 5

# Check if MCP server is running
if ! ps -p $MCP_PID > /dev/null; then
  echo "Failed to start MCP server. Check mcp-server.log for details."
  kill $MEDUSA_PID
  exit 1
fi

echo "MCP server started with PID: $MCP_PID"

# Start frontend
echo "Starting frontend..."
cd "$BASE_DIR/packages/frontend"
npm run dev > "$BASE_DIR/frontend.log" 2>&1 &
FRONTEND_PID=$!

# Wait for frontend to start
echo "Waiting for frontend to start..."
sleep 10

# Check if frontend is running
if ! ps -p $FRONTEND_PID > /dev/null; then
  echo "Failed to start frontend. Check frontend.log for details."
  kill $MEDUSA_PID $MCP_PID
  exit 1
fi

echo "Frontend started with PID: $FRONTEND_PID"

# Print URLs
echo ""
echo "Services are running:"
echo "- Medusa backend/admin: http://localhost:9001"
echo "- Medusa admin panel: http://localhost:9001/app"
echo "- Frontend: http://localhost:3000"
echo "- MCP Inspector: http://localhost:6274"
echo "- MCP Proxy: http://localhost:6279"
echo ""
echo "Log files:"
echo "- Medusa backend: $BASE_DIR/medusa-backend.log"
echo "- MCP server: $BASE_DIR/mcp-server.log"
echo "- Frontend: $BASE_DIR/frontend.log"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user to press Ctrl+C
trap "echo 'Stopping all services...'; kill $MEDUSA_PID $MCP_PID $FRONTEND_PID; exit 0" INT
wait
