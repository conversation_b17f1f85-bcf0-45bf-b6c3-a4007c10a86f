
> @ondc-seller/frontend@0.0.1 dev
> next dev

   ▲ Next.js 14.0.4
   - Local:        http://localhost:3000
   - Environments: .env.local, .env

npm error code ENOWORKSPACES
npm error This command does not support workspaces.
npm error A complete log of this run can be found in: /home/<USER>/.npm/_logs/2025-06-05T04_52_44_008Z-debug-0.log
 ✓ Ready in 5.8s
 ○ Compiling / ...
The user aborted a request.

Retrying 1/3...
 ✓ Compiled / in 14.1s (784 modules)
 ○ Compiling /not-found ...
 ✓ Compiled /not-found in 3.8s (764 modules)
 ⨯ The requested resource isn't a valid image for /images/hero-bg.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/women-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/kids-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/men-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-react-infinity.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-court-vision.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-air-zoom.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-zoom-fly.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled /[slug] in 237ms (772 modules)
 ○ Compiling /admin ...
 ✓ Compiled /admin in 3.2s (1902 modules)
 ✓ Compiled /[slug] in 179ms (855 modules)
 ✓ Compiled (860 modules)
 ○ Compiling /admin/products/new ...
 ✓ Compiled /admin/products/new in 3.6s (2894 modules)
 ⨯ The requested resource isn't a valid image for /images/collections/kids-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/hero-bg.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/women-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/men-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-react-infinity.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-court-vision.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-zoom-fly.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-air-zoom.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/kids-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/hero-bg.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/women-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/collections/men-shoes.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-react-infinity.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-court-vision.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-zoom-fly.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/products/nike-air-zoom.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled in 1595ms (2900 modules)
 ✓ Compiled /not-found in 268ms (1348 modules)
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled in 714ms (2903 modules)
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled in 807ms (2903 modules)
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled in 590ms (2903 modules)
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled in 965ms (2903 modules)
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled in 639ms (2903 modules)
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ⨯ The requested resource isn't a valid image for /images/placeholder.jpg received text/html; charset=utf-8
 ✓ Compiled in 879ms (2901 modules)
 ✓ Compiled in 692ms (2898 modules)
 ⚠ Fast Refresh had to perform a full reload. Read more: https://nextjs.org/docs/messages/fast-refresh-reload
 ⨯ app/page.tsx (32:11) @ Image
 ⨯ ReferenceError: Image is not defined
    at HomePage (./app/page.tsx:37:111)
[0m [90m 30 |[39m       [33m<[39m[33msection[39m className[33m=[39m[32m"relative bg-gray-900 text-white"[39m[33m>[39m[0m
[0m [90m 31 |[39m         [33m<[39m[33mdiv[39m className[33m=[39m[32m"absolute inset-0"[39m[33m>[39m[0m
[0m[31m[1m>[22m[39m[90m 32 |[39m           [33m<[39m[33mImage[39m[0m
[0m [90m    |[39m           [31m[1m^[22m[39m[0m
[0m [90m 33 |[39m             src[33m=[39m[32m"/images/hero-bg.jpg"[39m[0m
[0m [90m 34 |[39m             alt[33m=[39m[32m"Hero Background"[39m[0m
[0m [90m 35 |[39m             fill[0m
 ○ Compiling /not-found ...
 ✓ Compiled /not-found in 603ms (1349 modules)
 ✓ Compiled (1352 modules)
 ⨯ ./app/page.tsx:6:0
Module not found: Can't resolve '@/components/homepage/HeroBanner'
[0m [90m 4 |[39m [36mimport[39m { useAuth } [36mfrom[39m [32m'@/context/AuthContext'[39m[33m;[39m[0m
[0m [90m 5 |[39m [36mimport[39m { useCart } [36mfrom[39m [32m'@/context/CartContext'[39m[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m 6 |[39m [36mimport[39m [33mHeroBanner[39m [36mfrom[39m [32m'@/components/homepage/HeroBanner'[39m[33m;[39m[0m
[0m [90m 7 |[39m [36mimport[39m [33mShopByCategory[39m [36mfrom[39m [32m'@/components/homepage/ShopByCategory'[39m[33m;[39m[0m
[0m [90m 8 |[39m [36mimport[39m [33mFeaturedProducts[39m [36mfrom[39m [32m'@/components/homepage/FeaturedProducts'[39m[33m;[39m[0m
[0m [90m 9 |[39m [36mimport[39m [33mPopularDeals[39m [36mfrom[39m [32m'@/components/homepage/PopularDeals'[39m[33m;[39m[0m

https://nextjs.org/docs/messages/module-not-found
 ⨯ ./app/page.tsx:6:0
Module not found: Can't resolve '@/components/homepage/HeroBanner'
[0m [90m 4 |[39m [36mimport[39m { useAuth } [36mfrom[39m [32m'@/context/AuthContext'[39m[33m;[39m[0m
[0m [90m 5 |[39m [36mimport[39m { useCart } [36mfrom[39m [32m'@/context/CartContext'[39m[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m 6 |[39m [36mimport[39m [33mHeroBanner[39m [36mfrom[39m [32m'@/components/homepage/HeroBanner'[39m[33m;[39m[0m
[0m [90m 7 |[39m [36mimport[39m [33mShopByCategory[39m [36mfrom[39m [32m'@/components/homepage/ShopByCategory'[39m[33m;[39m[0m
[0m [90m 8 |[39m [36mimport[39m [33mFeaturedProducts[39m [36mfrom[39m [32m'@/components/homepage/FeaturedProducts'[39m[33m;[39m[0m
[0m [90m 9 |[39m [36mimport[39m [33mPopularDeals[39m [36mfrom[39m [32m'@/components/homepage/PopularDeals'[39m[33m;[39m[0m

https://nextjs.org/docs/messages/module-not-found
 ⨯ ./app/page.tsx:6:0
Module not found: Can't resolve '@/components/homepage/HeroBanner'
[0m [90m 4 |[39m [36mimport[39m { useAuth } [36mfrom[39m [32m'@/context/AuthContext'[39m[33m;[39m[0m
[0m [90m 5 |[39m [36mimport[39m { useCart } [36mfrom[39m [32m'@/context/CartContext'[39m[33m;[39m[0m
[0m[31m[1m>[22m[39m[90m 6 |[39m [36mimport[39m [33mHeroBanner[39m [36mfrom[39m [32m'@/components/homepage/HeroBanner'[39m[33m;[39m[0m
[0m [90m 7 |[39m [36mimport[39m [33mShopByCategory[39m [36mfrom[39m [32m'@/components/homepage/ShopByCategory'[39m[33m;[39m[0m
[0m [90m 8 |[39m [36mimport[39m [33mFeaturedProducts[39m [36mfrom[39m [32m'@/components/homepage/FeaturedProducts'[39m[33m;[39m[0m
[0m [90m 9 |[39m [36mimport[39m [33mPopularDeals[39m [36mfrom[39m [32m'@/components/homepage/PopularDeals'[39m[33m;[39m[0m

https://nextjs.org/docs/messages/module-not-found
 ✓ Compiled in 3.2s (3058 modules)
 ✓ Compiled in 1652ms (3019 modules)
 ✓ Compiled /not-found in 406ms (1408 modules)
 ✓ Compiled (1411 modules)
 ✓ Compiled in 1261ms (3019 modules)
 ✓ Compiled /not-found in 213ms (1408 modules)
 ✓ Compiled (1411 modules)
 ⨯ Failed to generate static paths for /[slug]:
Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ]
}
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/admin'
}
 ○ Compiling /_error ...
 ✓ Compiled /_error in 1694ms (3172 modules)
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/admin'
}
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/favicon.ico'
}
 ✓ Compiled in 1164ms (3186 modules)
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/favicon.ico'
}
 ✓ Compiled in 889ms (3186 modules)
 ✓ Compiled in 1133ms (3184 modules)
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ components/layouts/CustomerLayout.tsx (186:9) @ searchOpen
 ⨯ ReferenceError: searchOpen is not defined
    at CustomerLayout (./components/layouts/CustomerLayout.tsx:365:21)
[0m [90m 184 |[39m[0m
[0m [90m 185 |[39m         {[90m/* Search bar */[39m}[0m
[0m[31m[1m>[22m[39m[90m 186 |[39m         {searchOpen [33m&&[39m ([0m
[0m [90m     |[39m         [31m[1m^[22m[39m[0m
[0m [90m 187 |[39m           [33m<[39m[33mdiv[39m className[33m=[39m[32m"border-t border-gray-200 px-4 py-3"[39m[33m>[39m[0m
[0m [90m 188 |[39m             [33m<[39m[33mdiv[39m className[33m=[39m[32m"max-w-7xl mx-auto"[39m[33m>[39m[0m
[0m [90m 189 |[39m               [33m<[39m[33mdiv[39m className[33m=[39m[32m"relative"[39m[33m>[39m[0m
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/favicon.ico'
}
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/favicon.ico'
}
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/favicon.ico'
}
 ✓ Compiled in 1093ms (3184 modules)
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/favicon.ico'
}
 ⨯ Error: Cannot find module './vendor-chunks/@heroicons.js'
Require stack:
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js
- /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js
- /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js
    at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:55:36
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.<anonymous> (node:internal/modules/cjs/loader:1487:12)
    at mod.require (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require-hook.js:65:28)
    at require (node:internal/modules/helpers:135:16) {
  type: 'Error',
  code: 'MODULE_NOT_FOUND',
  requireStack: [
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/webpack-runtime.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/server/app/[slug]/page.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/require.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/load-components.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/build/utils.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/dev/static-paths-worker.js',
    '/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/compiled/jest-worker/processChild.js'
  ],
  page: '/favicon.ico'
}
 ○ Compiling /_error ...
 ✓ Compiled /_error in 671ms (3167 modules)
 ✓ Compiled /[slug] in 247ms (1449 modules)
 ✓ Compiled in 4.3s (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/server-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 1839ms (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 3.1s (3176 modules)
 ✓ Compiled in 1397ms (3176 modules)
 ✓ Compiled in 1171ms (3176 modules)
 ✓ Compiled in 7.1s (3176 modules)
 ✓ Compiled in 2.4s (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: ENOENT: no such file or directory, rename '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development/0.pack.gz_' -> '/home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development/0.pack.gz'
 ✓ Compiled in 2.9s (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 1836ms (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 3.6s (3176 modules)
 ✓ Compiled in 1264ms (3176 modules)
 ✓ Compiled in 1031ms (3176 modules)
 ✓ Compiled in 961ms (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 1254ms (3176 modules)
 ✓ Compiled in 1089ms (3176 modules)
 ✓ Compiled in 995ms (3176 modules)
 ✓ Compiled in 1248ms (3176 modules)
 ✓ Compiled in 1437ms (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/server-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 1633ms (3176 modules)
 ✓ Compiled in 1574ms (3176 modules)
 ✓ Compiled in 1259ms (3176 modules)
 ✓ Compiled in 1026ms (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 1539ms (3176 modules)
 ✓ Compiled in 1151ms (3176 modules)
 ✓ Compiled in 1817ms (3176 modules)
 ✓ Compiled in 795ms (3176 modules)
 ✓ Compiled in 795ms (3176 modules)
 ✓ Compiled in 798ms (3176 modules)
 ✓ Compiled in 856ms (3176 modules)
 ✓ Compiled in 1016ms (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 920ms (3176 modules)
 ✓ Compiled in 770ms (3176 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/server-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 1035ms (3176 modules)
 ✓ Compiled in 684ms (3176 modules)
 ✓ Compiled in 837ms (3176 modules)
 ✓ Compiled in 771ms (3176 modules)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1601:65)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 791ms (1736 modules)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1601:65)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 634ms (1736 modules)
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1601:65)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/server-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 1389ms (3162 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 531ms (1722 modules)
 ✓ Compiled in 254ms (1722 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 686ms (1722 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ⚠ Failed to reload dynamic routes: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1601:65)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ⨯ unhandledRejection: Error: You cannot use different slug names for the same dynamic path ('id' !== 'slug').
    at handleSlug (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:94:31)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:131:17)
    at UrlNode._insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:142:40)
    at UrlNode.insert (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:13:14)
    at /home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:46
    at Array.forEach (<anonymous>)
    at getSortedRoutes (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/shared/lib/router/utils/sorted-routes.js:165:21)
    at DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/default-route-matcher-manager.js:112:55)
    at async DevRouteMatcherManager.reload (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/future/route-matcher-managers/dev-route-matcher-manager.js:110:9)
    at async Object.propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/render-server.js:68:13)
    at async propagateServerField (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:142:9)
    at async Watchpack.<anonymous> (/home/<USER>/Git Projects/ondc/ondc-seller/node_modules/next/dist/server/lib/router-utils/setup-dev-bundler.js:1670:17)
 ✓ Compiled in 826ms (1736 modules)
 ✓ Compiled in 465ms (1736 modules)
 ✓ Compiled in 2.5s (1736 modules)
<w> [webpack.cache.PackFileCacheStrategy] Restoring pack from /home/<USER>/Git Projects/ondc/ondc-seller/packages/frontend/.next/cache/webpack/client-development.pack.gz failed: TypeError: Cannot read properties of undefined (reading 'hasStartTime')
 ✓ Compiled in 576ms (1736 modules)
 ✓ Compiled /not-found in 223ms (1443 modules)
 ○ Compiling /_error ...
 ✓ Compiled /_error in 608ms (3167 modules)
