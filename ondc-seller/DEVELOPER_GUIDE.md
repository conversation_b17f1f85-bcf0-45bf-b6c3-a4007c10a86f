# ONDC Seller Platform - Developer Guide

## Table of Contents
1. [Project Overview](#project-overview)
2. [Architecture](#architecture)
3. [Development Setup](#development-setup)
4. [Frontend Development](#frontend-development)
5. [Backend Development](#backend-development)
6. [Authentication & Authorization](#authentication--authorization)
7. [Database Configuration](#database-configuration)
8. [Real-time Notifications](#real-time-notifications)
9. [Testing](#testing)
10. [Deployment](#deployment)
11. [API Documentation](#api-documentation)
12. [Troubleshooting](#troubleshooting)

## Project Overview

The ONDC Seller Platform is a comprehensive e-commerce solution built for the Open Network for Digital Commerce (ONDC) ecosystem. It provides both admin and customer interfaces with Material-UI components, comprehensive form validation, and real-time features.

### Key Features
- **Dual Interface Architecture**: Separate admin (`/admin/*`) and customer (`/`) interfaces
- **Material-UI Integration**: Modern, accessible UI components
- **Comprehensive Form Validation**: Real-time validation with error handling
- **Multi-tenant Support**: Support for multiple store configurations
- **ONDC Integration**: Native support for ONDC protocols and APIs
- **Real-time Notifications**: Live updates using Supabase or RabbitMQ
- **Role-based Access Control**: Secure authentication with OneSSO/Keycloak

## Architecture

```
ondc-seller/
├── packages/
│   ├── frontend/          # Next.js 14 frontend application
│   │   ├── app/           # App router pages
│   │   │   ├── admin/     # Admin interface routes
│   │   │   └── (customer) # Customer interface routes
│   │   ├── components/    # Reusable UI components
│   │   ├── hooks/         # Custom React hooks
│   │   ├── lib/           # Utility libraries
│   │   └── __tests__/     # Test files
│   └── backend/           # Node.js/Express backend (future)
├── docs/                  # Documentation
└── scripts/               # Build and deployment scripts
```

## Development Setup

### Prerequisites
- Node.js 18+ and npm
- Git
- Docker (for database)
- VS Code (recommended)

### Quick Start

1. **Clone the repository**
```bash
git clone <repository-url>
cd ondc-seller
```

2. **Install dependencies**
```bash
cd packages/frontend
npm install
```

3. **Environment setup**
```bash
cp .env.example .env.local
# Edit .env.local with your configuration
```

4. **Start development server**
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Admin Interface: http://localhost:3000/admin

### Development vs Production Modes

#### Development Mode
- **Authentication**: Hardcoded credentials (userName: `demo`, password: `demo`)
- **Database**: Local development database
- **Hot Reload**: Enabled for rapid development
- **Debug Logging**: Verbose logging enabled

#### Production Mode
- **Authentication**: OneSSO/Keycloak integration
- **Database**: Self-hosted Supabase
- **Optimized Build**: Minified and optimized assets
- **Error Logging**: Production-level logging

## Frontend Development

### Technology Stack
- **Framework**: Next.js 14 with App Router
- **UI Library**: Material-UI (MUI) v6
- **Styling**: CSS-in-JS with MUI's sx prop
- **State Management**: React hooks and context
- **Form Handling**: Custom validation hooks
- **Testing**: Jest + React Testing Library

### Key Components

#### Settings Pages
- **Admin Settings**: `/admin/settings` - Comprehensive store configuration
- **Customer Settings**: `/settings` - User-facing settings with tabs

#### Form Validation
```typescript
// Example usage of form validation hook
import { useSettingsFormValidation } from '@/hooks/useFormValidation';

const {
  data,
  errors,
  touched,
  isValid,
  isSubmitting,
  handleChange,
  handleBlur,
  handleSubmit,
} = useSettingsFormValidation({
  initialData: initialSettings,
  onSubmit: async (data) => {
    // Handle form submission
  },
});
```

#### Material-UI Integration
```typescript
// Example Material-UI form component
<TextField
  fullWidth
  label="Store Name"
  name="storeName"
  value={formData.storeName || ''}
  onChange={handleChange}
  onBlur={handleBlur}
  error={Boolean(errors.storeName && touched.storeName)}
  helperText={errors.storeName && touched.storeName && errors.storeName}
  disabled={isSubmitting}
  variant="outlined"
/>
```

### Development Guidelines

1. **Component Structure**
   - Use functional components with hooks
   - Implement proper TypeScript types
   - Follow Material-UI design patterns

2. **Form Handling**
   - Use the custom `useFormValidation` hook
   - Implement real-time validation on blur/change
   - Provide comprehensive error messages

3. **Testing**
   - Write unit tests for all components
   - Test form validation and user interactions
   - Maintain >90% test coverage

4. **Styling**
   - Use Material-UI's sx prop for styling
   - Follow responsive design principles
   - Maintain consistent spacing and typography

### Running Tests
```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- __tests__/admin/settings.test.tsx
```

## Backend Development

### Technology Stack (Future Implementation)
- **Framework**: Node.js with Express.js
- **Database**: Self-hosted Supabase (PostgreSQL)
- **Authentication**: OneSSO/Keycloak integration
- **Real-time**: Supabase real-time or RabbitMQ
- **API Documentation**: OpenAPI 3.0 specifications

### API Structure
```
/api/
├── auth/              # Authentication endpoints
├── admin/             # Admin-only endpoints
│   ├── settings/      # Store settings management
│   ├── products/      # Product management
│   └── orders/        # Order management
├── customer/          # Customer endpoints
│   ├── profile/       # User profile management
│   └── orders/        # Order history
└── ondc/              # ONDC integration endpoints
```

### Database Schema
```sql
-- Example settings table
CREATE TABLE store_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL,
  store_name VARCHAR(255) NOT NULL,
  store_email VARCHAR(255) NOT NULL,
  store_phone VARCHAR(50),
  store_address JSONB,
  ondc_settings JSONB,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## Authentication & Authorization

### OneSSO/Keycloak Integration

#### Development Setup
```javascript
// Development mode - hardcoded credentials
const DEV_CREDENTIALS = {
  userName: 'demo',
  password: 'demo'
};
```

#### Production Setup
```javascript
// Keycloak configuration
const keycloakConfig = {
  url: process.env.KEYCLOAK_URL,
  realm: process.env.KEYCLOAK_REALM,
  clientId: process.env.KEYCLOAK_CLIENT_ID,
};
```

### Role-based Access Control
- **Admin**: Full access to admin interface and APIs
- **Customer**: Access to customer interface and profile management
- **Guest**: Limited access to public pages

## Database Configuration

### Self-hosted Supabase Setup

1. **Docker Compose Setup**
```yaml
version: '3.8'
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ondc_seller
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: your_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  supabase:
    image: supabase/supabase:latest
    environment:
      DATABASE_URL: *************************************************/ondc_seller
    ports:
      - "8000:8000"
    depends_on:
      - postgres
```

2. **Environment Variables**
```bash
DATABASE_URL=postgresql://postgres:password@localhost:5432/ondc_seller
SUPABASE_URL=http://localhost:8000
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## Real-time Notifications

### Option 1: Supabase Real-time
```typescript
// Subscribe to real-time updates
const supabase = createClient(supabaseUrl, supabaseKey);

supabase
  .channel('store_settings')
  .on('postgres_changes', 
    { event: 'UPDATE', schema: 'public', table: 'store_settings' },
    (payload) => {
      console.log('Settings updated:', payload);
      // Update UI accordingly
    }
  )
  .subscribe();
```

### Option 2: RabbitMQ
```typescript
// RabbitMQ setup for notifications
import amqp from 'amqplib';

const connection = await amqp.connect(process.env.RABBITMQ_URL);
const channel = await connection.createChannel();

// Subscribe to notifications
await channel.assertQueue('settings_updates');
channel.consume('settings_updates', (message) => {
  const update = JSON.parse(message.content.toString());
  // Handle notification
});
```

## Testing

### Test Structure
```
__tests__/
├── admin/
│   └── settings.test.tsx      # Admin settings tests (11 tests)
├── settings.test.tsx          # Customer settings tests (16 tests)
├── components/                # Component tests
└── utils/                     # Utility function tests
```

### Test Coverage Requirements
- **Components**: >95% coverage
- **Hooks**: >90% coverage
- **Utilities**: >95% coverage
- **Integration**: Key user flows tested

### Running Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in CI mode
npm run test:ci
```

## Deployment

### Environment Configuration
```bash
# Production environment variables
NODE_ENV=production
NEXT_PUBLIC_API_URL=https://api.ondcseller.com
KEYCLOAK_URL=https://auth.ondcseller.com
DATABASE_URL=postgresql://user:<EMAIL>:5432/ondc_seller
```

### Build Process
```bash
# Build for production
npm run build

# Start production server
npm start

# Docker deployment
docker build -t ondc-seller-frontend .
docker run -p 3000:3000 ondc-seller-frontend
```

### Monitoring & Logging
- **Error Tracking**: Implement error boundaries and logging
- **Performance Monitoring**: Monitor Core Web Vitals
- **User Analytics**: Track user interactions and form submissions

## API Documentation

### OpenAPI Specification
All APIs are documented using OpenAPI 3.0 specifications. The documentation includes:
- Endpoint descriptions
- Request/response schemas
- Authentication requirements
- Error codes and messages

### Example API Endpoint
```yaml
/api/admin/settings:
  get:
    summary: Get store settings
    security:
      - bearerAuth: []
    responses:
      200:
        description: Store settings retrieved successfully
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StoreSettings'
```

## Troubleshooting

### Common Issues

1. **Build Failures**
   - Check TypeScript errors: `npx tsc --noEmit`
   - Verify dependencies: `npm install`
   - Clear cache: `rm -rf .next && npm run build`

2. **Test Failures**
   - Update snapshots: `npm test -- -u`
   - Check mock implementations
   - Verify test environment setup

3. **Authentication Issues**
   - Verify environment variables
   - Check Keycloak configuration
   - Validate token expiration

4. **Database Connection**
   - Verify DATABASE_URL format
   - Check network connectivity
   - Validate credentials

### Debug Mode
```bash
# Enable debug logging
DEBUG=ondc:* npm run dev

# Check application logs
tail -f logs/application.log
```

### Support
For additional support:
- Check the GitHub issues
- Review the API documentation
- Contact the development team

---

**Last Updated**: December 2024
**Version**: 1.0.0
