# Frontend Developer Guide

This guide provides information for frontend developers working on the ONDC Seller Platform.

## Technology Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Query / SWR
- **API Client**: Axios
- **Testing**: Jest + React Testing Library

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- npm (v9 or later)

### Installation

1. Clone the repository:

```bash
git clone https://github.com/your-username/ondc-seller.git
cd ondc-seller
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

```
packages/frontend/
├── public/              # Static assets
├── src/
│   ├── app/             # Next.js App Router pages
│   │   ├── layout.tsx   # Root layout
│   │   ├── page.tsx     # Home page
│   │   └── products/    # Products pages
│   ├── components/      # Reusable UI components
│   └── lib/             # Utility functions and API clients
├── .env                 # Environment variables
├── next.config.js       # Next.js configuration
├── package.json         # Package dependencies
├── tailwind.config.js   # Tailwind CSS configuration
└── tsconfig.json        # TypeScript configuration
```

## Development Workflow

### Creating a New Page

1. Create a new directory in `src/app` for your page
2. Create a `page.tsx` file in the directory
3. Export a React component as the default export

Example:

```tsx
// src/app/orders/page.tsx
export default function OrdersPage() {
  return (
    <div>
      <h1>Orders</h1>
      {/* Page content */}
    </div>
  );
}
```

### Creating a New Component

1. Create a new file in `src/components`
2. Export a React component

Example:

```tsx
// src/components/OrderCard.tsx
interface OrderCardProps {
  id: string;
  total: number;
  status: string;
}

export default function OrderCard({ id, total, status }: OrderCardProps) {
  return (
    <div className="bg-white p-4 rounded shadow">
      <h2>Order #{id}</h2>
      <p>Total: ${total}</p>
      <p>Status: {status}</p>
    </div>
  );
}
```

### API Integration

The API client is configured in `src/lib/api.ts`. It provides typed adapter functions for communicating with the Medusa backend with multi-tenancy support.

#### API Client Structure

The API client consists of several key components:

1. **API Instances**
   - `medusaApi`: For direct communication with the Medusa backend
   - `api`: For general API calls (including custom endpoints)

2. **Adapter Functions**
   - `fetchProducts`: Retrieves products with optional filtering
   - `createOrder`: Creates new orders with items and shipping details
   - `updateInventory`: Updates inventory levels for products

3. **API Groups**
   - `productsApi`: Product-related operations
   - `ordersApi`: Order-related operations
   - `inventoryApi`: Inventory-related operations
   - `authApi`: Authentication operations

#### Using the API Client

```tsx
import { productsApi } from '@/lib/api';
import { ProductQueryParams, ProductResponse } from '@/types/api';
import { useEffect, useState } from 'react';

export default function ProductsPage() {
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setLoading(true);
        // Use the typed API client with query parameters
        const params: ProductQueryParams = {
          limit: 10,
          offset: 0,
          category_id: 'electronics'
        };

        const response: ProductResponse = await productsApi.getAll(params);
        setProducts(response.products);
        setLoading(false);
      } catch (err) {
        setError(err);
        setLoading(false);
      }
    };

    fetchProducts();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>Products</h1>
      {products.map(product => (
        <div key={product.id}>
          <h2>{product.title}</h2>
          <p>{product.description}</p>
        </div>
      ))}
    </div>
  );
}
```

#### Multi-tenancy Support

The API client automatically includes the tenant ID in all requests:

```tsx
import { setCurrentTenant } from '@/lib/tenant';
import { Tenant } from '@/types/api';

// Set the current tenant
const tenant: Tenant = {
  id: 'tenant-123',
  name: 'Seller Store',
  // ...other tenant properties
};

// This will store the tenant in localStorage and use it for all API requests
setCurrentTenant(tenant);

// Now all API calls will include the x-tenant-id header
const products = await productsApi.getAll();
```

#### Error Handling

The API client includes built-in error handling with retry logic:

```tsx
import { createOrder } from '@/lib/api';
import { OrderCreateData } from '@/types/api';

try {
  const orderData: OrderCreateData = {
    // Order details
  };

  // This will automatically retry on network failures
  const response = await createOrder(orderData);
  console.log('Order created:', response.order);
} catch (error) {
  // Error is already formatted with consistent structure
  console.error('Failed to create order:', error.message);

  // You can check for specific error codes
  if (error.status === 401) {
    // Handle authentication error
  }
}
```

## Authentication

In development mode, the app uses hardcoded credentials:

- Username: `demo`
- Password: `demo`

In production, you'll need to integrate with oneSSO (Keycloak).

## Testing

### Running Tests

```bash
npm run test
```

### Writing Tests

Tests are written using Jest and React Testing Library. Create a test file with the `.test.tsx` extension:

```tsx
// src/components/ProductCard.test.tsx
import { render, screen } from '@testing-library/react';
import ProductCard from './ProductCard';

describe('ProductCard', () => {
  it('renders product information', () => {
    render(
      <ProductCard
        id="1"
        title="Test Product"
        description="Test Description"
        thumbnail={null}
        price={{ amount: 1000, currency_code: 'inr' }}
        inventory_quantity={10}
      />
    );

    expect(screen.getByText('Test Product')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByText('₹10.00')).toBeInTheDocument();
    expect(screen.getByText('In Stock (10)')).toBeInTheDocument();
  });
});
```

## Deployment

### Building for Production

```bash
npm run build
```

### Starting the Production Server

```bash
npm run start
```

### Environment Variables

- `NEXT_PUBLIC_MEDUSA_URL`: URL of the Medusa backend (default: http://localhost:9000)
- `NEXT_PUBLIC_API_URL`: URL of the general API (defaults to MEDUSA_URL if not set)
- `DEV_USERNAME`: Username for development mode (default: demo)
- `DEV_PASSWORD`: Password for development mode (default: demo)

## Customization

### Styling

This project uses Tailwind CSS for styling. You can customize the theme in `tailwind.config.js`.

### Adding Dependencies

```bash
npm install package-name
```

## Troubleshooting

### Common Issues

- **API Connection Issues**: Make sure the backend server is running and the `NEXT_PUBLIC_MEDUSA_URL` environment variable is set correctly.
- **Authentication Issues**: Check that the authentication headers are being sent correctly. In development mode, verify that the `DEV_USERNAME` and `DEV_PASSWORD` environment variables are set.
- **Multi-tenancy Issues**: Ensure that the tenant ID is being set correctly. Check the localStorage for the `currentTenantId` value.
- **TypeScript Errors**: Run `npm run lint` to check for TypeScript errors. Make sure all API types are properly defined in the `types/api.ts` file.
- **Build Errors**: Make sure all dependencies are installed and the environment variables are set correctly.
- **Network Errors**: If you're seeing network errors, check your internet connection. The API client includes retry logic for network failures, but persistent issues may require further investigation.
