# Strapi CMS and Medusa Integration Guide

This guide explains how to integrate Strapi CMS with Medusa Commerce in the ONDC Seller Platform.

## Architecture Overview

The integration follows a headless architecture:

1. **Strapi CMS**: Manages content like pages, banners, and marketing content
2. **Medusa Backend**: Handles commerce functionality like products, orders, and checkout
3. **Next.js Frontend**: Consumes data from both systems and presents a unified experience to users

## Integration Components

### 1. API Adapter Layer

The API adapter layer (`/packages/frontend/lib/api/index.js`) provides a unified interface for accessing data from both Strapi and Medusa. It:

- Creates separate axios instances for each API
- Normalizes data structures from both systems
- Supports multi-tenancy through headers
- Provides convenience methods for common operations

### 2. Data Provider

The data provider (`/packages/frontend/lib/data-provider.js`) combines data from both systems to create unified data objects for the frontend. It:

- Fetches data from both systems in parallel
- Combines related data (e.g., product details from Medusa with additional content from Strapi)
- Handles errors gracefully
- Supports multi-tenancy

### 3. Tenant Context

The tenant context (`/packages/frontend/context/TenantContext.js`) manages tenant information and selection. It:

- Loads tenant data from Strapi
- Allows users to switch between tenants
- Sets the tenant ID in API requests
- Provides tenant information to components

## Integration Points

### 1. Content Types in Strapi

To integrate with Medusa, create these content types in Strapi:

#### Products (Extended Content)

Create a "Product" content type in Strapi with:

- `medusa_id` (Text): References the product ID in Medusa
- `long_description` (Rich Text): Extended product description
- `specifications` (Component, Repeatable): Product specifications
- `videos` (Media): Product videos
- `documents` (Media): Product documents

#### Categories (Extended Content)

Create a "Product Category" content type in Strapi with:

- `medusa_id` (Text): References the collection ID in Medusa
- `description` (Rich Text): Category description
- `banner_image` (Media): Category banner image
- `seo` (Component): SEO metadata

#### Sellers/Tenants

Create a "Seller" content type in Strapi with:

- `name` (Text): Seller name
- `description` (Rich Text): Seller description
- `logo` (Media): Seller logo
- `banner` (Media): Seller banner
- `ondc_seller_id` (Text): ONDC seller ID (used for multi-tenancy)

#### Pages

Create a "Page" content type in Strapi with:

- `title` (Text): Page title
- `slug` (UID): Page URL slug
- `content` (Rich Text): Page content
- `featured_image` (Media): Featured image
- `seo` (Component): SEO metadata

#### Banners

Create a "Banner" content type in Strapi with:

- `title` (Text): Banner title
- `subtitle` (Text): Banner subtitle
- `image` (Media): Banner image
- `cta_text` (Text): Call-to-action text
- `cta_link` (Text): Call-to-action link
- `active` (Boolean): Whether the banner is active

### 2. Multi-Tenancy Support

The integration supports multi-tenancy through:

1. **Tenant Selection**: Users can select a tenant in the frontend
2. **Tenant Header**: The selected tenant ID is included in requests to Medusa
3. **Tenant-Specific Content**: Content can be filtered by tenant in Strapi

To implement multi-tenancy in Medusa:

1. Install the Medusa multi-tenancy plugin
2. Configure the plugin to use the `x-tenant-id` header
3. Ensure the Medusa backend properly isolates data by tenant

### 3. Frontend Integration

The frontend integrates with both systems through:

1. **API Adapter**: Provides a unified interface for API calls
2. **Data Provider**: Combines data from both systems
3. **Tenant Context**: Manages tenant selection and information
4. **Page Components**: Consume combined data from both systems

## Implementation Steps

### 1. Set Up Environment Variables

Create a `.env` file in the frontend package with:

```
# Strapi Configuration
NEXT_PUBLIC_STRAPI_API_URL=http://localhost:1339
STRAPI_API_TOKEN=your_strapi_api_token_here

# Medusa Configuration
NEXT_PUBLIC_MEDUSA_API_URL=http://localhost:9000

# Default Tenant ID (optional)
NEXT_PUBLIC_DEFAULT_TENANT_ID=
```

### 2. Install Dependencies

```bash
cd packages/frontend
npm install axios
```

### 3. Create API Adapter

Create the API adapter layer as shown in `/packages/frontend/lib/api/index.js`.

### 4. Create Data Provider

Create the data provider as shown in `/packages/frontend/lib/data-provider.js`.

### 5. Create Tenant Context

Create the tenant context as shown in `/packages/frontend/context/TenantContext.js`.

### 6. Update Next.js App

Update `_app.js` to include the tenant provider:

```jsx
import React from 'react';
import { TenantProvider } from '../context/TenantContext';
import '../styles/globals.css';

function MyApp({ Component, pageProps }) {
  return (
    <TenantProvider>
      <Component {...pageProps} />
    </TenantProvider>
  );
}

export default MyApp;
```

### 7. Create Page Components

Create page components that use data from both systems, as shown in:

- `/packages/frontend/pages/index.js`
- `/packages/frontend/pages/[slug].js`
- `/packages/frontend/pages/products/[id].js`

## Data Flow

1. **User visits a page**: Next.js server renders the page
2. **getServerSideProps**: Fetches data from both Strapi and Medusa
3. **Data Provider**: Combines data from both systems
4. **Page Component**: Renders the combined data
5. **User interacts**: Client-side code handles interactions (e.g., add to cart)

## Best Practices

1. **Keep Systems Decoupled**: Maintain clear boundaries between Strapi and Medusa
2. **Use Normalized Data Structures**: Convert API responses to consistent formats
3. **Implement Caching**: Cache responses to improve performance
4. **Handle Errors Gracefully**: Provide fallbacks when one system is unavailable
5. **Use TypeScript**: Add type definitions for better code quality
6. **Document Integration Points**: Keep documentation up-to-date

## Troubleshooting

### Strapi API Returns 403 Forbidden

1. Check API permissions in Strapi (Settings > Roles > Public)
2. Create and use an API token
3. Verify the API token is correctly set in environment variables

### Medusa API Returns 404 Not Found

1. Check if the Medusa server is running
2. Verify the API URL is correct
3. Check if the requested resource exists

### Multi-Tenancy Issues

1. Verify the tenant ID is correctly set in the API requests
2. Check if the Medusa multi-tenancy plugin is properly configured
3. Ensure data is properly isolated by tenant in both systems

## Further Enhancements

1. **Implement Caching**: Add Redis caching for API responses
2. **Add TypeScript**: Convert JavaScript files to TypeScript
3. **Add Authentication**: Implement user authentication
4. **Improve Error Handling**: Add more robust error handling
5. **Add Testing**: Write unit and integration tests
