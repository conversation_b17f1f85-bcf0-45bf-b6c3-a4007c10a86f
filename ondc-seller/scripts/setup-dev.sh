#!/bin/bash

# Exit on error
set -e

# Print commands
set -x

# Create directories if they don't exist
mkdir -p packages/frontend
mkdir -p packages/backend
mkdir -p packages/prisma

# Install dependencies
npm install

# Set up Prisma
cd packages/prisma
npm install
npm run db:generate

# Set up Backend
cd ../backend
npm install

# Set up Frontend
cd ../frontend
npm install

# Return to root
cd ../..

# Make scripts executable
chmod +x packages/backend/scripts/generate-api-docs.js
chmod +x scripts/setup-dev.sh

echo "Development environment setup complete!"
